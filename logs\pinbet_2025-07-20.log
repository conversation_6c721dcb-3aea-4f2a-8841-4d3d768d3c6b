2025-07-20 13:34:44,075 - PinBet - INFO - 尝试自动登录平博账号: mfc123
2025-07-20 13:34:45,233 - PinBet - INFO - 登录成功(带tokens): mfc123
2025-07-20 13:34:45,608 - PinBet - INFO - 余额获取成功: 26389.05
2025-07-20 13:34:45,993 - PinBet - INFO - 余额获取成功: 26389.05
2025-07-20 13:34:51,226 - PinBet - INFO - 余额获取成功: 26389.05
2025-07-20 13:35:02,519 - PinBet - INFO - 余额获取成功: 26389.05
2025-07-20 13:36:04,350 - PinBet - INFO - 余额获取成功: 26389.05
2025-07-20 13:47:31,297 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 13:47:31,297 - PinBet - INFO - 开始查询未结算注单
2025-07-20 13:47:51,373 - PinBet - ERROR - 获取未结算注单异常: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
Traceback (most recent call last):
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
               ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
                       ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 1378, in getresponse
    response.begin()
  File "D:\Program Files\Python311\Lib\http\client.py", line 318, in begin
    version, status, reason = self._read_status()
                              ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 287, in _read_status
    raise RemoteDisconnected("Remote end closed connection without"
http.client.RemoteDisconnected: Remote end closed connection without response

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\Python311\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\util\util.py", line 38, in reraise
    raise value.with_traceback(tb)
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
               ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
                       ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 1378, in getresponse
    response.begin()
  File "D:\Program Files\Python311\Lib\http\client.py", line 318, in begin
    version, status, reason = self._read_status()
                              ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 287, in _read_status
    raise RemoteDisconnected("Remote end closed connection without"
urllib3.exceptions.ProtocolError: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "d:\betburgerbot\platforms\pinbet_bk.py", line 1426, in get_pending_wagers
    response = self.session.post(
               ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\sessions.py", line 637, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\adapters.py", line 682, in send
    raise ConnectionError(err, request=request)
requests.exceptions.ConnectionError: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 13:48:21,195 - PinBet - INFO - 余额获取成功: 26389.05
2025-07-20 13:48:26,954 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 13:48:26,954 - PinBet - INFO - 开始查询未结算注单
2025-07-20 13:48:27,335 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 13:48:27,336 - PinBet - INFO - 处理完成: 原始记录 1 条，处理后 1 条记录
2025-07-20 14:30:05,655 - PinBet - INFO - 检测到现有会话，验证有效性
2025-07-20 14:30:07,195 - PinBet - INFO - 余额获取成功: 28369.44
2025-07-20 14:30:07,197 - PinBet - INFO - 现有会话验证成功
2025-07-20 14:30:07,690 - PinBet - INFO - 余额获取成功: 28369.44
2025-07-20 14:30:13,634 - PinBet - INFO - 余额获取成功: 28369.44
2025-07-20 14:30:15,066 - PinBet - INFO - 余额获取成功: 28369.44
2025-07-20 14:30:21,887 - PinBet - INFO - 解析结果: 法国 vs 日本, 让球=日本, 盘口=19.5, 赔率=2.09
2025-07-20 14:30:21,888 - PinBet - INFO - 开始执行投注: 法国 vs 日本, 让球=日本
2025-07-20 14:30:21,888 - PinBet - INFO - 获取今日比赛数据
2025-07-20 14:30:21,888 - PinBet - INFO - 获取今日篮球比赛数据
2025-07-20 14:30:22,737 - PinBet - INFO - 今日篮球比赛数据获取成功
2025-07-20 14:30:22,738 - PinBet - INFO - 获取早场比赛数据
2025-07-20 14:30:22,738 - PinBet - INFO - 获取早场篮球比赛数据
2025-07-20 14:30:23,265 - PinBet - INFO - 早场篮球比赛数据获取成功
2025-07-20 14:30:23,265 - PinBet - INFO - 查找匹配比赛: 法国 vs 日本, 让球=日本, 盘口=19.5
2025-07-20 14:30:23,266 - PinBet - INFO - 找到匹配比赛: 法国 vs 日本 (联赛: 国际篮联 - U19女子篮球世界杯) (匹配分数: 1.00)
2025-07-20 14:30:23,266 - PinBet - INFO - 可用的让球盘口: ['23.5', '23.0', '22.5', '22.0', '21.5', '21.0', '20.5', '20.0', '19.5', '19.0', '18.5']
2025-07-20 14:30:23,266 - PinBet - INFO - 标准化后的目标盘口值: 19.5
2025-07-20 14:30:23,266 - PinBet - INFO - 找到精确匹配的让球盘口: 19.5, line_id=49602190329
2025-07-20 14:30:23,266 - PinBet - INFO - 获取投注表单: event_id=1611849832, line_id=49602190329, period_num=0, bet_type=2, team_side=away, handicap=19.5
2025-07-20 14:30:23,267 - PinBet - INFO - 让分盘设置 - team_side: away, team_side_code: 1, selection_direction: 1
2025-07-20 14:30:23,267 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 14:30:23,267 - PinBet - INFO - 构建投注ID: odds_id=1611849832|0|2|1|1|19.5, selection_id=49602190329|1611849832|0|2|1|1|19.5|1
2025-07-20 14:30:23,267 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1752993023267', 'withCredentials': 'true'}
2025-07-20 14:30:23,267 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611849832|0|2|1|1|19.5", "oddsSelectionsType": "NORMAL", "selectionId": "49602190329|1611849832|0|2|1|1|19.5|1"}]}
2025-07-20 14:30:23,879 - PinBet - INFO - 投注表单获取成功: 1611849832, 赔率: 2.090
2025-07-20 14:30:23,879 - PinBet - INFO - API返回的selectionId: 3183821417|49602190329|1611849832|0|2|1|1|19.50|1
2025-07-20 14:30:23,880 - PinBet - INFO - API返回的oddsId: 1611849832|0|2|1|1|19.5
2025-07-20 14:30:23,880 - PinBet - INFO - 将使用投注选项: selectionId=3183821417|49602190329|1611849832|0|2|1|1|19.50|1, oddsId=1611849832|0|2|1|1|19.5, odds=2.090
2025-07-20 14:30:23,880 - PinBet - INFO - 执行投注: 金额=723.44
2025-07-20 14:30:23,880 - PinBet - INFO - 从列表中提取投注信息: selection_id=3183821417|49602190329|1611849832|0|2|1|1|19.50|1, odds_id=1611849832|0|2|1|1|19.5, odds=2.090, stake=723.44
2025-07-20 14:30:24,646 - PinBet - INFO - 投注响应数据: {"response": [{"wagerId": 660844354, "status": "ACCEPTED", "selectionId": "3183821417|49602190329|1611849832|0|2|1|1|19.50|1", "betId": 2099408696, "uniqueRequestId": "59d0ccbe-4177-4e2c-8008-d7e4c81e2de3", "wagerType": "NORMAL", "betterLineWasAccepted": false, "jsonString": "{\"bets\":[{\"status\":\"ACCEPTED\",\"errorCode\":null,\"uniqueRequestId\":\"59d0ccbe-4177-4e2c-8008-d7e4c81e2de3\",\"lineId\":3183821449,\"altLineId\":49602190993,\"price\":2.09,\"minRiskStake\":0.07,\"maxRiskStake\":3588.98,\"minWinStake\":0.08,\"maxWinStake\":3911.98,\"straightBet\":{\"betId\":2099408696,\"uniqueRequestId\":\"59d0ccbe-4177-4e2c-8008-d7e4c81e2de3\",\"wagerNumber\":1,\"placedAt\":\"2025-07-20T06:30:24Z\",\"betStatus\":\"ACCEPTED\",\"betStatus2\":\"ACCEPTED\",\"betType\":\"SPREAD\",\"win\":788.55,\"risk\":723.44,\"oddsFormat\":\"DECIMAL\",\"updateSequence\":2928698636,\"externalRef\":\"660844354\",\"sportId\":4,\"sportName\":\"Basketball\",\"leagueId\":214756,\"leagueName\":\"FIBA - U19 World Cup Women\",\"eventId\":1611849832,\"handicap\":19.5,\"price\":2.09,\"teamName\":\"Japan\",\"team1\":\"France\",\"team2\":\"Japan\",\"periodNumber\":0,\"isLive\":\"FALSE\",\"homeTeamType\":\"Team1\",\"eventStartTime\":\"2025-07-20T12:00:00Z\",\"resultingUnit\":\"Regular\"}}]}", "oddsId": "1611849832|0|2|1|1|19.5", "psBetVO": {"betId": 2099408696, "uniqueRequestId": "59d0ccbe-4177-4e2c-8008-d7e4c81e2de3", "wagerNumber": 1, "placedAt": "2025-07-20T06:30:24Z", "betStatus": "ACCEPTED", "betType": "SPREAD", "win": 788.55, "risk": 723.44, "oddsFormat": "DECIMAL", "updateSequence": 1752993024516, "sportId": 4, "sportName": "Basketball", "leagueId": 214756, "leagueName": "FIBA - U19 World Cup Women", "eventId": 1611849832, "handicap": 19.5, "price": 2.09, "teamName": "Japan", "team1": "France", "team2": "Japan", "periodNumber": 0, "isLive": "FALSE", "eventStartTime": "2025-07-20T12:00:00Z", "resultingUnit": "Regular"}, "odds": 2.09}]}
2025-07-20 14:30:24,647 - PinBet - INFO - 投注成功: 注单ID 660844354, 投注ID 2099408696
2025-07-20 14:30:24,647 - PinBet - INFO - 投注成功: {'response': [{'wagerId': 660844354, 'status': 'ACCEPTED', 'selectionId': '3183821417|49602190329|1611849832|0|2|1|1|19.50|1', 'betId': 2099408696, 'uniqueRequestId': '59d0ccbe-4177-4e2c-8008-d7e4c81e2de3', 'wagerType': 'NORMAL', 'betterLineWasAccepted': False, 'jsonString': '{"bets":[{"status":"ACCEPTED","errorCode":null,"uniqueRequestId":"59d0ccbe-4177-4e2c-8008-d7e4c81e2de3","lineId":3183821449,"altLineId":49602190993,"price":2.09,"minRiskStake":0.07,"maxRiskStake":3588.98,"minWinStake":0.08,"maxWinStake":3911.98,"straightBet":{"betId":2099408696,"uniqueRequestId":"59d0ccbe-4177-4e2c-8008-d7e4c81e2de3","wagerNumber":1,"placedAt":"2025-07-20T06:30:24Z","betStatus":"ACCEPTED","betStatus2":"ACCEPTED","betType":"SPREAD","win":788.55,"risk":723.44,"oddsFormat":"DECIMAL","updateSequence":2928698636,"externalRef":"660844354","sportId":4,"sportName":"Basketball","leagueId":214756,"leagueName":"FIBA - U19 World Cup Women","eventId":1611849832,"handicap":19.5,"price":2.09,"teamName":"Japan","team1":"France","team2":"Japan","periodNumber":0,"isLive":"FALSE","homeTeamType":"Team1","eventStartTime":"2025-07-20T12:00:00Z","resultingUnit":"Regular"}}]}', 'oddsId': '1611849832|0|2|1|1|19.5', 'psBetVO': {'betId': 2099408696, 'uniqueRequestId': '59d0ccbe-4177-4e2c-8008-d7e4c81e2de3', 'wagerNumber': 1, 'placedAt': '2025-07-20T06:30:24Z', 'betStatus': 'ACCEPTED', 'betType': 'SPREAD', 'win': 788.55, 'risk': 723.44, 'oddsFormat': 'DECIMAL', 'updateSequence': 1752993024516, 'sportId': 4, 'sportName': 'Basketball', 'leagueId': 214756, 'leagueName': 'FIBA - U19 World Cup Women', 'eventId': 1611849832, 'handicap': 19.5, 'price': 2.09, 'teamName': 'Japan', 'team1': 'France', 'team2': 'Japan', 'periodNumber': 0, 'isLive': 'FALSE', 'eventStartTime': '2025-07-20T12:00:00Z', 'resultingUnit': 'Regular'}, 'odds': 2.09}]}
2025-07-20 14:30:24,648 - PinBet - INFO - 投注记录已保存到: data\pinbet_records_2025-07-20.json
2025-07-20 14:30:26,648 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 14:30:26,648 - PinBet - INFO - 开始查询未结算注单
2025-07-20 14:30:27,130 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 14:30:27,131 - PinBet - INFO - 处理完成: 原始记录 1 条，处理后 1 条记录
2025-07-20 14:30:42,680 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 14:30:42,681 - PinBet - INFO - 开始查询未结算注单
2025-07-20 14:30:43,268 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 14:30:43,268 - PinBet - INFO - 处理完成: 原始记录 1 条，处理后 1 条记录
2025-07-20 14:31:21,366 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 14:31:21,366 - PinBet - INFO - 开始查询未结算注单
2025-07-20 14:31:21,826 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 14:31:21,827 - PinBet - INFO - 处理完成: 原始记录 1 条，处理后 1 条记录
2025-07-20 14:38:30,106 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 14:38:30,106 - PinBet - INFO - 开始查询未结算注单
2025-07-20 14:38:49,780 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 14:38:49,781 - PinBet - INFO - 开始查询未结算注单
2025-07-20 14:38:50,958 - PinBet - ERROR - 获取未结算注单失败: 权限不足，可能需要重新登录
2025-07-20 14:38:56,728 - PinBet - ERROR - 获取未结算注单异常: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
Traceback (most recent call last):
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
               ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
                       ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 1378, in getresponse
    response.begin()
  File "D:\Program Files\Python311\Lib\http\client.py", line 318, in begin
    version, status, reason = self._read_status()
                              ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 287, in _read_status
    raise RemoteDisconnected("Remote end closed connection without"
http.client.RemoteDisconnected: Remote end closed connection without response

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\Python311\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\util\util.py", line 38, in reraise
    raise value.with_traceback(tb)
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
               ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
                       ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 1378, in getresponse
    response.begin()
  File "D:\Program Files\Python311\Lib\http\client.py", line 318, in begin
    version, status, reason = self._read_status()
                              ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 287, in _read_status
    raise RemoteDisconnected("Remote end closed connection without"
urllib3.exceptions.ProtocolError: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "d:\betburgerbot\platforms\pinbet_bk.py", line 1426, in get_pending_wagers
    response = self.session.post(
               ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\sessions.py", line 637, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\adapters.py", line 682, in send
    raise ConnectionError(err, request=request)
requests.exceptions.ConnectionError: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 14:39:13,149 - PinBet - INFO - 解析结果: 罗马尼亚 U20 vs 比利时, 让球=比利时, 盘口=-9.5, 赔率=2.09
2025-07-20 14:39:13,149 - PinBet - INFO - 开始执行投注: 罗马尼亚 U20 vs 比利时, 让球=比利时
2025-07-20 14:39:13,150 - PinBet - INFO - 获取早场比赛数据
2025-07-20 14:39:13,150 - PinBet - INFO - 获取早场篮球比赛数据
2025-07-20 14:39:14,013 - PinBet - INFO - 早场篮球比赛数据获取成功
2025-07-20 14:39:14,013 - PinBet - INFO - 查找匹配比赛: 罗马尼亚 U20 vs 比利时, 让球=比利时, 盘口=-9.5
2025-07-20 14:39:14,013 - PinBet - INFO - 找到匹配比赛: 罗马尼亚 vs 比利时 (联赛: 欧洲U20甲级锦标赛) (匹配分数: 0.90)
2025-07-20 14:39:14,013 - PinBet - INFO - 可用的让球盘口: ['8.0', '8.5', '9.0', '9.5', '10.0', '10.5', '11.0', '11.5', '12.0', '12.5', '13.0']
2025-07-20 14:39:14,015 - PinBet - INFO - 标准化后的目标盘口值: -9.5
2025-07-20 14:39:14,015 - PinBet - INFO - 获取投注表单: event_id=1611886002, line_id=49602188612, period_num=0, bet_type=2, team_side=away, handicap=-9.5
2025-07-20 14:39:14,015 - PinBet - INFO - 让分盘设置 - team_side: away, team_side_code: 1, selection_direction: 1
2025-07-20 14:39:14,015 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 14:39:14,015 - PinBet - INFO - 构建投注ID: odds_id=1611886002|0|2|1|1|-9.5, selection_id=49602188612|1611886002|0|2|1|1|-9.5|1
2025-07-20 14:39:14,015 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1752993554015', 'withCredentials': 'true'}
2025-07-20 14:39:14,015 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611886002|0|2|1|1|-9.5", "oddsSelectionsType": "NORMAL", "selectionId": "49602188612|1611886002|0|2|1|1|-9.5|1"}]}
2025-07-20 14:39:14,416 - PinBet - ERROR - 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:14,416 - PinBet - WARNING - 获取投注表单失败，正在重试 (1/3): 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:15,416 - PinBet - INFO - 让分盘设置 - team_side: away, team_side_code: 1, selection_direction: 1
2025-07-20 14:39:15,416 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 14:39:15,416 - PinBet - INFO - 构建投注ID: odds_id=1611886002|0|2|1|1|-9.5, selection_id=49602188612|1611886002|0|2|1|1|-9.5|1
2025-07-20 14:39:15,416 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1752993555416', 'withCredentials': 'true'}
2025-07-20 14:39:15,417 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611886002|0|2|1|1|-9.5", "oddsSelectionsType": "NORMAL", "selectionId": "49602188612|1611886002|0|2|1|1|-9.5|1"}]}
2025-07-20 14:39:15,772 - PinBet - ERROR - 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:15,772 - PinBet - WARNING - 获取投注表单失败，正在重试 (2/3): 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:16,774 - PinBet - INFO - 让分盘设置 - team_side: away, team_side_code: 1, selection_direction: 1
2025-07-20 14:39:16,774 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 14:39:16,774 - PinBet - INFO - 构建投注ID: odds_id=1611886002|0|2|1|1|-9.5, selection_id=49602188612|1611886002|0|2|1|1|-9.5|1
2025-07-20 14:39:16,775 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1752993556774', 'withCredentials': 'true'}
2025-07-20 14:39:16,775 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611886002|0|2|1|1|-9.5", "oddsSelectionsType": "NORMAL", "selectionId": "49602188612|1611886002|0|2|1|1|-9.5|1"}]}
2025-07-20 14:39:17,122 - PinBet - ERROR - 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:17,122 - PinBet - WARNING - 获取投注表单失败，正在重试 (3/3): 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:18,123 - PinBet - ERROR - 获取投注表单失败，已重试 3 次
2025-07-20 14:39:19,125 - PinBet - INFO - 解析结果: 罗马尼亚 U20 vs 比利时, 让球=比利时, 盘口=-9.5, 赔率=2.09
2025-07-20 14:39:19,125 - PinBet - INFO - 开始执行投注: 罗马尼亚 U20 vs 比利时, 让球=比利时
2025-07-20 14:39:19,125 - PinBet - INFO - 获取早场比赛数据
2025-07-20 14:39:19,125 - PinBet - INFO - 获取早场篮球比赛数据
2025-07-20 14:39:19,496 - PinBet - INFO - 早场篮球比赛数据获取成功
2025-07-20 14:39:19,496 - PinBet - INFO - 查找匹配比赛: 罗马尼亚 U20 vs 比利时, 让球=比利时, 盘口=-9.5
2025-07-20 14:39:19,496 - PinBet - INFO - 找到匹配比赛: 罗马尼亚 vs 比利时 (联赛: 欧洲U20甲级锦标赛) (匹配分数: 0.90)
2025-07-20 14:39:19,497 - PinBet - INFO - 可用的让球盘口: ['8.0', '8.5', '9.0', '9.5', '10.0', '10.5', '11.0', '11.5', '12.0', '12.5', '13.0']
2025-07-20 14:39:19,497 - PinBet - INFO - 标准化后的目标盘口值: -9.5
2025-07-20 14:39:19,497 - PinBet - INFO - 获取投注表单: event_id=1611886002, line_id=49602188612, period_num=0, bet_type=2, team_side=away, handicap=-9.5
2025-07-20 14:39:19,497 - PinBet - INFO - 让分盘设置 - team_side: away, team_side_code: 1, selection_direction: 1
2025-07-20 14:39:19,498 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 14:39:19,498 - PinBet - INFO - 构建投注ID: odds_id=1611886002|0|2|1|1|-9.5, selection_id=49602188612|1611886002|0|2|1|1|-9.5|1
2025-07-20 14:39:19,498 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1752993559497', 'withCredentials': 'true'}
2025-07-20 14:39:19,498 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611886002|0|2|1|1|-9.5", "oddsSelectionsType": "NORMAL", "selectionId": "49602188612|1611886002|0|2|1|1|-9.5|1"}]}
2025-07-20 14:39:19,850 - PinBet - ERROR - 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:19,851 - PinBet - WARNING - 获取投注表单失败，正在重试 (1/3): 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:20,851 - PinBet - INFO - 让分盘设置 - team_side: away, team_side_code: 1, selection_direction: 1
2025-07-20 14:39:20,851 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 14:39:20,852 - PinBet - INFO - 构建投注ID: odds_id=1611886002|0|2|1|1|-9.5, selection_id=49602188612|1611886002|0|2|1|1|-9.5|1
2025-07-20 14:39:20,852 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1752993560851', 'withCredentials': 'true'}
2025-07-20 14:39:20,852 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611886002|0|2|1|1|-9.5", "oddsSelectionsType": "NORMAL", "selectionId": "49602188612|1611886002|0|2|1|1|-9.5|1"}]}
2025-07-20 14:39:21,229 - PinBet - ERROR - 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:21,229 - PinBet - WARNING - 获取投注表单失败，正在重试 (2/3): 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:22,231 - PinBet - INFO - 让分盘设置 - team_side: away, team_side_code: 1, selection_direction: 1
2025-07-20 14:39:22,231 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 14:39:22,231 - PinBet - INFO - 构建投注ID: odds_id=1611886002|0|2|1|1|-9.5, selection_id=49602188612|1611886002|0|2|1|1|-9.5|1
2025-07-20 14:39:22,231 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1752993562231', 'withCredentials': 'true'}
2025-07-20 14:39:22,232 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611886002|0|2|1|1|-9.5", "oddsSelectionsType": "NORMAL", "selectionId": "49602188612|1611886002|0|2|1|1|-9.5|1"}]}
2025-07-20 14:39:22,621 - PinBet - ERROR - 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:22,621 - PinBet - WARNING - 获取投注表单失败，正在重试 (3/3): 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:23,622 - PinBet - ERROR - 获取投注表单失败，已重试 3 次
2025-07-20 14:39:23,623 - PinBet - WARNING - 无法解析比赛队伍: 
2025-07-20 14:39:25,624 - PinBet - WARNING - 无法解析比赛队伍: 
2025-07-20 14:39:44,438 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 14:39:44,439 - PinBet - INFO - 开始查询未结算注单
2025-07-20 14:39:44,796 - PinBet - ERROR - 获取未结算注单失败: 权限不足，可能需要重新登录
2025-07-20 14:57:30,385 - PinBet - INFO - 会话即将过期，执行自动刷新
2025-07-20 14:57:31,419 - PinBet - WARNING - 余额API返回success=False，会话可能已失效
2025-07-20 14:57:31,419 - PinBet - INFO - 清除平博登录会话
2025-07-20 14:57:31,421 - PinBet - INFO - 登录会话已清除
2025-07-20 14:57:31,421 - PinBet - WARNING - 会话刷新失败: 会话已失效，需要重新登录
2025-07-20 15:07:22,842 - PinBet - INFO - 尝试自动登录平博账号: mfc123
2025-07-20 15:07:23,998 - PinBet - INFO - 登录成功(带tokens): mfc123
2025-07-20 15:07:24,378 - PinBet - INFO - 余额获取成功: 26778.74
2025-07-20 15:07:24,772 - PinBet - INFO - 余额获取成功: 26778.74
2025-07-20 15:07:29,882 - PinBet - INFO - 余额获取成功: 26778.74
2025-07-20 15:07:45,522 - PinBet - INFO - 会话空闲1752995266秒，执行主动刷新以保持活跃
2025-07-20 15:07:45,909 - PinBet - INFO - 余额获取成功: 26778.74
2025-07-20 15:07:45,909 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 15:07:45,909 - PinBet - INFO - 开始查询未结算注单
2025-07-20 15:07:46,294 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 15:07:46,294 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 15:07:58,032 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 15:07:58,042 - PinBet - INFO - 开始查询未结算注单
2025-07-20 15:07:58,442 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 15:07:58,442 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 16:45:15,038 - PinBet - INFO - 会话空闲5849秒，执行主动刷新以保持活跃
2025-07-20 16:45:30,334 - PinBet - INFO - 会话空闲5864秒，执行主动刷新以保持活跃
2025-07-20 16:45:31,024 - PinBet - WARNING - 余额API返回success=False，会话可能已失效
2025-07-20 16:45:31,024 - PinBet - INFO - 清除平博登录会话
2025-07-20 16:45:31,025 - PinBet - INFO - 登录会话已清除
2025-07-20 16:45:31,025 - PinBet - WARNING - 会话已失效，需要重新登录
2025-07-20 16:45:31,025 - PinBet - WARNING - 主动刷新失败: 会话已失效，需要重新登录
2025-07-20 16:45:31,025 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 16:45:31,025 - PinBet - INFO - 开始查询未结算注单
2025-07-20 16:45:31,354 - PinBet - WARNING - 获取未结算注单失败: 权限不足，检测到会话失效
2025-07-20 16:45:31,354 - PinBet - INFO - 清除平博登录会话
2025-07-20 16:45:31,355 - PinBet - INFO - 登录会话已清除
2025-07-20 16:45:31,355 - PinBet - INFO - 尝试重新登录以恢复查询会话
2025-07-20 16:45:31,355 - PinBet - INFO - 尝试自动登录平博账号: mfc123
2025-07-20 16:45:31,786 - PinBet - INFO - 登录成功(带tokens): mfc123
2025-07-20 16:45:32,211 - PinBet - INFO - 余额获取成功: 26778.74
2025-07-20 16:45:32,213 - PinBet - INFO - 重新登录成功，请重试查询操作
2025-07-20 16:45:34,290 - PinBet - ERROR - 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 16:45:34,290 - PinBet - WARNING - 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 16:45:34,291 - PinBet - WARNING - 主动刷新失败: 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 16:45:34,291 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 16:45:34,291 - PinBet - INFO - 开始查询未结算注单
2025-07-20 16:45:35,032 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 16:45:35,032 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 16:46:22,391 - PinBet - INFO - 余额获取成功: 26778.74
2025-07-20 17:16:40,043 - PinBet - INFO - 会话空闲7734秒，执行主动刷新以保持活跃
2025-07-20 17:17:01,045 - PinBet - ERROR - 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:17:01,045 - PinBet - WARNING - 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:17:01,045 - PinBet - WARNING - 主动刷新失败: 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:17:01,046 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 17:17:01,046 - PinBet - INFO - 开始查询未结算注单
2025-07-20 17:17:02,124 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 17:17:02,124 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 17:32:40,484 - PinBet - INFO - 会话空闲8695秒，执行主动刷新以保持活跃
2025-07-20 17:32:59,790 - PinBet - ERROR - 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:32:59,790 - PinBet - WARNING - 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:32:59,790 - PinBet - WARNING - 主动刷新失败: 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:32:59,791 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 17:32:59,791 - PinBet - INFO - 开始查询未结算注单
2025-07-20 17:33:00,529 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 17:33:00,529 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 17:58:41,284 - PinBet - INFO - 会话空闲10255秒，执行主动刷新以保持活跃
2025-07-20 17:59:04,096 - PinBet - ERROR - 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:59:04,096 - PinBet - WARNING - 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:59:04,097 - PinBet - WARNING - 主动刷新失败: 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:59:04,097 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 17:59:04,097 - PinBet - INFO - 开始查询未结算注单
2025-07-20 17:59:04,830 - PinBet - WARNING - 获取未结算注单失败: 权限不足，检测到会话失效
2025-07-20 17:59:04,830 - PinBet - INFO - 清除平博登录会话
2025-07-20 17:59:04,831 - PinBet - INFO - 登录会话已清除
2025-07-20 17:59:04,831 - PinBet - INFO - 尝试重新登录以恢复查询会话
2025-07-20 17:59:04,831 - PinBet - INFO - 尝试自动登录平博账号: mfc123
2025-07-20 17:59:05,256 - PinBet - INFO - 登录成功(带tokens): mfc123
2025-07-20 17:59:05,641 - PinBet - INFO - 余额获取成功: 26770.74
2025-07-20 17:59:05,645 - PinBet - INFO - 重新登录成功，请重试查询操作
2025-07-20 17:59:14,030 - PinBet - INFO - 会话空闲10288秒，执行主动刷新以保持活跃
2025-07-20 17:59:14,392 - PinBet - INFO - 余额获取成功: 26770.74
2025-07-20 17:59:14,392 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 17:59:14,392 - PinBet - INFO - 开始查询未结算注单
2025-07-20 17:59:14,777 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 17:59:14,777 - PinBet - INFO - 处理完成: 原始记录 3 条，处理后 3 条记录
2025-07-20 18:09:37,229 - PinBet - INFO - 会话空闲623秒，执行主动刷新以保持活跃
2025-07-20 18:09:56,485 - PinBet - ERROR - 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 18:09:56,485 - PinBet - WARNING - 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 18:09:56,485 - PinBet - WARNING - 主动刷新失败: 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 18:09:56,485 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 18:09:56,486 - PinBet - INFO - 开始查询未结算注单
2025-07-20 18:09:57,590 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 18:09:57,590 - PinBet - INFO - 处理完成: 原始记录 3 条，处理后 3 条记录
2025-07-20 19:14:03,852 - PinBet - INFO - 检测到现有会话，验证有效性
2025-07-20 19:14:04,577 - PinBet - WARNING - 余额API返回success=False，会话可能已失效
2025-07-20 19:14:04,577 - PinBet - INFO - 清除平博登录会话
2025-07-20 19:14:04,579 - PinBet - INFO - 登录会话已清除
2025-07-20 19:14:04,579 - PinBet - WARNING - 现有会话验证失败，清除会话并重新登录
2025-07-20 19:14:04,579 - PinBet - INFO - 清除平博登录会话
2025-07-20 19:14:04,579 - PinBet - INFO - 登录会话已清除
2025-07-20 19:14:04,580 - PinBet - INFO - 尝试自动登录平博账号: mfc123
2025-07-20 19:14:05,025 - PinBet - INFO - 登录成功(带tokens): mfc123
2025-07-20 19:14:05,378 - PinBet - INFO - 余额获取成功: 26770.74
2025-07-20 19:14:05,747 - PinBet - INFO - 余额获取成功: 26770.74
2025-07-20 19:14:10,931 - PinBet - INFO - 余额获取成功: 26770.74
2025-07-20 19:14:16,317 - PinBet - INFO - 余额获取成功: 26770.74
2025-07-20 20:02:26,256 - PinBet - INFO - 解析结果: 美国 vs 澳大利亚, 让球=美国, 盘口=-19.0, 赔率=2.18
2025-07-20 20:02:26,257 - PinBet - INFO - 开始执行投注: 美国 vs 澳大利亚, 让球=美国
2025-07-20 20:02:26,258 - PinBet - INFO - 获取今日比赛数据
2025-07-20 20:02:26,258 - PinBet - INFO - 获取今日篮球比赛数据
2025-07-20 20:02:45,531 - PinBet - ERROR - 获取今日篮球比赛异常: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 20:02:45,531 - PinBet - ERROR - 获取今日比赛失败
2025-07-20 20:02:46,542 - PinBet - INFO - 解析结果: 美国 vs 澳大利亚, 让球=美国, 盘口=-19.0, 赔率=2.18
2025-07-20 20:02:46,543 - PinBet - INFO - 开始执行投注: 美国 vs 澳大利亚, 让球=美国
2025-07-20 20:02:46,543 - PinBet - INFO - 获取今日比赛数据
2025-07-20 20:02:46,544 - PinBet - INFO - 获取今日篮球比赛数据
2025-07-20 20:02:47,572 - PinBet - INFO - 今日篮球比赛数据获取成功
2025-07-20 20:02:47,572 - PinBet - INFO - 获取早场比赛数据
2025-07-20 20:02:47,572 - PinBet - INFO - 获取早场篮球比赛数据
2025-07-20 20:02:47,926 - PinBet - INFO - 早场篮球比赛数据获取成功
2025-07-20 20:02:47,926 - PinBet - INFO - 查找匹配比赛: 美国 vs 澳大利亚, 让球=美国, 盘口=-19.0
2025-07-20 20:02:47,927 - PinBet - INFO - 找到匹配比赛: 日本 vs 澳大利亚 (联赛: FIBA - 女子亚洲杯) (匹配分数: 0.50)
2025-07-20 20:02:47,927 - PinBet - INFO - 找到匹配比赛: 美国 vs 澳大利亚 (联赛: 国际篮联 - U19女子篮球世界杯) (匹配分数: 1.00)
2025-07-20 20:02:47,928 - PinBet - INFO - 可用的让球盘口: ['19.5', '19.0', '18.5', '18.0', '17.5', '17.0', '16.5', '16.0', '15.5', '15.0', '14.5']
2025-07-20 20:02:47,928 - PinBet - INFO - 标准化后的目标盘口值: -19.0
2025-07-20 20:02:47,928 - PinBet - INFO - 获取投注表单: event_id=1611860550, line_id=49607510791, period_num=0, bet_type=2, team_side=home, handicap=-19.0
2025-07-20 20:02:47,929 - PinBet - INFO - 会话空闲1753012968秒，执行主动刷新以保持活跃
2025-07-20 20:02:48,325 - PinBet - INFO - 余额获取成功: 26770.74
2025-07-20 20:02:48,326 - PinBet - INFO - 让分盘设置 - team_side: home, team_side_code: 0, selection_direction: 0
2025-07-20 20:02:48,326 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 20:02:48,326 - PinBet - INFO - 构建投注ID: odds_id=1611860550|0|2|0|1|-19, selection_id=49607510791|1611860550|0|2|0|1|-19|0
2025-07-20 20:02:48,327 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1753012968326', 'withCredentials': 'true'}
2025-07-20 20:02:48,327 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611860550|0|2|0|1|-19", "oddsSelectionsType": "NORMAL", "selectionId": "49607510791|1611860550|0|2|0|1|-19|0"}]}
2025-07-20 20:02:48,721 - PinBet - INFO - 投注表单获取成功: 1611860550, 赔率: 2.180
2025-07-20 20:02:48,721 - PinBet - INFO - API返回的selectionId: **********|***********|1611860550|0|2|0|1|-19.00|0
2025-07-20 20:02:48,721 - PinBet - INFO - API返回的oddsId: 1611860550|0|2|0|1|-19
2025-07-20 20:02:48,722 - PinBet - INFO - 将使用投注选项: selectionId=**********|***********|1611860550|0|2|0|1|-19.00|0, oddsId=1611860550|0|2|0|1|-19, odds=2.180
2025-07-20 20:02:48,722 - PinBet - INFO - 执行投注: 金额=726.61
2025-07-20 20:02:48,722 - PinBet - INFO - 从列表中提取投注信息: selection_id=**********|***********|1611860550|0|2|0|1|-19.00|0, odds_id=1611860550|0|2|0|1|-19, odds=2.180, stake=726.61
2025-07-20 20:02:49,484 - PinBet - INFO - 投注响应数据: {"response": [{"wagerId": *********, "status": "ACCEPTED", "selectionId": "**********|***********|1611860550|0|2|0|1|-19.00|0", "betId": **********, "uniqueRequestId": "bd12212f-054f-4cfd-8d23-3ff9f3822522", "wagerType": "NORMAL", "betterLineWasAccepted": false, "jsonString": "{\"bets\":[{\"status\":\"ACCEPTED\",\"errorCode\":null,\"uniqueRequestId\":\"bd12212f-054f-4cfd-8d23-3ff9f3822522\",\"lineId\":**********,\"altLineId\":***********,\"price\":2.18,\"minRiskStake\":0.07,\"maxRiskStake\":3588.98,\"minWinStake\":0.09,\"maxWinStake\":4234.99,\"straightBet\":{\"betId\":**********,\"uniqueRequestId\":\"bd12212f-054f-4cfd-8d23-3ff9f3822522\",\"wagerNumber\":1,\"placedAt\":\"2025-07-20T12:02:50Z\",\"betStatus\":\"ACCEPTED\",\"betStatus2\":\"ACCEPTED\",\"betType\":\"SPREAD\",\"win\":857.4,\"risk\":726.61,\"oddsFormat\":\"DECIMAL\",\"updateSequence\":**********,\"externalRef\":\"*********\",\"sportId\":4,\"sportName\":\"Basketball\",\"leagueId\":214756,\"leagueName\":\"FIBA - U19 World Cup Women\",\"eventId\":1611860550,\"handicap\":-19.0,\"price\":2.18,\"teamName\":\"USA\",\"team1\":\"USA\",\"team2\":\"Australia\",\"periodNumber\":0,\"isLive\":\"FALSE\",\"homeTeamType\":\"Team1\",\"eventStartTime\":\"2025-07-20T18:00:00Z\",\"resultingUnit\":\"Regular\"}}]}", "oddsId": "1611860550|0|2|0|1|-19", "psBetVO": {"betId": **********, "uniqueRequestId": "bd12212f-054f-4cfd-8d23-3ff9f3822522", "wagerNumber": 1, "placedAt": "2025-07-20T12:02:50Z", "betStatus": "ACCEPTED", "betType": "SPREAD", "win": 857.4, "risk": 726.61, "oddsFormat": "DECIMAL", "updateSequence": 1753012970460, "sportId": 4, "sportName": "Basketball", "leagueId": 214756, "leagueName": "FIBA - U19 World Cup Women", "eventId": 1611860550, "handicap": -19.0, "price": 2.18, "teamName": "USA", "team1": "USA", "team2": "Australia", "periodNumber": 0, "isLive": "FALSE", "eventStartTime": "2025-07-20T18:00:00Z", "resultingUnit": "Regular"}, "odds": 2.18}]}
2025-07-20 20:02:49,485 - PinBet - INFO - 投注成功: 注单ID *********, 投注ID **********
2025-07-20 20:02:49,486 - PinBet - INFO - 投注成功: {'response': [{'wagerId': *********, 'status': 'ACCEPTED', 'selectionId': '**********|***********|1611860550|0|2|0|1|-19.00|0', 'betId': **********, 'uniqueRequestId': 'bd12212f-054f-4cfd-8d23-3ff9f3822522', 'wagerType': 'NORMAL', 'betterLineWasAccepted': False, 'jsonString': '{"bets":[{"status":"ACCEPTED","errorCode":null,"uniqueRequestId":"bd12212f-054f-4cfd-8d23-3ff9f3822522","lineId":**********,"altLineId":***********,"price":2.18,"minRiskStake":0.07,"maxRiskStake":3588.98,"minWinStake":0.09,"maxWinStake":4234.99,"straightBet":{"betId":**********,"uniqueRequestId":"bd12212f-054f-4cfd-8d23-3ff9f3822522","wagerNumber":1,"placedAt":"2025-07-20T12:02:50Z","betStatus":"ACCEPTED","betStatus2":"ACCEPTED","betType":"SPREAD","win":857.4,"risk":726.61,"oddsFormat":"DECIMAL","updateSequence":**********,"externalRef":"*********","sportId":4,"sportName":"Basketball","leagueId":214756,"leagueName":"FIBA - U19 World Cup Women","eventId":1611860550,"handicap":-19.0,"price":2.18,"teamName":"USA","team1":"USA","team2":"Australia","periodNumber":0,"isLive":"FALSE","homeTeamType":"Team1","eventStartTime":"2025-07-20T18:00:00Z","resultingUnit":"Regular"}}]}', 'oddsId': '1611860550|0|2|0|1|-19', 'psBetVO': {'betId': **********, 'uniqueRequestId': 'bd12212f-054f-4cfd-8d23-3ff9f3822522', 'wagerNumber': 1, 'placedAt': '2025-07-20T12:02:50Z', 'betStatus': 'ACCEPTED', 'betType': 'SPREAD', 'win': 857.4, 'risk': 726.61, 'oddsFormat': 'DECIMAL', 'updateSequence': 1753012970460, 'sportId': 4, 'sportName': 'Basketball', 'leagueId': 214756, 'leagueName': 'FIBA - U19 World Cup Women', 'eventId': 1611860550, 'handicap': -19.0, 'price': 2.18, 'teamName': 'USA', 'team1': 'USA', 'team2': 'Australia', 'periodNumber': 0, 'isLive': 'FALSE', 'eventStartTime': '2025-07-20T18:00:00Z', 'resultingUnit': 'Regular'}, 'odds': 2.18}]}
2025-07-20 20:02:49,488 - PinBet - INFO - 投注记录已保存到: data\pinbet_records_2025-07-20.json
2025-07-20 20:02:51,489 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 20:02:51,489 - PinBet - INFO - 开始查询未结算注单
2025-07-20 20:02:51,861 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 20:02:51,861 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 20:03:51,298 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 20:03:51,299 - PinBet - INFO - 开始查询未结算注单
2025-07-20 20:03:51,670 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 20:03:51,670 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 20:04:02,155 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 20:04:02,155 - PinBet - INFO - 开始查询未结算注单
2025-07-20 20:04:02,523 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 20:04:02,523 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 20:05:08,496 - PinBet - INFO - 解析结果: 冰岛 vs 德国, 大小球=大, 盘口=155.5, 赔率=2.17
2025-07-20 20:05:08,496 - PinBet - INFO - 开始执行投注: 冰岛 vs 德国, 大小球=大
2025-07-20 20:05:08,496 - PinBet - INFO - 查找匹配比赛: 冰岛 vs 德国, 大小球=大, 盘口=155.5
2025-07-20 20:05:08,496 - PinBet - INFO - 找到匹配比赛: 冰岛 vs 德国 (联赛: 欧洲U20甲级锦标赛) (匹配分数: 1.00)
2025-07-20 20:05:08,497 - PinBet - INFO - 可用的大小球盘口: ['158.5', '158.0', '157.5', '157.0', '156.5', '156.0', '155.5', '155.0', '154.5', '154.0', '153.5']
2025-07-20 20:05:08,497 - PinBet - INFO - 标准化后的目标盘口值: 155.5
2025-07-20 20:05:08,497 - PinBet - INFO - 找到精确匹配的大小球盘口: 155.5, line_id=49606992144
2025-07-20 20:05:08,497 - PinBet - INFO - 获取投注表单: event_id=1611849798, line_id=49606992144, period_num=0, bet_type=3, team_side=over, handicap=155.5
2025-07-20 20:05:08,497 - PinBet - INFO - 大小球盘设置 - team_side: over, team_side_code: 3, selection_direction: 0
2025-07-20 20:05:08,497 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 20:05:08,497 - PinBet - INFO - 构建投注ID: odds_id=1611849798|0|3|3|1|155.5, selection_id=49606992144|1611849798|0|3|3|1|155.5|0
2025-07-20 20:05:08,497 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1753013108497', 'withCredentials': 'true'}
2025-07-20 20:05:08,497 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611849798|0|3|3|1|155.5", "oddsSelectionsType": "NORMAL", "selectionId": "49606992144|1611849798|0|3|3|1|155.5|0"}]}
2025-07-20 20:05:08,902 - PinBet - INFO - 投注表单获取成功: 1611849798, 赔率: 2.170
2025-07-20 20:05:08,902 - PinBet - INFO - API返回的selectionId: 3184153411|49607567445|1611849798|0|3|3|1|155.50|0
2025-07-20 20:05:08,902 - PinBet - INFO - API返回的oddsId: 1611849798|0|3|3|1|155.5
2025-07-20 20:05:08,902 - PinBet - INFO - 将使用投注选项: selectionId=3184153411|49607567445|1611849798|0|3|3|1|155.50|0, oddsId=1611849798|0|3|3|1|155.5, odds=2.170
2025-07-20 20:05:08,902 - PinBet - INFO - 执行投注: 金额=738.06
2025-07-20 20:05:08,902 - PinBet - INFO - 从列表中提取投注信息: selection_id=3184153411|49607567445|1611849798|0|3|3|1|155.50|0, odds_id=1611849798|0|3|3|1|155.5, odds=2.170, stake=738.06
2025-07-20 20:05:09,604 - PinBet - INFO - 投注响应数据: {"response": [{"wagerId": 660909655, "status": "ACCEPTED", "selectionId": "3184153411|49607567445|1611849798|0|3|3|1|155.50|0", "betId": 2099487924, "uniqueRequestId": "c2cc61d5-1990-458d-a58b-b4b158197abe", "wagerType": "NORMAL", "betterLineWasAccepted": false, "jsonString": "{\"bets\":[{\"status\":\"ACCEPTED\",\"errorCode\":null,\"uniqueRequestId\":\"c2cc61d5-1990-458d-a58b-b4b158197abe\",\"lineId\":3184153411,\"altLineId\":49607567445,\"price\":2.17,\"minRiskStake\":0.07,\"maxRiskStake\":10766.93,\"minWinStake\":0.09,\"maxWinStake\":12597.30,\"straightBet\":{\"betId\":2099487924,\"uniqueRequestId\":\"c2cc61d5-1990-458d-a58b-b4b158197abe\",\"wagerNumber\":1,\"placedAt\":\"2025-07-20T12:05:10Z\",\"betStatus\":\"ACCEPTED\",\"betStatus2\":\"ACCEPTED\",\"betType\":\"TOTAL_POINTS\",\"win\":863.53,\"risk\":738.06,\"oddsFormat\":\"DECIMAL\",\"updateSequence\":2928900782,\"externalRef\":\"660909655\",\"sportId\":4,\"sportName\":\"Basketball\",\"leagueId\":357,\"leagueName\":\"European U20 Championship Division A\",\"eventId\":1611849798,\"handicap\":155.5,\"price\":2.17,\"side\":\"OVER\",\"team1\":\"Iceland\",\"team2\":\"Germany\",\"periodNumber\":0,\"isLive\":\"FALSE\",\"homeTeamType\":\"Team1\",\"eventStartTime\":\"2025-07-20T12:30:00Z\",\"resultingUnit\":\"Regular\"}}]}", "oddsId": "1611849798|0|3|3|1|155.5", "psBetVO": {"betId": 2099487924, "uniqueRequestId": "c2cc61d5-1990-458d-a58b-b4b158197abe", "wagerNumber": 1, "placedAt": "2025-07-20T12:05:10Z", "betStatus": "ACCEPTED", "betType": "TOTAL_POINTS", "win": 863.53, "risk": 738.06, "oddsFormat": "DECIMAL", "updateSequence": 1753013110588, "sportId": 4, "sportName": "Basketball", "leagueId": 357, "leagueName": "European U20 Championship Division A", "eventId": 1611849798, "handicap": 155.5, "price": 2.17, "side": "OVER", "team1": "Iceland", "team2": "Germany", "periodNumber": 0, "isLive": "FALSE", "eventStartTime": "2025-07-20T12:30:00Z", "resultingUnit": "Regular"}, "odds": 2.17}]}
2025-07-20 20:05:09,605 - PinBet - INFO - 投注成功: 注单ID 660909655, 投注ID 2099487924
2025-07-20 20:05:09,605 - PinBet - INFO - 投注成功: {'response': [{'wagerId': 660909655, 'status': 'ACCEPTED', 'selectionId': '3184153411|49607567445|1611849798|0|3|3|1|155.50|0', 'betId': 2099487924, 'uniqueRequestId': 'c2cc61d5-1990-458d-a58b-b4b158197abe', 'wagerType': 'NORMAL', 'betterLineWasAccepted': False, 'jsonString': '{"bets":[{"status":"ACCEPTED","errorCode":null,"uniqueRequestId":"c2cc61d5-1990-458d-a58b-b4b158197abe","lineId":3184153411,"altLineId":49607567445,"price":2.17,"minRiskStake":0.07,"maxRiskStake":10766.93,"minWinStake":0.09,"maxWinStake":12597.30,"straightBet":{"betId":2099487924,"uniqueRequestId":"c2cc61d5-1990-458d-a58b-b4b158197abe","wagerNumber":1,"placedAt":"2025-07-20T12:05:10Z","betStatus":"ACCEPTED","betStatus2":"ACCEPTED","betType":"TOTAL_POINTS","win":863.53,"risk":738.06,"oddsFormat":"DECIMAL","updateSequence":2928900782,"externalRef":"660909655","sportId":4,"sportName":"Basketball","leagueId":357,"leagueName":"European U20 Championship Division A","eventId":1611849798,"handicap":155.5,"price":2.17,"side":"OVER","team1":"Iceland","team2":"Germany","periodNumber":0,"isLive":"FALSE","homeTeamType":"Team1","eventStartTime":"2025-07-20T12:30:00Z","resultingUnit":"Regular"}}]}', 'oddsId': '1611849798|0|3|3|1|155.5', 'psBetVO': {'betId': 2099487924, 'uniqueRequestId': 'c2cc61d5-1990-458d-a58b-b4b158197abe', 'wagerNumber': 1, 'placedAt': '2025-07-20T12:05:10Z', 'betStatus': 'ACCEPTED', 'betType': 'TOTAL_POINTS', 'win': 863.53, 'risk': 738.06, 'oddsFormat': 'DECIMAL', 'updateSequence': 1753013110588, 'sportId': 4, 'sportName': 'Basketball', 'leagueId': 357, 'leagueName': 'European U20 Championship Division A', 'eventId': 1611849798, 'handicap': 155.5, 'price': 2.17, 'side': 'OVER', 'team1': 'Iceland', 'team2': 'Germany', 'periodNumber': 0, 'isLive': 'FALSE', 'eventStartTime': '2025-07-20T12:30:00Z', 'resultingUnit': 'Regular'}, 'odds': 2.17}]}
2025-07-20 20:05:09,606 - PinBet - INFO - 投注记录已保存到: data\pinbet_records_2025-07-20.json
2025-07-20 20:05:11,608 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 20:05:11,608 - PinBet - INFO - 开始查询未结算注单
2025-07-20 20:05:11,989 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 20:05:11,989 - PinBet - INFO - 处理完成: 原始记录 3 条，处理后 3 条记录
2025-07-20 20:05:56,192 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 20:05:56,192 - PinBet - INFO - 开始查询未结算注单
2025-07-20 20:05:56,572 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 20:05:56,574 - PinBet - INFO - 处理完成: 原始记录 3 条，处理后 3 条记录
2025-07-20 20:06:03,039 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 20:06:03,039 - PinBet - INFO - 开始查询未结算注单
2025-07-20 20:06:03,404 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 20:06:03,404 - PinBet - INFO - 处理完成: 原始记录 3 条，处理后 3 条记录
2025-07-20 20:10:49,042 - PinBet - INFO - 会话空闲481秒，执行主动刷新以保持活跃
2025-07-20 20:11:08,284 - PinBet - ERROR - 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 20:11:08,284 - PinBet - WARNING - 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 20:11:08,284 - PinBet - WARNING - 主动刷新失败: 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 20:11:08,284 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 20:11:08,284 - PinBet - INFO - 开始查询未结算注单
2025-07-20 20:11:09,278 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 20:11:09,278 - PinBet - INFO - 处理完成: 原始记录 3 条，处理后 3 条记录
