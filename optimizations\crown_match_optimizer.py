#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
皇冠比赛匹配优化器 - 解决比赛匹配失败问题

主要优化：
1. 智能队名标准化
2. 模糊匹配算法优化
3. 多级匹配策略
4. 匹配结果缓存
"""

import re
import json
import logging
from typing import Dict, List, Tuple, Optional, Any
from difflib import SequenceMatcher
from dataclasses import dataclass
import unicodedata

logger = logging.getLogger(__name__)


@dataclass
class MatchResult:
    """匹配结果数据类"""
    match_id: str
    home_team: str
    away_team: str
    confidence: float
    match_type: str
    platform: str


class TeamNameNormalizer:
    """队名标准化器 - 单一职责原则"""
    
    def __init__(self):
        # 常见队名变体映射
        self.team_variants = {
            # 澳洲篮球队名变体
            "洛坎普顿火箭队": ["罗克汉普顿火箭", "洛坎普顿火箭", "Rockhampton Rockets"],
            "威弗莱猎鹰": ["韦弗利猎鹰", "威弗莱猎鹰", "Waverley Falcons"],
            "凯洛尔雷霆": ["凯洛尔雷霆", "Keilor Thunder"],
            "凤凰阳光海岸": ["凤凰阳光海岸", "Phoenix Sunshine Coast"],
            
            # 添加更多变体映射
            "丹德农游骑兵": ["丹德农游骑兵", "Dandenong Rangers"],
            "钻石谷老鹰": ["钻石谷老鹰", "Diamond Valley Eagles"],
            "凯恩斯海豚": ["凯恩斯海豚", "Cairns Dolphins"],
            "汤斯维尔烈焰": ["汤斯维尔烈焰", "Townsville Flames"],
        }
        
        # 需要移除的后缀
        self.suffixes_to_remove = [
            "(女)", "(男)", "(W)", "(M)",
            "队", "俱乐部", "Club", "Basketball Club",
            "FC", "BC", "SC"
        ]
        
        # 需要标准化的词汇
        self.word_replacements = {
            "vs": "vs",
            "对": "vs",
            "對": "vs",
            "V": "vs",
            "-": "vs"
        }
        
        logger.info("队名标准化器已初始化")
    
    def normalize_team_name(self, team_name: str) -> str:
        """标准化队名"""
        if not team_name:
            return ""
        
        # 1. Unicode标准化
        normalized = unicodedata.normalize('NFKC', team_name)
        
        # 2. 移除多余空格
        normalized = re.sub(r'\s+', ' ', normalized.strip())
        
        # 3. 移除后缀
        for suffix in self.suffixes_to_remove:
            if normalized.endswith(suffix):
                normalized = normalized[:-len(suffix)].strip()
        
        # 4. 词汇替换
        for old_word, new_word in self.word_replacements.items():
            normalized = normalized.replace(old_word, new_word)
        
        # 5. 检查变体映射
        for standard_name, variants in self.team_variants.items():
            if normalized in variants:
                return standard_name
        
        return normalized
    
    def get_all_variants(self, team_name: str) -> List[str]:
        """获取队名的所有变体"""
        normalized = self.normalize_team_name(team_name)
        variants = [normalized, team_name]
        
        # 添加映射中的变体
        for standard_name, variant_list in self.team_variants.items():
            if normalized == standard_name or normalized in variant_list:
                variants.extend(variant_list)
        
        # 去重并返回
        return list(set(variants))


class MatchScorer:
    """匹配评分器 - 单一职责原则"""
    
    def __init__(self):
        self.normalizer = TeamNameNormalizer()
    
    def calculate_team_similarity(self, team1: str, team2: str) -> float:
        """计算队名相似度"""
        if not team1 or not team2:
            return 0.0
        
        # 标准化队名
        norm_team1 = self.normalizer.normalize_team_name(team1)
        norm_team2 = self.normalizer.normalize_team_name(team2)
        
        # 完全匹配
        if norm_team1 == norm_team2:
            return 1.0
        
        # 获取所有变体进行匹配
        variants1 = self.normalizer.get_all_variants(team1)
        variants2 = self.normalizer.get_all_variants(team2)
        
        max_similarity = 0.0
        for v1 in variants1:
            for v2 in variants2:
                if v1 == v2:
                    return 1.0
                # 计算字符串相似度
                similarity = SequenceMatcher(None, v1.lower(), v2.lower()).ratio()
                max_similarity = max(max_similarity, similarity)
        
        return max_similarity
    
    def calculate_match_score(self, match1: Dict, match2: Dict) -> float:
        """计算比赛匹配分数"""
        try:
            # 主队相似度
            home_score = self.calculate_team_similarity(
                match1.get('home_team', ''),
                match2.get('home_team', '')
            )
            
            # 客队相似度
            away_score = self.calculate_team_similarity(
                match1.get('away_team', ''),
                match2.get('away_team', '')
            )
            
            # 交叉匹配（主客队可能颠倒）
            cross_home_score = self.calculate_team_similarity(
                match1.get('home_team', ''),
                match2.get('away_team', '')
            )
            
            cross_away_score = self.calculate_team_similarity(
                match1.get('away_team', ''),
                match2.get('home_team', '')
            )
            
            # 选择最高分数
            normal_score = (home_score + away_score) / 2
            cross_score = (cross_home_score + cross_away_score) / 2
            
            final_score = max(normal_score, cross_score)
            
            # 联赛匹配加分
            if self.is_same_league(match1.get('league', ''), match2.get('league', '')):
                final_score += 0.1  # 联赛匹配加10%
            
            return min(final_score, 1.0)  # 确保不超过1.0
            
        except Exception as e:
            logger.error(f"计算匹配分数失败: {e}")
            return 0.0
    
    def is_same_league(self, league1: str, league2: str) -> bool:
        """判断是否为同一联赛"""
        if not league1 or not league2:
            return False
        
        # 标准化联赛名称
        norm_league1 = re.sub(r'[^\w\s]', '', league1.lower())
        norm_league2 = re.sub(r'[^\w\s]', '', league2.lower())
        
        # 关键词匹配
        keywords1 = set(norm_league1.split())
        keywords2 = set(norm_league2.split())
        
        # 计算交集比例
        intersection = keywords1.intersection(keywords2)
        union = keywords1.union(keywords2)
        
        if len(union) == 0:
            return False
        
        similarity = len(intersection) / len(union)
        return similarity > 0.5  # 50%以上关键词匹配


class CrownMatchOptimizer:
    """皇冠比赛匹配优化器 - 主控制器"""
    
    def __init__(self):
        self.scorer = MatchScorer()
        self.match_cache = {}  # 匹配结果缓存
        self.failed_matches = set()  # 失败匹配记录
        
        # 匹配阈值配置
        self.thresholds = {
            'high_confidence': 0.9,    # 高置信度
            'medium_confidence': 0.7,  # 中等置信度
            'low_confidence': 0.5,     # 低置信度（原来是0.48）
            'minimum': 0.3             # 最低阈值
        }
        
        logger.info("皇冠比赛匹配优化器已初始化")
    
    def find_best_match(self, target_match: Dict, candidate_matches: List[Dict], 
                       min_threshold: float = None) -> Optional[MatchResult]:
        """查找最佳匹配"""
        if not target_match or not candidate_matches:
            return None
        
        if min_threshold is None:
            min_threshold = self.thresholds['low_confidence']
        
        # 生成缓存键
        cache_key = self._generate_cache_key(target_match)
        if cache_key in self.match_cache:
            cached_result = self.match_cache[cache_key]
            logger.debug(f"使用缓存匹配结果: {cached_result.match_id}")
            return cached_result
        
        best_match = None
        best_score = 0.0
        
        for candidate in candidate_matches:
            try:
                score = self.scorer.calculate_match_score(target_match, candidate)
                
                logger.debug(f"匹配评分: {target_match.get('home_team', '')} vs {target_match.get('away_team', '')} "
                           f"<-> {candidate.get('home_team', '')} vs {candidate.get('away_team', '')} = {score:.3f}")
                
                if score > best_score and score >= min_threshold:
                    best_score = score
                    best_match = MatchResult(
                        match_id=candidate.get('match_id', ''),
                        home_team=candidate.get('home_team', ''),
                        away_team=candidate.get('away_team', ''),
                        confidence=score,
                        match_type=candidate.get('match_type', ''),
                        platform=candidate.get('platform', '')
                    )
                    
            except Exception as e:
                logger.error(f"匹配评分计算失败: {e}")
                continue
        
        # 缓存结果
        if best_match:
            self.match_cache[cache_key] = best_match
            logger.info(f"找到最佳匹配: {best_match.match_id}, 置信度: {best_match.confidence:.3f}")
        else:
            logger.warning(f"未找到匹配的比赛（最高分数: {best_score:.2f}，阈值: {min_threshold:.2f}）")
            self.failed_matches.add(cache_key)
        
        return best_match
    
    def multi_level_match(self, target_match: Dict, candidate_matches: List[Dict]) -> Optional[MatchResult]:
        """多级匹配策略"""
        # 第一级：高置信度匹配
        result = self.find_best_match(target_match, candidate_matches, 
                                    self.thresholds['high_confidence'])
        if result:
            logger.info(f"高置信度匹配成功: {result.confidence:.3f}")
            return result
        
        # 第二级：中等置信度匹配
        result = self.find_best_match(target_match, candidate_matches, 
                                    self.thresholds['medium_confidence'])
        if result:
            logger.info(f"中等置信度匹配成功: {result.confidence:.3f}")
            return result
        
        # 第三级：低置信度匹配
        result = self.find_best_match(target_match, candidate_matches, 
                                    self.thresholds['low_confidence'])
        if result:
            logger.warning(f"低置信度匹配: {result.confidence:.3f}")
            return result
        
        # 第四级：最低阈值匹配（需要人工确认）
        result = self.find_best_match(target_match, candidate_matches, 
                                    self.thresholds['minimum'])
        if result:
            logger.warning(f"最低阈值匹配（需要确认）: {result.confidence:.3f}")
            return result
        
        return None
    
    def _generate_cache_key(self, match: Dict) -> str:
        """生成缓存键"""
        home = self.scorer.normalizer.normalize_team_name(match.get('home_team', ''))
        away = self.scorer.normalizer.normalize_team_name(match.get('away_team', ''))
        return f"{home}_vs_{away}"
    
    def clear_cache(self):
        """清空缓存"""
        self.match_cache.clear()
        self.failed_matches.clear()
        logger.info("匹配缓存已清空")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'cached_matches': len(self.match_cache),
            'failed_matches': len(self.failed_matches),
            'thresholds': self.thresholds,
            'cache_hit_rate': len(self.match_cache) / max(1, len(self.match_cache) + len(self.failed_matches))
        }
    
    def debug_match_process(self, target_match: Dict, candidate_matches: List[Dict]):
        """调试匹配过程"""
        logger.info("=== 匹配调试信息 ===")
        logger.info(f"目标比赛: {target_match.get('home_team', '')} vs {target_match.get('away_team', '')}")
        
        # 显示标准化后的队名
        home_normalized = self.scorer.normalizer.normalize_team_name(target_match.get('home_team', ''))
        away_normalized = self.scorer.normalizer.normalize_team_name(target_match.get('away_team', ''))
        logger.info(f"标准化后: {home_normalized} vs {away_normalized}")
        
        # 显示所有变体
        home_variants = self.scorer.normalizer.get_all_variants(target_match.get('home_team', ''))
        away_variants = self.scorer.normalizer.get_all_variants(target_match.get('away_team', ''))
        logger.info(f"主队变体: {home_variants}")
        logger.info(f"客队变体: {away_variants}")
        
        # 显示候选比赛
        logger.info("候选比赛:")
        for i, candidate in enumerate(candidate_matches[:5]):  # 只显示前5个
            score = self.scorer.calculate_match_score(target_match, candidate)
            logger.info(f"  {i+1}. {candidate.get('home_team', '')} vs {candidate.get('away_team', '')} (分数: {score:.3f})")
        
        logger.info("=== 调试信息结束 ===")


def apply_crown_match_optimization(crown_instance):
    """应用皇冠比赛匹配优化"""
    optimizer = CrownMatchOptimizer()
    
    # 替换原有的匹配方法
    original_find_match = getattr(crown_instance, 'find_match_by_teams', None)
    if original_find_match:
        def optimized_find_match(home_team, away_team, matches_list):
            target_match = {
                'home_team': home_team,
                'away_team': away_team
            }
            
            result = optimizer.multi_level_match(target_match, matches_list)
            if result:
                return result.match_id, result.confidence
            else:
                return None, 0.0
        
        crown_instance.find_match_by_teams = optimized_find_match
    
    # 添加优化器引用
    crown_instance.match_optimizer = optimizer
    
    logger.info("皇冠比赛匹配优化已应用")
    return optimizer


# 测试用例
def test_crown_match_optimization():
    """测试皇冠匹配优化"""
    optimizer = CrownMatchOptimizer()
    
    # 测试数据
    target_match = {
        'home_team': '凤凰阳光海岸',
        'away_team': '洛坎普顿火箭队'
    }
    
    candidate_matches = [
        {
            'match_id': '9429681',
            'home_team': '凤凰阳光海岸',
            'away_team': '罗克汉普顿火箭',
            'league': 'NBL1北部篮球联赛'
        }
    ]
    
    # 调试匹配过程
    optimizer.debug_match_process(target_match, candidate_matches)
    
    # 执行匹配
    result = optimizer.multi_level_match(target_match, candidate_matches)
    
    if result:
        print(f"匹配成功: {result.match_id}, 置信度: {result.confidence:.3f}")
    else:
        print("匹配失败")


if __name__ == "__main__":
    test_crown_match_optimization()