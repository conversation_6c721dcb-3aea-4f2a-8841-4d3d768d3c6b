#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
import argparse
import logging
import os
from datetime import datetime

# 导入现有系统
from platforms.pinbet_bk import PinBetSystem
from platforms.hgbet_bk import HGBetBK
from utils.utils import load_config

# 导入对冲系统
from hedge.hedge_integration import HedgeIntegration
from config.hedge_config import HedgeConfig

# 导入学习系统
from utils.match_learning_controller import MatchLearningController

logger = logging.getLogger(__name__)

def setup_hedge_logging():
    """设置对冲系统专用日志配置"""
    # 创建日志目录
    today_str = datetime.now().strftime("%Y-%m-%d")
    log_dir = 'logs'
    os.makedirs(log_dir, exist_ok=True)

    # 生成带时间戳的日志文件名
    log_file = os.path.join(log_dir, f'hedge_{today_str}.log')

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ],
        force=True  # 强制重新配置，覆盖已有配置
    )

    logger.info(f"对冲系统日志将保存到: {log_file}")
    return log_file

class HedgeBettingSystem:
    """对冲投注系统主类"""
    
    def __init__(self):
        """初始化对冲投注系统"""
        logger.info("初始化对冲投注系统")

        # 加载配置
        self.config = load_config()
        self.hedge_config = HedgeConfig()

        # 初始化投注系统
        self.pinbet_system = None
        self.crown_system = None
        self.hedge_integration = None

        # 初始化学习系统
        self.learning_controller = MatchLearningController()

        # 系统状态
        self.systems_initialized = False
        self.systems_logged_in = False
        self.learning_initialized = False
    
    def initialize_systems(self):
        """初始化投注系统"""
        try:
            logger.info("正在初始化投注系统...")

            # 创建平博系统
            self.pinbet_system = PinBetSystem()
            logger.info("平博系统初始化完成")

            # 创建皇冠系统
            self.crown_system = HGBetBK()
            logger.info("皇冠系统初始化完成")

            # 创建对冲集成器
            self.hedge_integration = HedgeIntegration(
                self.pinbet_system, self.crown_system, self.learning_controller
            )
            logger.info("对冲集成器初始化完成")

            self.systems_initialized = True
            return True

        except Exception as e:
            logger.error(f"初始化投注系统失败: {e}")
            return False

    def initialize_learning_system(self):
        """初始化学习系统"""
        try:
            logger.info("正在初始化比赛匹配学习系统...")

            # 初始化学习系统（需要在登录后调用）
            if self.systems_logged_in:
                success = self.learning_controller.initialize_system(
                    self.pinbet_system, self.crown_system
                )

                if success:
                    self.learning_initialized = True
                    logger.info("比赛匹配学习系统初始化完成")

                    # 显示学习系统状态
                    status = self.learning_controller.get_system_status()
                    if status.get('platform_data'):
                        for platform, data in status['platform_data'].items():
                            logger.info(f"{platform}平台数据: {data['total_matches']}场比赛")

                    return True
                else:
                    logger.error("学习系统初始化失败")
                    return False
            else:
                logger.warning("系统未登录，无法初始化学习系统")
                return False

        except Exception as e:
            logger.error(f"初始化学习系统失败: {e}")
            return False

    def get_pinbet_records(self):
        """获取平博投注记录 - 直接调用平台API"""
        try:
            if not self.pinbet_system or not hasattr(self.pinbet_system, 'api'):
                return []

            success, wagers_data = self.pinbet_system.api.get_pending_wagers()

            if success and isinstance(wagers_data, dict):
                page_info = wagers_data.get('page', {})
                return page_info.get('records', [])
            else:
                return []

        except Exception as e:
            return []

    def get_crown_records(self):
        """获取皇冠投注记录 - 直接调用平台API"""
        try:
            if not self.crown_system:
                return []

            wagers = self.crown_system.get_today_wagers_direct(gtype="BK")
            return wagers if wagers else []

        except Exception as e:
            return []
    
    def login_systems(self):
        """登录所有系统"""
        if not self.systems_initialized:
            logger.error("系统未初始化，无法登录")
            return False
        
        try:
            logger.info("正在登录投注系统...")
            
            # 登录平博
            pinbet_config = self.config.get("pinbo_api", {}).get("account", {})
            pinbet_username = pinbet_config.get("username")
            pinbet_password = pinbet_config.get("password")
            
            if pinbet_username and pinbet_password:
                print(f"正在登录平博账号: {pinbet_username}")
                pinbet_success = self.pinbet_system.login(pinbet_username, pinbet_password)
            else:
                print("平博账号信息未配置，请手动输入")
                pinbet_success = self.pinbet_system.login()
            
            if not pinbet_success:
                logger.error("平博登录失败")
                print("✗ 平博登录失败")
                print("  提示: 请检查用户名密码是否正确，或账户是否被锁定")
                print("  可以尝试手动登录或联系客服")
                return False
            else:
                print("✓ 平博登录成功")
            
            # 登录皇冠
            print("正在登录皇冠账号...")
            crown_success = self.crown_system.login()

            if not crown_success:
                # 检查是否为维护状态
                print("✗ 皇冠平台当前可能处于维护状态")
                print("  提示: 请稍后再试，或联系客服确认平台状态")
                logger.warning("皇冠平台可能处于维护状态，登录失败")
                return False
            
            self.systems_logged_in = True
            logger.info("所有系统登录成功")

            # 登录成功后初始化学习系统
            print("正在初始化比赛匹配学习系统...")
            if self.initialize_learning_system():
                print("✓ 学习系统初始化成功")
            else:
                print("✗ 学习系统初始化失败（不影响对冲功能）")

            return True
            
        except Exception as e:
            logger.error(f"登录系统失败: {e}")
            return False
    
    def show_system_status(self):
        """显示系统状态"""
        print("\n===== 系统状态 =====")
        print(f"系统初始化: {'✓' if self.systems_initialized else '✗'}")
        print(f"系统登录: {'✓' if self.systems_logged_in else '✗'}")
        
        if self.systems_initialized:
            print(f"平博登录: {'✓' if getattr(self.pinbet_system, 'is_logged_in', False) else '✗'}")
            print(f"皇冠登录: {'✓' if getattr(self.crown_system, 'is_logged_in', False) else '✗'}")
            print(f"对冲功能: {'✓' if self.hedge_integration.is_hedge_available() else '✗'}")
        
        print(f"对冲配置启用: {'✓' if self.hedge_config.is_enabled() else '✗'}")
        print("=" * 20)
    
    def show_account_balances(self):
        """显示账户余额"""
        if not self.systems_logged_in:
            print("系统未登录，无法获取余额")
            return
        
        print("\n===== 账户余额 =====")
        
        try:
            # 获取平博余额
            if self.pinbet_system.get_account_balance():
                pinbet_balance = getattr(self.pinbet_system.api, 'balance', {})
                print(f"平博余额: {pinbet_balance.get('betCredit', 0)} {pinbet_balance.get('currency', 'CNY')}")
            else:
                print("平博余额: 获取失败")
        except Exception as e:
            print(f"平博余额: 获取异常 - {e}")
        
        try:
            # 获取皇冠余额
            self.crown_system.get_account_balance()
            print(f"皇冠余额: {getattr(self.crown_system, 'credit', 0)}")
        except Exception as e:
            print(f"皇冠余额: 获取异常 - {e}")
        
        print("=" * 20)
    
    def execute_single_platform_bet(self, platform: str):
        """执行单平台投注"""
        if not self.systems_logged_in:
            print("系统未登录，无法执行投注")
            return
        
        try:
            if platform.lower() == "pinbet":
                print("执行平博单独投注...")
                result = self.pinbet_system.process_bet_data()
                print(f"平博投注结果: {'成功' if result else '失败'}")
            elif platform.lower() == "crown":
                print("执行皇冠单独投注...")
                result = self.crown_system.process_bet_data()
                print(f"皇冠投注结果: {'成功' if result else '失败'}")
            else:
                print("无效的平台选择")
        except Exception as e:
            logger.error(f"执行单平台投注失败: {e}")
            print(f"投注执行失败: {e}")
    
    def execute_hedge_bet(self):
        """执行单次对冲投注"""
        if not self.hedge_integration.is_hedge_available():
            print("对冲功能不可用，请检查配置和登录状态")
            return

        try:
            print("正在查找对冲机会...")
            opportunities = self.hedge_integration.fetch_arbitrage_opportunities()

            if not opportunities:
                print("未找到对冲机会")
                return

            print(f"找到 {len(opportunities)} 个对冲机会")

            # 显示对冲机会
            for i, opportunity in enumerate(opportunities):
                print(f"\n对冲机会 {i+1}:")

                # 显示投注选项
                for j, option in enumerate(opportunity[:2]):  # 只显示前两个投注选项
                    print(f"  选项{j+1}: {option.get('bookmaker')} - {option.get('description')} - 赔率: {option.get('koef')}")

                # 显示验证结果
                if len(opportunity) > 2 and "validation" in opportunity[2]:
                    validation = opportunity[2]["validation"]
                    reason = opportunity[2]["reason"]
                    print(f"  验证结果: {reason}")

                    # 显示匹配分数
                    if "match_validation" in validation and validation["match_validation"]["score"]:
                        score = validation["match_validation"]["score"]
                        print(f"  匹配分数: {score['total_score']:.3f} (置信度: {score['confidence']})")

                    # 显示安全检查摘要
                    if "summary" in validation:
                        summary = validation["summary"]
                        print(f"  安全检查: {summary['passed_checks']}/{summary['total_checks']} 通过")

            # 询问用户是否执行
            choice = input("\n是否执行第一个对冲机会? (y/n): ")
            if choice.lower() == 'y':
                print("正在执行对冲投注...")
                result = self.hedge_integration.execute_hedge_opportunity(opportunities[0])

                # 处理不同的执行结果
                if result.get("success"):
                    print("✓ 对冲投注执行成功！")
                    print(f"投注详情: {result.get('message', '')}")
                elif result.get("requires_confirmation"):
                    # 中置信度需要用户确认
                    print(f"⚠ {result.get('message')}")
                    print(f"当前置信度: {result.get('confidence')}")
                    confirm = input("是否仍要继续执行? (y/n): ")
                    if confirm.lower() == 'y':
                        print("强制执行对冲投注...")
                        force_result = self.hedge_integration.execute_hedge_opportunity_force(opportunities[0])
                        if force_result.get("success"):
                            print("✓ 强制对冲投注执行成功！")
                        else:
                            print("✗ 强制对冲投注执行失败")
                            print(f"失败原因: {force_result.get('message', '')}")
                    else:
                        print("已取消对冲投注")
                elif result.get("confidence_changed"):
                    # 置信度发生变化
                    print(f"⚠ {result.get('message')}")
                    print(f"置信度变化: {result.get('old_confidence')} -> {result.get('new_confidence')}")
                    print("建议重新评估投注机会")
                else:
                    print("✗ 对冲投注执行失败")
                    print(f"失败原因: {result.get('message', '')}")
            else:
                print("已取消对冲投注")

        except Exception as e:
            logger.error(f"执行对冲投注失败: {e}")
            print(f"对冲投注执行失败: {e}")

    def execute_continuous_hedge_bet(self, interval: int = 30):
        """执行持续对冲投注，直到用户按 Ctrl+C 退出"""
        if not self.hedge_integration.is_hedge_available():
            print("对冲功能不可用，请检查配置和登录状态")
            return

        print(f"开始持续对冲投注监控，检查间隔: {interval}秒")
        print("按 Ctrl+C 停止监控")
        print("静默监控中，只有发现对冲机会时才会显示信息...")
        print("=" * 50)

        # 统计信息
        total_checks = 0
        opportunities_found = 0
        successful_bets = 0
        failed_bets = 0
        start_time = datetime.now()

        try:
            while True:
                total_checks += 1
                current_time = datetime.now().strftime("%H:%M:%S")

                try:
                    opportunities = self.hedge_integration.fetch_arbitrage_opportunities()

                    if opportunities:
                        opportunities_found += len(opportunities)
                        print(f"\n[{current_time}] 第 {total_checks} 次检查 - 找到 {len(opportunities)} 个对冲机会！")

                        # 显示第一个对冲机会的详细信息
                        opportunity = opportunities[0]
                        print(f"\n最佳对冲机会:")

                        # 显示投注选项
                        for j, option in enumerate(opportunity[:2]):
                            print(f"  选项{j+1}: {option.get('bookmaker')} - {option.get('description')} - 赔率: {option.get('koef')}")

                        # 显示验证结果
                        if len(opportunity) > 2 and "validation" in opportunity[2]:
                            validation = opportunity[2]["validation"]
                            reason = opportunity[2]["reason"]
                            print(f"  验证结果: {reason}")

                            # 显示匹配分数
                            if "match_validation" in validation and validation["match_validation"]["score"]:
                                score = validation["match_validation"]["score"]
                                print(f"  匹配分数: {score['total_score']:.3f} (置信度: {score['confidence']})")

                        # 自动执行对冲投注
                        print("正在自动执行对冲投注...")
                        result = self.hedge_integration.execute_hedge_opportunity(opportunities[0])

                        # 处理执行结果
                        if result.get("success"):
                            successful_bets += 1
                            print("✓ 对冲投注执行成功！")
                            print(f"投注详情: {result.get('message', '')}")
                        elif result.get("requires_confirmation"):
                            # 中置信度自动强制执行
                            print(f"⚠ {result.get('message')}")
                            print(f"当前置信度: {result.get('confidence')}")
                            print("自动强制执行对冲投注...")
                            force_result = self.hedge_integration.execute_hedge_opportunity_force(opportunities[0])
                            if force_result.get("success"):
                                successful_bets += 1
                                print("✓ 强制对冲投注执行成功！")
                            else:
                                failed_bets += 1
                                print("✗ 强制对冲投注执行失败")
                                print(f"失败原因: {force_result.get('message', '')}")
                        elif result.get("confidence_changed"):
                            failed_bets += 1
                            print(f"⚠ {result.get('message')}")
                            print(f"置信度变化: {result.get('old_confidence')} -> {result.get('new_confidence')}")
                            print("跳过此次投注机会")
                        else:
                            failed_bets += 1
                            print("✗ 对冲投注执行失败")
                            print(f"失败原因: {result.get('message', '')}")

                        # 只有在找到对冲机会时才显示详细统计信息
                        runtime = datetime.now() - start_time
                        print(f"\n--- 运行统计 ---")
                        print(f"运行时间: {runtime}")
                        print(f"总检查次数: {total_checks}")
                        print(f"发现机会数: {opportunities_found}")
                        print(f"成功投注: {successful_bets}")
                        print(f"失败投注: {failed_bets}")
                        print(f"成功率: {(successful_bets/(successful_bets+failed_bets)*100):.1f}%" if (successful_bets+failed_bets) > 0 else "0.0%")
                        print("-" * 20)

                except Exception as e:
                    logger.error(f"检查对冲机会时发生异常: {e}")
                    print(f"[{current_time}] 检查异常: {e}")

                # 等待下次检查（只有在找到对冲机会时才显示等待信息）
                if opportunities:
                    print(f"等待 {interval} 秒后继续监控...")
                time.sleep(interval)

        except KeyboardInterrupt:
            print(f"\n\n持续对冲投注已停止")
            runtime = datetime.now() - start_time
            print(f"\n=== 最终统计 ===")
            print(f"总运行时间: {runtime}")
            print(f"总检查次数: {total_checks}")
            print(f"发现机会总数: {opportunities_found}")
            print(f"成功投注: {successful_bets}")
            print(f"失败投注: {failed_bets}")
            print(f"最终成功率: {(successful_bets/(successful_bets+failed_bets)*100):.1f}%" if (successful_bets+failed_bets) > 0 else "0.0%")
            print("=" * 20)
        except Exception as e:
            logger.error(f"持续对冲投注异常: {e}")
            print(f"监控异常: {e}")
    
    def start_auto_hedge_monitoring(self, interval: int = 10):
        """启动自动对冲监控"""
        if not self.hedge_integration.is_hedge_available():
            print("对冲功能不可用，无法启动自动监控")
            return
        
        print(f"开始自动对冲监控，检查间隔: {interval}秒")
        print("按 Ctrl+C 停止监控")
        
        try:
            self.hedge_integration.monitor_and_execute_hedges(interval)
        except KeyboardInterrupt:
            print("\n自动对冲监控已停止")
        except Exception as e:
            logger.error(f"自动对冲监控异常: {e}")
            print(f"监控异常: {e}")
    
    def show_hedge_config(self):
        """显示对冲配置"""
        print("\n" + self.hedge_config.get_config_summary())

        # 显示执行模式说明
        execution_mode = self.hedge_config.get_execution_mode()
        if execution_mode == "sequential":
            print("\n🔒 当前使用顺序投注模式（推荐）")
            print("   - 基准平台先投注，成功后再投注对冲平台")
            print("   - 避免双边投注风险，提高安全性")
            print(f"   - 基准平台成功后等待 {self.hedge_config.get_base_first_delay()} 秒")
        else:
            print("\n⚡ 当前使用并发投注模式")
            print("   - 同时向两个平台发送投注请求")
            print("   - 速度更快但存在双边风险")
    
    def update_hedge_config(self):
        """更新对冲配置"""
        print("\n===== 对冲配置更新 =====")
        print("1. 启用/禁用对冲功能")
        print("2. 设置基准投注金额")
        print("3. 设置基准平台")
        print("4. 设置重试次数")
        print("5. 设置执行模式")
        print("6. 设置基准延迟时间")
        print("0. 返回")
        
        choice = input("请选择要更新的配置: ")
        
        try:
            if choice == "1":
                current = self.hedge_config.is_enabled()
                new_value = not current
                self.hedge_config.update_config({"enable": new_value})
                print(f"对冲功能已{'启用' if new_value else '禁用'}")
                
            elif choice == "2":
                current = self.hedge_config.get_base_amount()
                new_amount = float(input(f"当前基准金额: {current}, 请输入新金额: "))
                self.hedge_config.update_config({"base_amount": new_amount})
                print(f"基准金额已更新为: {new_amount}")
                
            elif choice == "3":
                current = self.hedge_config.get_base_platform()
                print(f"当前基准平台: {current}")
                print("可选项: auto, pinbet, crown")
                new_platform = input("请输入新的基准平台: ")
                self.hedge_config.update_config({"base_platform": new_platform})
                print(f"基准平台已更新为: {new_platform}")
                
            elif choice == "4":
                current = self.hedge_config.get_retry_attempts()
                new_retries = int(input(f"当前重试次数: {current}, 请输入新的重试次数: "))
                self.hedge_config.update_config({"retry_attempts": new_retries})
                print(f"重试次数已更新为: {new_retries}")

            elif choice == "5":
                current = self.hedge_config.get_execution_mode()
                print(f"当前执行模式: {current}")
                print("可选项:")
                print("  sequential - 顺序投注（推荐，更安全）")
                print("  concurrent - 并发投注（更快，但有风险）")
                new_mode = input("请输入新的执行模式: ")
                self.hedge_config.update_config({"execution_mode": new_mode})
                print(f"执行模式已更新为: {new_mode}")

            elif choice == "6":
                current = self.hedge_config.get_base_first_delay()
                print(f"当前基准延迟时间: {current}秒")
                print("说明: 基准平台投注成功后等待的时间，确保投注完全确认")
                new_delay = float(input("请输入新的延迟时间（秒，范围0.1-10.0）: "))
                self.hedge_config.update_config({"base_first_delay": new_delay})
                print(f"基准延迟时间已更新为: {new_delay}秒")

            elif choice == "0":
                return
            else:
                print("无效选择")
                
            # 保存配置到文件
            if self.hedge_config.save_config_to_file():
                print("配置已保存到文件")
            else:
                print("配置保存失败")
                
        except ValueError as e:
            print(f"输入格式错误: {e}")
        except Exception as e:
            print(f"更新配置失败: {e}")


    def show_match_safety_status(self):
        """显示比赛匹配安全状态"""
        if not self.hedge_integration:
            print("对冲集成器未初始化")
            return

        print("\n===== 比赛匹配安全状态 =====")

        try:
            # 显示验证器状态
            validator = self.hedge_integration.match_validator
            safety = self.hedge_integration.match_safety

            print("验证器状态:")
            print(f"  队伍名称映射规则: {len(validator.team_name_replacements)} 条")
            print(f"  联赛名称映射规则: {len(validator.league_name_replacements)} 条")
            print(f"  盘口类型映射规则: {len(validator.market_type_mapping)} 条")

            print("\n安全检查器状态:")
            print(f"  安全规则数量: {len(safety.safety_rules)} 条")
            enabled_rules = sum(1 for rule in safety.safety_rules if rule.enabled)
            print(f"  启用的规则: {enabled_rules} 条")

            print(f"  黑名单队伍: {len(safety.blacklist.get('teams', []))} 个")
            print(f"  黑名单联赛: {len(safety.blacklist.get('leagues', []))} 个")

            # 显示最近的安全检查统计
            print("\n最近安全检查统计:")
            print("  (需要实际运行对冲后才有数据)")

            # 提供测试选项
            print("\n测试选项:")
            print("1. 运行比赛匹配测试")
            print("2. 查看黑名单配置")
            print("3. 添加到黑名单")
            print("0. 返回主菜单")

            choice = input("\n请选择操作: ")

            if choice == "1":
                self._run_match_test()
            elif choice == "2":
                self._show_blacklist()
            elif choice == "3":
                self._add_to_blacklist()

        except Exception as e:
            print(f"显示安全状态失败: {e}")

            
    def _show_blacklist(self):
        """显示黑名单配置"""
        try:
            safety = self.hedge_integration.match_safety
            print("\n当前黑名单配置:")
            print("队伍黑名单:")
            for team in safety.blacklist.get('teams', []):
                print(f"  - {team}")

            print("\n联赛黑名单:")
            for league in safety.blacklist.get('leagues', []):
                print(f"  - {league}")
        except Exception as e:
            print(f"显示黑名单失败: {e}")

    def _add_to_blacklist(self):
        """添加到黑名单"""
        try:
            safety = self.hedge_integration.match_safety

            print("\n添加到黑名单:")
            print("1. 添加队伍")
            print("2. 添加联赛")

            choice = input("请选择类型: ")

            if choice == "1":
                team_name = input("请输入队伍名称: ")
                if team_name:
                    safety.add_to_blacklist("teams", team_name)
                    print(f"已添加队伍到黑名单: {team_name}")
            elif choice == "2":
                league_name = input("请输入联赛名称: ")
                if league_name:
                    safety.add_to_blacklist("leagues", league_name)
                    print(f"已添加联赛到黑名单: {league_name}")

        except Exception as e:
            print(f"添加到黑名单失败: {e}")

    def show_main_menu(self):
        """显示主菜单"""
        while True:
            print("\n===== 对冲投注系统 =====")
            print("1. 显示系统状态")
            print("2. 显示账户余额")
            print("3. 平博单独投注")
            print("4. 皇冠单独投注")
            print("5. 持续对冲投注（按Ctrl+C退出）")
            print("6. 单次对冲投注")
            print("7. 启动自动对冲监控")
            print("8. 显示对冲配置")
            print("9. 更新对冲配置")
            print("10. 比赛匹配安全检查")
            print("11. 重新登录系统")
            print("12. 学习系统管理")
            print("0. 退出")
            
            choice = input("\n请选择操作: ")
            
            try:
                if choice == "1":
                    self.show_system_status()
                elif choice == "2":
                    self.show_account_balances()
                elif choice == "3":
                    self.execute_single_platform_bet("pinbet")
                elif choice == "4":
                    self.execute_single_platform_bet("crown")
                elif choice == "5":
                    interval = int(input("请输入检查间隔（秒，默认30）: ") or "30")
                    self.execute_continuous_hedge_bet(interval)
                elif choice == "6":
                    self.execute_hedge_bet()
                elif choice == "7":
                    interval = int(input("请输入检查间隔（秒，默认10）: ") or "10")
                    self.start_auto_hedge_monitoring(interval)
                elif choice == "8":
                    self.show_hedge_config()
                elif choice == "9":
                    self.update_hedge_config()
                elif choice == "10":
                    self.show_match_safety_status()
                elif choice == "11":
                    if self.login_systems():
                        print("重新登录成功")
                    else:
                        print("重新登录失败")
                elif choice == "12":
                    self.show_learning_system_menu()
                elif choice == "0":
                    print("谢谢使用，再见!")
                    break
                else:
                    print("无效选择，请重新输入")
                    
            except ValueError as e:
                print(f"输入格式错误: {e}")
            except Exception as e:
                logger.error(f"菜单操作异常: {e}")
                print(f"操作失败: {e}")

    def show_learning_system_menu(self):
        """显示学习系统管理菜单"""
        while True:
            print("\n===== 学习系统管理 =====")
            print("1. 显示学习系统状态")
            print("2. 刷新平台数据")
            print("3. 手动保存学习别名")
            print("4. 显示学习统计")
            print("5. 导出学习数据")
            print("6. 清理旧数据")
            print("7. 重新初始化学习系统")
            print("0. 返回主菜单")

            choice = input("\n请选择操作: ")

            try:
                if choice == "1":
                    self.show_learning_system_status()
                elif choice == "2":
                    self.refresh_learning_data()
                elif choice == "3":
                    self.manual_save_aliases()
                elif choice == "4":
                    self.show_learning_statistics()
                elif choice == "5":
                    self.export_learning_data()
                elif choice == "6":
                    self.cleanup_learning_data()
                elif choice == "7":
                    self.reinitialize_learning_system()
                elif choice == "0":
                    break
                else:
                    print("无效选择，请重新输入")

            except Exception as e:
                logger.error(f"学习系统菜单操作异常: {e}")
                print(f"操作失败: {e}")

    def show_learning_system_status(self):
        """显示学习系统状态"""
        try:
            print("\n===== 学习系统状态 =====")

            if not self.learning_initialized:
                print("❌ 学习系统未初始化")
                return

            status = self.learning_controller.get_system_status()

            print(f"✓ 学习系统已初始化")
            print(f"自动保存: {'启用' if status.get('auto_save_enabled') else '禁用'}")

            if status.get('last_data_fetch'):
                print(f"最后数据获取: {status['last_data_fetch']}")

            # 平台数据状态
            if status.get('platform_data'):
                print("\n平台数据:")
                for platform, data in status['platform_data'].items():
                    print(f"  {platform}: {data['total_matches']}场比赛 "
                          f"(今日{data['today_matches']}场, 早场{data['early_matches']}场)")

            # 别名统计
            if status.get('alias_summary'):
                alias_info = status['alias_summary']
                print(f"\n别名配置:")
                print(f"  联赛数: {alias_info['total_leagues']}")
                print(f"  队伍数: {alias_info['total_teams']}")
                print(f"  别名数: {alias_info['total_aliases']}")

            # 学习统计
            if status.get('learning_summary', {}).get('learning_stats'):
                stats = status['learning_summary']['learning_stats']
                print(f"\n学习统计:")
                print(f"  总失败次数: {stats.get('total_failures', 0)}")
                print(f"  成功学习次数: {stats.get('successful_learning', 0)}")
                print(f"  学习成功率: {stats.get('learning_rate', 0):.1%}")

        except Exception as e:
            logger.error(f"显示学习系统状态失败: {e}")
            print(f"显示状态失败: {e}")

    def refresh_learning_data(self):
        """刷新学习数据"""
        try:
            print("\n正在刷新平台数据...")

            if not self.learning_initialized:
                print("❌ 学习系统未初始化")
                return

            success = self.learning_controller.refresh_platform_data(
                self.pinbet_system, self.crown_system
            )

            if success:
                print("✓ 平台数据刷新成功")
            else:
                print("❌ 平台数据刷新失败")

        except Exception as e:
            logger.error(f"刷新学习数据失败: {e}")
            print(f"刷新失败: {e}")

    def manual_save_aliases(self):
        """手动保存学习别名"""
        try:
            print("\n正在保存学习别名...")

            if not self.learning_initialized:
                print("❌ 学习系统未初始化")
                return

            success = self.learning_controller.manual_save_aliases()

            if success:
                print("✓ 学习别名保存成功")
            else:
                print("❌ 学习别名保存失败")

        except Exception as e:
            logger.error(f"保存学习别名失败: {e}")
            print(f"保存失败: {e}")

    def show_learning_statistics(self):
        """显示学习统计"""
        try:
            print("\n===== 学习统计详情 =====")

            if not self.learning_initialized:
                print("❌ 学习系统未初始化")
                return

            summary = self.learning_controller.learning_system.get_learning_summary()

            # 显示详细统计
            if summary.get('learning_stats'):
                stats = summary['learning_stats']
                print(f"总失败记录: {stats.get('total_failures', 0)}")
                print(f"成功学习次数: {stats.get('successful_learning', 0)}")
                print(f"学习成功率: {stats.get('learning_rate', 0):.1%}")
                print(f"最后更新: {stats.get('last_updated', 'N/A')}")

            # 显示最近学习的别名
            if summary.get('recent_aliases'):
                print(f"\n最近学习的别名:")
                for alias_info in summary['recent_aliases']:
                    print(f"  {alias_info.get('timestamp', 'N/A')[:19]} - "
                          f"{alias_info.get('league', 'N/A')}")
                    for team, aliases in alias_info.get('aliases', {}).items():
                        print(f"    {team} -> {aliases}")

        except Exception as e:
            logger.error(f"显示学习统计失败: {e}")
            print(f"显示统计失败: {e}")

    def export_learning_data(self):
        """导出学习数据"""
        try:
            export_dir = input("请输入导出目录（默认: exports）: ").strip() or "exports"

            print(f"\n正在导出学习数据到: {export_dir}")

            if not self.learning_initialized:
                print("❌ 学习系统未初始化")
                return

            success = self.learning_controller.export_learning_data(export_dir)

            if success:
                print(f"✓ 学习数据导出成功: {export_dir}")
            else:
                print("❌ 学习数据导出失败")

        except Exception as e:
            logger.error(f"导出学习数据失败: {e}")
            print(f"导出失败: {e}")

    def cleanup_learning_data(self):
        """清理学习数据"""
        try:
            days = input("请输入保留天数（默认: 30）: ").strip()
            days_to_keep = int(days) if days else 30

            print(f"\n正在清理{days_to_keep}天前的数据...")

            if not self.learning_initialized:
                print("❌ 学习系统未初始化")
                return

            success = self.learning_controller.cleanup_old_data(days_to_keep)

            if success:
                print(f"✓ 数据清理完成，保留{days_to_keep}天内的数据")
            else:
                print("❌ 数据清理失败")

        except Exception as e:
            logger.error(f"清理学习数据失败: {e}")
            print(f"清理失败: {e}")

    def reinitialize_learning_system(self):
        """重新初始化学习系统"""
        try:
            print("\n正在重新初始化学习系统...")

            if self.initialize_learning_system():
                print("✓ 学习系统重新初始化成功")
            else:
                print("❌ 学习系统重新初始化失败")

        except Exception as e:
            logger.error(f"重新初始化学习系统失败: {e}")
            print(f"重新初始化失败: {e}")

def main():
    """主程序入口"""
    # 设置命令行参数
    parser = argparse.ArgumentParser(description='对冲投注系统')
    parser.add_argument('--auto', action='store_true', help='启动自动对冲监控模式')
    parser.add_argument('--interval', type=int, default=10, help='自动监控检查间隔（秒）')
    args = parser.parse_args()
    
    # 初始化对冲系统专用日志
    setup_hedge_logging()
    
    print("===== 对冲投注系统启动 =====")
    
    # 创建系统实例
    hedge_system = HedgeBettingSystem()
    
    # 初始化系统
    if not hedge_system.initialize_systems():
        print("系统初始化失败，程序退出")
        return
    
    # 登录系统
    if not hedge_system.login_systems():
        print("系统登录失败，程序退出")
        return
    
    print("系统初始化和登录完成")
    
    # 根据命令行参数决定运行模式
    if args.auto:
        print(f"启动自动对冲监控模式，检查间隔: {args.interval}秒")
        hedge_system.start_auto_hedge_monitoring(args.interval)
    else:
        # 显示菜单
        hedge_system.show_main_menu()

if __name__ == "__main__":
    main()
