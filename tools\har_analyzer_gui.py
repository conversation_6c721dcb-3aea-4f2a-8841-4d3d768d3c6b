#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HAR文件分析工具 - GUI版本
支持拖拽、可视化分析、数据路径提取等功能
"""

import json
import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
try:
    from tkinterdnd2 import DND_FILES, TkinterDnD
    HAS_DND = True
except ImportError:
    HAS_DND = False
import threading
from typing import Dict, List, Any, Optional
from datetime import datetime

class HARAnalyzerGUI:
    """HAR文件分析器GUI"""
    
    def __init__(self):
        if HAS_DND:
            self.root = TkinterDnD.Tk()
        else:
            self.root = tk.Tk()

        self.root.title("HAR文件分析工具")
        self.root.geometry("1200x800")

        # 数据存储
        self.har_data = None
        self.simplified_data = None
        self.current_file = None

        # 创建界面
        self.create_widgets()

        if HAS_DND:
            self.setup_drag_drop()
        else:
            # 添加提示信息
            self.file_label.config(text="请选择HAR文件 (拖拽功能需要安装tkinterdnd2)")
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部工具栏
        toolbar = ttk.Frame(main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(toolbar, text="选择HAR文件", command=self.select_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="精简HAR", command=self.simplify_har).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="导出精简版", command=self.export_simplified).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="清空", command=self.clear_all).pack(side=tk.LEFT, padx=(0, 5))
        
        # 文件信息标签
        self.file_label = ttk.Label(toolbar, text="请选择或拖拽HAR文件到此处", foreground="gray")
        self.file_label.pack(side=tk.RIGHT)
        
        # 创建Notebook（标签页）
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 标签页1: 请求列表
        self.create_requests_tab()
        
        # 标签页2: 数据路径分析
        self.create_path_analysis_tab()
        
        # 标签页3: 响应内容查看
        self.create_content_tab()
        
        # 标签页4: 统计信息
        self.create_stats_tab()
        
    def create_requests_tab(self):
        """创建请求列表标签页"""
        requests_frame = ttk.Frame(self.notebook)
        self.notebook.add(requests_frame, text="请求列表")
        
        # 过滤器
        filter_frame = ttk.Frame(requests_frame)
        filter_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(filter_frame, text="过滤:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar()
        filter_entry = ttk.Entry(filter_frame, textvariable=self.filter_var, width=30)
        filter_entry.pack(side=tk.LEFT, padx=(5, 10))
        filter_entry.bind('<KeyRelease>', self.filter_requests)
        
        ttk.Button(filter_frame, text="只显示XHR请求", command=self.show_xhr_only).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(filter_frame, text="显示全部", command=self.show_all_requests).pack(side=tk.LEFT)
        
        # 请求列表
        columns = ('序号', '方法', 'URL', '状态码', '类型', '大小', '时间')
        self.requests_tree = ttk.Treeview(requests_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.requests_tree.heading(col, text=col)
            if col == 'URL':
                self.requests_tree.column(col, width=400)
            elif col in ['序号', '状态码', '大小', '时间']:
                self.requests_tree.column(col, width=80)
            else:
                self.requests_tree.column(col, width=100)
        
        # 滚动条
        requests_scrollbar = ttk.Scrollbar(requests_frame, orient=tk.VERTICAL, command=self.requests_tree.yview)
        self.requests_tree.configure(yscrollcommand=requests_scrollbar.set)
        
        self.requests_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        requests_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.requests_tree.bind('<Double-1>', self.on_request_double_click)
        
    def create_path_analysis_tab(self):
        """创建数据路径分析标签页"""
        path_frame = ttk.Frame(self.notebook)
        self.notebook.add(path_frame, text="数据路径分析")
        
        # 顶部说明
        info_frame = ttk.Frame(path_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(info_frame, text="数据路径格式: n-联赛索引-比赛索引 (如: n-0-2 表示第1个联赛的第3场比赛)", 
                 foreground="blue").pack(anchor=tk.W)
        
        # 路径输入
        path_input_frame = ttk.Frame(path_frame)
        path_input_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(path_input_frame, text="数据路径:").pack(side=tk.LEFT)
        self.path_var = tk.StringVar(value="n-0-2")
        path_entry = ttk.Entry(path_input_frame, textvariable=self.path_var, width=20)
        path_entry.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Button(path_input_frame, text="提取数据", command=self.extract_path_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(path_input_frame, text="分析结构", command=self.analyze_structure).pack(side=tk.LEFT)
        
        # 结果显示
        self.path_result = scrolledtext.ScrolledText(path_frame, height=25, wrap=tk.WORD)
        self.path_result.pack(fill=tk.BOTH, expand=True)
        
    def create_content_tab(self):
        """创建响应内容查看标签页"""
        content_frame = ttk.Frame(self.notebook)
        self.notebook.add(content_frame, text="响应内容")
        
        # 请求选择
        select_frame = ttk.Frame(content_frame)
        select_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(select_frame, text="选择请求:").pack(side=tk.LEFT)
        self.request_combo = ttk.Combobox(select_frame, width=60, state="readonly")
        self.request_combo.pack(side=tk.LEFT, padx=(5, 10))
        self.request_combo.bind('<<ComboboxSelected>>', self.show_request_content)
        
        ttk.Button(select_frame, text="格式化JSON", command=self.format_json_content).pack(side=tk.LEFT)
        
        # 内容显示
        self.content_text = scrolledtext.ScrolledText(content_frame, height=25, wrap=tk.WORD)
        self.content_text.pack(fill=tk.BOTH, expand=True)
        
    def create_stats_tab(self):
        """创建统计信息标签页"""
        stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(stats_frame, text="统计信息")
        
        self.stats_text = scrolledtext.ScrolledText(stats_frame, height=25, wrap=tk.WORD)
        self.stats_text.pack(fill=tk.BOTH, expand=True)
        
    def setup_drag_drop(self):
        """设置拖拽功能"""
        def drop_file(event):
            files = event.data.split()
            if files:
                file_path = files[0].strip('{}')  # 移除可能的大括号
                if file_path.endswith('.har'):
                    self.load_har_file(file_path)

        # 绑定拖拽事件
        self.root.drop_target_register(DND_FILES)
        self.root.dnd_bind('<<Drop>>', drop_file)
        
    def select_file(self):
        """选择HAR文件"""
        file_path = filedialog.askopenfilename(
            title="选择HAR文件",
            filetypes=[("HAR files", "*.har"), ("All files", "*.*")]
        )
        
        if file_path:
            self.load_har_file(file_path)
            
    def load_har_file(self, file_path: str):
        """加载HAR文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.har_data = json.load(f)
            
            self.current_file = file_path
            self.file_label.config(text=f"已加载: {os.path.basename(file_path)}")
            
            # 更新界面
            self.update_requests_list()
            self.update_request_combo()
            self.update_stats()
            
            messagebox.showinfo("成功", f"HAR文件加载成功!\n文件: {os.path.basename(file_path)}")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载HAR文件失败:\n{str(e)}")
            
    def update_requests_list(self):
        """更新请求列表"""
        if not self.har_data:
            return
            
        # 清空现有数据
        for item in self.requests_tree.get_children():
            self.requests_tree.delete(item)
            
        entries = self.har_data.get('log', {}).get('entries', [])
        
        for i, entry in enumerate(entries):
            request = entry.get('request', {})
            response = entry.get('response', {})
            
            method = request.get('method', '')
            url = request.get('url', '')
            status = response.get('status', '')
            resource_type = entry.get('_resourceType', '')
            size = response.get('content', {}).get('size', 0)
            time_ms = entry.get('time', 0)
            
            self.requests_tree.insert('', 'end', values=(
                i + 1, method, url, status, resource_type, 
                f"{size}B" if size else "", f"{time_ms:.0f}ms"
            ))
            
    def filter_requests(self, event=None):
        """过滤请求列表"""
        if not self.har_data:
            return
            
        filter_text = self.filter_var.get().lower()
        
        # 清空现有数据
        for item in self.requests_tree.get_children():
            self.requests_tree.delete(item)
            
        entries = self.har_data.get('log', {}).get('entries', [])
        
        for i, entry in enumerate(entries):
            request = entry.get('request', {})
            response = entry.get('response', {})
            
            method = request.get('method', '')
            url = request.get('url', '')
            status = str(response.get('status', ''))
            resource_type = entry.get('_resourceType', '')
            
            # 检查是否匹配过滤条件
            if (not filter_text or 
                filter_text in url.lower() or 
                filter_text in method.lower() or 
                filter_text in resource_type.lower() or
                filter_text in status):
                
                size = response.get('content', {}).get('size', 0)
                time_ms = entry.get('time', 0)
                
                self.requests_tree.insert('', 'end', values=(
                    i + 1, method, url, status, resource_type, 
                    f"{size}B" if size else "", f"{time_ms:.0f}ms"
                ))
                
    def show_xhr_only(self):
        """只显示XHR请求"""
        if not self.har_data:
            return

        # 清空现有数据
        for item in self.requests_tree.get_children():
            self.requests_tree.delete(item)

        entries = self.har_data.get('log', {}).get('entries', [])

        for i, entry in enumerate(entries):
            resource_type = entry.get('_resourceType', '').lower()

            # 只显示XHR类型的请求
            if resource_type == 'xhr':
                request = entry.get('request', {})
                response = entry.get('response', {})

                method = request.get('method', '')
                url = request.get('url', '')
                status = response.get('status', '')
                size = response.get('content', {}).get('size', 0)
                time_ms = entry.get('time', 0)

                self.requests_tree.insert('', 'end', values=(
                    i + 1, method, url, status, resource_type,
                    f"{size}B" if size else "", f"{time_ms:.0f}ms"
                ))
        
    def show_all_requests(self):
        """显示所有请求"""
        self.filter_var.set("")
        self.filter_requests()
        
    def update_request_combo(self):
        """更新请求下拉框"""
        if not self.har_data:
            return

        entries = self.har_data.get('log', {}).get('entries', [])
        request_list = []

        for i, entry in enumerate(entries):
            resource_type = entry.get('_resourceType', '').lower()

            # 只显示XHR类型的请求
            if resource_type == 'xhr':
                request = entry.get('request', {})
                method = request.get('method', '')
                url = request.get('url', '')

                display_text = f"{i}: {method} {url[:80]}{'...' if len(url) > 80 else ''}"
                request_list.append(display_text)

        self.request_combo['values'] = request_list
        
    def on_request_double_click(self, event):
        """双击请求项"""
        selection = self.requests_tree.selection()
        if selection:
            item = self.requests_tree.item(selection[0])
            values = item['values']
            if values:
                index = int(values[0]) - 1  # 转换为0基索引
                self.show_request_details(index)
                
    def show_request_details(self, index: int):
        """显示请求详情"""
        if not self.har_data:
            return
            
        entries = self.har_data.get('log', {}).get('entries', [])
        if 0 <= index < len(entries):
            entry = entries[index]
            
            # 切换到响应内容标签页
            self.notebook.select(2)
            
            # 显示请求详情
            details = json.dumps(entry, indent=2, ensure_ascii=False)
            self.content_text.delete(1.0, tk.END)
            self.content_text.insert(1.0, details)
            
    def show_request_content(self, event=None):
        """显示选中请求的内容"""
        selection = self.request_combo.get()
        if not selection or not self.har_data:
            return
            
        try:
            index = int(selection.split(':')[0])
            entries = self.har_data.get('log', {}).get('entries', [])
            
            if 0 <= index < len(entries):
                entry = entries[index]
                response_content = entry.get('response', {}).get('content', {}).get('text', '')
                
                self.content_text.delete(1.0, tk.END)
                if response_content:
                    self.content_text.insert(1.0, response_content)
                else:
                    self.content_text.insert(1.0, "该请求没有响应内容")
                    
        except Exception as e:
            messagebox.showerror("错误", f"显示请求内容失败:\n{str(e)}")
            
    def format_json_content(self):
        """格式化JSON内容"""
        try:
            content = self.content_text.get(1.0, tk.END).strip()
            if content:
                parsed = json.loads(content)
                formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                
                self.content_text.delete(1.0, tk.END)
                self.content_text.insert(1.0, formatted)
                
        except json.JSONDecodeError:
            messagebox.showwarning("警告", "内容不是有效的JSON格式")
        except Exception as e:
            messagebox.showerror("错误", f"格式化失败:\n{str(e)}")
            
    def extract_path_data(self):
        """提取指定路径的数据"""
        if not self.har_data:
            messagebox.showwarning("警告", "请先加载HAR文件")
            return
            
        path = self.path_var.get().strip()
        if not path:
            messagebox.showwarning("警告", "请输入数据路径")
            return
            
        try:
            result = self._extract_data_by_path(path)
            
            self.path_result.delete(1.0, tk.END)
            if result:
                formatted_result = json.dumps(result, indent=2, ensure_ascii=False)
                self.path_result.insert(1.0, f"路径 '{path}' 的数据:\n\n{formatted_result}")
            else:
                self.path_result.insert(1.0, f"路径 '{path}' 没有找到数据")
                
        except Exception as e:
            messagebox.showerror("错误", f"提取数据失败:\n{str(e)}")
            
    def _extract_data_by_path(self, path: str) -> Any:
        """根据路径提取数据"""
        # 查找包含比赛数据的响应
        entries = self.har_data.get('log', {}).get('entries', [])
        
        for entry in entries:
            response = entry.get('response', {})
            content = response.get('content', {})
            text = content.get('text', '')
            
            if text:
                try:
                    data = json.loads(text)
                    
                    # 解析路径 (如: n-0-2)
                    if path.startswith('n-'):
                        parts = path.split('-')
                        if len(parts) >= 3:
                            league_index = int(parts[1])
                            match_index = int(parts[2])
                            
                            # 查找数据结构
                            if 'n' in data and isinstance(data['n'], list):
                                if 0 <= league_index < len(data['n']):
                                    league_data = data['n'][league_index]
                                    if isinstance(league_data, list) and len(league_data) > 2:
                                        matches = league_data[2]
                                        if isinstance(matches, list) and 0 <= match_index < len(matches):
                                            return matches[match_index]
                            
                            # 尝试其他可能的结构
                            if 'l' in data and isinstance(data['l'], list):
                                for sport_data in data['l']:
                                    if isinstance(sport_data, list) and len(sport_data) > 2:
                                        leagues = sport_data[2]
                                        if isinstance(leagues, list) and 0 <= league_index < len(leagues):
                                            league_info = leagues[league_index]
                                            if isinstance(league_info, list) and len(league_info) > 2:
                                                matches = league_info[2]
                                                if isinstance(matches, list) and 0 <= match_index < len(matches):
                                                    return matches[match_index]
                                                    
                except json.JSONDecodeError:
                    continue
                    
        return None

    def analyze_structure(self):
        """分析数据结构"""
        if not self.har_data:
            messagebox.showwarning("警告", "请先加载HAR文件")
            return

        try:
            analysis_result = []
            entries = self.har_data.get('log', {}).get('entries', [])

            for i, entry in enumerate(entries):
                request = entry.get('request', {})
                response = entry.get('response', {})
                content = response.get('content', {})
                text = content.get('text', '')
                url = request.get('url', '')

                if text and any(keyword in url.lower() for keyword in ['api', 'service', 'sports', 'events']):
                    try:
                        data = json.loads(text)
                        analysis_result.append(f"\n=== 请求 {i}: {url} ===")
                        analysis_result.append(self._analyze_json_structure(data, ""))

                        # 特别分析比赛数据结构
                        if 'n' in data or 'l' in data:
                            analysis_result.append("\n📊 比赛数据结构分析:")
                            analysis_result.append(self._analyze_match_structure(data))

                    except json.JSONDecodeError:
                        continue

            self.path_result.delete(1.0, tk.END)
            if analysis_result:
                self.path_result.insert(1.0, "\n".join(analysis_result))
            else:
                self.path_result.insert(1.0, "没有找到可分析的JSON响应数据")

        except Exception as e:
            messagebox.showerror("错误", f"分析结构失败:\n{str(e)}")

    def _analyze_json_structure(self, data: Any, prefix: str, max_depth: int = 3) -> str:
        """分析JSON结构"""
        if max_depth <= 0:
            return f"{prefix}... (深度限制)"

        result = []

        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    result.append(f"{prefix}{key}: {type(value).__name__}({len(value)})")
                    if len(result) < 20:  # 限制输出长度
                        result.append(self._analyze_json_structure(value, prefix + "  ", max_depth - 1))
                else:
                    result.append(f"{prefix}{key}: {type(value).__name__}")

        elif isinstance(data, list):
            if data:
                result.append(f"{prefix}[0]: {type(data[0]).__name__}")
                if isinstance(data[0], (dict, list)):
                    result.append(self._analyze_json_structure(data[0], prefix + "  ", max_depth - 1))
                if len(data) > 1:
                    result.append(f"{prefix}... (共{len(data)}项)")

        return "\n".join(result)

    def _analyze_match_structure(self, data: Dict) -> str:
        """分析比赛数据结构"""
        result = []

        # 分析 'n' 字段
        if 'n' in data and isinstance(data['n'], list):
            result.append(f"n字段: {len(data['n'])} 个联赛")
            for i, league in enumerate(data['n'][:3]):  # 只显示前3个
                if isinstance(league, list) and len(league) > 2:
                    league_name = league[1] if len(league) > 1 else "未知联赛"
                    matches = league[2] if len(league) > 2 else []
                    match_count = len(matches) if isinstance(matches, list) else 0
                    result.append(f"  联赛{i}: {league_name} ({match_count}场比赛)")

                    # 显示比赛示例
                    if isinstance(matches, list) and matches:
                        for j, match in enumerate(matches[:2]):  # 只显示前2场
                            if isinstance(match, list) and len(match) >= 3:
                                home = match[1] if len(match) > 1 else "未知"
                                away = match[2] if len(match) > 2 else "未知"
                                result.append(f"    比赛{j}: {home} vs {away}")
                                result.append(f"      路径: n-{i}-{j}")

        # 分析 'l' 字段
        if 'l' in data and isinstance(data['l'], list):
            result.append(f"\nl字段: {len(data['l'])} 个体育类型")
            for sport_idx, sport_data in enumerate(data['l'][:2]):  # 只显示前2个体育类型
                if isinstance(sport_data, list) and len(sport_data) > 2:
                    sport_name = sport_data[1] if len(sport_data) > 1 else "未知体育"
                    leagues = sport_data[2] if len(sport_data) > 2 else []
                    result.append(f"  体育{sport_idx}: {sport_name}")

                    if isinstance(leagues, list):
                        for league_idx, league_info in enumerate(leagues[:2]):  # 只显示前2个联赛
                            if isinstance(league_info, list) and len(league_info) > 2:
                                league_name = league_info[1] if len(league_info) > 1 else "未知联赛"
                                matches = league_info[2] if len(league_info) > 2 else []
                                match_count = len(matches) if isinstance(matches, list) else 0
                                result.append(f"    联赛{league_idx}: {league_name} ({match_count}场比赛)")

                                # 显示比赛示例
                                if isinstance(matches, list) and matches:
                                    for match_idx, match in enumerate(matches[:2]):
                                        if isinstance(match, list) and len(match) >= 3:
                                            home = match[1] if len(match) > 1 else "未知"
                                            away = match[2] if len(match) > 2 else "未知"
                                            result.append(f"      比赛{match_idx}: {home} vs {away}")
                                            result.append(f"        路径: l-{sport_idx}-{league_idx}-{match_idx}")

        return "\n".join(result) if result else "未找到标准的比赛数据结构"

    def simplify_har(self):
        """精简HAR文件"""
        if not self.har_data:
            messagebox.showwarning("警告", "请先加载HAR文件")
            return

        try:
            from tools.har_simplifier import HARSimplifier

            simplifier = HARSimplifier()
            self.simplified_data = simplifier.simplify_har(self.har_data)

            # 统计信息
            original_count = len(self.har_data.get('log', {}).get('entries', []))
            simplified_count = len(self.simplified_data['log']['entries'])

            messagebox.showinfo("成功",
                f"HAR文件精简完成!\n"
                f"原始请求数: {original_count}\n"
                f"精简后请求数: {simplified_count}\n"
                f"压缩比: {simplified_count/original_count*100:.1f}%")

        except Exception as e:
            messagebox.showerror("错误", f"精简HAR文件失败:\n{str(e)}")

    def export_simplified(self):
        """导出精简版HAR"""
        if not self.simplified_data:
            messagebox.showwarning("警告", "请先精简HAR文件")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="保存精简版HAR文件",
                defaultextension=".har",
                filetypes=[("HAR files", "*.har"), ("All files", "*.*")]
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.simplified_data, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("成功", f"精简版HAR文件已保存:\n{file_path}")

        except Exception as e:
            messagebox.showerror("错误", f"导出失败:\n{str(e)}")

    def update_stats(self):
        """更新统计信息"""
        if not self.har_data:
            return

        try:
            entries = self.har_data.get('log', {}).get('entries', [])

            # 基本统计
            total_requests = len(entries)
            methods = {}
            status_codes = {}
            resource_types = {}
            domains = {}

            total_size = 0
            total_time = 0

            for entry in entries:
                request = entry.get('request', {})
                response = entry.get('response', {})

                # 方法统计
                method = request.get('method', 'Unknown')
                methods[method] = methods.get(method, 0) + 1

                # 状态码统计
                status = response.get('status', 0)
                status_codes[status] = status_codes.get(status, 0) + 1

                # 资源类型统计
                resource_type = entry.get('_resourceType', 'Unknown')
                resource_types[resource_type] = resource_types.get(resource_type, 0) + 1

                # 域名统计
                url = request.get('url', '')
                if url:
                    try:
                        from urllib.parse import urlparse
                        domain = urlparse(url).netloc
                        domains[domain] = domains.get(domain, 0) + 1
                    except:
                        pass

                # 大小和时间统计
                size = response.get('content', {}).get('size', 0)
                time_ms = entry.get('time', 0)

                if isinstance(size, (int, float)):
                    total_size += size
                if isinstance(time_ms, (int, float)):
                    total_time += time_ms

            # 生成统计报告
            stats_text = []
            stats_text.append("=== HAR文件统计信息 ===\n")
            stats_text.append(f"总请求数: {total_requests}")
            stats_text.append(f"总大小: {total_size:,.0f} 字节 ({total_size/1024/1024:.2f} MB)")
            stats_text.append(f"总时间: {total_time:,.0f} 毫秒 ({total_time/1000:.2f} 秒)")

            stats_text.append(f"\n=== 请求方法分布 ===")
            for method, count in sorted(methods.items(), key=lambda x: x[1], reverse=True):
                percentage = count / total_requests * 100
                stats_text.append(f"{method}: {count} ({percentage:.1f}%)")

            stats_text.append(f"\n=== 状态码分布 ===")
            for status, count in sorted(status_codes.items(), key=lambda x: x[1], reverse=True):
                percentage = count / total_requests * 100
                stats_text.append(f"{status}: {count} ({percentage:.1f}%)")

            stats_text.append(f"\n=== 资源类型分布 ===")
            for resource_type, count in sorted(resource_types.items(), key=lambda x: x[1], reverse=True):
                percentage = count / total_requests * 100
                stats_text.append(f"{resource_type}: {count} ({percentage:.1f}%)")

            stats_text.append(f"\n=== 域名分布 (Top 10) ===")
            top_domains = sorted(domains.items(), key=lambda x: x[1], reverse=True)[:10]
            for domain, count in top_domains:
                percentage = count / total_requests * 100
                stats_text.append(f"{domain}: {count} ({percentage:.1f}%)")

            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, "\n".join(stats_text))

        except Exception as e:
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, f"统计信息生成失败: {str(e)}")

    def clear_all(self):
        """清空所有数据"""
        self.har_data = None
        self.simplified_data = None
        self.current_file = None

        self.file_label.config(text="请选择或拖拽HAR文件到此处")

        # 清空所有显示区域
        for item in self.requests_tree.get_children():
            self.requests_tree.delete(item)

        self.request_combo['values'] = []
        self.path_result.delete(1.0, tk.END)
        self.content_text.delete(1.0, tk.END)
        self.stats_text.delete(1.0, tk.END)

        messagebox.showinfo("完成", "所有数据已清空")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = HARAnalyzerGUI()
        app.run()
    except Exception as e:
        print(f"启动GUI失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
