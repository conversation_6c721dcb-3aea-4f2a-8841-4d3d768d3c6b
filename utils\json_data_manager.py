#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
JSON 数据管理器
提供安全的 JSON 文件读写、备份、恢复和文件锁功能
"""

import os
import json
import time
import shutil
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from contextlib import contextmanager

# 尝试导入文件锁模块
try:
    import fcntl
    HAS_FCNTL = True
except ImportError:
    # Windows 系统没有 fcntl
    HAS_FCNTL = False
    try:
        import msvcrt
        HAS_MSVCRT = True
    except ImportError:
        HAS_MSVCRT = False

logger = logging.getLogger(__name__)

class JSONDataManager:
    """JSON 数据管理器"""
    
    def __init__(self, data_dir: str = "data", max_file_size: int = 10*1024*1024):
        """
        初始化数据管理器
        
        Args:
            data_dir: 数据目录
            max_file_size: 单个文件最大大小（字节）
        """
        self.data_dir = data_dir
        self.max_file_size = max_file_size
        self.backup_dir = os.path.join(data_dir, "backups")
        self.lock_dir = os.path.join(data_dir, "locks")
        
        # 文件锁字典
        self._file_locks = {}
        self._lock_mutex = threading.Lock()
        
        # 确保目录存在
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        for directory in [self.data_dir, self.backup_dir, self.lock_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"创建目录: {directory}")
    
    @contextmanager
    def _file_lock(self, file_path: str):
        """文件锁上下文管理器"""
        lock_file = os.path.join(self.lock_dir, f"{os.path.basename(file_path)}.lock")

        with self._lock_mutex:
            if lock_file not in self._file_locks:
                self._file_locks[lock_file] = threading.Lock()
            file_lock = self._file_locks[lock_file]

        with file_lock:
            lock_fd = None
            try:
                if HAS_FCNTL:
                    # Unix/Linux 系统使用 fcntl
                    lock_fd = os.open(lock_file, os.O_CREAT | os.O_WRONLY | os.O_TRUNC)

                    try:
                        fcntl.flock(lock_fd, fcntl.LOCK_EX | fcntl.LOCK_NB)
                    except (IOError, OSError):
                        time.sleep(0.1)
                        fcntl.flock(lock_fd, fcntl.LOCK_EX)

                elif HAS_MSVCRT:
                    # Windows 系统使用 msvcrt
                    lock_fd = os.open(lock_file, os.O_CREAT | os.O_WRONLY | os.O_TRUNC)

                    try:
                        msvcrt.locking(lock_fd, msvcrt.LK_NBLCK, 1)
                    except (IOError, OSError):
                        time.sleep(0.1)
                        msvcrt.locking(lock_fd, msvcrt.LK_LOCK, 1)
                else:
                    # 无文件锁支持，仅使用线程锁
                    with open(lock_file, 'w') as f:
                        f.write(str(os.getpid()))

                yield

            finally:
                if lock_fd is not None:
                    try:
                        if HAS_FCNTL:
                            fcntl.flock(lock_fd, fcntl.LOCK_UN)
                        elif HAS_MSVCRT:
                            msvcrt.locking(lock_fd, msvcrt.LK_UNLCK, 1)
                        os.close(lock_fd)
                    except:
                        pass

                    # 清理锁文件
                    try:
                        os.remove(lock_file)
                    except:
                        pass
    
    def _get_file_path(self, filename: str, date_str: str = None) -> str:
        """获取文件路径"""
        if date_str:
            # 按日期分片的文件
            return os.path.join(self.data_dir, f"{filename}_{date_str}.json")
        else:
            # 普通文件
            return os.path.join(self.data_dir, f"{filename}.json")
    
    def _get_backup_path(self, file_path: str) -> str:
        """获取备份文件路径"""
        filename = os.path.basename(file_path)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return os.path.join(self.backup_dir, f"{filename}.{timestamp}.backup")
    
    def _create_backup(self, file_path: str) -> bool:
        """创建文件备份"""
        try:
            if os.path.exists(file_path):
                backup_path = self._get_backup_path(file_path)
                shutil.copy2(file_path, backup_path)
                logger.debug(f"创建备份: {backup_path}")
                
                # 清理旧备份（保留最近10个）
                self._cleanup_old_backups(file_path)
                return True
            return False
        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            return False
    
    def _cleanup_old_backups(self, file_path: str, keep_count: int = 10):
        """清理旧备份文件"""
        try:
            filename = os.path.basename(file_path)
            backup_pattern = f"{filename}."
            
            # 获取所有备份文件
            backup_files = []
            for f in os.listdir(self.backup_dir):
                if f.startswith(backup_pattern) and f.endswith('.backup'):
                    backup_path = os.path.join(self.backup_dir, f)
                    backup_files.append((backup_path, os.path.getmtime(backup_path)))
            
            # 按修改时间排序，删除旧的备份
            backup_files.sort(key=lambda x: x[1], reverse=True)
            for backup_path, _ in backup_files[keep_count:]:
                os.remove(backup_path)
                logger.debug(f"删除旧备份: {backup_path}")
                
        except Exception as e:
            logger.error(f"清理旧备份失败: {e}")
    
    def _check_file_size(self, file_path: str) -> bool:
        """检查文件大小是否超限"""
        try:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                return size < self.max_file_size
            return True
        except Exception as e:
            logger.error(f"检查文件大小失败: {e}")
            return True
    
    def load_data(self, filename: str, date_str: str = None, default: Any = None) -> Any:
        """
        加载 JSON 数据
        
        Args:
            filename: 文件名（不含扩展名）
            date_str: 日期字符串（用于分片文件）
            default: 默认值
            
        Returns:
            加载的数据或默认值
        """
        file_path = self._get_file_path(filename, date_str)
        
        try:
            with self._file_lock(file_path):
                if not os.path.exists(file_path):
                    logger.debug(f"文件不存在: {file_path}")
                    return default
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    logger.debug(f"加载数据成功: {file_path}")
                    return data
                    
        except json.JSONDecodeError as e:
            logger.error(f"JSON 解析失败: {file_path}, 错误: {e}")
            # 尝试从备份恢复
            if self._restore_from_backup(file_path):
                return self.load_data(filename, date_str, default)
            return default
        except Exception as e:
            logger.error(f"加载数据失败: {file_path}, 错误: {e}")
            return default
    
    def save_data(self, filename: str, data: Any, date_str: str = None, create_backup: bool = True) -> bool:
        """
        保存 JSON 数据
        
        Args:
            filename: 文件名（不含扩展名）
            data: 要保存的数据
            date_str: 日期字符串（用于分片文件）
            create_backup: 是否创建备份
            
        Returns:
            是否保存成功
        """
        file_path = self._get_file_path(filename, date_str)
        
        try:
            with self._file_lock(file_path):
                # 检查文件大小
                if not self._check_file_size(file_path):
                    logger.warning(f"文件大小超限: {file_path}")
                    return False
                
                # 创建备份
                if create_backup:
                    self._create_backup(file_path)
                
                # 保存数据
                temp_path = file_path + '.tmp'
                with open(temp_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                
                # 原子性替换
                if os.name == 'nt':  # Windows
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    os.rename(temp_path, file_path)
                else:  # Unix/Linux
                    os.rename(temp_path, file_path)
                
                logger.debug(f"保存数据成功: {file_path}")
                return True
                
        except Exception as e:
            logger.error(f"保存数据失败: {file_path}, 错误: {e}")
            # 清理临时文件
            temp_path = file_path + '.tmp'
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass
            return False
    
    def _restore_from_backup(self, file_path: str) -> bool:
        """从备份恢复文件"""
        try:
            filename = os.path.basename(file_path)
            backup_pattern = f"{filename}."
            
            # 查找最新的备份文件
            latest_backup = None
            latest_time = 0
            
            for f in os.listdir(self.backup_dir):
                if f.startswith(backup_pattern) and f.endswith('.backup'):
                    backup_path = os.path.join(self.backup_dir, f)
                    mtime = os.path.getmtime(backup_path)
                    if mtime > latest_time:
                        latest_time = mtime
                        latest_backup = backup_path
            
            if latest_backup:
                shutil.copy2(latest_backup, file_path)
                logger.info(f"从备份恢复文件: {latest_backup} -> {file_path}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"从备份恢复失败: {e}")
            return False
    
    def get_daily_files(self, filename: str, days: int = 30) -> List[str]:
        """
        获取指定天数内的日期分片文件列表
        
        Args:
            filename: 文件名前缀
            days: 天数
            
        Returns:
            文件路径列表
        """
        files = []
        today = datetime.now()
        
        for i in range(days):
            date = today - timedelta(days=i)
            date_str = date.strftime("%Y-%m-%d")
            file_path = self._get_file_path(filename, date_str)
            
            if os.path.exists(file_path):
                files.append(file_path)
        
        return files
    
    def cleanup_old_files(self, filename: str, keep_days: int = 30) -> int:
        """
        清理旧的日期分片文件
        
        Args:
            filename: 文件名前缀
            keep_days: 保留天数
            
        Returns:
            删除的文件数量
        """
        deleted_count = 0
        cutoff_date = datetime.now() - timedelta(days=keep_days)
        
        try:
            pattern = f"{filename}_"
            for f in os.listdir(self.data_dir):
                if f.startswith(pattern) and f.endswith('.json'):
                    file_path = os.path.join(self.data_dir, f)
                    
                    # 从文件名提取日期
                    try:
                        date_part = f[len(pattern):-5]  # 去掉前缀和.json
                        file_date = datetime.strptime(date_part, "%Y-%m-%d")
                        
                        if file_date < cutoff_date:
                            os.remove(file_path)
                            deleted_count += 1
                            logger.info(f"删除旧文件: {file_path}")
                    except ValueError:
                        # 文件名格式不匹配，跳过
                        continue
            
            logger.info(f"清理完成，删除了 {deleted_count} 个旧文件")
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")
            return deleted_count


# 全局实例
_data_manager = None

def get_data_manager(data_dir: str = "data") -> JSONDataManager:
    """获取数据管理器实例"""
    global _data_manager
    if _data_manager is None:
        _data_manager = JSONDataManager(data_dir)
    return _data_manager
