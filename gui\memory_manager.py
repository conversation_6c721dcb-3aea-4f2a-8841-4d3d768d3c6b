#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
内存管理器 - 遵循SOLID原则的内存优化

功能：
1. LRU缓存管理
2. 日志去重优化
3. 文件位置跟踪优化
4. 内存使用监控
"""

import time
import hashlib
import threading
import psutil
import os
import logging
from collections import OrderedDict
from typing import Dict, Set, Optional, Any, Tuple
from dataclasses import dataclass
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


@dataclass
class MemoryStats:
    """内存统计信息"""
    process_memory_mb: float
    system_memory_percent: float
    cache_sizes: Dict[str, int]
    total_cached_items: int


class CacheInterface(ABC):
    """缓存接口 - 接口隔离原则"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        pass
    
    @abstractmethod
    def put(self, key: str, value: Any) -> None:
        pass
    
    @abstractmethod
    def clear(self) -> None:
        pass
    
    @abstractmethod
    def size(self) -> int:
        pass


class LRUCache(CacheInterface):
    """LRU缓存实现 - 单一职责原则"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache = OrderedDict()
        self.lock = threading.RLock()
        self.hit_count = 0
        self.miss_count = 0
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        with self.lock:
            if key in self.cache:
                # 移动到末尾（最近使用）
                value = self.cache.pop(key)
                self.cache[key] = value
                self.hit_count += 1
                return value
            else:
                self.miss_count += 1
                return None
    
    def put(self, key: str, value: Any) -> None:
        """添加缓存项"""
        with self.lock:
            if key in self.cache:
                # 更新现有项
                self.cache.pop(key)
            elif len(self.cache) >= self.max_size:
                # 删除最久未使用的项
                self.cache.popitem(last=False)
            
            self.cache[key] = value
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.hit_count = 0
            self.miss_count = 0
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)
    
    def get_hit_rate(self) -> float:
        """获取缓存命中率"""
        total = self.hit_count + self.miss_count
        return self.hit_count / total if total > 0 else 0.0


class LogDeduplicator:
    """日志去重器 - 优化内存使用"""
    
    def __init__(self, max_size: int = 5000, cleanup_threshold: float = 0.8):
        """
        初始化日志去重器
        
        Args:
            max_size: 最大缓存大小
            cleanup_threshold: 清理阈值（当达到max_size * threshold时触发清理）
        """
        self.max_size = max_size
        self.cleanup_threshold = cleanup_threshold
        self.cleanup_size = int(max_size * cleanup_threshold)
        
        # 使用LRU缓存存储日志哈希
        self.log_cache = LRUCache(max_size)
        
        # 统计信息
        self.total_logs = 0
        self.duplicate_logs = 0
        self.last_cleanup_time = time.time()
    
    def is_duplicate(self, log_content: str) -> bool:
        """
        检查日志是否重复
        
        Args:
            log_content: 日志内容
            
        Returns:
            是否为重复日志
        """
        # 生成日志哈希
        log_hash = hashlib.md5(log_content.encode('utf-8')).hexdigest()
        
        self.total_logs += 1
        
        # 检查是否已存在
        if self.log_cache.get(log_hash) is not None:
            self.duplicate_logs += 1
            return True
        
        # 添加到缓存
        self.log_cache.put(log_hash, time.time())
        
        # 检查是否需要清理
        if self.log_cache.size() >= self.cleanup_size:
            self._cleanup_old_entries()
        
        return False
    
    def _cleanup_old_entries(self):
        """清理旧的缓存条目"""
        current_time = time.time()
        
        # 只保留最近的条目（LRU会自动处理）
        # 这里主要是更新统计信息
        self.last_cleanup_time = current_time
        
        logger.debug(f"日志去重器清理完成，当前缓存大小: {self.log_cache.size()}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'total_logs': self.total_logs,
            'duplicate_logs': self.duplicate_logs,
            'cache_size': self.log_cache.size(),
            'hit_rate': self.log_cache.get_hit_rate(),
            'duplicate_rate': self.duplicate_logs / self.total_logs if self.total_logs > 0 else 0.0
        }
    
    def clear(self):
        """清空去重器"""
        self.log_cache.clear()
        self.total_logs = 0
        self.duplicate_logs = 0


class FilePositionTracker:
    """文件位置跟踪器 - 优化文件读取"""
    
    def __init__(self, max_files: int = 50):
        """
        初始化文件位置跟踪器
        
        Args:
            max_files: 最大跟踪文件数
        """
        self.max_files = max_files
        self.positions = LRUCache(max_files)
        self.file_sizes = LRUCache(max_files)
        self.lock = threading.RLock()
    
    def get_position(self, file_path: str) -> int:
        """获取文件读取位置"""
        with self.lock:
            position = self.positions.get(file_path)
            return position if position is not None else 0
    
    def set_position(self, file_path: str, position: int):
        """设置文件读取位置"""
        with self.lock:
            self.positions.put(file_path, position)
            
            # 同时记录文件大小用于验证
            try:
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    self.file_sizes.put(file_path, file_size)
            except OSError:
                pass
    
    def is_file_changed(self, file_path: str) -> bool:
        """检查文件是否发生变化"""
        try:
            if not os.path.exists(file_path):
                return False
            
            current_size = os.path.getsize(file_path)
            cached_size = self.file_sizes.get(file_path)
            
            return cached_size is None or current_size != cached_size
            
        except OSError:
            return False
    
    def get_new_content_size(self, file_path: str) -> int:
        """获取新内容的大小"""
        try:
            if not os.path.exists(file_path):
                return 0
            
            current_size = os.path.getsize(file_path)
            last_position = self.get_position(file_path)
            
            return max(0, current_size - last_position)
            
        except OSError:
            return 0
    
    def clear_file(self, file_path: str):
        """清除特定文件的跟踪信息"""
        with self.lock:
            # LRU缓存没有直接删除方法，但可以设置为None
            self.positions.put(file_path, 0)
            self.file_sizes.put(file_path, 0)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'tracked_files': self.positions.size(),
            'position_hit_rate': self.positions.get_hit_rate(),
            'size_hit_rate': self.file_sizes.get_hit_rate()
        }


class MemoryManager:
    """内存管理器 - 统一管理所有内存相关功能"""
    
    def __init__(self, 
                 log_cache_size: int = 5000,
                 file_tracker_size: int = 50,
                 enable_monitoring: bool = True):
        """
        初始化内存管理器
        
        Args:
            log_cache_size: 日志缓存大小
            file_tracker_size: 文件跟踪器大小
            enable_monitoring: 是否启用内存监控
        """
        self.log_deduplicator = LogDeduplicator(log_cache_size)
        self.file_tracker = FilePositionTracker(file_tracker_size)
        self.enable_monitoring = enable_monitoring
        
        # 内存监控
        self.process = psutil.Process(os.getpid()) if enable_monitoring else None
        self.last_memory_check = time.time()
        self.memory_check_interval = 60  # 每分钟检查一次
        
        logger.info(f"内存管理器已初始化 - 日志缓存: {log_cache_size}, "
                   f"文件跟踪: {file_tracker_size}, 监控: {enable_monitoring}")
    
    def is_duplicate_log(self, log_content: str) -> bool:
        """检查是否为重复日志"""
        return self.log_deduplicator.is_duplicate(log_content)
    
    def get_file_position(self, file_path: str) -> int:
        """获取文件读取位置"""
        return self.file_tracker.get_position(file_path)
    
    def set_file_position(self, file_path: str, position: int):
        """设置文件读取位置"""
        self.file_tracker.set_position(file_path, position)
    
    def is_file_changed(self, file_path: str) -> bool:
        """检查文件是否变化"""
        return self.file_tracker.is_file_changed(file_path)
    
    def get_new_content_size(self, file_path: str) -> int:
        """获取新内容大小"""
        return self.file_tracker.get_new_content_size(file_path)
    
    def get_memory_stats(self) -> Optional[MemoryStats]:
        """获取内存统计信息"""
        if not self.enable_monitoring or not self.process:
            return None
        
        try:
            # 获取进程内存信息
            memory_info = self.process.memory_info()
            process_memory_mb = memory_info.rss / 1024 / 1024
            
            # 获取系统内存使用率
            system_memory = psutil.virtual_memory()
            system_memory_percent = system_memory.percent
            
            # 获取缓存大小
            cache_sizes = {
                'log_deduplicator': self.log_deduplicator.log_cache.size(),
                'file_positions': self.file_tracker.positions.size(),
                'file_sizes': self.file_tracker.file_sizes.size()
            }
            
            total_cached_items = sum(cache_sizes.values())
            
            return MemoryStats(
                process_memory_mb=process_memory_mb,
                system_memory_percent=system_memory_percent,
                cache_sizes=cache_sizes,
                total_cached_items=total_cached_items
            )
            
        except Exception as e:
            logger.error(f"获取内存统计失败: {e}")
            return None
    
    def check_memory_usage(self) -> bool:
        """检查内存使用情况，如果需要则触发清理"""
        current_time = time.time()
        
        if current_time - self.last_memory_check < self.memory_check_interval:
            return False
        
        self.last_memory_check = current_time
        
        stats = self.get_memory_stats()
        if not stats:
            return False
        
        # 如果进程内存超过500MB或系统内存使用率超过85%，触发清理
        if stats.process_memory_mb > 500 or stats.system_memory_percent > 85:
            logger.warning(f"内存使用过高 - 进程: {stats.process_memory_mb:.1f}MB, "
                          f"系统: {stats.system_memory_percent:.1f}%")
            self.cleanup_caches()
            return True
        
        return False
    
    def cleanup_caches(self):
        """清理缓存"""
        logger.info("开始清理内存缓存...")
        
        # 清理日志去重器（保留最近的50%）
        old_log_size = self.log_deduplicator.log_cache.size()
        if old_log_size > 100:
            # 创建新的较小缓存
            new_cache = LRUCache(old_log_size // 2)
            # 复制最近使用的项
            with self.log_deduplicator.log_cache.lock:
                items = list(self.log_deduplicator.log_cache.cache.items())
                for key, value in items[-old_log_size//2:]:
                    new_cache.put(key, value)
            
            self.log_deduplicator.log_cache = new_cache
        
        logger.info(f"内存缓存清理完成 - 日志缓存从 {old_log_size} 减少到 "
                   f"{self.log_deduplicator.log_cache.size()}")
    
    def get_full_statistics(self) -> Dict[str, Any]:
        """获取完整统计信息"""
        stats = {
            'log_deduplicator': self.log_deduplicator.get_statistics(),
            'file_tracker': self.file_tracker.get_statistics(),
            'memory_stats': None
        }
        
        memory_stats = self.get_memory_stats()
        if memory_stats:
            stats['memory_stats'] = {
                'process_memory_mb': memory_stats.process_memory_mb,
                'system_memory_percent': memory_stats.system_memory_percent,
                'cache_sizes': memory_stats.cache_sizes,
                'total_cached_items': memory_stats.total_cached_items
            }
        
        return stats
    
    def clear_all(self):
        """清空所有缓存"""
        self.log_deduplicator.clear()
        # 文件跟踪器不完全清空，只重置位置
        logger.info("所有内存缓存已清空")


# 全局单例实例
_memory_manager = None


def get_memory_manager() -> MemoryManager:
    """获取全局内存管理器实例"""
    global _memory_manager
    if _memory_manager is None:
        _memory_manager = MemoryManager()
    return _memory_manager


def cleanup_memory_manager():
    """清理全局内存管理器"""
    global _memory_manager
    if _memory_manager:
        _memory_manager.clear_all()
        _memory_manager = None