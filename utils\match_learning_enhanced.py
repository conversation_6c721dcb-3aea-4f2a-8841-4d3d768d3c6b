#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict

from utils.match_data_fetcher import Match<PERSON><PERSON><PERSON><PERSON><PERSON>, MatchInfo
from utils.advanced_similarity import calculate_team_similarity

logger = logging.getLogger(__name__)

@dataclass
class MatchFailure:
    """匹配失败记录"""
    timestamp: str
    arbitrage_home: str
    arbitrage_away: str
    arbitrage_league: str
    source_platform: str
    target_platform: str
    failure_reason: str
    best_match_score: float
    best_match_info: Optional[Dict] = None
    learned_aliases: Optional[Dict] = None

@dataclass
class LearningResult:
    """学习结果"""
    success: bool
    message: str
    suggested_aliases: Dict = None
    confidence_score: float = 0.0

class MatchLearningEnhanced:
    """增强的比赛匹配学习系统"""
    
    def __init__(self, data_dir: str = "data/match_learning"):
        """
        初始化学习系统
        
        参数:
            data_dir: 数据目录
        """
        self.data_dir = data_dir
        self.failures_file = os.path.join(data_dir, "match_failures.jsonl")
        self.learned_aliases_file = os.path.join(data_dir, "learned_aliases.json")
        self.learning_stats_file = os.path.join(data_dir, "learning_stats.json")
        
        # 确保目录存在
        os.makedirs(data_dir, exist_ok=True)
        
        # 初始化数据获取器
        self.data_fetcher = MatchDataFetcher()
        
        # 加载已学习的别名
        self.learned_aliases = self._load_learned_aliases()
        
        # 学习统计
        self.stats = self._load_learning_stats()
        
        logger.info(f"增强匹配学习系统初始化完成，数据目录: {data_dir}")
    
    def record_match_failure(self, arbitrage_data: Dict, target_platform: str, 
                           failure_reason: str) -> str:
        """
        记录匹配失败并尝试学习
        
        参数:
            arbitrage_data: 套利数据
            target_platform: 目标平台
            failure_reason: 失败原因
            
        返回:
            学习结果ID
        """
        try:
            # 提取套利数据信息
            arbitrage_home, arbitrage_away = self._extract_team_names(arbitrage_data)
            arbitrage_league = arbitrage_data.get("league", "")
            source_platform = arbitrage_data.get("source", "betburger")
            
            logger.info(f"记录匹配失败: {arbitrage_home} vs {arbitrage_away} -> {target_platform}")
            
            # 在缓存数据中查找相似比赛
            similar_matches = self.data_fetcher.find_similar_matches(
                arbitrage_home, arbitrage_away, target_platform
            )
            
            best_match_score = 0.0
            best_match_info = None
            
            if similar_matches:
                best_match, best_match_score = similar_matches[0]
                best_match_info = best_match.to_dict()
                logger.info(f"找到最佳匹配: {best_match.home_team} vs {best_match.away_team}, 分数: {best_match_score:.3f}")
            
            # 创建失败记录
            failure = MatchFailure(
                timestamp=datetime.now().isoformat(),
                arbitrage_home=arbitrage_home,
                arbitrage_away=arbitrage_away,
                arbitrage_league=arbitrage_league,
                source_platform=source_platform,
                target_platform=target_platform,
                failure_reason=failure_reason,
                best_match_score=best_match_score,
                best_match_info=best_match_info
            )
            
            # 尝试学习
            learning_result = self._attempt_learning(failure, similar_matches)
            
            if learning_result.success:
                failure.learned_aliases = learning_result.suggested_aliases
                logger.info(f"学习成功: {learning_result.message}")
            else:
                logger.warning(f"学习失败: {learning_result.message}")
            
            # 保存失败记录
            failure_id = self._save_failure_record(failure)
            
            # 更新统计
            self._update_stats(failure, learning_result.success)
            
            return failure_id
            
        except Exception as e:
            logger.error(f"记录匹配失败时出错: {e}")
            return ""
    
    def _extract_team_names(self, arbitrage_data: Dict) -> Tuple[str, str]:
        """从套利数据中提取队伍名"""
        try:
            # 尝试多种可能的字段名
            home_fields = ["home_team", "team_h", "home", "team1"]
            away_fields = ["away_team", "team_c", "away", "team2"]
            
            home_team = ""
            away_team = ""
            
            for field in home_fields:
                if field in arbitrage_data and arbitrage_data[field]:
                    home_team = str(arbitrage_data[field]).strip()
                    break
            
            for field in away_fields:
                if field in arbitrage_data and arbitrage_data[field]:
                    away_team = str(arbitrage_data[field]).strip()
                    break
            
            # 如果没有找到，尝试从比赛名称中解析
            if not home_team or not away_team:
                match_name = arbitrage_data.get("match", "")
                if " vs " in match_name:
                    teams = match_name.split(" vs ")
                    if len(teams) == 2:
                        home_team = teams[0].strip()
                        away_team = teams[1].strip()
                elif " v " in match_name:
                    teams = match_name.split(" v ")
                    if len(teams) == 2:
                        home_team = teams[0].strip()
                        away_team = teams[1].strip()
            
            return home_team, away_team
            
        except Exception as e:
            logger.error(f"提取队伍名失败: {e}")
            return "", ""
    
    def _attempt_learning(self, failure: MatchFailure, 
                         similar_matches: List[Tuple[MatchInfo, float]]) -> LearningResult:
        """
        尝试从失败中学习
        
        参数:
            failure: 失败记录
            similar_matches: 相似比赛列表
            
        返回:
            学习结果
        """
        try:
            if not similar_matches:
                return LearningResult(
                    success=False,
                    message="没有找到相似的比赛数据"
                )
            
            # 获取最佳匹配
            best_match, best_score = similar_matches[0]
            
            # 设置学习阈值
            learning_threshold = 0.6
            
            if best_score < learning_threshold:
                return LearningResult(
                    success=False,
                    message=f"最佳匹配分数过低: {best_score:.3f} < {learning_threshold}",
                    confidence_score=best_score
                )
            
            # 生成别名建议
            suggested_aliases = self._generate_alias_suggestions(
                failure.arbitrage_home, failure.arbitrage_away,
                best_match.home_team, best_match.away_team,
                failure.arbitrage_league, best_match.league
            )
            
            if not suggested_aliases:
                return LearningResult(
                    success=False,
                    message="无法生成有效的别名建议",
                    confidence_score=best_score
                )
            
            # 保存学习到的别名
            self._save_learned_aliases(suggested_aliases, failure.arbitrage_league)
            
            return LearningResult(
                success=True,
                message=f"成功学习别名，匹配分数: {best_score:.3f}",
                suggested_aliases=suggested_aliases,
                confidence_score=best_score
            )
            
        except Exception as e:
            logger.error(f"学习过程出错: {e}")
            return LearningResult(
                success=False,
                message=f"学习过程出错: {str(e)}"
            )
    
    def _generate_alias_suggestions(self, arb_home: str, arb_away: str,
                                  match_home: str, match_away: str,
                                  arb_league: str, match_league: str) -> Dict:
        """
        生成别名建议
        
        参数:
            arb_home: 套利主队名
            arb_away: 套利客队名
            match_home: 匹配主队名
            match_away: 匹配客队名
            arb_league: 套利联赛名
            match_league: 匹配联赛名
            
        返回:
            别名建议字典
        """
        try:
            suggestions = {}
            
            # 检查主队是否需要别名
            if arb_home.lower() != match_home.lower():
                # 计算单独的相似度
                home_score, _ = calculate_team_similarity(
                    arb_home, "", match_home, "", {}
                )
                
                if home_score > 0.5:  # 有一定相似度才建议
                    if arb_home not in suggestions:
                        suggestions[arb_home] = []
                    if match_home not in suggestions[arb_home]:
                        suggestions[arb_home].append(match_home)
            
            # 检查客队是否需要别名
            if arb_away.lower() != match_away.lower():
                # 计算单独的相似度
                away_score, _ = calculate_team_similarity(
                    "", arb_away, "", match_away, {}
                )
                
                if away_score > 0.5:  # 有一定相似度才建议
                    if arb_away not in suggestions:
                        suggestions[arb_away] = []
                    if match_away not in suggestions[arb_away]:
                        suggestions[arb_away].append(match_away)
            
            return suggestions
            
        except Exception as e:
            logger.error(f"生成别名建议失败: {e}")
            return {}
    
    def _save_learned_aliases(self, aliases: Dict, league: str):
        """保存学习到的别名"""
        try:
            if league not in self.learned_aliases:
                self.learned_aliases[league] = {}
            
            for team, alias_list in aliases.items():
                if team not in self.learned_aliases[league]:
                    self.learned_aliases[league][team] = []
                
                for alias in alias_list:
                    if alias not in self.learned_aliases[league][team]:
                        self.learned_aliases[league][team].append(alias)
            
            # 保存到文件
            with open(self.learned_aliases_file, 'w', encoding='utf-8') as f:
                json.dump(self.learned_aliases, f, ensure_ascii=False, indent=2)
            
            logger.info(f"保存学习别名: {aliases}")
            
        except Exception as e:
            logger.error(f"保存学习别名失败: {e}")
    
    def _save_failure_record(self, failure: MatchFailure) -> str:
        """保存失败记录"""
        try:
            failure_id = f"failure_{int(datetime.now().timestamp() * 1000)}"
            
            record = {
                "id": failure_id,
                **asdict(failure)
            }
            
            # 追加到JSONL文件
            with open(self.failures_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(record, ensure_ascii=False) + '\n')
            
            return failure_id
            
        except Exception as e:
            logger.error(f"保存失败记录出错: {e}")
            return ""
    
    def _load_learned_aliases(self) -> Dict:
        """加载已学习的别名"""
        try:
            if os.path.exists(self.learned_aliases_file):
                with open(self.learned_aliases_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"加载学习别名失败: {e}")
            return {}
    
    def _load_learning_stats(self) -> Dict:
        """加载学习统计"""
        try:
            if os.path.exists(self.learning_stats_file):
                with open(self.learning_stats_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {
                "total_failures": 0,
                "successful_learning": 0,
                "learning_rate": 0.0,
                "last_updated": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"加载学习统计失败: {e}")
            return {}
    
    def _update_stats(self, failure: MatchFailure, learning_success: bool):
        """更新学习统计"""
        try:
            self.stats["total_failures"] += 1
            if learning_success:
                self.stats["successful_learning"] += 1
            
            self.stats["learning_rate"] = (
                self.stats["successful_learning"] / self.stats["total_failures"]
            ) if self.stats["total_failures"] > 0 else 0.0
            
            self.stats["last_updated"] = datetime.now().isoformat()
            
            # 保存统计
            with open(self.learning_stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"更新学习统计失败: {e}")
    
    def get_learned_aliases_for_team_aliases(self) -> Dict:
        """
        获取适用于team_aliases.json格式的学习别名
        
        返回:
            team_aliases格式的字典
        """
        try:
            team_aliases_format = {}
            
            for league, teams in self.learned_aliases.items():
                if league not in team_aliases_format:
                    team_aliases_format[league] = {}
                
                for team, aliases in teams.items():
                    if aliases:  # 只包含有别名的队伍
                        team_aliases_format[league][team] = aliases
            
            return team_aliases_format
            
        except Exception as e:
            logger.error(f"转换学习别名格式失败: {e}")
            return {}
    
    def get_learning_summary(self) -> Dict:
        """获取学习摘要"""
        try:
            total_aliases = 0
            total_leagues = len(self.learned_aliases)
            
            for league, teams in self.learned_aliases.items():
                total_aliases += len(teams)
            
            return {
                "total_leagues": total_leagues,
                "total_learned_teams": total_aliases,
                "learning_stats": self.stats,
                "recent_aliases": self._get_recent_aliases(5)
            }
            
        except Exception as e:
            logger.error(f"获取学习摘要失败: {e}")
            return {}
    
    def _get_recent_aliases(self, limit: int = 5) -> List[Dict]:
        """获取最近学习的别名"""
        try:
            recent = []
            
            # 读取最近的失败记录
            if os.path.exists(self.failures_file):
                with open(self.failures_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 取最后几行
                for line in lines[-limit:]:
                    try:
                        record = json.loads(line.strip())
                        if record.get("learned_aliases"):
                            recent.append({
                                "timestamp": record.get("timestamp"),
                                "league": record.get("arbitrage_league"),
                                "aliases": record.get("learned_aliases")
                            })
                    except:
                        continue
            
            return recent
            
        except Exception as e:
            logger.error(f"获取最近别名失败: {e}")
            return []
