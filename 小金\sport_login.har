{"_priority": "High", "_resourceType": "fetch", "cache": {}, "connection": "1767667", "pageref": "page_7", "request": {"method": "GET", "url": "https://sports-launch-api.sbk-188-sports.com/api/v1/member/login?c=44&u=https://www.grandwanjiajin88.com&reg=China&q=4ZSen51z2n67c329zsCOpH0mCDydKDTmcGyOv2bfJ2ks7Ov42ZCHz6F0_4pTCaW9CnnZqArcBJXOOTMbtaF0U1Ue-9ZJkjLZO-rq_fKBG3s.&tz=480&pid=18802", "httpVersion": "http/2.0", "headers": [{"name": ":authority", "value": "sports-launch-api.sbk-188-sports.com"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/api/v1/member/login?c=44&u=https://www.grandwanjiajin88.com&reg=China&q=4ZSen51z2n67c329zsCOpH0mCDydKDTmcGyOv2bfJ2ks7Ov42ZCHz6F0_4pTCaW9CnnZqArcBJXOOTMbtaF0U1Ue-9ZJkjLZO-rq_fKBG3s.&tz=480&pid=18802"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br"}, {"name": "accept-language", "value": "zh-CN,zh;q=0.9"}, {"name": "authorization", "value": "Bearer null"}, {"name": "cache-control", "value": "max-age=0"}, {"name": "client", "value": "ba85d040-0e1e-4efa-ac78-77aed1214432"}, {"name": "devicetype", "value": "Web"}, {"name": "origin", "value": "https://sports.sbk-188-sports.com"}, {"name": "referer", "value": "https://sports.sbk-188-sports.com/"}, {"name": "sec-ch-ua", "value": "\"Not=A?Brand\";v=\"99\", \"Chromium\";v=\"118\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-site"}, {"name": "sessionid", "value": "2239481a-9a9e-476b-88d0-a8594a4398e0"}, {"name": "tabid", "value": "217589"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}], "queryString": [{"name": "c", "value": "44"}, {"name": "u", "value": "https://www.grandwanjiajin88.com"}, {"name": "reg", "value": "China"}, {"name": "q", "value": "4ZSen51z2n67c329zsCOpH0mCDydKDTmcGyOv2bfJ2ks7Ov42ZCHz6F0_4pTCaW9CnnZqArcBJXOOTMbtaF0U1Ue-9ZJkjLZO-rq_fKBG3s."}, {"name": "tz", "value": "480"}, {"name": "pid", "value": "18802"}], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "http/2.0", "headers": [{"name": "_ga", "value": "GA1.cUNrVkJzUUg0YmtaL2l2L0lqa1NhZjIyRGdUazdiM1dkelBNblFHOXJINnM4S1c5QnZSVCs5NitJSkVnWmFvbE9Kbm1XMXBUUWQvTEQvQTB3UHlUc1E9PQ=="}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "*"}, {"name": "access-control-allow-origin", "value": "https://sports.sbk-188-sports.com"}, {"name": "access-control-expose-headers", "value": "Authorization,Server,Client,Set-Cookie,SessionID,_ga"}, {"name": "authorization", "value": "Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.scmDOammE77al__0sZ2c3psMTk6WXQWGWCQB8t0F5p8"}, {"name": "cache-control", "value": "no-cache"}, {"name": "client", "value": "ba85d040-0e1e-4efa-ac78-77aed1214432"}, {"name": "content-length", "value": "0"}, {"name": "date", "value": "Tue, 01 Jul 2025 09:13:09 GMT"}, {"name": "expires", "value": "-1"}, {"name": "pragma", "value": "no-cache"}, {"name": "server", "value": "AEEAAE"}, {"name": "sessionid", "value": "2239481a-9a9e-476b-88d0-a8594a4398e0"}], "cookies": [], "content": {"size": 0, "mimeType": "x-unknown"}, "redirectURL": "", "headersSize": -1, "bodySize": -1, "_transferSize": 892, "_error": "net::ERR_ABORTED"}}