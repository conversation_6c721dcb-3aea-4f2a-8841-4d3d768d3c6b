import logging
import json
import os

def load_config():
    """加载配置文件（兼容旧版本）"""
    try:
        # 尝试使用安全配置加载器
        try:
            from .config_security import get_config_security
            config_security = get_config_security()
            config = config_security.load_config()
            if config is not None:
                return config
        except ImportError:
            logging.warning("配置安全模块不可用，使用传统方式加载配置")
        except Exception as e:
            logging.warning(f"安全配置加载失败，使用传统方式: {e}")

        # 传统方式加载配置
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        config_path = os.path.join(project_root, 'config', 'config.json')

        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        raise FileNotFoundError("未找到配置文件 config/config.json，请创建该文件") from e

def save_config(config):
    """保存配置文件（兼容旧版本）"""
    try:
        # 尝试使用安全配置保存器
        try:
            from .config_security import get_config_security
            config_security = get_config_security()
            if config_security.save_config(config):
                return True
        except ImportError:
            logging.warning("配置安全模块不可用，使用传统方式保存配置")
        except Exception as e:
            logging.warning(f"安全配置保存失败，使用传统方式: {e}")

        # 传统方式保存配置
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        config_path = os.path.join(project_root, 'config', 'config.json')

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        logging.error(f"保存配置文件失败: {e}")
        return False

def setup_logging(level=None):
    """配置日志"""
    config = load_config()
    log_level = level or config.get("log_level", "INFO")
    
    level_map = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR
    }
    
    numeric_level = level_map.get(log_level.upper(), logging.INFO)
    
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("hgbet.log", encoding="utf-8"),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger()

def format_handicap(value):
    """格式化盘口数值，如果小数点后为0则取整"""
    try:
        if value is None or value == "":
            return "0"
        if isinstance(value, str):
            if not value.strip():
                return "0"
            value = float(value)
        if value == int(value):
            return str(int(value))
        else:
            return str(value)
    except:
        return "0" if not value else str(value)

def get_handicap_sign(handicap_value):
    """获取盘口值的符号表示"""
    try:
        handicap_value = float(handicap_value)
        if handicap_value > 0:
            return "+"
        elif handicap_value < 0:
            return ""  # 负号已经包含在数值中
        else:
            return ""
    except (ValueError, TypeError):
        return ""

def load_json_data(filename):
    """加载JSON文件数据"""
    try:
        if os.path.exists(filename):
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []
    except Exception as e:
        logging.error(f"加载JSON文件 {filename} 失败: {e}")
        return []

def save_json_data(filename, data):
    """保存数据到JSON文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logging.error(f"保存JSON文件 {filename} 失败: {e}")
        return False 