2025-07-06 12:35:47,895 - __main__ - INFO - 皇冠日志将保存到: logs\hgbet_2025-07-06.log
2025-07-06 12:35:47,895 - __main__ - INFO - 初始化皇冠篮球投注模块
2025-07-06 12:35:47,907 - utils.config_security - INFO - 加载现有加密密钥
2025-07-06 12:35:47,908 - utils.config_security - INFO - 加载未加密的配置文件
2025-07-06 12:35:47,908 - __main__ - INFO - 投注记录将保存到: data\bet_records_2025-07-06.json
2025-07-06 12:35:47,908 - __main__ - INFO - 尝试登录...
2025-07-06 12:35:50,358 - __main__ - INFO - 登录状态码: 200
2025-07-06 12:35:50,358 - __main__ - INFO - 提取到UID: ved7ngda1am38245145l6432313b1
2025-07-06 12:35:50,623 - __main__ - INFO - 登录成功，当前余额：11327.3，版本号: 2025-04-30-CRM-55_89
2025-07-06 12:35:57,566 - __main__ - INFO - 开始处理套利数据
2025-07-06 12:35:57,568 - utils.config_security - INFO - 加载现有加密密钥
2025-07-06 12:35:57,568 - utils.config_security - INFO - 加载未加密的配置文件
2025-07-06 12:36:00,013 - betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-06 12:36:00,015 - betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250706.json
2025-07-06 12:36:00,015 - __main__ - INFO - 成功加载最新套利数据，共 2 条，提取到 1 个皇冠投注选项
2025-07-06 12:36:00,015 - __main__ - INFO - 获取到1条套利数据
2025-07-06 12:36:00,017 - __main__ - INFO - 创建新的投注记录文件: data\bet_records_2025-07-06.json
2025-07-06 12:36:00,017 - __main__ - INFO - 解析到投注信息: 韩国 vs 泰國, 市场类型: 19, 盘口: 148.5, 赔率: 1.97
2025-07-06 12:36:00,017 - __main__ - INFO - 转换后的赔率值: 1.97
2025-07-06 12:36:00,017 - __main__ - INFO - 处理市场类型 19
2025-07-06 12:36:00,018 - __main__ - INFO - 解析为大球: 148.5
2025-07-06 12:36:00,018 - __main__ - INFO - 处理投注信息: 韩国 vs 泰國, 大小球=大, 盘口: 148.5
2025-07-06 12:36:00,018 - __main__ - INFO - 检查重复投注: 韩国 vs 泰國, 大小球, 投注方向=大, 时段=全场, 比赛ID=410068408, 盘口值=148.5
2025-07-06 12:36:00,018 - __main__ - INFO - 检测到皇冠投注选项: 皇冠
2025-07-06 12:36:00,019 - __main__ - INFO - 赔率符合要求: 1.97 >= 1.6
2025-07-06 12:36:00,019 - __main__ - INFO - 投注信息: 韩国 vs 泰國, 大小球=大, 盘口: 148.5, 赔率: 1.97
2025-07-06 12:36:00,019 - __main__ - INFO - 开始执行投注流程
2025-07-06 12:36:00,019 - __main__ - INFO - 投注信息: 韩国 vs 泰國, 大小球=大, 盘口: 148.5, 赔率: 1.97
2025-07-06 12:36:00,019 - __main__ - INFO - 比赛投注次数检查: 泰國_vs_韩国, 当前次数: 0, 限制: 2
2025-07-06 12:36:00,019 - __main__ - INFO - 比赛投注次数检查通过: 泰國_vs_韩国, 可继续投注
2025-07-06 12:36:00,227 - __main__ - INFO - 等待3秒后执行投注...
2025-07-06 12:36:03,229 - __main__ - INFO - 投注比赛: 韩国 vs 泰國
2025-07-06 12:36:03,229 - __main__ - INFO - 投注类型: 大小球, 投注队伍: 大, 盘口: 148.5, 比赛阶段: 全场
2025-07-06 12:36:03,230 - __main__ - INFO - 使用配置文件中的投注金额: 50
2025-07-06 12:36:03,230 - __main__ - INFO - 最终确定的投注金额: 50 (类型: <class 'int'>)
2025-07-06 12:36:03,231 - __main__ - INFO - 确认投注金额: 50, 赔率: 1.97
2025-07-06 12:36:03,231 - __main__ - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-06 12:36:03,458 - __main__ - INFO - 获取到 11 场比赛
2025-07-06 12:36:03,459 - __main__ - WARNING - 比赛ID 410068408 在today比赛列表中不存在，尝试在early中查找
2025-07-06 12:36:03,459 - __main__ - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-06 12:36:03,677 - __main__ - INFO - 获取到 4 场比赛
2025-07-06 12:36:03,678 - __main__ - WARNING - 比赛ID 410068408 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-06 12:36:03,679 - __main__ - INFO - 国家名称不匹配: 韩国/None vs None/None
2025-07-06 12:36:03,680 - __main__ - INFO - 国家名称不匹配: 韩国/None vs None/None
2025-07-06 12:36:03,681 - __main__ - INFO - 国家名称不匹配: 韩国/None vs None/None
2025-07-06 12:36:03,682 - __main__ - INFO - 国家名称不匹配: 韩国/None vs None/None
2025-07-06 12:36:03,683 - __main__ - INFO - 国家名称不匹配: 韩国/None vs None/菲律宾
2025-07-06 12:36:03,684 - __main__ - INFO - 国家名称不匹配: 韩国/None vs 韩国/泰国
2025-07-06 12:36:03,684 - __main__ - INFO - 国家名称不匹配: 韩国/None vs None/菲律宾
2025-07-06 12:36:03,685 - __main__ - INFO - 国家名称不匹配: 韩国/None vs None/None
2025-07-06 12:36:03,686 - __main__ - INFO - 国家名称不匹配: 韩国/None vs None/None
2025-07-06 12:36:03,687 - __main__ - INFO - 国家名称不匹配: 韩国/None vs None/None
2025-07-06 12:36:03,687 - __main__ - INFO - 国家名称不匹配: 韩国/None vs None/None
2025-07-06 12:36:03,688 - __main__ - INFO - 国家名称不匹配: 韩国/None vs None/None
2025-07-06 12:36:03,689 - __main__ - INFO - 国家名称不匹配: 韩国/None vs None/None
2025-07-06 12:36:03,689 - __main__ - INFO - 国家名称不匹配: 韩国/None vs None/None
2025-07-06 12:36:03,690 - __main__ - INFO - 国家名称不匹配: 韩国/None vs None/None
2025-07-06 12:36:03,690 - __main__ - WARNING - 未找到匹配的比赛ID（最高分数: 0.00，阈值: 0.6），无法继续投注
2025-07-06 12:36:03,690 - __main__ - INFO - 本轮共处理了 1 个套利项，未找到有效的投注选项
2025-07-06 12:36:08,693 - utils.config_security - INFO - 加载现有加密密钥
2025-07-06 12:36:08,695 - utils.config_security - INFO - 加载未加密的配置文件
2025-07-06 12:36:10,979 - betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-06 12:36:10,982 - betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250706.json
2025-07-06 12:36:10,983 - __main__ - INFO - 成功加载最新套利数据，共 2 条，提取到 1 个皇冠投注选项
2025-07-06 12:36:10,983 - __main__ - INFO - 获取到1条套利数据
2025-07-06 12:36:12,236 - __main__ - INFO - 用户中断监控
2025-07-06 13:24:37,234 - __main__ - INFO - 皇冠日志将保存到: logs\hgbet_2025-07-06.log
2025-07-06 13:24:37,236 - __main__ - INFO - 初始化皇冠篮球投注模块
2025-07-06 13:24:37,247 - utils.config_security - INFO - 加载现有加密密钥
2025-07-06 13:24:37,247 - utils.config_security - INFO - 加载未加密的配置文件
2025-07-06 13:24:37,247 - __main__ - INFO - 投注记录将保存到: data\bet_records_2025-07-06.json
2025-07-06 13:24:37,247 - __main__ - INFO - 尝试登录...
2025-07-06 13:24:39,659 - __main__ - INFO - 登录状态码: 200
2025-07-06 13:24:39,659 - __main__ - INFO - 提取到UID: kc62nikfm38245145l6432703b1
2025-07-06 13:24:39,871 - __main__ - INFO - 登录成功，当前余额：11327.3，版本号: 2025-04-30-CRM-55_89
2025-07-06 13:24:41,976 - __main__ - INFO - 开始处理套利数据
2025-07-06 13:24:41,977 - utils.config_security - INFO - 加载现有加密密钥
2025-07-06 13:24:41,978 - utils.config_security - INFO - 加载未加密的配置文件
2025-07-06 13:24:45,280 - betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-06 13:24:45,282 - betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250706.json
2025-07-06 13:24:45,283 - __main__ - INFO - 成功加载最新套利数据，共 2 条，提取到 1 个皇冠投注选项
2025-07-06 13:24:45,283 - __main__ - INFO - 获取到1条套利数据
2025-07-06 13:24:45,283 - __main__ - INFO - 解析到投注信息: 韩国 vs 泰國, 市场类型: 19, 盘口: 147.5, 赔率: 1.89
2025-07-06 13:24:45,283 - __main__ - INFO - 转换后的赔率值: 1.89
2025-07-06 13:24:45,283 - __main__ - INFO - 处理市场类型 19
2025-07-06 13:24:45,284 - __main__ - INFO - 解析为大球: 147.5
2025-07-06 13:24:45,284 - __main__ - INFO - 处理投注信息: 韩国 vs 泰國, 大小球=大, 盘口: 147.5
2025-07-06 13:24:45,284 - __main__ - INFO - 检查重复投注: 韩国 vs 泰國, 大小球, 投注方向=大, 时段=全场, 比赛ID=410068408, 盘口值=147.5
2025-07-06 13:24:45,285 - __main__ - INFO - 检测到皇冠投注选项: 皇冠
2025-07-06 13:24:45,286 - __main__ - INFO - 赔率符合要求: 1.89 >= 1.6
2025-07-06 13:24:45,286 - __main__ - INFO - 投注信息: 韩国 vs 泰國, 大小球=大, 盘口: 147.5, 赔率: 1.89
2025-07-06 13:24:45,286 - __main__ - INFO - 开始执行投注流程
2025-07-06 13:24:45,286 - __main__ - INFO - 投注信息: 韩国 vs 泰國, 大小球=大, 盘口: 147.5, 赔率: 1.89
2025-07-06 13:24:45,286 - __main__ - INFO - 比赛投注次数检查: 泰國_vs_韩国, 当前次数: 0, 限制: 2
2025-07-06 13:24:45,286 - __main__ - INFO - 比赛投注次数检查通过: 泰國_vs_韩国, 可继续投注
2025-07-06 13:24:45,490 - __main__ - INFO - 等待3秒后执行投注...
2025-07-06 13:24:48,491 - __main__ - INFO - 投注比赛: 韩国 vs 泰國
2025-07-06 13:24:48,491 - __main__ - INFO - 投注类型: 大小球, 投注队伍: 大, 盘口: 147.5, 比赛阶段: 全场
2025-07-06 13:24:48,492 - __main__ - INFO - 使用配置文件中的投注金额: 50
2025-07-06 13:24:48,492 - __main__ - INFO - 最终确定的投注金额: 50 (类型: <class 'int'>)
2025-07-06 13:24:48,493 - __main__ - INFO - 确认投注金额: 50, 赔率: 1.89
2025-07-06 13:24:48,493 - __main__ - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-06 13:24:48,693 - __main__ - INFO - 获取到 15 场比赛
2025-07-06 13:24:48,694 - __main__ - WARNING - 比赛ID 410068408 在today比赛列表中不存在，尝试在early中查找
2025-07-06 13:24:48,694 - __main__ - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-06 13:24:48,894 - __main__ - INFO - 获取到 4 场比赛
2025-07-06 13:24:48,895 - __main__ - WARNING - 比赛ID 410068408 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-06 13:24:48,899 - __main__ - WARNING - 未找到匹配的比赛ID（最高分数: 0.00，阈值: 0.6），无法继续投注
2025-07-06 13:24:48,900 - __main__ - INFO - 本轮共处理了 1 个套利项，未找到有效的投注选项
2025-07-06 13:24:53,902 - utils.config_security - INFO - 加载现有加密密钥
2025-07-06 13:24:53,904 - utils.config_security - INFO - 加载未加密的配置文件
2025-07-06 13:24:55,076 - betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-06 13:24:55,076 - betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250706.json
2025-07-06 13:24:55,077 - __main__ - INFO - 成功加载最新套利数据，共 2 条，提取到 1 个皇冠投注选项
2025-07-06 13:24:55,077 - __main__ - INFO - 获取到1条套利数据
2025-07-06 13:24:56,356 - __main__ - INFO - 用户中断监控
