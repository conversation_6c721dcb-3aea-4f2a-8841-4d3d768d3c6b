#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置文件安全管理模块
提供配置文件加密、解密和完整性校验功能
"""

import os
import json
import hashlib
import base64
import logging
from typing import Dict, Any, Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

logger = logging.getLogger(__name__)

class ConfigSecurity:
    """配置文件安全管理器"""

    # 类级别的配置缓存
    _cached_config = None
    _cache_time = 0
    _cache_duration = 300  # 缓存5分钟
    _config_loaded_once = False  # 标记是否已经加载过配置

    def __init__(self, config_path: str = None):
        """
        初始化配置安全管理器
        
        Args:
            config_path: 配置文件路径
        """
        if config_path is None:
            # 默认配置文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)
            config_path = os.path.join(project_root, 'config', 'config.json')
        
        self.config_path = config_path
        self.key_file_path = os.path.join(os.path.dirname(config_path), '.config_key')
        self.backup_path = config_path + '.backup'
        
        # 需要加密的敏感字段（只加密密码相关字段）
        self.sensitive_fields = [
            'password'
        ]
        
        # 初始化加密密钥
        self._init_encryption_key()
    
    def _init_encryption_key(self):
        """初始化或加载加密密钥"""
        try:
            if os.path.exists(self.key_file_path):
                # 加载现有密钥
                with open(self.key_file_path, 'rb') as f:
                    self.key = f.read()
                # 密钥加载日志将在load_config中统一处理
            else:
                # 生成新密钥
                self.key = Fernet.generate_key()
                with open(self.key_file_path, 'wb') as f:
                    f.write(self.key)
                # 设置密钥文件权限（仅所有者可读写）
                os.chmod(self.key_file_path, 0o600)
                logger.info("生成新的加密密钥")

            self.cipher = Fernet(self.key)

        except Exception as e:
            logger.error(f"初始化加密密钥失败: {e}")
            raise
    
    def _encrypt_value(self, value: str) -> str:
        """加密字符串值"""
        try:
            if not isinstance(value, str):
                value = str(value)
            encrypted_bytes = self.cipher.encrypt(value.encode('utf-8'))
            return base64.b64encode(encrypted_bytes).decode('utf-8')
        except Exception as e:
            logger.error(f"加密值失败: {e}")
            return value
    
    def _decrypt_value(self, encrypted_value: str) -> str:
        """解密字符串值"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_value.encode('utf-8'))
            decrypted_bytes = self.cipher.decrypt(encrypted_bytes)
            return decrypted_bytes.decode('utf-8')
        except Exception as e:
            logger.error(f"解密值失败: {e}")
            return encrypted_value
    
    def _is_encrypted(self, value: str) -> bool:
        """检查值是否已加密"""
        try:
            # 尝试解密，如果成功说明已加密
            if not isinstance(value, str):
                return False
            base64.b64decode(value.encode('utf-8'))
            encrypted_bytes = base64.b64decode(value.encode('utf-8'))
            self.cipher.decrypt(encrypted_bytes)
            return True
        except:
            return False
    
    def _encrypt_config_recursive(self, config: Dict[str, Any], path: str = "") -> Dict[str, Any]:
        """递归加密配置中的敏感字段"""
        encrypted_config = {}
        
        for key, value in config.items():
            current_path = f"{path}.{key}" if path else key
            
            if isinstance(value, dict):
                # 递归处理字典
                encrypted_config[key] = self._encrypt_config_recursive(value, current_path)
            elif key.lower() in self.sensitive_fields and isinstance(value, str):
                # 加密敏感字段
                if not self._is_encrypted(value):
                    encrypted_config[key] = self._encrypt_value(value)
                    logger.debug(f"加密字段: {current_path}")
                else:
                    encrypted_config[key] = value
                    logger.debug(f"字段已加密: {current_path}")
            else:
                # 普通字段直接复制
                encrypted_config[key] = value
        
        return encrypted_config
    
    def _decrypt_config_recursive(self, config: Dict[str, Any], path: str = "") -> Dict[str, Any]:
        """递归解密配置中的敏感字段"""
        decrypted_config = {}
        
        for key, value in config.items():
            current_path = f"{path}.{key}" if path else key
            
            if isinstance(value, dict):
                # 递归处理字典
                decrypted_config[key] = self._decrypt_config_recursive(value, current_path)
            elif key.lower() in self.sensitive_fields and isinstance(value, str):
                # 解密敏感字段
                if self._is_encrypted(value):
                    decrypted_config[key] = self._decrypt_value(value)
                    logger.debug(f"解密字段: {current_path}")
                else:
                    decrypted_config[key] = value
                    logger.debug(f"字段未加密: {current_path}")
            else:
                # 普通字段直接复制
                decrypted_config[key] = value
        
        return decrypted_config
    
    def _calculate_checksum(self, config: Dict[str, Any]) -> str:
        """计算配置文件校验和"""
        config_str = json.dumps(config, sort_keys=True, ensure_ascii=False)
        return hashlib.sha256(config_str.encode('utf-8')).hexdigest()
    
    def create_backup(self) -> bool:
        """创建配置文件备份"""
        try:
            if os.path.exists(self.config_path):
                import shutil
                shutil.copy2(self.config_path, self.backup_path)
                logger.info(f"配置文件备份创建成功: {self.backup_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"创建配置文件备份失败: {e}")
            return False
    
    def restore_backup(self) -> bool:
        """从备份恢复配置文件"""
        try:
            if os.path.exists(self.backup_path):
                import shutil
                shutil.copy2(self.backup_path, self.config_path)
                logger.info(f"配置文件从备份恢复成功: {self.backup_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"从备份恢复配置文件失败: {e}")
            return False
    
    def encrypt_config_file(self) -> bool:
        """加密配置文件中的敏感信息"""
        try:
            # 创建备份
            self.create_backup()
            
            # 读取原始配置
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 加密敏感字段
            encrypted_config = self._encrypt_config_recursive(config)
            
            # 添加校验和
            encrypted_config['_checksum'] = self._calculate_checksum(encrypted_config)
            encrypted_config['_encrypted'] = True
            
            # 保存加密后的配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(encrypted_config, f, indent=2, ensure_ascii=False)
            
            logger.info("配置文件加密完成")
            return True
            
        except Exception as e:
            logger.error(f"加密配置文件失败: {e}")
            # 尝试恢复备份
            self.restore_backup()
            return False
    
    def load_config(self) -> Optional[Dict[str, Any]]:
        """加载并解密配置文件（使用缓存）"""
        import time

        # 检查缓存是否有效
        current_time = time.time()
        if (ConfigSecurity._cached_config is not None and
            (current_time - ConfigSecurity._cache_time) < ConfigSecurity._cache_duration):
            # 使用缓存的配置
            return ConfigSecurity._cached_config

        try:
            if not os.path.exists(self.config_path):
                logger.error(f"配置文件不存在: {self.config_path}")
                return None

            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 检查是否为加密配置
            if config.get('_encrypted', False):
                # 验证校验和
                stored_checksum = config.pop('_checksum', None)
                config.pop('_encrypted', None)

                if stored_checksum:
                    current_checksum = self._calculate_checksum(config)
                    if current_checksum != stored_checksum:
                        logger.warning("配置文件校验和不匹配，可能已被篡改")

                # 解密敏感字段
                decrypted_config = self._decrypt_config_recursive(config)

                # 缓存配置
                ConfigSecurity._cached_config = decrypted_config
                ConfigSecurity._cache_time = current_time

                # 只在第一次加载时记录日志
                if not ConfigSecurity._config_loaded_once:
                    ConfigSecurity._config_loaded_once = True
                    logger.info("加载现有加密密钥")
                    logger.info("配置文件解密完成")

                return decrypted_config
            else:
                # 未加密的配置文件
                # 缓存配置
                ConfigSecurity._cached_config = config
                ConfigSecurity._cache_time = current_time

                # 只在第一次加载时记录日志
                if not ConfigSecurity._config_loaded_once:
                    ConfigSecurity._config_loaded_once = True
                    logger.info("加载现有加密密钥")
                    logger.info("加载未加密的配置文件")

                return config

        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return None

    @classmethod
    def clear_cache(cls):
        """清除配置缓存，强制下次重新加载"""
        cls._cached_config = None
        cls._cache_time = 0
        logger.debug("配置缓存已清除")

    def save_config(self, config: Dict[str, Any]) -> bool:
        """保存并加密配置文件"""
        try:
            # 创建备份
            self.create_backup()
            
            # 加密敏感字段
            encrypted_config = self._encrypt_config_recursive(config)
            
            # 添加校验和和加密标记
            encrypted_config['_checksum'] = self._calculate_checksum(encrypted_config)
            encrypted_config['_encrypted'] = True
            
            # 保存配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(encrypted_config, f, indent=2, ensure_ascii=False)

            # 清除缓存，确保下次加载最新配置
            ConfigSecurity.clear_cache()

            logger.info("配置文件保存并加密完成")
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            # 尝试恢复备份
            self.restore_backup()
            return False
    
    def verify_integrity(self) -> bool:
        """验证配置文件完整性"""
        try:
            config = self.load_config()
            if config is None:
                return False
            
            # 重新加载原始文件检查校验和
            with open(self.config_path, 'r', encoding='utf-8') as f:
                raw_config = json.load(f)
            
            if raw_config.get('_encrypted', False):
                stored_checksum = raw_config.get('_checksum')
                if stored_checksum:
                    # 移除校验和和加密标记后计算
                    temp_config = raw_config.copy()
                    temp_config.pop('_checksum', None)
                    temp_config.pop('_encrypted', None)
                    current_checksum = self._calculate_checksum(temp_config)
                    return current_checksum == stored_checksum
            
            return True
            
        except Exception as e:
            logger.error(f"验证配置文件完整性失败: {e}")
            return False


def get_config_security(config_path: str = None) -> ConfigSecurity:
    """获取配置安全管理器实例"""
    return ConfigSecurity(config_path)
