2025-07-20 13:33:30,825 - __main__ - INFO - G<PERSON>日志将保存到: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 13:33:31,849 - __main__ - INFO - [system] BetBurger对冲投注系统GUI已启动
2025-07-20 13:33:31,849 - __main__ - INFO - [system] 请先点击'初始化系统'，然后'登录系统'
2025-07-20 13:33:31,961 - __main__ - INFO - [system] 配置加载成功
2025-07-20 13:33:31,961 - __main__ - INFO - [GUI] GUI程序启动，日志文件: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 13:33:33,965 - __main__ - INFO - [GUI] 正在加载最近日志...
2025-07-20 13:33:33,977 - __main__ - INFO - [GUI] 最近日志加载完成，共显示 6 条日志
2025-07-20 13:34:35,064 - __main__ - INFO - G<PERSON>日志将保存到: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 13:34:35,755 - __main__ - INFO - [system] BetBurger对冲投注系统GUI已启动
2025-07-20 13:34:35,756 - __main__ - INFO - [system] 请先点击'初始化系统'，然后'登录系统'
2025-07-20 13:34:35,880 - __main__ - INFO - [system] 配置加载成功
2025-07-20 13:34:35,881 - __main__ - INFO - [GUI] GUI程序启动，日志文件: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 13:34:37,880 - __main__ - INFO - [GUI] 正在加载最近日志...
2025-07-20 13:34:37,885 - __main__ - INFO - [GUI] 最近日志加载完成，共显示 13 条日志
2025-07-20 13:34:40,695 - __main__ - INFO - [system] 正在初始化系统...
2025-07-20 13:34:40,695 - hedge_main - INFO - 初始化对冲投注系统
2025-07-20 13:34:40,708 - utils.config_security - INFO - 加载现有加密密钥
2025-07-20 13:34:40,708 - utils.config_security - INFO - 加载未加密的配置文件
2025-07-20 13:34:40,708 - config.hedge_config - INFO - 对冲配置首次加载完成
2025-07-20 13:34:40,708 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 13:34:40,708 - config.hedge_config - WARNING - 参数 enable_rollback 不是布尔值, 使用默认值: False
2025-07-20 13:34:40,710 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 13:34:40,710 - utils.match_data_fetcher - INFO - 比赛数据获取器初始化完成，缓存目录: data/match_cache
2025-07-20 13:34:40,710 - utils.match_data_fetcher - INFO - 比赛数据获取器初始化完成，缓存目录: data/match_cache
2025-07-20 13:34:40,711 - utils.match_learning_enhanced - INFO - 增强匹配学习系统初始化完成，数据目录: data/match_learning
2025-07-20 13:34:40,711 - utils.alias_manager - INFO - 别名管理器初始化完成，配置目录: config
2025-07-20 13:34:40,712 - utils.match_learning_controller - INFO - 比赛匹配学习控制器初始化完成
2025-07-20 13:34:40,712 - hedge_main - INFO - 正在初始化投注系统...
2025-07-20 13:34:40,713 - hedge_main - INFO - 平博系统初始化完成
2025-07-20 13:34:40,713 - platforms.hgbet_bk - INFO - 初始化皇冠篮球投注模块
2025-07-20 13:34:40,714 - platforms.hgbet_bk - INFO - 投注记录将保存到: data\bet_records_2025-07-20.json
2025-07-20 13:34:40,714 - hedge_main - INFO - 皇冠系统初始化完成
2025-07-20 13:34:40,714 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 13:34:40,714 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 13:34:40,714 - validation.match_validator - INFO - 比赛匹配验证器初始化完成
2025-07-20 13:34:40,715 - validation.match_safety - INFO - 比赛匹配安全检查器初始化完成
2025-07-20 13:34:40,715 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 13:34:40,715 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 13:34:40,715 - hedge.hedge_calculator - INFO - 投注金额计算器初始化完成
2025-07-20 13:34:40,715 - hedge.hedge_monitor - INFO - 投注监控器初始化完成
2025-07-20 13:34:40,716 - hedge.hedge_record_manager - INFO - 对冲记录管理器初始化完成
2025-07-20 13:34:40,716 - hedge.hedge_manager - INFO - 对冲记录管理器初始化成功
2025-07-20 13:34:40,716 - hedge.partial_record_manager - INFO - 单边记录管理器初始化完成
2025-07-20 13:34:40,716 - hedge.hedge_manager - INFO - 单边记录管理器初始化成功
2025-07-20 13:34:40,716 - hedge.hedge_manager - INFO - 对冲管理器初始化完成
2025-07-20 13:34:40,716 - hedge.hedge_integration - INFO - 对冲集成器初始化完成（实时匹配模式）
2025-07-20 13:34:40,716 - hedge_main - INFO - 对冲集成器初始化完成
2025-07-20 13:34:40,717 - __main__ - INFO - [system] 系统初始化成功
2025-07-20 13:34:40,717 - __main__ - INFO - [GUI] 对冲系统日志转发设置完成
2025-07-20 13:34:44,075 - __main__ - INFO - [system] 正在登录系统...
2025-07-20 13:34:44,075 - hedge_main - INFO - 正在登录投注系统...
2025-07-20 13:34:44,075 - platforms.smart_login_manager - INFO - 会话已过期
2025-07-20 13:34:44,075 - PinBet - INFO - 尝试自动登录平博账号: mfc123
2025-07-20 13:34:45,233 - PinBet - INFO - 登录成功(带tokens): mfc123
2025-07-20 13:34:45,608 - PinBet - INFO - 余额获取成功: 26389.05
2025-07-20 13:34:45,611 - platforms.smart_login_manager - INFO - 记录登录尝试: 成功=True, 错误码=None, 消息=登录成功(带tokens)
2025-07-20 13:34:45,612 - platforms.smart_login_manager - INFO - 保存登录会话信息
2025-07-20 13:34:45,993 - PinBet - INFO - 余额获取成功: 26389.05
2025-07-20 13:34:45,993 - platforms.hgbet_bk - INFO - 尝试登录...
2025-07-20 13:34:48,424 - platforms.hgbet_bk - INFO - 登录状态码: 200
2025-07-20 13:34:48,424 - platforms.hgbet_bk - INFO - 提取到UID: g00q73ck4pm38245145l6562643b1
2025-07-20 13:34:48,631 - platforms.hgbet_bk - INFO - 登录成功，当前余额：11916.3，版本号: 2025-04-30-CRM-55_89
2025-07-20 13:34:48,631 - hedge_main - INFO - 所有系统登录成功
2025-07-20 13:34:48,631 - hedge_main - INFO - 正在初始化比赛匹配学习系统...
2025-07-20 13:34:48,632 - utils.match_learning_controller - INFO - 开始初始化比赛匹配学习系统
2025-07-20 13:34:48,632 - utils.match_learning_controller - INFO - 需要获取新的平台数据
2025-07-20 13:34:48,632 - utils.match_data_fetcher - INFO - 开始获取所有平台比赛数据
2025-07-20 13:34:48,632 - utils.match_data_fetcher - INFO - 获取平博平台比赛数据
2025-07-20 13:34:49,076 - utils.match_data_fetcher - ERROR - 解析平博比赛数据失败: 'NoneType' object is not iterable
2025-07-20 13:34:49,076 - utils.match_data_fetcher - INFO - 平博今日比赛 (mk=0): 0 场
2025-07-20 13:34:49,633 - utils.match_data_fetcher - INFO - 平博今日比赛 (mk=1): 9 场
2025-07-20 13:34:49,633 - utils.match_data_fetcher - INFO - 平博今日比赛总计: 9 场
2025-07-20 13:34:49,993 - utils.match_data_fetcher - ERROR - 解析平博比赛数据失败: 'NoneType' object is not iterable
2025-07-20 13:34:49,993 - utils.match_data_fetcher - INFO - 平博早场比赛 (mk=0): 0 场
2025-07-20 13:34:50,404 - utils.match_data_fetcher - INFO - 平博早场比赛 (mk=1): 9 场
2025-07-20 13:34:50,405 - utils.match_data_fetcher - INFO - 平博早场比赛总计: 9 场
2025-07-20 13:34:50,405 - utils.match_data_fetcher - INFO - 平博数据获取成功
2025-07-20 13:34:50,405 - utils.match_data_fetcher - INFO - 获取皇冠平台比赛数据
2025-07-20 13:34:50,405 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 13:34:50,615 - platforms.hgbet_bk - INFO - 获取到 34 场比赛
2025-07-20 13:34:50,615 - utils.match_data_fetcher - INFO - 皇冠今日比赛: 34 场
2025-07-20 13:34:50,615 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 13:34:50,815 - platforms.hgbet_bk - INFO - 获取到 0 场比赛
2025-07-20 13:34:50,816 - utils.match_data_fetcher - INFO - 皇冠数据获取成功
2025-07-20 13:34:50,817 - utils.match_data_fetcher - INFO - 缓存数据已保存到: data/match_cache\platform_matches.json
2025-07-20 13:34:50,818 - utils.match_data_fetcher - INFO - 队伍名和联赛名已保存
2025-07-20 13:34:50,818 - utils.match_data_fetcher - INFO - 成功获取 2 个平台的数据
2025-07-20 13:34:50,818 - utils.match_learning_controller - INFO - 平台数据获取成功
2025-07-20 13:34:50,818 - utils.match_learning_controller - INFO - 比赛匹配学习系统初始化完成
2025-07-20 13:34:50,820 - utils.match_learning_controller - INFO - === 比赛匹配学习系统初始化摘要 ===
2025-07-20 13:34:50,820 - utils.match_learning_controller - INFO - 系统状态: 已初始化
2025-07-20 13:34:50,820 - utils.match_learning_controller - INFO - pinbet平台: 总计18场比赛 (今日9场, 早场9场)
2025-07-20 13:34:50,820 - utils.match_learning_controller - INFO - crown平台: 总计34场比赛 (今日34场, 早场0场)
2025-07-20 13:34:50,820 - utils.match_learning_controller - INFO - 当前别名配置: 3个联赛, 11支队伍, 35个别名
2025-07-20 13:34:50,820 - utils.match_learning_controller - INFO - 学习统计: 总失败22次, 成功学习0次, 学习率0.0%
2025-07-20 13:34:50,820 - utils.match_learning_controller - INFO - === 初始化摘要结束 ===
2025-07-20 13:34:50,821 - hedge_main - INFO - 比赛匹配学习系统初始化完成
2025-07-20 13:34:50,821 - hedge_main - INFO - pinbet平台数据: 18场比赛
2025-07-20 13:34:50,821 - hedge_main - INFO - crown平台数据: 34场比赛
2025-07-20 13:34:50,822 - __main__ - INFO - [system] 系统登录成功
2025-07-20 13:34:51,226 - PinBet - INFO - 余额获取成功: 26389.05
2025-07-20 13:35:00,767 - __main__ - INFO - [system] 正在查询对冲记录...
2025-07-20 13:35:00,767 - __main__ - INFO - [system] 未找到任何对冲记录
2025-07-20 13:35:00,768 - __main__ - INFO - [system] 当天(2025-07-20)暂无对冲记录
2025-07-20 13:35:00,768 - __main__ - INFO - [system] 对冲记录查询完成
2025-07-20 13:35:02,519 - PinBet - INFO - 余额获取成功: 26389.05
2025-07-20 13:35:02,706 - __main__ - INFO - [system] 开启对冲监控
2025-07-20 13:35:02,706 - hedge.hedge_integration - INFO - 开始监控对冲机会，检查间隔: 4秒
2025-07-20 13:35:02,706 - hedge.hedge_integration - INFO - 开始监控对冲机会，检查间隔: 4秒
2025-07-20 13:35:05,433 - __main__ - INFO - [system] 正在停止对冲监控...
2025-07-20 13:35:05,434 - hedge.hedge_integration - INFO - 收到停止监控信号
2025-07-20 13:35:05,434 - hedge.hedge_integration - INFO - 收到停止监控信号
2025-07-20 13:35:06,335 - hedge.hedge_integration - INFO - 对冲监控已停止
2025-07-20 13:35:06,335 - hedge.hedge_integration - INFO - 对冲监控已停止
2025-07-20 13:35:06,337 - __main__ - INFO - [system] 对冲监控已成功停止
2025-07-20 13:36:04,350 - PinBet - INFO - 余额获取成功: 26389.05
2025-07-20 13:36:04,580 - __main__ - INFO - [system] 开启对冲监控
2025-07-20 13:36:04,580 - hedge.hedge_integration - INFO - 开始监控对冲机会，检查间隔: 4秒
2025-07-20 13:36:04,580 - hedge.hedge_integration - INFO - 开始监控对冲机会，检查间隔: 4秒
2025-07-20 13:36:49,175 - __main__ - INFO - [system] 已清除对冲集成器配置缓存
2025-07-20 13:36:49,175 - __main__ - WARNING - [system] 重新加载对冲配置时出错: 'dict' object has no attribute 'force_reload_config'
2025-07-20 13:36:49,176 - __main__ - INFO - [system] 配置保存成功，对冲系统配置已更新
2025-07-20 13:37:11,758 - __main__ - INFO - [system] 已清除对冲集成器配置缓存
2025-07-20 13:37:11,758 - __main__ - WARNING - [system] 重新加载对冲配置时出错: 'dict' object has no attribute 'force_reload_config'
2025-07-20 13:37:11,759 - __main__ - INFO - [system] 配置保存成功，对冲系统配置已更新
2025-07-20 13:47:30,953 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 13:47:31,296 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 13:47:31,297 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 13:47:31,297 - PinBet - INFO - 开始查询未结算注单
2025-07-20 13:47:41,785 - __main__ - INFO - [system] 切换到皇冠记录显示模式
2025-07-20 13:47:42,111 - __main__ - INFO - [system] 正在查询皇冠投注记录...
2025-07-20 13:47:42,112 - platforms.hgbet_bk - INFO - 直接获取今日BK注单
2025-07-20 13:47:42,339 - platforms.hgbet_bk - INFO - 今日总投注金额: 640
2025-07-20 13:47:42,339 - platforms.hgbet_bk - INFO - 找到 5 条注单
2025-07-20 13:47:42,362 - __main__ - INFO - [system] 加载皇冠记录 5 条
2025-07-20 13:47:42,363 - __main__ - INFO - [system] 皇冠投注记录查询完成
2025-07-20 13:47:51,373 - PinBet - ERROR - 获取未结算注单异常: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
Traceback (most recent call last):
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
               ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
                       ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 1378, in getresponse
    response.begin()
  File "D:\Program Files\Python311\Lib\http\client.py", line 318, in begin
    version, status, reason = self._read_status()
                              ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 287, in _read_status
    raise RemoteDisconnected("Remote end closed connection without"
http.client.RemoteDisconnected: Remote end closed connection without response

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\Python311\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\util\util.py", line 38, in reraise
    raise value.with_traceback(tb)
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
               ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
                       ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 1378, in getresponse
    response.begin()
  File "D:\Program Files\Python311\Lib\http\client.py", line 318, in begin
    version, status, reason = self._read_status()
                              ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 287, in _read_status
    raise RemoteDisconnected("Remote end closed connection without"
urllib3.exceptions.ProtocolError: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "d:\betburgerbot\platforms\pinbet_bk.py", line 1426, in get_pending_wagers
    response = self.session.post(
               ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\sessions.py", line 637, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\adapters.py", line 682, in send
    raise ConnectionError(err, request=request)
requests.exceptions.ConnectionError: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 13:47:51,383 - __main__ - WARNING - [system] 获取平博记录失败
2025-07-20 13:47:51,383 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 13:48:14,842 - __main__ - INFO - [system] 正在测试皇冠连接...
2025-07-20 13:48:15,049 - __main__ - INFO - [system] 皇冠连接测试成功，余额: 11917.3
2025-07-20 13:48:20,460 - __main__ - INFO - [system] 正在获取账户余额...
2025-07-20 13:48:21,195 - PinBet - INFO - 余额获取成功: 26389.05
2025-07-20 13:48:26,643 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 13:48:26,953 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 13:48:26,954 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 13:48:26,954 - PinBet - INFO - 开始查询未结算注单
2025-07-20 13:48:27,335 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 13:48:27,336 - PinBet - INFO - 处理完成: 原始记录 1 条，处理后 1 条记录
2025-07-20 13:48:27,336 - __main__ - INFO - [system] 加载平博记录 1 条
2025-07-20 13:48:27,336 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 13:54:09,903 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 13:54:09,906 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 13:54:09,906 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 13:54:09,906 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 13:54:09,907 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 韩国 - 卡塔尔 vs 韩国 - 卡塔尔
2025-07-20 13:54:09,907 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 韩国 - 卡塔尔 vs 韩国 - 卡塔尔
2025-07-20 13:54:09,907 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 13:54:09,907 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 13:54:09,907 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 13:54:09,907 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 13:54:09,907 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 13:54:09,907 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 13:54:09,907 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 13:54:09,907 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 13:54:09,907 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 13:54:09,907 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 13:54:09,908 - hedge.execution_lock - INFO - 投注执行锁管理器初始化完成
2025-07-20 13:54:09,908 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:韩国 - 卡塔尔, 锁ID: hedge:韩国 - 卡塔尔:1752990849, 超时: 30秒
2025-07-20 13:54:09,908 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注600.0 -> 实际投注750.00 (比例1.0, 折扣0.8)
2025-07-20 13:54:09,909 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=750.0, 对冲=561.76, 净利润=-165.76
2025-07-20 13:54:09,909 - hedge_system - INFO - 开始对冲投注: 韩国 - 卡塔尔
2025-07-20 13:54:09,909 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=750.0, 对冲金额=561.76, 总投资=1311.76
2025-07-20 13:54:09,909 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=750.0, 对冲金额=561.76, 总投资=1311.76
2025-07-20 13:54:09,909 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 13:54:09,909 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 13:54:09,909 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=750.0
2025-07-20 13:54:09,909 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=750.0
2025-07-20 13:54:09,909 - hedge.hedge_metrics - INFO - 对冲投注监控指标管理器初始化完成
2025-07-20 13:54:09,910 - platforms.hgbet_bk - INFO - 解析到投注信息: 韩国 vs 卡塔尔, 市场类型: 17, 盘口: -10.5, 赔率: 1.91
2025-07-20 13:54:09,910 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.91
2025-07-20 13:54:09,910 - platforms.hgbet_bk - INFO - 处理市场类型 17
2025-07-20 13:54:09,910 - platforms.hgbet_bk - INFO - 解析为主队让球: 韩国, 盘口: -10.5
2025-07-20 13:54:09,910 - platforms.hgbet_bk - INFO - 检查重复投注: 韩国 vs 卡塔尔, 让球, 投注方向=韩国, 时段=全场, 比赛ID=410141765, 盘口值=-10.5
2025-07-20 13:54:09,910 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=750.0, type=<class 'float'>
2025-07-20 13:54:09,910 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=750.0, type=<class 'float'>
2025-07-20 13:54:09,911 - platforms.hgbet_bk - INFO - 投注信息: 韩国 vs 卡塔尔, 让球=韩国, 盘口: -10.5, 赔率: 1.91
2025-07-20 13:54:09,911 - platforms.hgbet_bk - INFO - 开始执行投注流程
2025-07-20 13:54:09,911 - platforms.hgbet_bk - INFO - 投注信息: 韩国 vs 卡塔尔, 让球=韩国, 盘口: -10.5, 赔率: 1.91
2025-07-20 13:54:09,911 - platforms.hgbet_bk - INFO - 比赛投注次数检查: 卡塔尔_vs_韩国, 当前次数: 0, 限制: 2
2025-07-20 13:54:09,911 - platforms.hgbet_bk - INFO - 比赛投注次数检查通过: 卡塔尔_vs_韩国, 可继续投注
2025-07-20 13:54:10,160 - platforms.hgbet_bk - INFO - 等待3秒后执行投注...
2025-07-20 13:54:13,161 - platforms.hgbet_bk - INFO - 投注比赛: 韩国 vs 卡塔尔
2025-07-20 13:54:13,161 - platforms.hgbet_bk - INFO - 投注类型: 让球, 投注队伍: 韩国, 盘口: -10.5, 比赛阶段: 全场
2025-07-20 13:54:13,161 - platforms.hgbet_bk - INFO - 使用传入的投注金额: 750.0
2025-07-20 13:54:13,161 - platforms.hgbet_bk - INFO - 最终确定的投注金额: 750.0 (类型: <class 'float'>)
2025-07-20 13:54:13,162 - platforms.hgbet_bk - INFO - 确认投注金额: 750.0, 赔率: 1.91
2025-07-20 13:54:13,162 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 13:54:13,396 - platforms.hgbet_bk - INFO - 获取到 32 场比赛
2025-07-20 13:54:13,396 - platforms.hgbet_bk - WARNING - 比赛ID 410141765 在today比赛列表中不存在，尝试在early中查找
2025-07-20 13:54:13,397 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 13:54:13,582 - platforms.hgbet_bk - INFO - 获取到 0 场比赛
2025-07-20 13:54:13,583 - platforms.hgbet_bk - WARNING - 比赛ID 410141765 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-20 13:54:13,594 - platforms.hgbet_bk - WARNING - 未找到匹配的比赛ID（最高分数: 0.00，阈值: 0.48），无法继续投注
2025-07-20 13:54:13,595 - utils.match_learning - INFO - 记录匹配失败: 2025-07-20T13:54:13.594781_BetBurger_皇冠, 类别: team_mismatch
2025-07-20 13:54:13,595 - hedge.hedge_manager - INFO - crown投注失败，1.0秒后进行第2次尝试: 投注失败
2025-07-20 13:54:13,595 - hedge.hedge_manager - INFO - crown投注失败，1.0秒后进行第2次尝试: 投注失败
2025-07-20 13:54:14,611 - platforms.hgbet_bk - INFO - 解析到投注信息: 韩国 vs 卡塔尔, 市场类型: 17, 盘口: -10.5, 赔率: 1.91
2025-07-20 13:54:14,612 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.91
2025-07-20 13:54:14,612 - platforms.hgbet_bk - INFO - 处理市场类型 17
2025-07-20 13:54:14,612 - platforms.hgbet_bk - INFO - 解析为主队让球: 韩国, 盘口: -10.5
2025-07-20 13:54:14,612 - platforms.hgbet_bk - INFO - 检查重复投注: 韩国 vs 卡塔尔, 让球, 投注方向=韩国, 时段=全场, 比赛ID=410141765, 盘口值=-10.5
2025-07-20 13:54:14,612 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=750.0, type=<class 'float'>
2025-07-20 13:54:14,612 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=750.0, type=<class 'float'>
2025-07-20 13:54:14,613 - platforms.hgbet_bk - INFO - 开始执行投注流程
2025-07-20 13:54:14,613 - platforms.hgbet_bk - INFO - 投注信息: 韩国 vs 卡塔尔, 让球=韩国, 盘口: -10.5, 赔率: 1.91
2025-07-20 13:54:14,613 - platforms.hgbet_bk - INFO - 比赛投注次数检查: 卡塔尔_vs_韩国, 当前次数: 0, 限制: 2
2025-07-20 13:54:14,613 - platforms.hgbet_bk - INFO - 比赛投注次数检查通过: 卡塔尔_vs_韩国, 可继续投注
2025-07-20 13:54:14,821 - platforms.hgbet_bk - INFO - 等待3秒后执行投注...
2025-07-20 13:54:17,822 - platforms.hgbet_bk - INFO - 投注比赛: 韩国 vs 卡塔尔
2025-07-20 13:54:17,822 - platforms.hgbet_bk - INFO - 投注类型: 让球, 投注队伍: 韩国, 盘口: -10.5, 比赛阶段: 全场
2025-07-20 13:54:17,822 - platforms.hgbet_bk - INFO - 使用传入的投注金额: 750.0
2025-07-20 13:54:17,822 - platforms.hgbet_bk - INFO - 最终确定的投注金额: 750.0 (类型: <class 'float'>)
2025-07-20 13:54:17,822 - platforms.hgbet_bk - INFO - 确认投注金额: 750.0, 赔率: 1.91
2025-07-20 13:54:17,823 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 13:54:18,053 - platforms.hgbet_bk - INFO - 获取到 32 场比赛
2025-07-20 13:54:18,054 - platforms.hgbet_bk - WARNING - 比赛ID 410141765 在today比赛列表中不存在，尝试在early中查找
2025-07-20 13:54:18,054 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 13:54:18,270 - platforms.hgbet_bk - INFO - 获取到 0 场比赛
2025-07-20 13:54:18,271 - platforms.hgbet_bk - WARNING - 比赛ID 410141765 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-20 13:54:18,281 - platforms.hgbet_bk - WARNING - 未找到匹配的比赛ID（最高分数: 0.00，阈值: 0.48），无法继续投注
2025-07-20 13:54:18,283 - utils.match_learning - INFO - 记录匹配失败: 2025-07-20T13:54:18.281073_BetBurger_皇冠, 类别: team_mismatch
2025-07-20 13:54:18,283 - hedge.hedge_manager - WARNING - crown投注失败，不适合重试: 投注失败
2025-07-20 13:54:18,283 - hedge.hedge_manager - WARNING - crown投注失败，不适合重试: 投注失败
2025-07-20 13:54:18,283 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 投注失败
2025-07-20 13:54:18,283 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 投注失败
2025-07-20 13:54:18,283 - hedge_system - WARNING - 对冲投注失败: 韩国 - 卡塔尔
2025-07-20 13:54:18,283 - hedge.hedge_manager - INFO - 对冲投注完全失败，不保存记录
2025-07-20 13:54:18,283 - hedge.hedge_manager - INFO - 对冲投注完全失败，不保存记录
2025-07-20 13:54:18,283 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:韩国 - 卡塔尔, 锁ID: hedge:韩国 - 卡塔尔:1752990849
2025-07-20 13:54:18,283 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 13:54:18,283 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 13:54:18,283 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 13:54:18,283 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 13:54:23,439 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 13:54:23,440 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 13:54:23,440 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 13:54:23,440 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 13:54:23,440 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 韩国 - 卡塔尔 vs 韩国 - 卡塔尔
2025-07-20 13:54:23,440 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 韩国 - 卡塔尔 vs 韩国 - 卡塔尔
2025-07-20 13:54:23,440 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 13:54:23,440 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 13:54:23,440 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 13:54:23,440 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 13:54:23,440 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 13:54:23,440 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 13:54:23,441 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 13:54:23,441 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 13:54:23,441 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 13:54:23,441 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 13:54:23,441 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:韩国 - 卡塔尔, 锁ID: hedge:韩国 - 卡塔尔:1752990863, 超时: 30秒
2025-07-20 13:54:23,441 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注600.0 -> 实际投注750.00 (比例1.0, 折扣0.8)
2025-07-20 13:54:23,441 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=750.0, 对冲=561.76, 净利润=-165.76
2025-07-20 13:54:23,441 - hedge_system - INFO - 开始对冲投注: 韩国 - 卡塔尔
2025-07-20 13:54:23,441 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=750.0, 对冲金额=561.76, 总投资=1311.76
2025-07-20 13:54:23,441 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=750.0, 对冲金额=561.76, 总投资=1311.76
2025-07-20 13:54:23,441 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 13:54:23,441 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 13:54:23,441 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=750.0
2025-07-20 13:54:23,441 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=750.0
2025-07-20 13:54:23,441 - platforms.hgbet_bk - INFO - 解析到投注信息: 韩国 vs 卡塔尔, 市场类型: 17, 盘口: -10.5, 赔率: 1.91
2025-07-20 13:54:23,442 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.91
2025-07-20 13:54:23,442 - platforms.hgbet_bk - INFO - 处理市场类型 17
2025-07-20 13:54:23,442 - platforms.hgbet_bk - INFO - 解析为主队让球: 韩国, 盘口: -10.5
2025-07-20 13:54:23,442 - platforms.hgbet_bk - INFO - 检查重复投注: 韩国 vs 卡塔尔, 让球, 投注方向=韩国, 时段=全场, 比赛ID=410141765, 盘口值=-10.5
2025-07-20 13:54:23,442 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=750.0, type=<class 'float'>
2025-07-20 13:54:23,442 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=750.0, type=<class 'float'>
2025-07-20 13:54:23,442 - platforms.hgbet_bk - INFO - 开始执行投注流程
2025-07-20 13:54:23,443 - platforms.hgbet_bk - INFO - 投注信息: 韩国 vs 卡塔尔, 让球=韩国, 盘口: -10.5, 赔率: 1.91
2025-07-20 13:54:23,443 - platforms.hgbet_bk - INFO - 比赛投注次数检查: 卡塔尔_vs_韩国, 当前次数: 0, 限制: 2
2025-07-20 13:54:23,443 - platforms.hgbet_bk - INFO - 比赛投注次数检查通过: 卡塔尔_vs_韩国, 可继续投注
2025-07-20 13:54:23,641 - platforms.hgbet_bk - INFO - 等待3秒后执行投注...
2025-07-20 13:54:26,642 - platforms.hgbet_bk - INFO - 投注比赛: 韩国 vs 卡塔尔
2025-07-20 13:54:26,642 - platforms.hgbet_bk - INFO - 投注类型: 让球, 投注队伍: 韩国, 盘口: -10.5, 比赛阶段: 全场
2025-07-20 13:54:26,642 - platforms.hgbet_bk - INFO - 使用传入的投注金额: 750.0
2025-07-20 13:54:26,643 - platforms.hgbet_bk - INFO - 最终确定的投注金额: 750.0 (类型: <class 'float'>)
2025-07-20 13:54:26,643 - platforms.hgbet_bk - INFO - 确认投注金额: 750.0, 赔率: 1.91
2025-07-20 13:54:26,643 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 13:54:26,863 - platforms.hgbet_bk - INFO - 获取到 32 场比赛
2025-07-20 13:54:26,863 - platforms.hgbet_bk - WARNING - 比赛ID 410141765 在today比赛列表中不存在，尝试在early中查找
2025-07-20 13:54:26,864 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 13:54:27,086 - platforms.hgbet_bk - INFO - 获取到 0 场比赛
2025-07-20 13:54:27,087 - platforms.hgbet_bk - WARNING - 比赛ID 410141765 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-20 13:54:27,095 - platforms.hgbet_bk - WARNING - 未找到匹配的比赛ID（最高分数: 0.00，阈值: 0.48），无法继续投注
2025-07-20 13:54:27,098 - utils.match_learning - INFO - 记录匹配失败: 2025-07-20T13:54:27.095930_BetBurger_皇冠, 类别: team_mismatch
2025-07-20 13:54:27,098 - hedge.hedge_manager - INFO - crown投注失败，1.0秒后进行第2次尝试: 投注失败
2025-07-20 13:54:27,098 - hedge.hedge_manager - INFO - crown投注失败，1.0秒后进行第2次尝试: 投注失败
2025-07-20 13:54:28,104 - platforms.hgbet_bk - INFO - 解析到投注信息: 韩国 vs 卡塔尔, 市场类型: 17, 盘口: -10.5, 赔率: 1.91
2025-07-20 13:54:28,104 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.91
2025-07-20 13:54:28,104 - platforms.hgbet_bk - INFO - 处理市场类型 17
2025-07-20 13:54:28,105 - platforms.hgbet_bk - INFO - 解析为主队让球: 韩国, 盘口: -10.5
2025-07-20 13:54:28,106 - platforms.hgbet_bk - INFO - 检查重复投注: 韩国 vs 卡塔尔, 让球, 投注方向=韩国, 时段=全场, 比赛ID=410141765, 盘口值=-10.5
2025-07-20 13:54:28,106 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=750.0, type=<class 'float'>
2025-07-20 13:54:28,106 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=750.0, type=<class 'float'>
2025-07-20 13:54:28,106 - platforms.hgbet_bk - INFO - 开始执行投注流程
2025-07-20 13:54:28,107 - platforms.hgbet_bk - INFO - 投注信息: 韩国 vs 卡塔尔, 让球=韩国, 盘口: -10.5, 赔率: 1.91
2025-07-20 13:54:28,107 - platforms.hgbet_bk - INFO - 比赛投注次数检查: 卡塔尔_vs_韩国, 当前次数: 0, 限制: 2
2025-07-20 13:54:28,107 - platforms.hgbet_bk - INFO - 比赛投注次数检查通过: 卡塔尔_vs_韩国, 可继续投注
2025-07-20 13:54:28,315 - platforms.hgbet_bk - INFO - 等待3秒后执行投注...
2025-07-20 13:54:31,316 - platforms.hgbet_bk - INFO - 投注比赛: 韩国 vs 卡塔尔
2025-07-20 13:54:31,316 - platforms.hgbet_bk - INFO - 投注类型: 让球, 投注队伍: 韩国, 盘口: -10.5, 比赛阶段: 全场
2025-07-20 13:54:31,316 - platforms.hgbet_bk - INFO - 使用传入的投注金额: 750.0
2025-07-20 13:54:31,317 - platforms.hgbet_bk - INFO - 最终确定的投注金额: 750.0 (类型: <class 'float'>)
2025-07-20 13:54:31,317 - platforms.hgbet_bk - INFO - 确认投注金额: 750.0, 赔率: 1.91
2025-07-20 13:54:31,317 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 13:54:31,532 - platforms.hgbet_bk - INFO - 获取到 32 场比赛
2025-07-20 13:54:31,532 - platforms.hgbet_bk - WARNING - 比赛ID 410141765 在today比赛列表中不存在，尝试在early中查找
2025-07-20 13:54:31,532 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 13:54:31,744 - platforms.hgbet_bk - INFO - 获取到 0 场比赛
2025-07-20 13:54:31,744 - platforms.hgbet_bk - WARNING - 比赛ID 410141765 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-20 13:54:31,752 - platforms.hgbet_bk - WARNING - 未找到匹配的比赛ID（最高分数: 0.00，阈值: 0.48），无法继续投注
2025-07-20 13:54:31,754 - utils.match_learning - INFO - 记录匹配失败: 2025-07-20T13:54:31.752429_BetBurger_皇冠, 类别: team_mismatch
2025-07-20 13:54:31,754 - hedge.hedge_manager - WARNING - crown投注失败，不适合重试: 投注失败
2025-07-20 13:54:31,754 - hedge.hedge_manager - WARNING - crown投注失败，不适合重试: 投注失败
2025-07-20 13:54:31,754 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 投注失败
2025-07-20 13:54:31,754 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 投注失败
2025-07-20 13:54:31,754 - hedge_system - WARNING - 对冲投注失败: 韩国 - 卡塔尔
2025-07-20 13:54:31,754 - hedge.hedge_manager - INFO - 对冲投注完全失败，不保存记录
2025-07-20 13:54:31,754 - hedge.hedge_manager - INFO - 对冲投注完全失败，不保存记录
2025-07-20 13:54:31,754 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:韩国 - 卡塔尔, 锁ID: hedge:韩国 - 卡塔尔:1752990863
2025-07-20 13:54:31,754 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 13:54:31,754 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 13:54:31,754 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 13:54:31,754 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 13:55:00,948 - __main__ - INFO - [system] 正在停止对冲监控...
2025-07-20 13:55:00,948 - hedge.hedge_integration - INFO - 收到停止监控信号
2025-07-20 13:55:00,948 - hedge.hedge_integration - INFO - 收到停止监控信号
2025-07-20 13:55:01,110 - hedge.hedge_integration - INFO - 对冲监控已停止
2025-07-20 13:55:01,110 - hedge.hedge_integration - INFO - 对冲监控已停止
2025-07-20 13:55:01,113 - __main__ - INFO - [system] 对冲监控已成功停止
2025-07-20 14:10:55,146 - __main__ - INFO - GUI日志将保存到: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 14:10:55,777 - __main__ - INFO - [system] BetBurger对冲投注系统GUI已启动
2025-07-20 14:10:55,777 - __main__ - INFO - [system] 请先点击'初始化系统'，然后'登录系统'
2025-07-20 14:10:55,885 - __main__ - INFO - [system] 配置加载成功
2025-07-20 14:10:55,885 - __main__ - INFO - [GUI] GUI程序启动，日志文件: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 14:10:57,903 - __main__ - INFO - [GUI] 正在加载最近日志...
2025-07-20 14:10:57,909 - __main__ - INFO - [GUI] 最近日志加载完成，共显示 100 条日志
2025-07-20 14:11:10,207 - __main__ - INFO - [system] 配置保存成功，对冲系统配置已更新
2025-07-20 14:11:22,537 - __main__ - INFO - [system] 配置保存成功，对冲系统配置已更新
2025-07-20 14:11:33,687 - __main__ - INFO - [system] 配置保存成功，对冲系统配置已更新
2025-07-20 14:30:03,486 - __main__ - INFO - [system] 正在初始化系统...
2025-07-20 14:30:03,487 - hedge_main - INFO - 初始化对冲投注系统
2025-07-20 14:30:03,498 - utils.config_security - INFO - 加载现有加密密钥
2025-07-20 14:30:03,498 - utils.config_security - INFO - 加载未加密的配置文件
2025-07-20 14:30:03,499 - config.hedge_config - INFO - 对冲配置首次加载完成
2025-07-20 14:30:03,499 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 14:30:03,499 - config.hedge_config - WARNING - 参数 enable_rollback 不是布尔值, 使用默认值: False
2025-07-20 14:30:03,499 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 14:30:03,499 - utils.match_data_fetcher - INFO - 比赛数据获取器初始化完成，缓存目录: data/match_cache
2025-07-20 14:30:03,499 - utils.match_data_fetcher - INFO - 比赛数据获取器初始化完成，缓存目录: data/match_cache
2025-07-20 14:30:03,500 - utils.match_learning_enhanced - INFO - 增强匹配学习系统初始化完成，数据目录: data/match_learning
2025-07-20 14:30:03,500 - utils.alias_manager - INFO - 别名管理器初始化完成，配置目录: config
2025-07-20 14:30:03,500 - utils.match_learning_controller - INFO - 比赛匹配学习控制器初始化完成
2025-07-20 14:30:03,500 - hedge_main - INFO - 正在初始化投注系统...
2025-07-20 14:30:03,501 - hedge_main - INFO - 平博系统初始化完成
2025-07-20 14:30:03,501 - platforms.hgbet_bk - INFO - 初始化皇冠篮球投注模块
2025-07-20 14:30:03,501 - platforms.hgbet_bk - INFO - 投注记录将保存到: data\bet_records_2025-07-20.json
2025-07-20 14:30:03,501 - hedge_main - INFO - 皇冠系统初始化完成
2025-07-20 14:30:03,502 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 14:30:03,502 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 14:30:03,502 - validation.match_validator - INFO - 比赛匹配验证器初始化完成
2025-07-20 14:30:03,502 - validation.match_safety - INFO - 比赛匹配安全检查器初始化完成
2025-07-20 14:30:03,502 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 14:30:03,502 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 14:30:03,502 - hedge.hedge_calculator - INFO - 投注金额计算器初始化完成
2025-07-20 14:30:03,502 - hedge.hedge_monitor - INFO - 投注监控器初始化完成
2025-07-20 14:30:03,503 - hedge.hedge_record_manager - INFO - 对冲记录管理器初始化完成
2025-07-20 14:30:03,503 - hedge.hedge_manager - INFO - 对冲记录管理器初始化成功
2025-07-20 14:30:03,503 - hedge.partial_record_manager - INFO - 单边记录管理器初始化完成
2025-07-20 14:30:03,503 - hedge.hedge_manager - INFO - 单边记录管理器初始化成功
2025-07-20 14:30:03,503 - hedge.hedge_manager - INFO - 对冲管理器初始化完成
2025-07-20 14:30:03,503 - hedge.hedge_integration - INFO - 对冲集成器初始化完成（实时匹配模式）
2025-07-20 14:30:03,503 - hedge_main - INFO - 对冲集成器初始化完成
2025-07-20 14:30:03,503 - __main__ - INFO - [system] 系统初始化成功
2025-07-20 14:30:03,503 - __main__ - INFO - [GUI] 对冲系统日志转发设置完成
2025-07-20 14:30:05,654 - __main__ - INFO - [system] 正在登录系统...
2025-07-20 14:30:05,654 - hedge_main - INFO - 正在登录投注系统...
2025-07-20 14:30:05,655 - platforms.smart_login_manager - INFO - 会话仍然有效，剩余时间: 11:04:39.956836
2025-07-20 14:30:05,655 - PinBet - INFO - 检测到现有会话，验证有效性
2025-07-20 14:30:07,195 - PinBet - INFO - 余额获取成功: 28369.44
2025-07-20 14:30:07,197 - PinBet - INFO - 现有会话验证成功
2025-07-20 14:30:07,690 - PinBet - INFO - 余额获取成功: 28369.44
2025-07-20 14:30:07,691 - platforms.hgbet_bk - INFO - 尝试登录...
2025-07-20 14:30:10,132 - platforms.hgbet_bk - INFO - 登录状态码: 200
2025-07-20 14:30:10,132 - platforms.hgbet_bk - INFO - 提取到UID: 1jshu44i9m38245145l6562979b1
2025-07-20 14:30:10,328 - platforms.hgbet_bk - INFO - 登录成功，当前余额：12155.9，版本号: 2025-04-30-CRM-55_89
2025-07-20 14:30:10,328 - hedge_main - INFO - 所有系统登录成功
2025-07-20 14:30:10,328 - hedge_main - INFO - 正在初始化比赛匹配学习系统...
2025-07-20 14:30:10,329 - utils.match_learning_controller - INFO - 开始初始化比赛匹配学习系统
2025-07-20 14:30:10,329 - utils.match_learning_controller - INFO - 需要获取新的平台数据
2025-07-20 14:30:10,329 - utils.match_data_fetcher - INFO - 开始获取所有平台比赛数据
2025-07-20 14:30:10,329 - utils.match_data_fetcher - INFO - 获取平博平台比赛数据
2025-07-20 14:30:10,822 - utils.match_data_fetcher - ERROR - 解析平博比赛数据失败: 'NoneType' object is not iterable
2025-07-20 14:30:10,822 - utils.match_data_fetcher - INFO - 平博今日比赛 (mk=0): 0 场
2025-07-20 14:30:11,479 - utils.match_data_fetcher - INFO - 平博今日比赛 (mk=1): 8 场
2025-07-20 14:30:11,479 - utils.match_data_fetcher - INFO - 平博今日比赛总计: 8 场
2025-07-20 14:30:11,976 - utils.match_data_fetcher - ERROR - 解析平博比赛数据失败: 'NoneType' object is not iterable
2025-07-20 14:30:11,976 - utils.match_data_fetcher - INFO - 平博早场比赛 (mk=0): 0 场
2025-07-20 14:30:12,642 - utils.match_data_fetcher - INFO - 平博早场比赛 (mk=1): 8 场
2025-07-20 14:30:12,642 - utils.match_data_fetcher - INFO - 平博早场比赛总计: 8 场
2025-07-20 14:30:12,643 - utils.match_data_fetcher - INFO - 平博数据获取成功
2025-07-20 14:30:12,643 - utils.match_data_fetcher - INFO - 获取皇冠平台比赛数据
2025-07-20 14:30:12,643 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 14:30:12,863 - platforms.hgbet_bk - INFO - 获取到 29 场比赛
2025-07-20 14:30:12,863 - utils.match_data_fetcher - INFO - 皇冠今日比赛: 29 场
2025-07-20 14:30:12,863 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 14:30:13,069 - platforms.hgbet_bk - INFO - 获取到 0 场比赛
2025-07-20 14:30:13,070 - utils.match_data_fetcher - INFO - 皇冠数据获取成功
2025-07-20 14:30:13,071 - utils.match_data_fetcher - INFO - 缓存数据已保存到: data/match_cache\platform_matches.json
2025-07-20 14:30:13,072 - utils.match_data_fetcher - INFO - 队伍名和联赛名已保存
2025-07-20 14:30:13,072 - utils.match_data_fetcher - INFO - 成功获取 2 个平台的数据
2025-07-20 14:30:13,072 - utils.match_learning_controller - INFO - 平台数据获取成功
2025-07-20 14:30:13,072 - utils.match_learning_controller - INFO - 比赛匹配学习系统初始化完成
2025-07-20 14:30:13,073 - utils.match_learning_controller - INFO - === 比赛匹配学习系统初始化摘要 ===
2025-07-20 14:30:13,073 - utils.match_learning_controller - INFO - 系统状态: 已初始化
2025-07-20 14:30:13,073 - utils.match_learning_controller - INFO - pinbet平台: 总计16场比赛 (今日8场, 早场8场)
2025-07-20 14:30:13,073 - utils.match_learning_controller - INFO - crown平台: 总计29场比赛 (今日29场, 早场0场)
2025-07-20 14:30:13,073 - utils.match_learning_controller - INFO - 当前别名配置: 3个联赛, 11支队伍, 35个别名
2025-07-20 14:30:13,073 - utils.match_learning_controller - INFO - 学习统计: 总失败26次, 成功学习0次, 学习率0.0%
2025-07-20 14:30:13,073 - utils.match_learning_controller - INFO - === 初始化摘要结束 ===
2025-07-20 14:30:13,073 - hedge_main - INFO - 比赛匹配学习系统初始化完成
2025-07-20 14:30:13,074 - hedge_main - INFO - pinbet平台数据: 16场比赛
2025-07-20 14:30:13,074 - hedge_main - INFO - crown平台数据: 29场比赛
2025-07-20 14:30:13,074 - __main__ - INFO - [system] 系统登录成功
2025-07-20 14:30:13,634 - PinBet - INFO - 余额获取成功: 28369.44
2025-07-20 14:30:15,066 - PinBet - INFO - 余额获取成功: 28369.44
2025-07-20 14:30:15,280 - __main__ - INFO - [system] 开启对冲监控
2025-07-20 14:30:15,280 - hedge.hedge_integration - INFO - 开始监控对冲机会，检查间隔: 4秒
2025-07-20 14:30:15,280 - hedge.hedge_integration - INFO - 开始监控对冲机会，检查间隔: 4秒
2025-07-20 14:30:16,539 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 14:30:16,540 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 14:30:16,541 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 14:30:16,541 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 14:30:16,541 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 法国 - 日本 vs 法国 - 日本
2025-07-20 14:30:16,541 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 法国 - 日本 vs 法国 - 日本
2025-07-20 14:30:16,541 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 14:30:16,541 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 14:30:16,541 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 14:30:16,541 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 14:30:16,541 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 14:30:16,541 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 14:30:16,541 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 14:30:16,541 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 14:30:16,541 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 14:30:16,541 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 14:30:16,542 - hedge.execution_lock - INFO - 投注执行锁管理器初始化完成
2025-07-20 14:30:16,542 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:法国 - 日本, 锁ID: hedge:法国 - 日本:1752993016, 超时: 30秒
2025-07-20 14:30:16,542 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 14:30:16,542 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=723.44, 净利润=-211.44
2025-07-20 14:30:16,542 - hedge_system - INFO - 开始对冲投注: 法国 - 日本
2025-07-20 14:30:16,542 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=723.44, 总投资=1723.44
2025-07-20 14:30:16,542 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=723.44, 总投资=1723.44
2025-07-20 14:30:16,543 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 14:30:16,543 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 14:30:16,543 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 14:30:16,543 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 14:30:16,543 - hedge.hedge_metrics - INFO - 对冲投注监控指标管理器初始化完成
2025-07-20 14:30:16,544 - platforms.hgbet_bk - INFO - 解析到投注信息: 法国 vs 日本, 市场类型: 17, 盘口: -19.0, 赔率: 1.89
2025-07-20 14:30:16,544 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.89
2025-07-20 14:30:16,544 - platforms.hgbet_bk - INFO - 处理市场类型 17
2025-07-20 14:30:16,544 - platforms.hgbet_bk - INFO - 解析为主队让球: 法国, 盘口: -19.0
2025-07-20 14:30:16,544 - platforms.hgbet_bk - INFO - 检查重复投注: 法国 vs 日本, 让球, 投注方向=法国, 时段=全场, 比赛ID=410143812, 盘口值=-19.0
2025-07-20 14:30:16,544 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 14:30:16,544 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 14:30:16,544 - platforms.hgbet_bk - INFO - 投注信息: 法国 vs 日本, 让球=法国, 盘口: -19.0, 赔率: 1.89
2025-07-20 14:30:16,544 - platforms.hgbet_bk - INFO - 开始执行投注流程
2025-07-20 14:30:16,544 - platforms.hgbet_bk - INFO - 投注信息: 法国 vs 日本, 让球=法国, 盘口: -19.0, 赔率: 1.89
2025-07-20 14:30:16,545 - platforms.hgbet_bk - INFO - 比赛投注次数检查: 日本_vs_法国, 当前次数: 0, 限制: 2
2025-07-20 14:30:16,545 - platforms.hgbet_bk - INFO - 比赛投注次数检查通过: 日本_vs_法国, 可继续投注
2025-07-20 14:30:16,749 - platforms.hgbet_bk - INFO - 等待3秒后执行投注...
2025-07-20 14:30:19,750 - platforms.hgbet_bk - INFO - 投注比赛: 法国 vs 日本
2025-07-20 14:30:19,750 - platforms.hgbet_bk - INFO - 投注类型: 让球, 投注队伍: 法国, 盘口: -19.0, 比赛阶段: 全场
2025-07-20 14:30:19,750 - platforms.hgbet_bk - INFO - 使用传入的投注金额: 1000.0
2025-07-20 14:30:19,750 - platforms.hgbet_bk - INFO - 最终确定的投注金额: 1000.0 (类型: <class 'float'>)
2025-07-20 14:30:19,750 - platforms.hgbet_bk - INFO - 确认投注金额: 1000.0, 赔率: 1.89
2025-07-20 14:30:19,750 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 14:30:19,964 - platforms.hgbet_bk - INFO - 获取到 29 场比赛
2025-07-20 14:30:19,965 - platforms.hgbet_bk - WARNING - 比赛ID 410143812 在today比赛列表中不存在，尝试在early中查找
2025-07-20 14:30:19,965 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 14:30:20,182 - platforms.hgbet_bk - INFO - 获取到 0 场比赛
2025-07-20 14:30:20,183 - platforms.hgbet_bk - WARNING - 比赛ID 410143812 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-20 14:30:20,188 - platforms.hgbet_bk - INFO - 队伍匹配良好: 法国 vs 日本 <-> 法国U19(女) vs 日本U19(女), 分数: 1.000 (匹配类型: forward)
2025-07-20 14:30:20,192 - platforms.hgbet_bk - INFO - 找到匹配的比赛ID: 9401527, 匹配分数: 1.00
2025-07-20 14:30:20,192 - platforms.hgbet_bk - INFO - 投注类型代码: R, 选择: H, 比赛阶段: 全场
2025-07-20 14:30:20,192 - platforms.hgbet_bk - INFO - 使用标准wtype格式: R, 选项: H, 时段: 全场
2025-07-20 14:30:20,417 - platforms.hgbet_bk - INFO - 盘口值验证 - 原始: -19.0, 表单: 19.0, 绝对值差异: 0.0, 阈值: 0
2025-07-20 14:30:20,417 - platforms.hgbet_bk - INFO - 盘口值验证通过，绝对值差异 0.0 在阈值 0 范围内，继续投注
2025-07-20 14:30:20,418 - platforms.hgbet_bk - INFO - 赔率值验证 - 原始: 1.89, 表单: 1.88, 差异: 0.010000000000000009, 阈值: 0.08
2025-07-20 14:30:20,418 - platforms.hgbet_bk - INFO - 赔率值验证通过，差异 0.010000000000000009 在阈值 0.08 范围内，继续投注
2025-07-20 14:30:20,418 - platforms.hgbet_bk - INFO - 投注金额详细信息: amount=1000.0, type=<class 'float'>, str(amount)=1000.0
2025-07-20 14:30:20,609 - platforms.hgbet_bk - INFO - 投注参数 - golds: '1000', amount: 1000.0 -> 1000, type: <class 'float'>
2025-07-20 14:30:20,874 - platforms.hgbet_bk - INFO - 【投注】成功，订单号: 22280242246，余额: 11155.90
2025-07-20 14:30:20,874 - platforms.hgbet_bk - INFO - 投注成功: 法国 vs 日本, 让球=法国, 盘口: -19.0, 赔率: 1.89
2025-07-20 14:30:20,875 - platforms.hgbet_bk - INFO - 成功保存投注记录: {'match_id': '9401527', 'home_team': '法国', 'away_team': '日本', 'bet_type': '让球', 'bet_team': '法国', 'handicap_value': '-19.0', 'odds': 1.89, 'amount': 1000.0, 'period': '全场', 'timestamp': 1752993020.8740969, 'datetime': '2025-07-20 14:30:20', 'order_no': '22280242246', 'success': True}
2025-07-20 14:30:20,875 - platforms.hgbet_bk - INFO - 更新比赛投注次数: 日本_vs_法国, 新次数: 1
2025-07-20 14:30:20,876 - platforms.hgbet_bk - ERROR - 播放提示音失败: No module named 'betburger_fetcher'
2025-07-20 14:30:20,876 - hedge.hedge_manager - INFO - 基准平台(crown)投注成功，等待1.0秒后开始对冲平台投注
2025-07-20 14:30:20,876 - hedge.hedge_manager - INFO - 基准平台(crown)投注成功，等待1.0秒后开始对冲平台投注
2025-07-20 14:30:21,887 - PinBet - INFO - 解析结果: 法国 vs 日本, 让球=日本, 盘口=19.5, 赔率=2.09
2025-07-20 14:30:21,888 - PinBet - INFO - 开始执行投注: 法国 vs 日本, 让球=日本
2025-07-20 14:30:21,888 - PinBet - INFO - 获取今日比赛数据
2025-07-20 14:30:21,888 - PinBet - INFO - 获取今日篮球比赛数据
2025-07-20 14:30:22,737 - PinBet - INFO - 今日篮球比赛数据获取成功
2025-07-20 14:30:22,738 - PinBet - INFO - 获取早场比赛数据
2025-07-20 14:30:22,738 - PinBet - INFO - 获取早场篮球比赛数据
2025-07-20 14:30:23,265 - PinBet - INFO - 早场篮球比赛数据获取成功
2025-07-20 14:30:23,265 - PinBet - INFO - 查找匹配比赛: 法国 vs 日本, 让球=日本, 盘口=19.5
2025-07-20 14:30:23,266 - PinBet - INFO - 找到匹配比赛: 法国 vs 日本 (联赛: 国际篮联 - U19女子篮球世界杯) (匹配分数: 1.00)
2025-07-20 14:30:23,266 - PinBet - INFO - 可用的让球盘口: ['23.5', '23.0', '22.5', '22.0', '21.5', '21.0', '20.5', '20.0', '19.5', '19.0', '18.5']
2025-07-20 14:30:23,266 - PinBet - INFO - 标准化后的目标盘口值: 19.5
2025-07-20 14:30:23,266 - PinBet - INFO - 找到精确匹配的让球盘口: 19.5, line_id=49602190329
2025-07-20 14:30:23,266 - PinBet - INFO - 获取投注表单: event_id=1611849832, line_id=49602190329, period_num=0, bet_type=2, team_side=away, handicap=19.5
2025-07-20 14:30:23,267 - PinBet - INFO - 让分盘设置 - team_side: away, team_side_code: 1, selection_direction: 1
2025-07-20 14:30:23,267 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 14:30:23,267 - PinBet - INFO - 构建投注ID: odds_id=1611849832|0|2|1|1|19.5, selection_id=49602190329|1611849832|0|2|1|1|19.5|1
2025-07-20 14:30:23,267 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1752993023267', 'withCredentials': 'true'}
2025-07-20 14:30:23,267 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611849832|0|2|1|1|19.5", "oddsSelectionsType": "NORMAL", "selectionId": "49602190329|1611849832|0|2|1|1|19.5|1"}]}
2025-07-20 14:30:23,879 - PinBet - INFO - 投注表单获取成功: 1611849832, 赔率: 2.090
2025-07-20 14:30:23,879 - PinBet - INFO - API返回的selectionId: 3183821417|49602190329|1611849832|0|2|1|1|19.50|1
2025-07-20 14:30:23,880 - PinBet - INFO - API返回的oddsId: 1611849832|0|2|1|1|19.5
2025-07-20 14:30:23,880 - PinBet - INFO - 将使用投注选项: selectionId=3183821417|49602190329|1611849832|0|2|1|1|19.50|1, oddsId=1611849832|0|2|1|1|19.5, odds=2.090
2025-07-20 14:30:23,880 - PinBet - INFO - 执行投注: 金额=723.44
2025-07-20 14:30:23,880 - PinBet - INFO - 从列表中提取投注信息: selection_id=3183821417|49602190329|1611849832|0|2|1|1|19.50|1, odds_id=1611849832|0|2|1|1|19.5, odds=2.090, stake=723.44
2025-07-20 14:30:24,646 - PinBet - INFO - 投注响应数据: {"response": [{"wagerId": 660844354, "status": "ACCEPTED", "selectionId": "3183821417|49602190329|1611849832|0|2|1|1|19.50|1", "betId": 2099408696, "uniqueRequestId": "59d0ccbe-4177-4e2c-8008-d7e4c81e2de3", "wagerType": "NORMAL", "betterLineWasAccepted": false, "jsonString": "{\"bets\":[{\"status\":\"ACCEPTED\",\"errorCode\":null,\"uniqueRequestId\":\"59d0ccbe-4177-4e2c-8008-d7e4c81e2de3\",\"lineId\":3183821449,\"altLineId\":49602190993,\"price\":2.09,\"minRiskStake\":0.07,\"maxRiskStake\":3588.98,\"minWinStake\":0.08,\"maxWinStake\":3911.98,\"straightBet\":{\"betId\":2099408696,\"uniqueRequestId\":\"59d0ccbe-4177-4e2c-8008-d7e4c81e2de3\",\"wagerNumber\":1,\"placedAt\":\"2025-07-20T06:30:24Z\",\"betStatus\":\"ACCEPTED\",\"betStatus2\":\"ACCEPTED\",\"betType\":\"SPREAD\",\"win\":788.55,\"risk\":723.44,\"oddsFormat\":\"DECIMAL\",\"updateSequence\":2928698636,\"externalRef\":\"660844354\",\"sportId\":4,\"sportName\":\"Basketball\",\"leagueId\":214756,\"leagueName\":\"FIBA - U19 World Cup Women\",\"eventId\":1611849832,\"handicap\":19.5,\"price\":2.09,\"teamName\":\"Japan\",\"team1\":\"France\",\"team2\":\"Japan\",\"periodNumber\":0,\"isLive\":\"FALSE\",\"homeTeamType\":\"Team1\",\"eventStartTime\":\"2025-07-20T12:00:00Z\",\"resultingUnit\":\"Regular\"}}]}", "oddsId": "1611849832|0|2|1|1|19.5", "psBetVO": {"betId": 2099408696, "uniqueRequestId": "59d0ccbe-4177-4e2c-8008-d7e4c81e2de3", "wagerNumber": 1, "placedAt": "2025-07-20T06:30:24Z", "betStatus": "ACCEPTED", "betType": "SPREAD", "win": 788.55, "risk": 723.44, "oddsFormat": "DECIMAL", "updateSequence": 1752993024516, "sportId": 4, "sportName": "Basketball", "leagueId": 214756, "leagueName": "FIBA - U19 World Cup Women", "eventId": 1611849832, "handicap": 19.5, "price": 2.09, "teamName": "Japan", "team1": "France", "team2": "Japan", "periodNumber": 0, "isLive": "FALSE", "eventStartTime": "2025-07-20T12:00:00Z", "resultingUnit": "Regular"}, "odds": 2.09}]}
2025-07-20 14:30:24,647 - PinBet - INFO - 投注成功: 注单ID 660844354, 投注ID 2099408696
2025-07-20 14:30:24,647 - PinBet - INFO - 投注成功: {'response': [{'wagerId': 660844354, 'status': 'ACCEPTED', 'selectionId': '3183821417|49602190329|1611849832|0|2|1|1|19.50|1', 'betId': 2099408696, 'uniqueRequestId': '59d0ccbe-4177-4e2c-8008-d7e4c81e2de3', 'wagerType': 'NORMAL', 'betterLineWasAccepted': False, 'jsonString': '{"bets":[{"status":"ACCEPTED","errorCode":null,"uniqueRequestId":"59d0ccbe-4177-4e2c-8008-d7e4c81e2de3","lineId":3183821449,"altLineId":49602190993,"price":2.09,"minRiskStake":0.07,"maxRiskStake":3588.98,"minWinStake":0.08,"maxWinStake":3911.98,"straightBet":{"betId":2099408696,"uniqueRequestId":"59d0ccbe-4177-4e2c-8008-d7e4c81e2de3","wagerNumber":1,"placedAt":"2025-07-20T06:30:24Z","betStatus":"ACCEPTED","betStatus2":"ACCEPTED","betType":"SPREAD","win":788.55,"risk":723.44,"oddsFormat":"DECIMAL","updateSequence":2928698636,"externalRef":"660844354","sportId":4,"sportName":"Basketball","leagueId":214756,"leagueName":"FIBA - U19 World Cup Women","eventId":1611849832,"handicap":19.5,"price":2.09,"teamName":"Japan","team1":"France","team2":"Japan","periodNumber":0,"isLive":"FALSE","homeTeamType":"Team1","eventStartTime":"2025-07-20T12:00:00Z","resultingUnit":"Regular"}}]}', 'oddsId': '1611849832|0|2|1|1|19.5', 'psBetVO': {'betId': 2099408696, 'uniqueRequestId': '59d0ccbe-4177-4e2c-8008-d7e4c81e2de3', 'wagerNumber': 1, 'placedAt': '2025-07-20T06:30:24Z', 'betStatus': 'ACCEPTED', 'betType': 'SPREAD', 'win': 788.55, 'risk': 723.44, 'oddsFormat': 'DECIMAL', 'updateSequence': 1752993024516, 'sportId': 4, 'sportName': 'Basketball', 'leagueId': 214756, 'leagueName': 'FIBA - U19 World Cup Women', 'eventId': 1611849832, 'handicap': 19.5, 'price': 2.09, 'teamName': 'Japan', 'team1': 'France', 'team2': 'Japan', 'periodNumber': 0, 'isLive': 'FALSE', 'eventStartTime': '2025-07-20T12:00:00Z', 'resultingUnit': 'Regular'}, 'odds': 2.09}]}
2025-07-20 14:30:24,648 - PinBet - INFO - 投注记录已保存到: data\pinbet_records_2025-07-20.json
2025-07-20 14:30:26,648 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 14:30:26,648 - PinBet - INFO - 开始查询未结算注单
2025-07-20 14:30:27,130 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 14:30:27,131 - PinBet - INFO - 处理完成: 原始记录 1 条，处理后 1 条记录
2025-07-20 14:30:27,131 - hedge.hedge_manager - INFO - 顺序对冲投注完全成功
2025-07-20 14:30:27,131 - hedge.hedge_manager - INFO - 顺序对冲投注完全成功
2025-07-20 14:30:27,131 - hedge_system - INFO - 对冲投注成功: 法国 - 日本
2025-07-20 14:30:27,131 - hedge.hedge_manager - INFO - 对冲投注完全成功，保存到对冲记录
2025-07-20 14:30:27,131 - hedge.hedge_manager - INFO - 对冲投注完全成功，保存到对冲记录
2025-07-20 14:30:27,132 - hedge.hedge_record_manager - INFO - 添加新对冲记录: hedge_1752993016
2025-07-20 14:30:27,132 - hedge.hedge_record_manager - INFO - 添加新对冲记录: hedge_1752993016
2025-07-20 14:30:27,134 - hedge.hedge_record_manager - INFO - 对冲记录保存成功: hedge_1752993016
2025-07-20 14:30:27,134 - hedge.hedge_record_manager - INFO - 对冲记录保存成功: hedge_1752993016
2025-07-20 14:30:27,134 - hedge.hedge_manager - INFO - 完全成功对冲记录保存成功: hedge_1752993016
2025-07-20 14:30:27,134 - hedge.hedge_manager - INFO - 完全成功对冲记录保存成功: hedge_1752993016
2025-07-20 14:30:27,134 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:法国 - 日本, 锁ID: hedge:法国 - 日本:1752993016
2025-07-20 14:30:27,135 - hedge.hedge_integration - INFO - 对冲投注执行成功
2025-07-20 14:30:27,135 - hedge.hedge_integration - INFO - 对冲投注执行成功
2025-07-20 14:30:27,164 - hedge.hedge_integration - INFO - 播放对冲成功提示音
2025-07-20 14:30:27,164 - hedge.hedge_integration - INFO - 播放对冲成功提示音
2025-07-20 14:30:27,164 - hedge.hedge_integration - INFO - 对冲投注成功，暂停监控以避免过度投注
2025-07-20 14:30:27,164 - hedge.hedge_integration - INFO - 对冲投注成功，暂停监控以避免过度投注
2025-07-20 14:30:37,534 - __main__ - INFO - [system] 正在查询对冲记录...
2025-07-20 14:30:37,535 - __main__ - INFO - [system] 优先加载当天对冲记录: hedge_records_2025-07-20.json
2025-07-20 14:30:37,535 - __main__ - INFO - [system] 显示当天对冲记录 1 条
2025-07-20 14:30:37,535 - __main__ - INFO - [system] 加载对冲记录 1 条 (来自 1 个文件)
2025-07-20 14:30:37,535 - __main__ - INFO - [system] 对冲记录查询完成
2025-07-20 14:30:42,287 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 14:30:42,680 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 14:30:42,680 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 14:30:42,681 - PinBet - INFO - 开始查询未结算注单
2025-07-20 14:30:43,268 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 14:30:43,268 - PinBet - INFO - 处理完成: 原始记录 1 条，处理后 1 条记录
2025-07-20 14:30:43,268 - __main__ - INFO - [system] 加载平博记录 1 条
2025-07-20 14:30:43,269 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 14:30:45,463 - __main__ - INFO - [system] 切换到皇冠记录显示模式
2025-07-20 14:30:45,868 - __main__ - INFO - [system] 正在查询皇冠投注记录...
2025-07-20 14:30:45,869 - platforms.hgbet_bk - INFO - 直接获取今日BK注单
2025-07-20 14:30:46,094 - platforms.hgbet_bk - INFO - 今日总投注金额: 1,384
2025-07-20 14:30:46,094 - platforms.hgbet_bk - INFO - 找到 4 条注单
2025-07-20 14:30:46,114 - __main__ - INFO - [system] 加载皇冠记录 4 条
2025-07-20 14:30:46,115 - __main__ - INFO - [system] 皇冠投注记录查询完成
2025-07-20 14:31:20,869 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 14:31:21,365 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 14:31:21,366 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 14:31:21,366 - PinBet - INFO - 开始查询未结算注单
2025-07-20 14:31:21,826 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 14:31:21,827 - PinBet - INFO - 处理完成: 原始记录 1 条，处理后 1 条记录
2025-07-20 14:31:21,827 - __main__ - INFO - [system] 加载平博记录 1 条
2025-07-20 14:31:21,827 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 14:31:51,201 - __main__ - INFO - [system] 已清除HedgeConfig类级别缓存
2025-07-20 14:31:51,201 - __main__ - INFO - [system] 已清除对冲集成器配置缓存
2025-07-20 14:31:51,202 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 14:31:51,202 - config.hedge_config - WARNING - 参数 enable_rollback 不是布尔值, 使用默认值: False
2025-07-20 14:31:51,202 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 14:31:51,203 - config.hedge_config - INFO - 配置已强制重新加载
2025-07-20 14:31:51,203 - __main__ - INFO - [system] 对冲配置已强制重新加载
2025-07-20 14:31:51,203 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 14:31:51,203 - config.hedge_config - WARNING - 参数 enable_rollback 不是布尔值, 使用默认值: False
2025-07-20 14:31:51,203 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 14:31:51,203 - config.hedge_config - INFO - 配置已强制重新加载
2025-07-20 14:31:51,204 - __main__ - INFO - [system] 对冲管理器配置已强制重新加载
2025-07-20 14:31:51,204 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 14:31:51,204 - config.hedge_config - WARNING - 参数 enable_rollback 不是布尔值, 使用默认值: False
2025-07-20 14:31:51,204 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 14:31:51,204 - config.hedge_config - INFO - 配置已强制重新加载
2025-07-20 14:31:51,204 - __main__ - INFO - [system] 对冲集成器配置已强制重新加载
2025-07-20 14:31:51,204 - __main__ - INFO - [system] 配置保存成功，对冲系统配置已更新
2025-07-20 14:34:45,550 - __main__ - INFO - [system] 切换到皇冠记录显示模式
2025-07-20 14:34:45,957 - __main__ - INFO - [system] 正在查询皇冠投注记录...
2025-07-20 14:34:45,958 - platforms.hgbet_bk - INFO - 直接获取今日BK注单
2025-07-20 14:34:46,185 - platforms.hgbet_bk - INFO - 今日总投注金额: 1,128
2025-07-20 14:34:46,185 - platforms.hgbet_bk - INFO - 找到 2 条注单
2025-07-20 14:34:46,186 - __main__ - INFO - [system] 加载皇冠记录 2 条
2025-07-20 14:34:46,186 - __main__ - INFO - [system] 皇冠投注记录查询完成
2025-07-20 14:38:29,725 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 14:38:30,105 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 14:38:30,106 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 14:38:30,106 - PinBet - INFO - 开始查询未结算注单
2025-07-20 14:38:43,719 - __main__ - INFO - [system] 切换到对冲记录显示模式
2025-07-20 14:38:44,698 - __main__ - INFO - [system] 开启对冲记录自动刷新
2025-07-20 14:38:44,698 - __main__ - INFO - [system] 检测到新的对冲记录，自动刷新...
2025-07-20 14:38:44,699 - __main__ - INFO - [system] 优先加载当天对冲记录: hedge_records_2025-07-20.json
2025-07-20 14:38:44,699 - __main__ - INFO - [system] 显示当天对冲记录 1 条
2025-07-20 14:38:44,699 - __main__ - INFO - [system] 加载对冲记录 1 条 (来自 1 个文件)
2025-07-20 14:38:46,847 - __main__ - INFO - [system] 切换到单边待补单显示模式
2025-07-20 14:38:47,809 - __main__ - INFO - [system] 正在查询单边待补单记录...
2025-07-20 14:38:47,809 - hedge.partial_record_manager - INFO - 单边记录管理器初始化完成
2025-07-20 14:38:47,809 - hedge.partial_record_manager - INFO - 单边记录管理器初始化完成
2025-07-20 14:38:47,812 - __main__ - INFO - [system] 未找到单边待补单记录
2025-07-20 14:38:47,813 - __main__ - INFO - [system] 单边待补单记录查询完成
2025-07-20 14:38:49,086 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 14:38:49,779 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 14:38:49,780 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 14:38:49,781 - PinBet - INFO - 开始查询未结算注单
2025-07-20 14:38:50,958 - PinBet - ERROR - 获取未结算注单失败: 权限不足，可能需要重新登录
2025-07-20 14:38:50,959 - __main__ - WARNING - [system] 获取平博记录失败
2025-07-20 14:38:50,959 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 14:38:56,728 - PinBet - ERROR - 获取未结算注单异常: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
Traceback (most recent call last):
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
               ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
                       ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 1378, in getresponse
    response.begin()
  File "D:\Program Files\Python311\Lib\http\client.py", line 318, in begin
    version, status, reason = self._read_status()
                              ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 287, in _read_status
    raise RemoteDisconnected("Remote end closed connection without"
http.client.RemoteDisconnected: Remote end closed connection without response

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Program Files\Python311\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\util\util.py", line 38, in reraise
    raise value.with_traceback(tb)
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
               ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
                       ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 1378, in getresponse
    response.begin()
  File "D:\Program Files\Python311\Lib\http\client.py", line 318, in begin
    version, status, reason = self._read_status()
                              ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\http\client.py", line 287, in _read_status
    raise RemoteDisconnected("Remote end closed connection without"
urllib3.exceptions.ProtocolError: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "d:\betburgerbot\platforms\pinbet_bk.py", line 1426, in get_pending_wagers
    response = self.session.post(
               ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\sessions.py", line 637, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\site-packages\requests\adapters.py", line 682, in send
    raise ConnectionError(err, request=request)
requests.exceptions.ConnectionError: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 14:38:56,732 - __main__ - WARNING - [system] 获取平博记录失败
2025-07-20 14:38:56,732 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 14:39:07,709 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 14:39:07,710 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 14:39:07,711 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 14:39:07,711 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 14:39:07,711 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 罗马尼亚 U20 - 比利时 vs 罗马尼亚 U20 - 比利时
2025-07-20 14:39:07,711 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 罗马尼亚 U20 - 比利时 vs 罗马尼亚 U20 - 比利时
2025-07-20 14:39:07,711 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 14:39:07,711 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 14:39:07,711 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 14:39:07,711 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 14:39:07,711 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 14:39:07,711 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 14:39:07,711 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 14:39:07,711 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 14:39:07,711 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 14:39:07,711 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 14:39:07,712 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:罗马尼亚 U20 - 比利时, 锁ID: hedge:罗马尼亚 U20 - 比利时:1752993547, 超时: 30秒
2025-07-20 14:39:07,712 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 14:39:07,712 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=795.79, 净利润=-283.79
2025-07-20 14:39:07,712 - hedge_system - INFO - 开始对冲投注: 罗马尼亚 U20 - 比利时
2025-07-20 14:39:07,712 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=795.79, 总投资=1795.79
2025-07-20 14:39:07,712 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=795.79, 总投资=1795.79
2025-07-20 14:39:07,712 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 14:39:07,712 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 14:39:07,712 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 14:39:07,712 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 14:39:07,713 - platforms.hgbet_bk - INFO - 解析到投注信息: 罗马尼亚 U20 vs 比利时, 市场类型: 17, 盘口: +10.5, 赔率: 1.89
2025-07-20 14:39:07,713 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.89
2025-07-20 14:39:07,713 - platforms.hgbet_bk - INFO - 处理市场类型 17
2025-07-20 14:39:07,713 - platforms.hgbet_bk - INFO - 解析为主队让球: 罗马尼亚 U20, 盘口: +10.5
2025-07-20 14:39:07,713 - platforms.hgbet_bk - INFO - 检查重复投注: 罗马尼亚 U20 vs 比利时, 让球, 投注方向=罗马尼亚 U20, 时段=全场, 比赛ID=410145724, 盘口值=+10.5
2025-07-20 14:39:07,714 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 14:39:07,714 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 14:39:07,714 - platforms.hgbet_bk - INFO - 投注信息: 罗马尼亚 U20 vs 比利时, 让球=罗马尼亚 U20, 盘口: +10.5, 赔率: 1.89
2025-07-20 14:39:07,714 - platforms.hgbet_bk - INFO - 开始执行投注流程
2025-07-20 14:39:07,714 - platforms.hgbet_bk - INFO - 投注信息: 罗马尼亚 U20 vs 比利时, 让球=罗马尼亚 U20, 盘口: +10.5, 赔率: 1.89
2025-07-20 14:39:07,714 - platforms.hgbet_bk - INFO - 比赛投注次数检查: 比利时_vs_罗马尼亚u20, 当前次数: 0, 限制: 2
2025-07-20 14:39:07,714 - platforms.hgbet_bk - INFO - 比赛投注次数检查通过: 比利时_vs_罗马尼亚u20, 可继续投注
2025-07-20 14:39:07,938 - platforms.hgbet_bk - INFO - 等待3秒后执行投注...
2025-07-20 14:39:10,940 - platforms.hgbet_bk - INFO - 投注比赛: 罗马尼亚 U20 vs 比利时
2025-07-20 14:39:10,940 - platforms.hgbet_bk - INFO - 投注类型: 让球, 投注队伍: 罗马尼亚 U20, 盘口: +10.5, 比赛阶段: 全场
2025-07-20 14:39:10,940 - platforms.hgbet_bk - INFO - 使用传入的投注金额: 1000.0
2025-07-20 14:39:10,940 - platforms.hgbet_bk - INFO - 最终确定的投注金额: 1000.0 (类型: <class 'float'>)
2025-07-20 14:39:10,940 - platforms.hgbet_bk - INFO - 确认投注金额: 1000.0, 赔率: 1.89
2025-07-20 14:39:10,940 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 14:39:11,181 - platforms.hgbet_bk - INFO - 获取到 32 场比赛
2025-07-20 14:39:11,181 - platforms.hgbet_bk - WARNING - 比赛ID 410145724 在today比赛列表中不存在，尝试在early中查找
2025-07-20 14:39:11,181 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 14:39:11,388 - platforms.hgbet_bk - INFO - 获取到 0 场比赛
2025-07-20 14:39:11,388 - platforms.hgbet_bk - WARNING - 比赛ID 410145724 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-20 14:39:11,394 - platforms.hgbet_bk - INFO - 队伍匹配良好: 罗马尼亚 U20 vs 比利时 <-> 罗马尼亚U20 vs 比利时U20, 分数: 1.000 (匹配类型: forward)
2025-07-20 14:39:11,399 - platforms.hgbet_bk - INFO - 找到匹配的比赛ID: 9401485, 匹配分数: 1.00
2025-07-20 14:39:11,399 - platforms.hgbet_bk - INFO - 投注类型代码: R, 选择: H, 比赛阶段: 全场
2025-07-20 14:39:11,399 - platforms.hgbet_bk - INFO - 使用标准wtype格式: R, 选项: H, 时段: 全场
2025-07-20 14:39:11,596 - platforms.hgbet_bk - INFO - 盘口值验证 - 原始: 10.5, 表单: 10.5, 绝对值差异: 0.0, 阈值: 0
2025-07-20 14:39:11,597 - platforms.hgbet_bk - INFO - 盘口值验证通过，绝对值差异 0.0 在阈值 0 范围内，继续投注
2025-07-20 14:39:11,597 - platforms.hgbet_bk - INFO - 赔率值验证 - 原始: 1.89, 表单: 1.86, 差异: 0.029999999999999805, 阈值: 0.08
2025-07-20 14:39:11,597 - platforms.hgbet_bk - INFO - 赔率值验证通过，差异 0.029999999999999805 在阈值 0.08 范围内，继续投注
2025-07-20 14:39:11,597 - platforms.hgbet_bk - INFO - 投注金额详细信息: amount=1000.0, type=<class 'float'>, str(amount)=1000.0
2025-07-20 14:39:11,826 - platforms.hgbet_bk - INFO - 投注参数 - golds: '1000', amount: 1000.0 -> 1000, type: <class 'float'>
2025-07-20 14:39:12,146 - platforms.hgbet_bk - INFO - 【投注】成功，订单号: 22280286207，余额: 10157.90
2025-07-20 14:39:12,147 - platforms.hgbet_bk - INFO - 投注成功: 罗马尼亚 U20 vs 比利时, 让球=罗马尼亚 U20, 盘口: +10.5, 赔率: 1.89
2025-07-20 14:39:12,148 - platforms.hgbet_bk - INFO - 成功保存投注记录: {'match_id': '9401485', 'home_team': '罗马尼亚 U20', 'away_team': '比利时', 'bet_type': '让球', 'bet_team': '罗马尼亚 U20', 'handicap_value': '+10.5', 'odds': 1.89, 'amount': 1000.0, 'period': '全场', 'timestamp': 1752993552.1470776, 'datetime': '2025-07-20 14:39:12', 'order_no': '22280286207', 'success': True}
2025-07-20 14:39:12,148 - platforms.hgbet_bk - INFO - 更新比赛投注次数: 比利时_vs_罗马尼亚u20, 新次数: 1
2025-07-20 14:39:12,149 - platforms.hgbet_bk - ERROR - 播放提示音失败: No module named 'betburger_fetcher'
2025-07-20 14:39:12,149 - hedge.hedge_manager - INFO - 基准平台(crown)投注成功，等待1.0秒后开始对冲平台投注
2025-07-20 14:39:12,149 - hedge.hedge_manager - INFO - 基准平台(crown)投注成功，等待1.0秒后开始对冲平台投注
2025-07-20 14:39:13,149 - PinBet - INFO - 解析结果: 罗马尼亚 U20 vs 比利时, 让球=比利时, 盘口=-9.5, 赔率=2.09
2025-07-20 14:39:13,149 - PinBet - INFO - 开始执行投注: 罗马尼亚 U20 vs 比利时, 让球=比利时
2025-07-20 14:39:13,150 - PinBet - INFO - 获取早场比赛数据
2025-07-20 14:39:13,150 - PinBet - INFO - 获取早场篮球比赛数据
2025-07-20 14:39:14,013 - PinBet - INFO - 早场篮球比赛数据获取成功
2025-07-20 14:39:14,013 - PinBet - INFO - 查找匹配比赛: 罗马尼亚 U20 vs 比利时, 让球=比利时, 盘口=-9.5
2025-07-20 14:39:14,013 - PinBet - INFO - 找到匹配比赛: 罗马尼亚 vs 比利时 (联赛: 欧洲U20甲级锦标赛) (匹配分数: 0.90)
2025-07-20 14:39:14,013 - PinBet - INFO - 可用的让球盘口: ['8.0', '8.5', '9.0', '9.5', '10.0', '10.5', '11.0', '11.5', '12.0', '12.5', '13.0']
2025-07-20 14:39:14,015 - PinBet - INFO - 标准化后的目标盘口值: -9.5
2025-07-20 14:39:14,015 - PinBet - INFO - 获取投注表单: event_id=1611886002, line_id=49602188612, period_num=0, bet_type=2, team_side=away, handicap=-9.5
2025-07-20 14:39:14,015 - PinBet - INFO - 让分盘设置 - team_side: away, team_side_code: 1, selection_direction: 1
2025-07-20 14:39:14,015 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 14:39:14,015 - PinBet - INFO - 构建投注ID: odds_id=1611886002|0|2|1|1|-9.5, selection_id=49602188612|1611886002|0|2|1|1|-9.5|1
2025-07-20 14:39:14,015 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1752993554015', 'withCredentials': 'true'}
2025-07-20 14:39:14,015 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611886002|0|2|1|1|-9.5", "oddsSelectionsType": "NORMAL", "selectionId": "49602188612|1611886002|0|2|1|1|-9.5|1"}]}
2025-07-20 14:39:14,416 - PinBet - ERROR - 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:14,416 - PinBet - WARNING - 获取投注表单失败，正在重试 (1/3): 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:15,416 - PinBet - INFO - 让分盘设置 - team_side: away, team_side_code: 1, selection_direction: 1
2025-07-20 14:39:15,416 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 14:39:15,416 - PinBet - INFO - 构建投注ID: odds_id=1611886002|0|2|1|1|-9.5, selection_id=49602188612|1611886002|0|2|1|1|-9.5|1
2025-07-20 14:39:15,416 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1752993555416', 'withCredentials': 'true'}
2025-07-20 14:39:15,417 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611886002|0|2|1|1|-9.5", "oddsSelectionsType": "NORMAL", "selectionId": "49602188612|1611886002|0|2|1|1|-9.5|1"}]}
2025-07-20 14:39:15,772 - PinBet - ERROR - 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:15,772 - PinBet - WARNING - 获取投注表单失败，正在重试 (2/3): 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:16,774 - PinBet - INFO - 让分盘设置 - team_side: away, team_side_code: 1, selection_direction: 1
2025-07-20 14:39:16,774 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 14:39:16,774 - PinBet - INFO - 构建投注ID: odds_id=1611886002|0|2|1|1|-9.5, selection_id=49602188612|1611886002|0|2|1|1|-9.5|1
2025-07-20 14:39:16,775 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1752993556774', 'withCredentials': 'true'}
2025-07-20 14:39:16,775 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611886002|0|2|1|1|-9.5", "oddsSelectionsType": "NORMAL", "selectionId": "49602188612|1611886002|0|2|1|1|-9.5|1"}]}
2025-07-20 14:39:17,122 - PinBet - ERROR - 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:17,122 - PinBet - WARNING - 获取投注表单失败，正在重试 (3/3): 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:18,123 - PinBet - ERROR - 获取投注表单失败，已重试 3 次
2025-07-20 14:39:18,123 - hedge.hedge_manager - INFO - pinbet投注失败，1.0秒后进行第2次尝试: 投注失败
2025-07-20 14:39:18,123 - hedge.hedge_manager - INFO - pinbet投注失败，1.0秒后进行第2次尝试: 投注失败
2025-07-20 14:39:19,125 - PinBet - INFO - 解析结果: 罗马尼亚 U20 vs 比利时, 让球=比利时, 盘口=-9.5, 赔率=2.09
2025-07-20 14:39:19,125 - PinBet - INFO - 开始执行投注: 罗马尼亚 U20 vs 比利时, 让球=比利时
2025-07-20 14:39:19,125 - PinBet - INFO - 获取早场比赛数据
2025-07-20 14:39:19,125 - PinBet - INFO - 获取早场篮球比赛数据
2025-07-20 14:39:19,496 - PinBet - INFO - 早场篮球比赛数据获取成功
2025-07-20 14:39:19,496 - PinBet - INFO - 查找匹配比赛: 罗马尼亚 U20 vs 比利时, 让球=比利时, 盘口=-9.5
2025-07-20 14:39:19,496 - PinBet - INFO - 找到匹配比赛: 罗马尼亚 vs 比利时 (联赛: 欧洲U20甲级锦标赛) (匹配分数: 0.90)
2025-07-20 14:39:19,497 - PinBet - INFO - 可用的让球盘口: ['8.0', '8.5', '9.0', '9.5', '10.0', '10.5', '11.0', '11.5', '12.0', '12.5', '13.0']
2025-07-20 14:39:19,497 - PinBet - INFO - 标准化后的目标盘口值: -9.5
2025-07-20 14:39:19,497 - PinBet - INFO - 获取投注表单: event_id=1611886002, line_id=49602188612, period_num=0, bet_type=2, team_side=away, handicap=-9.5
2025-07-20 14:39:19,497 - PinBet - INFO - 让分盘设置 - team_side: away, team_side_code: 1, selection_direction: 1
2025-07-20 14:39:19,498 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 14:39:19,498 - PinBet - INFO - 构建投注ID: odds_id=1611886002|0|2|1|1|-9.5, selection_id=49602188612|1611886002|0|2|1|1|-9.5|1
2025-07-20 14:39:19,498 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1752993559497', 'withCredentials': 'true'}
2025-07-20 14:39:19,498 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611886002|0|2|1|1|-9.5", "oddsSelectionsType": "NORMAL", "selectionId": "49602188612|1611886002|0|2|1|1|-9.5|1"}]}
2025-07-20 14:39:19,850 - PinBet - ERROR - 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:19,851 - PinBet - WARNING - 获取投注表单失败，正在重试 (1/3): 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:20,851 - PinBet - INFO - 让分盘设置 - team_side: away, team_side_code: 1, selection_direction: 1
2025-07-20 14:39:20,851 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 14:39:20,852 - PinBet - INFO - 构建投注ID: odds_id=1611886002|0|2|1|1|-9.5, selection_id=49602188612|1611886002|0|2|1|1|-9.5|1
2025-07-20 14:39:20,852 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1752993560851', 'withCredentials': 'true'}
2025-07-20 14:39:20,852 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611886002|0|2|1|1|-9.5", "oddsSelectionsType": "NORMAL", "selectionId": "49602188612|1611886002|0|2|1|1|-9.5|1"}]}
2025-07-20 14:39:21,229 - PinBet - ERROR - 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:21,229 - PinBet - WARNING - 获取投注表单失败，正在重试 (2/3): 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:22,231 - PinBet - INFO - 让分盘设置 - team_side: away, team_side_code: 1, selection_direction: 1
2025-07-20 14:39:22,231 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 14:39:22,231 - PinBet - INFO - 构建投注ID: odds_id=1611886002|0|2|1|1|-9.5, selection_id=49602188612|1611886002|0|2|1|1|-9.5|1
2025-07-20 14:39:22,231 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1752993562231', 'withCredentials': 'true'}
2025-07-20 14:39:22,232 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611886002|0|2|1|1|-9.5", "oddsSelectionsType": "NORMAL", "selectionId": "49602188612|1611886002|0|2|1|1|-9.5|1"}]}
2025-07-20 14:39:22,621 - PinBet - ERROR - 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:22,621 - PinBet - WARNING - 获取投注表单失败，正在重试 (3/3): 获取投注表单失败: HTTP状态码 403, 错误信息: {"error": 403}
2025-07-20 14:39:23,622 - PinBet - ERROR - 获取投注表单失败，已重试 3 次
2025-07-20 14:39:23,622 - hedge.hedge_manager - WARNING - pinbet投注失败，不适合重试: 投注失败
2025-07-20 14:39:23,622 - hedge.hedge_manager - WARNING - pinbet投注失败，不适合重试: 投注失败
2025-07-20 14:39:23,622 - hedge.hedge_manager - WARNING - 基准平台(crown)成功，对冲平台(pinbet)失败，启动补单流程
2025-07-20 14:39:23,622 - hedge.hedge_manager - WARNING - 基准平台(crown)成功，对冲平台(pinbet)失败，启动补单流程
2025-07-20 14:39:23,622 - hedge.hedge_manager - INFO - 启动pinbet平台补单流程
2025-07-20 14:39:23,622 - hedge.hedge_manager - INFO - 启动pinbet平台补单流程
2025-07-20 14:39:23,622 - hedge.hedge_manager - INFO - 尝试补单 pinbet，第 1/2 次
2025-07-20 14:39:23,622 - hedge.hedge_manager - INFO - 尝试补单 pinbet，第 1/2 次
2025-07-20 14:39:23,623 - hedge_system - WARNING - 对冲投注失败: 罗马尼亚 U20 - 比利时
2025-07-20 14:39:23,623 - PinBet - WARNING - 无法解析比赛队伍: 
2025-07-20 14:39:23,623 - hedge.hedge_manager - INFO - 检测到单边风险，保存到单边记录: 基准=True, 对冲=False
2025-07-20 14:39:23,623 - hedge.hedge_manager - INFO - 检测到单边风险，保存到单边记录: 基准=True, 对冲=False
2025-07-20 14:39:23,623 - hedge.hedge_manager - WARNING - pinbet补单失败，第 1 次尝试
2025-07-20 14:39:23,623 - hedge.hedge_manager - WARNING - pinbet补单失败，第 1 次尝试
2025-07-20 14:39:23,623 - hedge.partial_record_manager - INFO - 添加新单边记录: hedge_1752993547
2025-07-20 14:39:23,623 - hedge.partial_record_manager - INFO - 添加新单边记录: hedge_1752993547
2025-07-20 14:39:23,625 - hedge.partial_record_manager - INFO - 单边记录保存成功: hedge_1752993547
2025-07-20 14:39:23,625 - hedge.partial_record_manager - INFO - 单边记录保存成功: hedge_1752993547
2025-07-20 14:39:23,625 - hedge.hedge_manager - INFO - 单边风险记录保存成功: hedge_1752993547
2025-07-20 14:39:23,625 - hedge.hedge_manager - INFO - 单边风险记录保存成功: hedge_1752993547
2025-07-20 14:39:23,625 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:罗马尼亚 U20 - 比利时, 锁ID: hedge:罗马尼亚 U20 - 比利时:1752993547
2025-07-20 14:39:23,625 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台成功，对冲平台(pinbet)失败，已启动补单流程
2025-07-20 14:39:23,625 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台成功，对冲平台(pinbet)失败，已启动补单流程
2025-07-20 14:39:23,626 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台成功，对冲平台(pinbet)失败，已启动补单流程
2025-07-20 14:39:23,626 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台成功，对冲平台(pinbet)失败，已启动补单流程
2025-07-20 14:39:25,624 - hedge.hedge_manager - INFO - 尝试补单 pinbet，第 2/2 次
2025-07-20 14:39:25,624 - hedge.hedge_manager - INFO - 尝试补单 pinbet，第 2/2 次
2025-07-20 14:39:25,624 - PinBet - WARNING - 无法解析比赛队伍: 
2025-07-20 14:39:25,624 - hedge.hedge_manager - WARNING - pinbet补单失败，第 2 次尝试
2025-07-20 14:39:25,624 - hedge.hedge_manager - WARNING - pinbet补单失败，第 2 次尝试
2025-07-20 14:39:25,624 - hedge.hedge_manager - ERROR - pinbet 补单失败，已达到最大重试次数
2025-07-20 14:39:25,624 - hedge.hedge_manager - ERROR - pinbet 补单失败，已达到最大重试次数
2025-07-20 14:39:25,624 - hedge.hedge_manager - WARNING - 注意：基准平台投注已成功，但pinbet补单失败，存在单边风险
2025-07-20 14:39:25,624 - hedge.hedge_manager - WARNING - 注意：基准平台投注已成功，但pinbet补单失败，存在单边风险
2025-07-20 14:39:28,716 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 14:39:28,717 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 14:39:28,718 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 14:39:28,718 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 14:39:28,718 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 罗马尼亚 U20 - 比利时 vs 罗马尼亚 U20 - 比利时
2025-07-20 14:39:28,718 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 罗马尼亚 U20 - 比利时 vs 罗马尼亚 U20 - 比利时
2025-07-20 14:39:28,718 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 14:39:28,718 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 14:39:28,718 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 14:39:28,718 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 14:39:28,718 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 14:39:28,718 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 14:39:28,718 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 14:39:28,718 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 14:39:28,718 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 14:39:28,718 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 14:39:28,718 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:罗马尼亚 U20 - 比利时, 锁ID: hedge:罗马尼亚 U20 - 比利时:1752993568, 超时: 30秒
2025-07-20 14:39:28,719 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 14:39:28,719 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=780.85, 净利润=-268.85
2025-07-20 14:39:28,719 - hedge_system - INFO - 开始对冲投注: 罗马尼亚 U20 - 比利时
2025-07-20 14:39:28,719 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=780.85, 总投资=1780.85
2025-07-20 14:39:28,719 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=780.85, 总投资=1780.85
2025-07-20 14:39:28,719 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 14:39:28,719 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 14:39:28,719 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 14:39:28,719 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 14:39:28,719 - platforms.hgbet_bk - INFO - 解析到投注信息: 罗马尼亚 U20 vs 比利时, 市场类型: 17, 盘口: +10.5, 赔率: 1.89
2025-07-20 14:39:28,720 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.89
2025-07-20 14:39:28,720 - platforms.hgbet_bk - INFO - 处理市场类型 17
2025-07-20 14:39:28,720 - platforms.hgbet_bk - INFO - 解析为主队让球: 罗马尼亚 U20, 盘口: +10.5
2025-07-20 14:39:28,720 - platforms.hgbet_bk - INFO - 检查重复投注: 罗马尼亚 U20 vs 比利时, 让球, 投注方向=罗马尼亚 U20, 时段=全场, 比赛ID=410145724, 盘口值=+10.5
2025-07-20 14:39:28,720 - platforms.hgbet_bk - WARNING - 重复投注: 罗马尼亚 U20 vs 比利时, 让球盘口
2025-07-20 14:39:28,720 - platforms.hgbet_bk - WARNING - 已有记录: 2025-07-20 14:39:12, 全场/罗马尼亚 U20/+10.5, 当前尝试: 全场/罗马尼亚 U20/+10.5
2025-07-20 14:39:28,721 - platforms.hgbet_bk - WARNING - 重复投注检测: 同一场比赛的让球盘口已有投注记录，不允许重复投注
2025-07-20 14:39:28,721 - platforms.hgbet_bk - WARNING - 比赛ID匹配: False, 队伍匹配: True, 盘口类型匹配: True
2025-07-20 14:39:28,721 - platforms.hgbet_bk - WARNING - 投注方向匹配: True, 盘口值匹配: True
2025-07-20 14:39:28,721 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 罗马尼亚 U20 vs 比利时, 让球=罗马尼亚 U20
2025-07-20 14:39:28,721 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 罗马尼亚 U20 vs 比利时, 让球=罗马尼亚 U20
2025-07-20 14:39:28,721 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 14:39:28,721 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 14:39:28,721 - hedge_system - WARNING - 对冲投注失败: 罗马尼亚 U20 - 比利时
2025-07-20 14:39:28,721 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 14:39:28,721 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 14:39:28,721 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:罗马尼亚 U20 - 比利时, 锁ID: hedge:罗马尼亚 U20 - 比利时:1752993568
2025-07-20 14:39:28,721 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 14:39:28,721 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 14:39:28,721 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 14:39:28,721 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 14:39:33,850 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 14:39:33,851 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 14:39:33,851 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 14:39:33,851 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 14:39:33,852 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 罗马尼亚 U20 - 比利时 vs 罗马尼亚 U20 - 比利时
2025-07-20 14:39:33,852 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 罗马尼亚 U20 - 比利时 vs 罗马尼亚 U20 - 比利时
2025-07-20 14:39:33,852 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 14:39:33,852 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 14:39:33,852 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 14:39:33,852 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 14:39:33,852 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 14:39:33,852 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 14:39:33,852 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 14:39:33,852 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 14:39:33,852 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 14:39:33,852 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 14:39:33,852 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:罗马尼亚 U20 - 比利时, 锁ID: hedge:罗马尼亚 U20 - 比利时:1752993573, 超时: 30秒
2025-07-20 14:39:33,852 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 14:39:33,852 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=780.85, 净利润=-268.85
2025-07-20 14:39:33,853 - hedge_system - INFO - 开始对冲投注: 罗马尼亚 U20 - 比利时
2025-07-20 14:39:33,853 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=780.85, 总投资=1780.85
2025-07-20 14:39:33,853 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=780.85, 总投资=1780.85
2025-07-20 14:39:33,853 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 14:39:33,853 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 14:39:33,853 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 14:39:33,853 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 14:39:33,853 - platforms.hgbet_bk - INFO - 解析到投注信息: 罗马尼亚 U20 vs 比利时, 市场类型: 17, 盘口: +10.5, 赔率: 1.89
2025-07-20 14:39:33,853 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.89
2025-07-20 14:39:33,853 - platforms.hgbet_bk - INFO - 处理市场类型 17
2025-07-20 14:39:33,853 - platforms.hgbet_bk - INFO - 解析为主队让球: 罗马尼亚 U20, 盘口: +10.5
2025-07-20 14:39:33,854 - platforms.hgbet_bk - INFO - 检查重复投注: 罗马尼亚 U20 vs 比利时, 让球, 投注方向=罗马尼亚 U20, 时段=全场, 比赛ID=410145724, 盘口值=+10.5
2025-07-20 14:39:33,854 - platforms.hgbet_bk - WARNING - 重复投注: 罗马尼亚 U20 vs 比利时, 让球盘口
2025-07-20 14:39:33,854 - platforms.hgbet_bk - WARNING - 已有记录: 2025-07-20 14:39:12, 全场/罗马尼亚 U20/+10.5, 当前尝试: 全场/罗马尼亚 U20/+10.5
2025-07-20 14:39:33,854 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 罗马尼亚 U20 vs 比利时, 让球=罗马尼亚 U20
2025-07-20 14:39:33,854 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 罗马尼亚 U20 vs 比利时, 让球=罗马尼亚 U20
2025-07-20 14:39:33,854 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 14:39:33,854 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 14:39:33,854 - hedge_system - WARNING - 对冲投注失败: 罗马尼亚 U20 - 比利时
2025-07-20 14:39:33,854 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 14:39:33,854 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 14:39:33,855 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:罗马尼亚 U20 - 比利时, 锁ID: hedge:罗马尼亚 U20 - 比利时:1752993573
2025-07-20 14:39:33,855 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 14:39:33,855 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 14:39:33,855 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 14:39:33,855 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 14:39:39,456 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 14:39:39,457 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 14:39:39,457 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 14:39:39,457 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 14:39:39,458 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 罗马尼亚 U20 - 比利时 vs 罗马尼亚 U20 - 比利时
2025-07-20 14:39:39,458 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 罗马尼亚 U20 - 比利时 vs 罗马尼亚 U20 - 比利时
2025-07-20 14:39:39,458 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 14:39:39,458 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 14:39:39,458 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 14:39:39,458 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 14:39:39,458 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 14:39:39,458 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 14:39:39,458 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 14:39:39,458 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 14:39:39,458 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 14:39:39,458 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 14:39:39,458 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:罗马尼亚 U20 - 比利时, 锁ID: hedge:罗马尼亚 U20 - 比利时:1752993579, 超时: 30秒
2025-07-20 14:39:39,459 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 14:39:39,459 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=780.85, 净利润=-268.85
2025-07-20 14:39:39,459 - hedge_system - INFO - 开始对冲投注: 罗马尼亚 U20 - 比利时
2025-07-20 14:39:39,459 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=780.85, 总投资=1780.85
2025-07-20 14:39:39,459 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=780.85, 总投资=1780.85
2025-07-20 14:39:39,459 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 14:39:39,459 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 14:39:39,459 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 14:39:39,459 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 14:39:39,459 - platforms.hgbet_bk - INFO - 解析到投注信息: 罗马尼亚 U20 vs 比利时, 市场类型: 17, 盘口: +10.5, 赔率: 1.89
2025-07-20 14:39:39,459 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.89
2025-07-20 14:39:39,459 - platforms.hgbet_bk - INFO - 处理市场类型 17
2025-07-20 14:39:39,459 - platforms.hgbet_bk - INFO - 解析为主队让球: 罗马尼亚 U20, 盘口: +10.5
2025-07-20 14:39:39,460 - platforms.hgbet_bk - INFO - 检查重复投注: 罗马尼亚 U20 vs 比利时, 让球, 投注方向=罗马尼亚 U20, 时段=全场, 比赛ID=410145724, 盘口值=+10.5
2025-07-20 14:39:39,460 - platforms.hgbet_bk - WARNING - 重复投注: 罗马尼亚 U20 vs 比利时, 让球盘口
2025-07-20 14:39:39,460 - platforms.hgbet_bk - WARNING - 已有记录: 2025-07-20 14:39:12, 全场/罗马尼亚 U20/+10.5, 当前尝试: 全场/罗马尼亚 U20/+10.5
2025-07-20 14:39:39,460 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 罗马尼亚 U20 vs 比利时, 让球=罗马尼亚 U20
2025-07-20 14:39:39,460 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 罗马尼亚 U20 vs 比利时, 让球=罗马尼亚 U20
2025-07-20 14:39:39,460 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 14:39:39,460 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 14:39:39,460 - hedge_system - WARNING - 对冲投注失败: 罗马尼亚 U20 - 比利时
2025-07-20 14:39:39,461 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 14:39:39,461 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 14:39:39,461 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:罗马尼亚 U20 - 比利时, 锁ID: hedge:罗马尼亚 U20 - 比利时:1752993579
2025-07-20 14:39:39,461 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 14:39:39,461 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 14:39:39,461 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 14:39:39,461 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 14:39:39,880 - __main__ - INFO - [system] 正在停止对冲监控...
2025-07-20 14:39:39,880 - hedge.hedge_integration - INFO - 收到停止监控信号
2025-07-20 14:39:39,880 - hedge.hedge_integration - INFO - 收到停止监控信号
2025-07-20 14:39:40,461 - hedge.hedge_integration - INFO - 对冲监控已停止
2025-07-20 14:39:40,461 - hedge.hedge_integration - INFO - 对冲监控已停止
2025-07-20 14:39:40,461 - __main__ - INFO - [system] 对冲监控已成功停止
2025-07-20 14:39:44,438 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 14:39:44,438 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 14:39:44,439 - PinBet - INFO - 开始查询未结算注单
2025-07-20 14:39:44,796 - PinBet - ERROR - 获取未结算注单失败: 权限不足，可能需要重新登录
2025-07-20 14:39:44,797 - __main__ - WARNING - [system] 获取平博记录失败
2025-07-20 14:39:44,797 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 14:39:50,827 - __main__ - INFO - [system] 切换到皇冠记录显示模式
2025-07-20 14:39:51,203 - __main__ - INFO - [system] 正在查询皇冠投注记录...
2025-07-20 14:39:51,204 - platforms.hgbet_bk - INFO - 直接获取今日BK注单
2025-07-20 14:39:51,422 - platforms.hgbet_bk - INFO - 今日总投注金额: 2,128
2025-07-20 14:39:51,422 - platforms.hgbet_bk - INFO - 找到 3 条注单
2025-07-20 14:39:51,433 - __main__ - INFO - [system] 加载皇冠记录 3 条
2025-07-20 14:39:51,433 - __main__ - INFO - [system] 皇冠投注记录查询完成
2025-07-20 15:07:04,095 - __main__ - INFO - GUI日志将保存到: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 15:07:04,738 - __main__ - INFO - [system] BetBurger对冲投注系统GUI已启动
2025-07-20 15:07:04,738 - __main__ - INFO - [system] 请先点击'初始化系统'，然后'登录系统'
2025-07-20 15:07:04,849 - __main__ - INFO - [system] 配置加载成功
2025-07-20 15:07:04,849 - __main__ - INFO - [GUI] GUI程序启动，日志文件: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 15:07:08,110 - __main__ - INFO - [GUI] 正在加载最近日志...
2025-07-20 15:07:08,113 - __main__ - INFO - [GUI] 最近日志加载完成，共显示 100 条日志
2025-07-20 15:07:19,442 - __main__ - INFO - [system] 正在初始化系统...
2025-07-20 15:07:19,442 - hedge_main - INFO - 初始化对冲投注系统
2025-07-20 15:07:19,458 - utils.config_security - INFO - 加载现有加密密钥
2025-07-20 15:07:19,458 - utils.config_security - INFO - 加载未加密的配置文件
2025-07-20 15:07:19,458 - config.hedge_config - INFO - 对冲配置首次加载完成
2025-07-20 15:07:19,458 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 15:07:19,458 - config.hedge_config - WARNING - 参数 enable_rollback 不是布尔值, 使用默认值: False
2025-07-20 15:07:19,458 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 15:07:19,458 - utils.match_data_fetcher - INFO - 比赛数据获取器初始化完成，缓存目录: data/match_cache
2025-07-20 15:07:19,458 - utils.match_data_fetcher - INFO - 比赛数据获取器初始化完成，缓存目录: data/match_cache
2025-07-20 15:07:19,458 - utils.match_learning_enhanced - INFO - 增强匹配学习系统初始化完成，数据目录: data/match_learning
2025-07-20 15:07:19,458 - utils.alias_manager - INFO - 别名管理器初始化完成，配置目录: config
2025-07-20 15:07:19,458 - utils.match_learning_controller - INFO - 比赛匹配学习控制器初始化完成
2025-07-20 15:07:19,458 - hedge_main - INFO - 正在初始化投注系统...
2025-07-20 15:07:19,458 - hedge_main - INFO - 平博系统初始化完成
2025-07-20 15:07:19,458 - platforms.hgbet_bk - INFO - 初始化皇冠篮球投注模块
2025-07-20 15:07:19,462 - platforms.hgbet_bk - INFO - 投注记录将保存到: data\bet_records_2025-07-20.json
2025-07-20 15:07:19,462 - hedge_main - INFO - 皇冠系统初始化完成
2025-07-20 15:07:19,462 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 15:07:19,462 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 15:07:19,462 - validation.match_validator - INFO - 比赛匹配验证器初始化完成
2025-07-20 15:07:19,462 - validation.match_safety - INFO - 比赛匹配安全检查器初始化完成
2025-07-20 15:07:19,462 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 15:07:19,462 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 15:07:19,462 - hedge.hedge_calculator - INFO - 投注金额计算器初始化完成
2025-07-20 15:07:19,462 - hedge.hedge_monitor - INFO - 投注监控器初始化完成
2025-07-20 15:07:19,464 - hedge.hedge_record_manager - INFO - 对冲记录管理器初始化完成
2025-07-20 15:07:19,464 - hedge.hedge_manager - INFO - 对冲记录管理器初始化成功
2025-07-20 15:07:19,464 - hedge.partial_record_manager - INFO - 单边记录管理器初始化完成
2025-07-20 15:07:19,464 - hedge.hedge_manager - INFO - 单边记录管理器初始化成功
2025-07-20 15:07:19,464 - hedge.hedge_manager - INFO - 对冲管理器初始化完成
2025-07-20 15:07:19,464 - hedge.hedge_integration - INFO - 对冲集成器初始化完成（实时匹配模式）
2025-07-20 15:07:19,464 - hedge_main - INFO - 对冲集成器初始化完成
2025-07-20 15:07:19,464 - __main__ - INFO - [system] 系统初始化成功
2025-07-20 15:07:19,464 - __main__ - INFO - [GUI] 对冲系统日志转发设置完成
2025-07-20 15:07:22,842 - __main__ - INFO - [system] 正在登录系统...
2025-07-20 15:07:22,842 - hedge_main - INFO - 正在登录投注系统...
2025-07-20 15:07:22,842 - PinBet - INFO - 尝试自动登录平博账号: mfc123
2025-07-20 15:07:23,998 - PinBet - INFO - 登录成功(带tokens): mfc123
2025-07-20 15:07:24,378 - PinBet - INFO - 余额获取成功: 26778.74
2025-07-20 15:07:24,382 - platforms.smart_login_manager - INFO - 记录登录尝试: 成功=True, 错误码=None, 消息=登录成功(带tokens)
2025-07-20 15:07:24,382 - platforms.smart_login_manager - INFO - 保存登录会话信息
2025-07-20 15:07:24,772 - PinBet - INFO - 余额获取成功: 26778.74
2025-07-20 15:07:24,772 - platforms.hgbet_bk - INFO - 尝试登录...
2025-07-20 15:07:27,197 - platforms.hgbet_bk - INFO - 登录状态码: 200
2025-07-20 15:07:27,197 - platforms.hgbet_bk - INFO - 提取到UID: iw1i6nkqm38245145l6563187b1
2025-07-20 15:07:27,412 - platforms.hgbet_bk - INFO - 登录成功，当前余额：10157.9，版本号: 2025-04-30-CRM-55_89
2025-07-20 15:07:27,412 - hedge_main - INFO - 所有系统登录成功
2025-07-20 15:07:27,412 - hedge_main - INFO - 正在初始化比赛匹配学习系统...
2025-07-20 15:07:27,412 - utils.match_learning_controller - INFO - 开始初始化比赛匹配学习系统
2025-07-20 15:07:27,412 - utils.match_learning_controller - INFO - 需要获取新的平台数据
2025-07-20 15:07:27,412 - utils.match_data_fetcher - INFO - 开始获取所有平台比赛数据
2025-07-20 15:07:27,412 - utils.match_data_fetcher - INFO - 获取平博平台比赛数据
2025-07-20 15:07:27,792 - utils.match_data_fetcher - ERROR - 解析平博比赛数据失败: 'NoneType' object is not iterable
2025-07-20 15:07:27,792 - utils.match_data_fetcher - INFO - 平博今日比赛 (mk=0): 0 场
2025-07-20 15:07:28,322 - utils.match_data_fetcher - INFO - 平博今日比赛 (mk=1): 4 场
2025-07-20 15:07:28,322 - utils.match_data_fetcher - INFO - 平博今日比赛总计: 4 场
2025-07-20 15:07:28,692 - utils.match_data_fetcher - ERROR - 解析平博比赛数据失败: 'NoneType' object is not iterable
2025-07-20 15:07:28,692 - utils.match_data_fetcher - INFO - 平博早场比赛 (mk=0): 0 场
2025-07-20 15:07:29,067 - utils.match_data_fetcher - INFO - 平博早场比赛 (mk=1): 4 场
2025-07-20 15:07:29,067 - utils.match_data_fetcher - INFO - 平博早场比赛总计: 4 场
2025-07-20 15:07:29,067 - utils.match_data_fetcher - INFO - 平博数据获取成功
2025-07-20 15:07:29,067 - utils.match_data_fetcher - INFO - 获取皇冠平台比赛数据
2025-07-20 15:07:29,067 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 15:07:29,272 - platforms.hgbet_bk - INFO - 获取到 29 场比赛
2025-07-20 15:07:29,272 - utils.match_data_fetcher - INFO - 皇冠今日比赛: 29 场
2025-07-20 15:07:29,272 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 15:07:29,482 - platforms.hgbet_bk - INFO - 获取到 5 场比赛
2025-07-20 15:07:29,482 - utils.match_data_fetcher - INFO - 皇冠早场比赛: 5 场
2025-07-20 15:07:29,482 - utils.match_data_fetcher - INFO - 皇冠数据获取成功
2025-07-20 15:07:29,482 - utils.match_data_fetcher - INFO - 缓存数据已保存到: data/match_cache\platform_matches.json
2025-07-20 15:07:29,492 - utils.match_data_fetcher - INFO - 队伍名和联赛名已保存
2025-07-20 15:07:29,492 - utils.match_data_fetcher - INFO - 成功获取 2 个平台的数据
2025-07-20 15:07:29,492 - utils.match_learning_controller - INFO - 平台数据获取成功
2025-07-20 15:07:29,492 - utils.match_learning_controller - INFO - 比赛匹配学习系统初始化完成
2025-07-20 15:07:29,492 - utils.match_learning_controller - INFO - === 比赛匹配学习系统初始化摘要 ===
2025-07-20 15:07:29,492 - utils.match_learning_controller - INFO - 系统状态: 已初始化
2025-07-20 15:07:29,492 - utils.match_learning_controller - INFO - pinbet平台: 总计8场比赛 (今日4场, 早场4场)
2025-07-20 15:07:29,492 - utils.match_learning_controller - INFO - crown平台: 总计34场比赛 (今日29场, 早场5场)
2025-07-20 15:07:29,492 - utils.match_learning_controller - INFO - 当前别名配置: 3个联赛, 11支队伍, 35个别名
2025-07-20 15:07:29,492 - utils.match_learning_controller - INFO - 学习统计: 总失败26次, 成功学习0次, 学习率0.0%
2025-07-20 15:07:29,494 - utils.match_learning_controller - INFO - === 初始化摘要结束 ===
2025-07-20 15:07:29,494 - hedge_main - INFO - 比赛匹配学习系统初始化完成
2025-07-20 15:07:29,494 - hedge_main - INFO - pinbet平台数据: 8场比赛
2025-07-20 15:07:29,494 - hedge_main - INFO - crown平台数据: 34场比赛
2025-07-20 15:07:29,495 - __main__ - INFO - [system] 系统登录成功
2025-07-20 15:07:29,882 - PinBet - INFO - 余额获取成功: 26778.74
2025-07-20 15:07:39,192 - __main__ - INFO - [system] 正在查询对冲记录...
2025-07-20 15:07:39,202 - __main__ - INFO - [system] 优先加载当天对冲记录: hedge_records_2025-07-20.json
2025-07-20 15:07:39,202 - __main__ - INFO - [system] 显示当天对冲记录 1 条
2025-07-20 15:07:39,205 - __main__ - INFO - [system] 加载对冲记录 1 条 (来自 1 个文件)
2025-07-20 15:07:39,205 - __main__ - INFO - [system] 对冲记录查询完成
2025-07-20 15:07:40,562 - __main__ - INFO - [system] 切换到单边待补单显示模式
2025-07-20 15:07:40,862 - __main__ - INFO - [system] 正在查询单边待补单记录...
2025-07-20 15:07:40,862 - hedge.partial_record_manager - INFO - 单边记录管理器初始化完成
2025-07-20 15:07:40,862 - hedge.partial_record_manager - INFO - 单边记录管理器初始化完成
2025-07-20 15:07:40,862 - __main__ - INFO - [system] 加载单边待补单记录 1 条
2025-07-20 15:07:40,862 - __main__ - INFO - [system] 单边待补单记录查询完成
2025-07-20 15:07:45,155 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 15:07:45,522 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 15:07:45,522 - PinBet - INFO - 会话空闲1752995266秒，执行主动刷新以保持活跃
2025-07-20 15:07:45,909 - PinBet - INFO - 余额获取成功: 26778.74
2025-07-20 15:07:45,909 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 15:07:45,909 - PinBet - INFO - 开始查询未结算注单
2025-07-20 15:07:46,294 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 15:07:46,294 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 15:07:46,305 - __main__ - INFO - [system] 加载平博记录 2 条
2025-07-20 15:07:46,305 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 15:07:49,394 - __main__ - INFO - [system] 切换到对冲记录显示模式
2025-07-20 15:07:50,003 - __main__ - INFO - [system] 开启对冲记录自动刷新
2025-07-20 15:07:50,003 - __main__ - INFO - [system] 检测到新的对冲记录，自动刷新...
2025-07-20 15:07:50,009 - __main__ - INFO - [system] 优先加载当天对冲记录: hedge_records_2025-07-20.json
2025-07-20 15:07:50,009 - __main__ - INFO - [system] 显示当天对冲记录 1 条
2025-07-20 15:07:50,009 - __main__ - INFO - [system] 加载对冲记录 1 条 (来自 1 个文件)
2025-07-20 15:07:56,222 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 15:07:58,032 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 15:07:58,032 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 15:07:58,042 - PinBet - INFO - 开始查询未结算注单
2025-07-20 15:07:58,442 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 15:07:58,442 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 15:07:58,442 - __main__ - INFO - [system] 加载平博记录 2 条
2025-07-20 15:07:58,452 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 15:08:01,130 - __main__ - INFO - [system] 切换到皇冠记录显示模式
2025-07-20 15:08:01,482 - __main__ - INFO - [system] 正在查询皇冠投注记录...
2025-07-20 15:08:01,482 - platforms.hgbet_bk - INFO - 直接获取今日BK注单
2025-07-20 15:08:01,703 - platforms.hgbet_bk - INFO - 今日总投注金额: 2,256
2025-07-20 15:08:01,703 - platforms.hgbet_bk - INFO - 找到 4 条注单
2025-07-20 15:08:01,721 - __main__ - INFO - [system] 加载皇冠记录 4 条
2025-07-20 15:08:01,721 - __main__ - INFO - [system] 皇冠投注记录查询完成
2025-07-20 16:45:09,759 - __main__ - INFO - [system] 正在查询皇冠投注记录...
2025-07-20 16:45:09,760 - platforms.hgbet_bk - INFO - 直接获取今日BK注单
2025-07-20 16:45:09,985 - platforms.hgbet_bk - INFO - 今日总投注金额: 2,128
2025-07-20 16:45:09,985 - platforms.hgbet_bk - INFO - 找到 3 条注单
2025-07-20 16:45:09,994 - __main__ - INFO - [system] 加载皇冠记录 3 条
2025-07-20 16:45:09,994 - __main__ - INFO - [system] 皇冠投注记录查询完成
2025-07-20 16:45:14,729 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 16:45:15,038 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 16:45:15,038 - PinBet - INFO - 会话空闲5849秒，执行主动刷新以保持活跃
2025-07-20 16:45:30,333 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 16:45:30,334 - PinBet - INFO - 会话空闲5864秒，执行主动刷新以保持活跃
2025-07-20 16:45:31,024 - PinBet - WARNING - 余额API返回success=False，会话可能已失效
2025-07-20 16:45:31,024 - PinBet - INFO - 清除平博登录会话
2025-07-20 16:45:31,024 - platforms.smart_login_manager - INFO - 清除会话数据
2025-07-20 16:45:31,025 - PinBet - INFO - 登录会话已清除
2025-07-20 16:45:31,025 - PinBet - WARNING - 会话已失效，需要重新登录
2025-07-20 16:45:31,025 - PinBet - WARNING - 主动刷新失败: 会话已失效，需要重新登录
2025-07-20 16:45:31,025 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 16:45:31,025 - PinBet - INFO - 开始查询未结算注单
2025-07-20 16:45:31,354 - PinBet - WARNING - 获取未结算注单失败: 权限不足，检测到会话失效
2025-07-20 16:45:31,354 - PinBet - INFO - 清除平博登录会话
2025-07-20 16:45:31,355 - platforms.smart_login_manager - INFO - 清除会话数据
2025-07-20 16:45:31,355 - PinBet - INFO - 登录会话已清除
2025-07-20 16:45:31,355 - PinBet - INFO - 尝试重新登录以恢复查询会话
2025-07-20 16:45:31,355 - PinBet - INFO - 尝试自动登录平博账号: mfc123
2025-07-20 16:45:31,786 - PinBet - INFO - 登录成功(带tokens): mfc123
2025-07-20 16:45:32,211 - PinBet - INFO - 余额获取成功: 26778.74
2025-07-20 16:45:32,212 - platforms.smart_login_manager - INFO - 记录登录尝试: 成功=True, 错误码=None, 消息=登录成功(带tokens)
2025-07-20 16:45:32,213 - platforms.smart_login_manager - INFO - 保存登录会话信息
2025-07-20 16:45:32,213 - PinBet - INFO - 重新登录成功，请重试查询操作
2025-07-20 16:45:32,213 - __main__ - WARNING - [system] 获取平博记录失败
2025-07-20 16:45:32,213 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 16:45:34,290 - PinBet - ERROR - 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 16:45:34,290 - PinBet - WARNING - 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 16:45:34,291 - PinBet - WARNING - 主动刷新失败: 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 16:45:34,291 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 16:45:34,291 - PinBet - INFO - 开始查询未结算注单
2025-07-20 16:45:35,032 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 16:45:35,032 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 16:45:35,033 - __main__ - INFO - [system] 加载平博记录 2 条
2025-07-20 16:45:35,033 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 16:45:44,833 - __main__ - INFO - [system] 切换到皇冠记录显示模式
2025-07-20 16:45:45,249 - __main__ - INFO - [system] 正在查询皇冠投注记录...
2025-07-20 16:45:45,250 - platforms.hgbet_bk - INFO - 直接获取今日BK注单
2025-07-20 16:45:45,457 - platforms.hgbet_bk - INFO - 今日总投注金额: 2,128
2025-07-20 16:45:45,458 - platforms.hgbet_bk - INFO - 找到 3 条注单
2025-07-20 16:45:45,468 - __main__ - INFO - [system] 加载皇冠记录 3 条
2025-07-20 16:45:45,468 - __main__ - INFO - [system] 皇冠投注记录查询完成
2025-07-20 16:46:22,391 - PinBet - INFO - 余额获取成功: 26778.74
2025-07-20 16:46:22,600 - __main__ - INFO - [system] 开启对冲监控
2025-07-20 16:46:22,600 - hedge.hedge_integration - INFO - 开始监控对冲机会，检查间隔: 4秒
2025-07-20 16:46:22,600 - hedge.hedge_integration - INFO - 开始监控对冲机会，检查间隔: 4秒
2025-07-20 16:49:54,439 - __main__ - INFO - [system] 切换到对冲记录显示模式
2025-07-20 16:49:57,244 - __main__ - INFO - [system] 切换到单边待补单显示模式
2025-07-20 16:49:58,409 - __main__ - INFO - [system] 切换到对冲记录显示模式
2025-07-20 16:49:58,749 - __main__ - INFO - [system] 正在查询对冲记录...
2025-07-20 16:49:58,750 - __main__ - INFO - [system] 优先加载当天对冲记录: hedge_records_2025-07-20.json
2025-07-20 16:49:58,750 - __main__ - INFO - [system] 显示当天对冲记录 1 条
2025-07-20 16:49:58,751 - __main__ - INFO - [system] 加载对冲记录 1 条 (来自 1 个文件)
2025-07-20 16:49:58,752 - __main__ - INFO - [system] 对冲记录查询完成
2025-07-20 16:50:01,447 - __main__ - INFO - [system] 切换到皇冠记录显示模式
2025-07-20 16:52:52,539 - __main__ - INFO - [system] 正在查询皇冠投注记录...
2025-07-20 16:52:52,540 - platforms.hgbet_bk - INFO - 直接获取今日BK注单
2025-07-20 16:52:52,758 - platforms.hgbet_bk - INFO - 今日总投注金额: 2,128
2025-07-20 16:52:52,758 - platforms.hgbet_bk - INFO - 找到 3 条注单
2025-07-20 16:52:52,763 - __main__ - INFO - [system] 加载皇冠记录 3 条
2025-07-20 16:52:52,764 - __main__ - INFO - [system] 皇冠投注记录查询完成
2025-07-20 17:16:39,642 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 17:16:40,042 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 17:16:40,043 - PinBet - INFO - 会话空闲7734秒，执行主动刷新以保持活跃
2025-07-20 17:17:01,045 - PinBet - ERROR - 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:17:01,045 - PinBet - WARNING - 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:17:01,045 - PinBet - WARNING - 主动刷新失败: 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:17:01,046 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 17:17:01,046 - PinBet - INFO - 开始查询未结算注单
2025-07-20 17:17:02,124 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 17:17:02,124 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 17:17:02,150 - __main__ - INFO - [system] 加载平博记录 2 条
2025-07-20 17:17:02,150 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 17:32:40,484 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 17:32:40,484 - PinBet - INFO - 会话空闲8695秒，执行主动刷新以保持活跃
2025-07-20 17:32:59,790 - PinBet - ERROR - 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:32:59,790 - PinBet - WARNING - 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:32:59,790 - PinBet - WARNING - 主动刷新失败: 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:32:59,791 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 17:32:59,791 - PinBet - INFO - 开始查询未结算注单
2025-07-20 17:33:00,529 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 17:33:00,529 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 17:33:00,530 - __main__ - INFO - [system] 加载平博记录 2 条
2025-07-20 17:33:00,530 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 17:58:41,283 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 17:58:41,284 - PinBet - INFO - 会话空闲10255秒，执行主动刷新以保持活跃
2025-07-20 17:59:04,096 - PinBet - ERROR - 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:59:04,096 - PinBet - WARNING - 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:59:04,097 - PinBet - WARNING - 主动刷新失败: 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 17:59:04,097 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 17:59:04,097 - PinBet - INFO - 开始查询未结算注单
2025-07-20 17:59:04,830 - PinBet - WARNING - 获取未结算注单失败: 权限不足，检测到会话失效
2025-07-20 17:59:04,830 - PinBet - INFO - 清除平博登录会话
2025-07-20 17:59:04,831 - platforms.smart_login_manager - INFO - 清除会话数据
2025-07-20 17:59:04,831 - PinBet - INFO - 登录会话已清除
2025-07-20 17:59:04,831 - PinBet - INFO - 尝试重新登录以恢复查询会话
2025-07-20 17:59:04,831 - PinBet - INFO - 尝试自动登录平博账号: mfc123
2025-07-20 17:59:05,256 - PinBet - INFO - 登录成功(带tokens): mfc123
2025-07-20 17:59:05,641 - PinBet - INFO - 余额获取成功: 26770.74
2025-07-20 17:59:05,644 - platforms.smart_login_manager - INFO - 记录登录尝试: 成功=True, 错误码=None, 消息=登录成功(带tokens)
2025-07-20 17:59:05,644 - platforms.smart_login_manager - INFO - 保存登录会话信息
2025-07-20 17:59:05,645 - PinBet - INFO - 重新登录成功，请重试查询操作
2025-07-20 17:59:05,645 - __main__ - WARNING - [system] 获取平博记录失败
2025-07-20 17:59:05,645 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 17:59:14,028 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 17:59:14,030 - PinBet - INFO - 会话空闲10288秒，执行主动刷新以保持活跃
2025-07-20 17:59:14,392 - PinBet - INFO - 余额获取成功: 26770.74
2025-07-20 17:59:14,392 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 17:59:14,392 - PinBet - INFO - 开始查询未结算注单
2025-07-20 17:59:14,777 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 17:59:14,777 - PinBet - INFO - 处理完成: 原始记录 3 条，处理后 3 条记录
2025-07-20 17:59:14,791 - __main__ - INFO - [system] 加载平博记录 3 条
2025-07-20 17:59:14,792 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 17:59:57,577 - __main__ - INFO - [system] 切换到皇冠记录显示模式
2025-07-20 17:59:57,985 - __main__ - INFO - [system] 正在查询皇冠投注记录...
2025-07-20 17:59:57,986 - platforms.hgbet_bk - INFO - 直接获取今日BK注单
2025-07-20 17:59:58,228 - platforms.hgbet_bk - INFO - 今日总投注金额: 2,000
2025-07-20 17:59:58,228 - platforms.hgbet_bk - INFO - 找到 2 条注单
2025-07-20 17:59:58,232 - __main__ - INFO - [system] 加载皇冠记录 2 条
2025-07-20 17:59:58,232 - __main__ - INFO - [system] 皇冠投注记录查询完成
2025-07-20 18:09:36,964 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 18:09:37,228 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 18:09:37,229 - PinBet - INFO - 会话空闲623秒，执行主动刷新以保持活跃
2025-07-20 18:09:56,485 - PinBet - ERROR - 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 18:09:56,485 - PinBet - WARNING - 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 18:09:56,485 - PinBet - WARNING - 主动刷新失败: 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 18:09:56,485 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 18:09:56,486 - PinBet - INFO - 开始查询未结算注单
2025-07-20 18:09:57,590 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 18:09:57,590 - PinBet - INFO - 处理完成: 原始记录 3 条，处理后 3 条记录
2025-07-20 18:09:57,592 - __main__ - INFO - [system] 加载平博记录 3 条
2025-07-20 18:09:57,592 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 18:19:47,177 - __main__ - INFO - [system] 切换到皇冠记录显示模式
2025-07-20 18:19:47,539 - __main__ - INFO - [system] 正在查询皇冠投注记录...
2025-07-20 18:19:47,540 - platforms.hgbet_bk - INFO - 直接获取今日BK注单
2025-07-20 18:19:47,765 - platforms.hgbet_bk - INFO - 今日总投注金额: 2,000
2025-07-20 18:19:47,765 - platforms.hgbet_bk - INFO - 找到 2 条注单
2025-07-20 18:19:47,769 - __main__ - INFO - [system] 加载皇冠记录 2 条
2025-07-20 18:19:47,770 - __main__ - INFO - [system] 皇冠投注记录查询完成
2025-07-20 18:47:43,288 - __main__ - INFO - [system] 正在停止对冲监控...
2025-07-20 18:47:43,288 - hedge.hedge_integration - INFO - 收到停止监控信号
2025-07-20 18:47:43,288 - hedge.hedge_integration - INFO - 收到停止监控信号
2025-07-20 18:47:44,193 - hedge.hedge_integration - INFO - 对冲监控已停止
2025-07-20 18:47:44,193 - hedge.hedge_integration - INFO - 对冲监控已停止
2025-07-20 18:47:44,194 - __main__ - INFO - [system] 对冲监控已成功停止
2025-07-20 19:13:57,521 - __main__ - INFO - GUI日志将保存到: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 19:13:58,283 - __main__ - INFO - [system] BetBurger对冲投注系统GUI已启动
2025-07-20 19:13:58,284 - __main__ - INFO - [system] 请先点击'初始化系统'，然后'登录系统'
2025-07-20 19:13:58,395 - __main__ - INFO - [system] 配置加载成功
2025-07-20 19:13:58,395 - __main__ - INFO - [GUI] GUI程序启动，日志文件: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 19:14:01,882 - __main__ - INFO - [GUI] 正在加载最近日志...
2025-07-20 19:14:01,889 - __main__ - INFO - [GUI] 最近日志加载完成，共显示 100 条日志
2025-07-20 19:14:01,889 - __main__ - INFO - [system] 正在初始化系统...
2025-07-20 19:14:01,889 - hedge_main - INFO - 初始化对冲投注系统
2025-07-20 19:14:01,913 - utils.config_security - INFO - 加载现有加密密钥
2025-07-20 19:14:01,913 - utils.config_security - INFO - 加载未加密的配置文件
2025-07-20 19:14:01,913 - config.hedge_config - INFO - 对冲配置首次加载完成
2025-07-20 19:14:01,913 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 19:14:01,913 - config.hedge_config - WARNING - 参数 enable_rollback 不是布尔值, 使用默认值: False
2025-07-20 19:14:01,913 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 19:14:01,914 - utils.match_data_fetcher - INFO - 比赛数据获取器初始化完成，缓存目录: data/match_cache
2025-07-20 19:14:01,914 - utils.match_data_fetcher - INFO - 比赛数据获取器初始化完成，缓存目录: data/match_cache
2025-07-20 19:14:01,914 - utils.match_learning_enhanced - INFO - 增强匹配学习系统初始化完成，数据目录: data/match_learning
2025-07-20 19:14:01,915 - utils.alias_manager - INFO - 别名管理器初始化完成，配置目录: config
2025-07-20 19:14:01,915 - utils.match_learning_controller - INFO - 比赛匹配学习控制器初始化完成
2025-07-20 19:14:01,915 - hedge_main - INFO - 正在初始化投注系统...
2025-07-20 19:14:01,916 - hedge_main - INFO - 平博系统初始化完成
2025-07-20 19:14:01,916 - platforms.hgbet_bk - INFO - 初始化皇冠篮球投注模块
2025-07-20 19:14:01,917 - platforms.hgbet_bk - INFO - 投注记录将保存到: data\bet_records_2025-07-20.json
2025-07-20 19:14:01,917 - hedge_main - INFO - 皇冠系统初始化完成
2025-07-20 19:14:01,917 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 19:14:01,917 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 19:14:01,917 - validation.match_validator - INFO - 比赛匹配验证器初始化完成
2025-07-20 19:14:01,917 - validation.match_safety - INFO - 比赛匹配安全检查器初始化完成
2025-07-20 19:14:01,917 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 19:14:01,917 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 19:14:01,917 - hedge.hedge_calculator - INFO - 投注金额计算器初始化完成
2025-07-20 19:14:01,917 - hedge.hedge_monitor - INFO - 投注监控器初始化完成
2025-07-20 19:14:01,917 - hedge.hedge_record_manager - INFO - 对冲记录管理器初始化完成
2025-07-20 19:14:01,917 - hedge.hedge_manager - INFO - 对冲记录管理器初始化成功
2025-07-20 19:14:01,917 - hedge.partial_record_manager - INFO - 单边记录管理器初始化完成
2025-07-20 19:14:01,917 - hedge.hedge_manager - INFO - 单边记录管理器初始化成功
2025-07-20 19:14:01,918 - hedge.hedge_manager - INFO - 对冲管理器初始化完成
2025-07-20 19:14:01,918 - hedge.hedge_integration - INFO - 对冲集成器初始化完成（实时匹配模式）
2025-07-20 19:14:01,918 - hedge_main - INFO - 对冲集成器初始化完成
2025-07-20 19:14:01,918 - __main__ - INFO - [system] 系统初始化成功
2025-07-20 19:14:01,918 - __main__ - INFO - [GUI] 对冲系统日志转发设置完成
2025-07-20 19:14:03,852 - __main__ - INFO - [system] 正在登录系统...
2025-07-20 19:14:03,852 - hedge_main - INFO - 正在登录投注系统...
2025-07-20 19:14:03,852 - platforms.smart_login_manager - INFO - 会话仍然有效，剩余时间: 10:45:01.792062
2025-07-20 19:14:03,852 - PinBet - INFO - 检测到现有会话，验证有效性
2025-07-20 19:14:04,577 - PinBet - WARNING - 余额API返回success=False，会话可能已失效
2025-07-20 19:14:04,577 - PinBet - INFO - 清除平博登录会话
2025-07-20 19:14:04,577 - platforms.smart_login_manager - INFO - 清除会话数据
2025-07-20 19:14:04,579 - PinBet - INFO - 登录会话已清除
2025-07-20 19:14:04,579 - PinBet - WARNING - 现有会话验证失败，清除会话并重新登录
2025-07-20 19:14:04,579 - PinBet - INFO - 清除平博登录会话
2025-07-20 19:14:04,579 - platforms.smart_login_manager - INFO - 清除会话数据
2025-07-20 19:14:04,579 - PinBet - INFO - 登录会话已清除
2025-07-20 19:14:04,580 - PinBet - INFO - 尝试自动登录平博账号: mfc123
2025-07-20 19:14:05,025 - PinBet - INFO - 登录成功(带tokens): mfc123
2025-07-20 19:14:05,378 - PinBet - INFO - 余额获取成功: 26770.74
2025-07-20 19:14:05,379 - platforms.smart_login_manager - INFO - 记录登录尝试: 成功=True, 错误码=None, 消息=登录成功(带tokens)
2025-07-20 19:14:05,380 - platforms.smart_login_manager - INFO - 保存登录会话信息
2025-07-20 19:14:05,747 - PinBet - INFO - 余额获取成功: 26770.74
2025-07-20 19:14:05,747 - platforms.hgbet_bk - INFO - 尝试登录...
2025-07-20 19:14:08,199 - platforms.hgbet_bk - INFO - 登录状态码: 200
2025-07-20 19:14:08,199 - platforms.hgbet_bk - INFO - 提取到UID: qatd8kvgfm38245145l6565097b1
2025-07-20 19:14:08,406 - platforms.hgbet_bk - INFO - 登录成功，当前余额：10158.9，版本号: 2025-04-30-CRM-55_89
2025-07-20 19:14:08,406 - hedge_main - INFO - 所有系统登录成功
2025-07-20 19:14:08,407 - hedge_main - INFO - 正在初始化比赛匹配学习系统...
2025-07-20 19:14:08,407 - utils.match_learning_controller - INFO - 开始初始化比赛匹配学习系统
2025-07-20 19:14:08,407 - utils.match_learning_controller - INFO - 需要获取新的平台数据
2025-07-20 19:14:08,407 - utils.match_data_fetcher - INFO - 开始获取所有平台比赛数据
2025-07-20 19:14:08,407 - utils.match_data_fetcher - INFO - 获取平博平台比赛数据
2025-07-20 19:14:08,778 - utils.match_data_fetcher - ERROR - 解析平博比赛数据失败: 'NoneType' object is not iterable
2025-07-20 19:14:08,778 - utils.match_data_fetcher - INFO - 平博今日比赛 (mk=0): 0 场
2025-07-20 19:14:09,276 - utils.match_data_fetcher - INFO - 平博今日比赛 (mk=1): 5 场
2025-07-20 19:14:09,277 - utils.match_data_fetcher - INFO - 平博今日比赛总计: 5 场
2025-07-20 19:14:09,645 - utils.match_data_fetcher - ERROR - 解析平博比赛数据失败: 'NoneType' object is not iterable
2025-07-20 19:14:09,645 - utils.match_data_fetcher - INFO - 平博早场比赛 (mk=0): 0 场
2025-07-20 19:14:10,161 - utils.match_data_fetcher - INFO - 平博早场比赛 (mk=1): 5 场
2025-07-20 19:14:10,161 - utils.match_data_fetcher - INFO - 平博早场比赛总计: 5 场
2025-07-20 19:14:10,162 - utils.match_data_fetcher - INFO - 平博数据获取成功
2025-07-20 19:14:10,162 - utils.match_data_fetcher - INFO - 获取皇冠平台比赛数据
2025-07-20 19:14:10,162 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 19:14:10,373 - platforms.hgbet_bk - INFO - 获取到 29 场比赛
2025-07-20 19:14:10,373 - utils.match_data_fetcher - INFO - 皇冠今日比赛: 29 场
2025-07-20 19:14:10,374 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 19:14:10,570 - platforms.hgbet_bk - INFO - 获取到 5 场比赛
2025-07-20 19:14:10,571 - utils.match_data_fetcher - INFO - 皇冠早场比赛: 5 场
2025-07-20 19:14:10,571 - utils.match_data_fetcher - INFO - 皇冠数据获取成功
2025-07-20 19:14:10,572 - utils.match_data_fetcher - INFO - 缓存数据已保存到: data/match_cache\platform_matches.json
2025-07-20 19:14:10,573 - utils.match_data_fetcher - INFO - 队伍名和联赛名已保存
2025-07-20 19:14:10,573 - utils.match_data_fetcher - INFO - 成功获取 2 个平台的数据
2025-07-20 19:14:10,573 - utils.match_learning_controller - INFO - 平台数据获取成功
2025-07-20 19:14:10,573 - utils.match_learning_controller - INFO - 比赛匹配学习系统初始化完成
2025-07-20 19:14:10,576 - utils.match_learning_controller - INFO - === 比赛匹配学习系统初始化摘要 ===
2025-07-20 19:14:10,576 - utils.match_learning_controller - INFO - 系统状态: 已初始化
2025-07-20 19:14:10,576 - utils.match_learning_controller - INFO - pinbet平台: 总计10场比赛 (今日5场, 早场5场)
2025-07-20 19:14:10,576 - utils.match_learning_controller - INFO - crown平台: 总计34场比赛 (今日29场, 早场5场)
2025-07-20 19:14:10,576 - utils.match_learning_controller - INFO - 当前别名配置: 3个联赛, 11支队伍, 35个别名
2025-07-20 19:14:10,576 - utils.match_learning_controller - INFO - 学习统计: 总失败26次, 成功学习0次, 学习率0.0%
2025-07-20 19:14:10,576 - utils.match_learning_controller - INFO - === 初始化摘要结束 ===
2025-07-20 19:14:10,577 - hedge_main - INFO - 比赛匹配学习系统初始化完成
2025-07-20 19:14:10,578 - hedge_main - INFO - pinbet平台数据: 10场比赛
2025-07-20 19:14:10,578 - hedge_main - INFO - crown平台数据: 34场比赛
2025-07-20 19:14:10,578 - __main__ - INFO - [system] 系统登录成功
2025-07-20 19:14:10,931 - PinBet - INFO - 余额获取成功: 26770.74
2025-07-20 19:14:16,317 - PinBet - INFO - 余额获取成功: 26770.74
2025-07-20 19:14:16,507 - __main__ - INFO - [system] 开启对冲监控
2025-07-20 19:14:16,508 - hedge.hedge_integration - INFO - 开始监控对冲机会，检查间隔: 4秒
2025-07-20 19:14:16,508 - hedge.hedge_integration - INFO - 开始监控对冲机会，检查间隔: 4秒
2025-07-20 19:20:32,999 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 19:20:33,000 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 19:20:33,000 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 19:20:33,000 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 19:20:33,000 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 圣米格尔比尔曼 - TNT短途篮球队 vs 圣米格尔比尔曼 - TNT短途篮球队
2025-07-20 19:20:33,000 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 圣米格尔比尔曼 - TNT短途篮球队 vs 圣米格尔比尔曼 - TNT短途篮球队
2025-07-20 19:20:33,000 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 19:20:33,000 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 19:20:33,001 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 19:20:33,001 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 19:20:33,001 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 19:20:33,001 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 19:20:33,001 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 19:20:33,001 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 19:20:33,001 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 19:20:33,001 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 19:20:33,001 - hedge.execution_lock - INFO - 投注执行锁管理器初始化完成
2025-07-20 19:20:33,002 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:圣米格尔比尔曼 - TNT短途篮球队, 锁ID: hedge:圣米格尔比尔曼 - TNT短途篮球队:1753010433, 超时: 30秒
2025-07-20 19:20:33,002 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 19:20:33,002 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=907.08, 净利润=-299.08
2025-07-20 19:20:33,002 - hedge_system - INFO - 开始对冲投注: 圣米格尔比尔曼 - TNT短途篮球队
2025-07-20 19:20:33,002 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=907.08, 总投资=1907.08
2025-07-20 19:20:33,002 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=907.08, 总投资=1907.08
2025-07-20 19:20:33,002 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 19:20:33,002 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 19:20:33,002 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 19:20:33,002 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 19:20:33,003 - hedge.hedge_metrics - INFO - 对冲投注监控指标管理器初始化完成
2025-07-20 19:20:33,003 - platforms.hgbet_bk - INFO - 解析到投注信息: 圣米格尔比尔曼 vs TNT短途篮球队, 市场类型: 19, 盘口: 205.5, 赔率: 2.01
2025-07-20 19:20:33,003 - platforms.hgbet_bk - INFO - 转换后的赔率值: 2.01
2025-07-20 19:20:33,003 - platforms.hgbet_bk - INFO - 处理市场类型 19
2025-07-20 19:20:33,003 - platforms.hgbet_bk - INFO - 解析为大球: 205.5
2025-07-20 19:20:33,003 - platforms.hgbet_bk - INFO - 检查重复投注: 圣米格尔比尔曼 vs TNT短途篮球队, 大小球, 投注方向=大, 时段=全场, 比赛ID=410141244, 盘口值=205.5
2025-07-20 19:20:33,006 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 19:20:33,006 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 19:20:33,006 - platforms.hgbet_bk - INFO - 投注信息: 圣米格尔比尔曼 vs TNT短途篮球队, 大小球=大, 盘口: 205.5, 赔率: 2.01
2025-07-20 19:20:33,006 - platforms.hgbet_bk - INFO - 开始执行投注流程
2025-07-20 19:20:33,006 - platforms.hgbet_bk - INFO - 投注信息: 圣米格尔比尔曼 vs TNT短途篮球队, 大小球=大, 盘口: 205.5, 赔率: 2.01
2025-07-20 19:20:33,006 - platforms.hgbet_bk - INFO - 比赛投注次数检查: tnt短途篮球队_vs_圣米格尔比尔曼, 当前次数: 0, 限制: 2
2025-07-20 19:20:33,007 - platforms.hgbet_bk - INFO - 比赛投注次数检查通过: tnt短途篮球队_vs_圣米格尔比尔曼, 可继续投注
2025-07-20 19:20:33,218 - platforms.hgbet_bk - INFO - 等待3秒后执行投注...
2025-07-20 19:20:36,219 - platforms.hgbet_bk - INFO - 投注比赛: 圣米格尔比尔曼 vs TNT短途篮球队
2025-07-20 19:20:36,219 - platforms.hgbet_bk - INFO - 投注类型: 大小球, 投注队伍: 大, 盘口: 205.5, 比赛阶段: 全场
2025-07-20 19:20:36,219 - platforms.hgbet_bk - INFO - 使用传入的投注金额: 1000.0
2025-07-20 19:20:36,219 - platforms.hgbet_bk - INFO - 最终确定的投注金额: 1000.0 (类型: <class 'float'>)
2025-07-20 19:20:36,219 - platforms.hgbet_bk - INFO - 确认投注金额: 1000.0, 赔率: 2.01
2025-07-20 19:20:36,219 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 19:20:36,458 - platforms.hgbet_bk - INFO - 获取到 28 场比赛
2025-07-20 19:20:36,459 - platforms.hgbet_bk - WARNING - 比赛ID 410141244 在today比赛列表中不存在，尝试在early中查找
2025-07-20 19:20:36,459 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 19:20:36,668 - platforms.hgbet_bk - INFO - 获取到 5 场比赛
2025-07-20 19:20:36,668 - platforms.hgbet_bk - WARNING - 比赛ID 410141244 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-20 19:20:36,684 - platforms.hgbet_bk - WARNING - 未找到匹配的比赛ID（最高分数: 0.00，阈值: 0.48），无法继续投注
2025-07-20 19:20:36,685 - utils.match_learning - INFO - 记录匹配失败: 2025-07-20T19:20:36.684190_BetBurger_皇冠, 类别: team_mismatch
2025-07-20 19:20:36,685 - hedge.hedge_manager - INFO - crown投注失败，1.0秒后进行第2次尝试: 投注失败
2025-07-20 19:20:36,685 - hedge.hedge_manager - INFO - crown投注失败，1.0秒后进行第2次尝试: 投注失败
2025-07-20 19:20:37,699 - platforms.hgbet_bk - INFO - 解析到投注信息: 圣米格尔比尔曼 vs TNT短途篮球队, 市场类型: 19, 盘口: 205.5, 赔率: 2.01
2025-07-20 19:20:37,699 - platforms.hgbet_bk - INFO - 转换后的赔率值: 2.01
2025-07-20 19:20:37,699 - platforms.hgbet_bk - INFO - 处理市场类型 19
2025-07-20 19:20:37,700 - platforms.hgbet_bk - INFO - 解析为大球: 205.5
2025-07-20 19:20:37,700 - platforms.hgbet_bk - INFO - 检查重复投注: 圣米格尔比尔曼 vs TNT短途篮球队, 大小球, 投注方向=大, 时段=全场, 比赛ID=410141244, 盘口值=205.5
2025-07-20 19:20:37,701 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 19:20:37,701 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 19:20:37,701 - platforms.hgbet_bk - INFO - 开始执行投注流程
2025-07-20 19:20:37,701 - platforms.hgbet_bk - INFO - 投注信息: 圣米格尔比尔曼 vs TNT短途篮球队, 大小球=大, 盘口: 205.5, 赔率: 2.01
2025-07-20 19:20:37,701 - platforms.hgbet_bk - INFO - 比赛投注次数检查: tnt短途篮球队_vs_圣米格尔比尔曼, 当前次数: 0, 限制: 2
2025-07-20 19:20:37,701 - platforms.hgbet_bk - INFO - 比赛投注次数检查通过: tnt短途篮球队_vs_圣米格尔比尔曼, 可继续投注
2025-07-20 19:20:37,897 - platforms.hgbet_bk - INFO - 等待3秒后执行投注...
2025-07-20 19:20:40,898 - platforms.hgbet_bk - INFO - 投注比赛: 圣米格尔比尔曼 vs TNT短途篮球队
2025-07-20 19:20:40,898 - platforms.hgbet_bk - INFO - 投注类型: 大小球, 投注队伍: 大, 盘口: 205.5, 比赛阶段: 全场
2025-07-20 19:20:40,898 - platforms.hgbet_bk - INFO - 使用传入的投注金额: 1000.0
2025-07-20 19:20:40,898 - platforms.hgbet_bk - INFO - 最终确定的投注金额: 1000.0 (类型: <class 'float'>)
2025-07-20 19:20:40,899 - platforms.hgbet_bk - INFO - 确认投注金额: 1000.0, 赔率: 2.01
2025-07-20 19:20:40,899 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 19:20:41,116 - platforms.hgbet_bk - INFO - 获取到 28 场比赛
2025-07-20 19:20:41,116 - platforms.hgbet_bk - WARNING - 比赛ID 410141244 在today比赛列表中不存在，尝试在early中查找
2025-07-20 19:20:41,116 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 19:20:41,334 - platforms.hgbet_bk - INFO - 获取到 5 场比赛
2025-07-20 19:20:41,335 - platforms.hgbet_bk - WARNING - 比赛ID 410141244 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-20 19:20:41,346 - platforms.hgbet_bk - WARNING - 未找到匹配的比赛ID（最高分数: 0.00，阈值: 0.48），无法继续投注
2025-07-20 19:20:41,349 - utils.match_learning - INFO - 记录匹配失败: 2025-07-20T19:20:41.346864_BetBurger_皇冠, 类别: team_mismatch
2025-07-20 19:20:41,349 - hedge.hedge_manager - WARNING - crown投注失败，不适合重试: 投注失败
2025-07-20 19:20:41,349 - hedge.hedge_manager - WARNING - crown投注失败，不适合重试: 投注失败
2025-07-20 19:20:41,350 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 投注失败
2025-07-20 19:20:41,350 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 投注失败
2025-07-20 19:20:41,350 - hedge_system - WARNING - 对冲投注失败: 圣米格尔比尔曼 - TNT短途篮球队
2025-07-20 19:20:41,350 - hedge.hedge_manager - INFO - 对冲投注完全失败，不保存记录
2025-07-20 19:20:41,350 - hedge.hedge_manager - INFO - 对冲投注完全失败，不保存记录
2025-07-20 19:20:41,350 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:圣米格尔比尔曼 - TNT短途篮球队, 锁ID: hedge:圣米格尔比尔曼 - TNT短途篮球队:1753010433
2025-07-20 19:20:41,351 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 19:20:41,351 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 19:20:41,351 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 19:20:41,351 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 19:20:46,544 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 19:20:46,546 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 19:20:46,546 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 19:20:46,546 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 19:20:46,547 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 圣米格尔比尔曼 - TNT短途篮球队 vs 圣米格尔比尔曼 - TNT短途篮球队
2025-07-20 19:20:46,547 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 圣米格尔比尔曼 - TNT短途篮球队 vs 圣米格尔比尔曼 - TNT短途篮球队
2025-07-20 19:20:46,547 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 19:20:46,547 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 19:20:46,547 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 19:20:46,547 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 19:20:46,547 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 19:20:46,547 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 19:20:46,547 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 19:20:46,547 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 19:20:46,547 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 19:20:46,547 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 19:20:46,547 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:圣米格尔比尔曼 - TNT短途篮球队, 锁ID: hedge:圣米格尔比尔曼 - TNT短途篮球队:1753010446, 超时: 30秒
2025-07-20 19:20:46,548 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 19:20:46,548 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=911.75, 净利润=-303.75
2025-07-20 19:20:46,548 - hedge_system - INFO - 开始对冲投注: 圣米格尔比尔曼 - TNT短途篮球队
2025-07-20 19:20:46,548 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=911.75, 总投资=1911.75
2025-07-20 19:20:46,548 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=911.75, 总投资=1911.75
2025-07-20 19:20:46,548 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 19:20:46,548 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 19:20:46,548 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 19:20:46,548 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 19:20:46,548 - platforms.hgbet_bk - INFO - 解析到投注信息: 圣米格尔比尔曼 vs TNT短途篮球队, 市场类型: 19, 盘口: 205.5, 赔率: 2.01
2025-07-20 19:20:46,548 - platforms.hgbet_bk - INFO - 转换后的赔率值: 2.01
2025-07-20 19:20:46,548 - platforms.hgbet_bk - INFO - 处理市场类型 19
2025-07-20 19:20:46,548 - platforms.hgbet_bk - INFO - 解析为大球: 205.5
2025-07-20 19:20:46,549 - platforms.hgbet_bk - INFO - 检查重复投注: 圣米格尔比尔曼 vs TNT短途篮球队, 大小球, 投注方向=大, 时段=全场, 比赛ID=410141244, 盘口值=205.5
2025-07-20 19:20:46,549 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 19:20:46,549 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 19:20:46,550 - platforms.hgbet_bk - INFO - 开始执行投注流程
2025-07-20 19:20:46,550 - platforms.hgbet_bk - INFO - 投注信息: 圣米格尔比尔曼 vs TNT短途篮球队, 大小球=大, 盘口: 205.5, 赔率: 2.01
2025-07-20 19:20:46,550 - platforms.hgbet_bk - INFO - 比赛投注次数检查: tnt短途篮球队_vs_圣米格尔比尔曼, 当前次数: 0, 限制: 2
2025-07-20 19:20:46,550 - platforms.hgbet_bk - INFO - 比赛投注次数检查通过: tnt短途篮球队_vs_圣米格尔比尔曼, 可继续投注
2025-07-20 19:20:46,755 - platforms.hgbet_bk - INFO - 等待3秒后执行投注...
2025-07-20 19:20:49,756 - platforms.hgbet_bk - INFO - 投注比赛: 圣米格尔比尔曼 vs TNT短途篮球队
2025-07-20 19:20:49,756 - platforms.hgbet_bk - INFO - 投注类型: 大小球, 投注队伍: 大, 盘口: 205.5, 比赛阶段: 全场
2025-07-20 19:20:49,756 - platforms.hgbet_bk - INFO - 使用传入的投注金额: 1000.0
2025-07-20 19:20:49,756 - platforms.hgbet_bk - INFO - 最终确定的投注金额: 1000.0 (类型: <class 'float'>)
2025-07-20 19:20:49,756 - platforms.hgbet_bk - INFO - 确认投注金额: 1000.0, 赔率: 2.01
2025-07-20 19:20:49,756 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 19:20:49,983 - platforms.hgbet_bk - INFO - 获取到 28 场比赛
2025-07-20 19:20:49,984 - platforms.hgbet_bk - WARNING - 比赛ID 410141244 在today比赛列表中不存在，尝试在early中查找
2025-07-20 19:20:49,984 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 19:20:50,178 - platforms.hgbet_bk - INFO - 获取到 5 场比赛
2025-07-20 19:20:50,178 - platforms.hgbet_bk - WARNING - 比赛ID 410141244 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-20 19:20:50,190 - platforms.hgbet_bk - WARNING - 未找到匹配的比赛ID（最高分数: 0.00，阈值: 0.48），无法继续投注
2025-07-20 19:20:50,193 - utils.match_learning - INFO - 记录匹配失败: 2025-07-20T19:20:50.190295_BetBurger_皇冠, 类别: team_mismatch
2025-07-20 19:20:50,193 - hedge.hedge_manager - INFO - crown投注失败，1.0秒后进行第2次尝试: 投注失败
2025-07-20 19:20:50,193 - hedge.hedge_manager - INFO - crown投注失败，1.0秒后进行第2次尝试: 投注失败
2025-07-20 19:20:51,196 - platforms.hgbet_bk - INFO - 解析到投注信息: 圣米格尔比尔曼 vs TNT短途篮球队, 市场类型: 19, 盘口: 205.5, 赔率: 2.01
2025-07-20 19:20:51,196 - platforms.hgbet_bk - INFO - 转换后的赔率值: 2.01
2025-07-20 19:20:51,198 - platforms.hgbet_bk - INFO - 处理市场类型 19
2025-07-20 19:20:51,198 - platforms.hgbet_bk - INFO - 解析为大球: 205.5
2025-07-20 19:20:51,199 - platforms.hgbet_bk - INFO - 检查重复投注: 圣米格尔比尔曼 vs TNT短途篮球队, 大小球, 投注方向=大, 时段=全场, 比赛ID=410141244, 盘口值=205.5
2025-07-20 19:20:51,200 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 19:20:51,200 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 19:20:51,201 - platforms.hgbet_bk - INFO - 开始执行投注流程
2025-07-20 19:20:51,201 - platforms.hgbet_bk - INFO - 投注信息: 圣米格尔比尔曼 vs TNT短途篮球队, 大小球=大, 盘口: 205.5, 赔率: 2.01
2025-07-20 19:20:51,201 - platforms.hgbet_bk - INFO - 比赛投注次数检查: tnt短途篮球队_vs_圣米格尔比尔曼, 当前次数: 0, 限制: 2
2025-07-20 19:20:51,202 - platforms.hgbet_bk - INFO - 比赛投注次数检查通过: tnt短途篮球队_vs_圣米格尔比尔曼, 可继续投注
2025-07-20 19:20:51,414 - platforms.hgbet_bk - INFO - 等待3秒后执行投注...
2025-07-20 19:20:54,416 - platforms.hgbet_bk - INFO - 投注比赛: 圣米格尔比尔曼 vs TNT短途篮球队
2025-07-20 19:20:54,416 - platforms.hgbet_bk - INFO - 投注类型: 大小球, 投注队伍: 大, 盘口: 205.5, 比赛阶段: 全场
2025-07-20 19:20:54,416 - platforms.hgbet_bk - INFO - 使用传入的投注金额: 1000.0
2025-07-20 19:20:54,416 - platforms.hgbet_bk - INFO - 最终确定的投注金额: 1000.0 (类型: <class 'float'>)
2025-07-20 19:20:54,417 - platforms.hgbet_bk - INFO - 确认投注金额: 1000.0, 赔率: 2.01
2025-07-20 19:20:54,417 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 19:20:54,627 - platforms.hgbet_bk - INFO - 获取到 28 场比赛
2025-07-20 19:20:54,627 - platforms.hgbet_bk - WARNING - 比赛ID 410141244 在today比赛列表中不存在，尝试在early中查找
2025-07-20 19:20:54,628 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 19:20:54,840 - platforms.hgbet_bk - INFO - 获取到 5 场比赛
2025-07-20 19:20:54,841 - platforms.hgbet_bk - WARNING - 比赛ID 410141244 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-20 19:20:54,862 - platforms.hgbet_bk - WARNING - 未找到匹配的比赛ID（最高分数: 0.00，阈值: 0.48），无法继续投注
2025-07-20 19:20:54,864 - utils.match_learning - INFO - 记录匹配失败: 2025-07-20T19:20:54.862454_BetBurger_皇冠, 类别: team_mismatch
2025-07-20 19:20:54,864 - hedge.hedge_manager - WARNING - crown投注失败，不适合重试: 投注失败
2025-07-20 19:20:54,864 - hedge.hedge_manager - WARNING - crown投注失败，不适合重试: 投注失败
2025-07-20 19:20:54,864 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 投注失败
2025-07-20 19:20:54,864 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 投注失败
2025-07-20 19:20:54,864 - hedge_system - WARNING - 对冲投注失败: 圣米格尔比尔曼 - TNT短途篮球队
2025-07-20 19:20:54,864 - hedge.hedge_manager - INFO - 对冲投注完全失败，不保存记录
2025-07-20 19:20:54,864 - hedge.hedge_manager - INFO - 对冲投注完全失败，不保存记录
2025-07-20 19:20:54,864 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:圣米格尔比尔曼 - TNT短途篮球队, 锁ID: hedge:圣米格尔比尔曼 - TNT短途篮球队:1753010446
2025-07-20 19:20:54,865 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 19:20:54,865 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 19:20:54,865 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 19:20:54,865 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:01:08,664 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 20:01:08,665 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 20:01:08,666 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:01:08,666 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:01:08,666 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 美国 - 澳大利亚 vs 美国 - 澳大利亚
2025-07-20 20:01:08,666 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 美国 - 澳大利亚 vs 美国 - 澳大利亚
2025-07-20 20:01:08,666 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:01:08,666 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:01:08,666 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:01:08,666 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:01:08,667 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:01:08,667 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:01:08,667 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:01:08,667 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:01:08,667 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:01:08,667 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:01:08,667 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:美国 - 澳大利亚, 锁ID: hedge:美国 - 澳大利亚:1753012868, 超时: 30秒
2025-07-20 20:01:08,667 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 20:01:08,667 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=792.83, 净利润=-264.83
2025-07-20 20:01:08,668 - hedge_system - INFO - 开始对冲投注: 美国 - 澳大利亚
2025-07-20 20:01:08,668 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=792.83, 总投资=1792.83
2025-07-20 20:01:08,668 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=792.83, 总投资=1792.83
2025-07-20 20:01:08,668 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:01:08,668 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:01:08,668 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:01:08,668 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:01:08,668 - platforms.hgbet_bk - INFO - 解析到投注信息: 美国 vs 澳大利亚, 市场类型: 17, 盘口: -16.5, 赔率: 1.91
2025-07-20 20:01:08,669 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.91
2025-07-20 20:01:08,669 - platforms.hgbet_bk - INFO - 处理市场类型 17
2025-07-20 20:01:08,669 - platforms.hgbet_bk - INFO - 解析为主队让球: 美国, 盘口: -16.5
2025-07-20 20:01:08,669 - platforms.hgbet_bk - INFO - 检查重复投注: 美国 vs 澳大利亚, 让球, 投注方向=美国, 时段=全场, 比赛ID=410145852, 盘口值=-16.5
2025-07-20 20:01:08,670 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 20:01:08,670 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 20:01:08,670 - platforms.hgbet_bk - INFO - 投注信息: 美国 vs 澳大利亚, 让球=美国, 盘口: -16.5, 赔率: 1.91
2025-07-20 20:01:08,670 - platforms.hgbet_bk - INFO - 开始执行投注流程
2025-07-20 20:01:08,670 - platforms.hgbet_bk - INFO - 投注信息: 美国 vs 澳大利亚, 让球=美国, 盘口: -16.5, 赔率: 1.91
2025-07-20 20:01:08,670 - platforms.hgbet_bk - INFO - 比赛投注次数检查: 澳大利亚_vs_美国, 当前次数: 0, 限制: 2
2025-07-20 20:01:08,671 - platforms.hgbet_bk - INFO - 比赛投注次数检查通过: 澳大利亚_vs_美国, 可继续投注
2025-07-20 20:01:08,893 - platforms.hgbet_bk - WARNING - 会话可能已过期，尝试重新登录
2025-07-20 20:01:08,894 - platforms.hgbet_bk - INFO - 尝试登录...
2025-07-20 20:01:11,346 - platforms.hgbet_bk - INFO - 登录状态码: 200
2025-07-20 20:01:11,347 - platforms.hgbet_bk - INFO - 提取到UID: 8u382xfm38245145l6565528b1
2025-07-20 20:01:11,567 - platforms.hgbet_bk - INFO - 登录成功，当前余额：12025.4，版本号: 2025-04-30-CRM-55_89
2025-07-20 20:01:11,567 - platforms.hgbet_bk - INFO - 重新登录成功
2025-07-20 20:01:11,798 - platforms.hgbet_bk - INFO - 等待3秒后执行投注...
2025-07-20 20:01:14,799 - platforms.hgbet_bk - INFO - 投注比赛: 美国 vs 澳大利亚
2025-07-20 20:01:14,799 - platforms.hgbet_bk - INFO - 投注类型: 让球, 投注队伍: 美国, 盘口: -16.5, 比赛阶段: 全场
2025-07-20 20:01:14,799 - platforms.hgbet_bk - INFO - 使用传入的投注金额: 1000.0
2025-07-20 20:01:14,799 - platforms.hgbet_bk - INFO - 最终确定的投注金额: 1000.0 (类型: <class 'float'>)
2025-07-20 20:01:14,800 - platforms.hgbet_bk - INFO - 确认投注金额: 1000.0, 赔率: 1.91
2025-07-20 20:01:14,800 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 20:01:15,049 - platforms.hgbet_bk - INFO - 获取到 24 场比赛
2025-07-20 20:01:15,049 - platforms.hgbet_bk - WARNING - 比赛ID 410145852 在today比赛列表中不存在，尝试在early中查找
2025-07-20 20:01:15,049 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 20:01:15,278 - platforms.hgbet_bk - INFO - 获取到 5 场比赛
2025-07-20 20:01:15,278 - platforms.hgbet_bk - WARNING - 比赛ID 410145852 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-20 20:01:15,281 - platforms.hgbet_bk - INFO - 队伍匹配良好: 美国 vs 澳大利亚 <-> 美国U19(女) vs 澳大利亚U19(女), 分数: 1.000 (匹配类型: forward)
2025-07-20 20:01:15,287 - platforms.hgbet_bk - INFO - 找到匹配的比赛ID: 9401653, 匹配分数: 1.00
2025-07-20 20:01:15,288 - platforms.hgbet_bk - INFO - 投注类型代码: R, 选择: H, 比赛阶段: 全场
2025-07-20 20:01:15,288 - platforms.hgbet_bk - INFO - 使用标准wtype格式: R, 选项: H, 时段: 全场
2025-07-20 20:01:15,523 - platforms.hgbet_bk - INFO - 盘口值验证 - 原始: -16.5, 表单: 19.5, 绝对值差异: 3.0, 阈值: 0
2025-07-20 20:01:15,523 - platforms.hgbet_bk - WARNING - 盘口值变化过大: 原始值 -16.5 vs 表单值 19.5, 绝对值差异 3.0 超过阈值 0, 取消投注
2025-07-20 20:01:15,524 - hedge.hedge_manager - INFO - crown投注失败，1.0秒后进行第2次尝试: 投注失败
2025-07-20 20:01:15,524 - hedge.hedge_manager - INFO - crown投注失败，1.0秒后进行第2次尝试: 投注失败
2025-07-20 20:01:16,528 - platforms.hgbet_bk - INFO - 解析到投注信息: 美国 vs 澳大利亚, 市场类型: 17, 盘口: -16.5, 赔率: 1.91
2025-07-20 20:01:16,528 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.91
2025-07-20 20:01:16,529 - platforms.hgbet_bk - INFO - 处理市场类型 17
2025-07-20 20:01:16,529 - platforms.hgbet_bk - INFO - 解析为主队让球: 美国, 盘口: -16.5
2025-07-20 20:01:16,530 - platforms.hgbet_bk - INFO - 检查重复投注: 美国 vs 澳大利亚, 让球, 投注方向=美国, 时段=全场, 比赛ID=410145852, 盘口值=-16.5
2025-07-20 20:01:16,530 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 20:01:16,530 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 20:01:16,532 - platforms.hgbet_bk - INFO - 开始执行投注流程
2025-07-20 20:01:16,532 - platforms.hgbet_bk - INFO - 投注信息: 美国 vs 澳大利亚, 让球=美国, 盘口: -16.5, 赔率: 1.91
2025-07-20 20:01:16,532 - platforms.hgbet_bk - INFO - 比赛投注次数检查: 澳大利亚_vs_美国, 当前次数: 0, 限制: 2
2025-07-20 20:01:16,532 - platforms.hgbet_bk - INFO - 比赛投注次数检查通过: 澳大利亚_vs_美国, 可继续投注
2025-07-20 20:01:16,736 - platforms.hgbet_bk - INFO - 等待3秒后执行投注...
2025-07-20 20:01:19,736 - platforms.hgbet_bk - INFO - 投注比赛: 美国 vs 澳大利亚
2025-07-20 20:01:19,736 - platforms.hgbet_bk - INFO - 投注类型: 让球, 投注队伍: 美国, 盘口: -16.5, 比赛阶段: 全场
2025-07-20 20:01:19,737 - platforms.hgbet_bk - INFO - 使用传入的投注金额: 1000.0
2025-07-20 20:01:19,737 - platforms.hgbet_bk - INFO - 最终确定的投注金额: 1000.0 (类型: <class 'float'>)
2025-07-20 20:01:19,737 - platforms.hgbet_bk - INFO - 确认投注金额: 1000.0, 赔率: 1.91
2025-07-20 20:01:19,737 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 20:01:19,950 - platforms.hgbet_bk - INFO - 获取到 24 场比赛
2025-07-20 20:01:19,950 - platforms.hgbet_bk - WARNING - 比赛ID 410145852 在today比赛列表中不存在，尝试在early中查找
2025-07-20 20:01:19,950 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 20:01:20,165 - platforms.hgbet_bk - INFO - 获取到 5 场比赛
2025-07-20 20:01:20,166 - platforms.hgbet_bk - WARNING - 比赛ID 410145852 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-20 20:01:20,170 - platforms.hgbet_bk - INFO - 队伍匹配良好: 美国 vs 澳大利亚 <-> 美国U19(女) vs 澳大利亚U19(女), 分数: 1.000 (匹配类型: forward)
2025-07-20 20:01:20,176 - platforms.hgbet_bk - INFO - 找到匹配的比赛ID: 9401653, 匹配分数: 1.00
2025-07-20 20:01:20,177 - platforms.hgbet_bk - INFO - 投注类型代码: R, 选择: H, 比赛阶段: 全场
2025-07-20 20:01:20,177 - platforms.hgbet_bk - INFO - 使用标准wtype格式: R, 选项: H, 时段: 全场
2025-07-20 20:01:20,418 - platforms.hgbet_bk - INFO - 盘口值验证 - 原始: -16.5, 表单: 19.5, 绝对值差异: 3.0, 阈值: 0
2025-07-20 20:01:20,418 - platforms.hgbet_bk - WARNING - 盘口值变化过大: 原始值 -16.5 vs 表单值 19.5, 绝对值差异 3.0 超过阈值 0, 取消投注
2025-07-20 20:01:20,418 - hedge.hedge_manager - WARNING - crown投注失败，不适合重试: 投注失败
2025-07-20 20:01:20,418 - hedge.hedge_manager - WARNING - crown投注失败，不适合重试: 投注失败
2025-07-20 20:01:20,419 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 投注失败
2025-07-20 20:01:20,419 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 投注失败
2025-07-20 20:01:20,419 - hedge_system - WARNING - 对冲投注失败: 美国 - 澳大利亚
2025-07-20 20:01:20,419 - hedge.hedge_manager - INFO - 对冲投注完全失败，不保存记录
2025-07-20 20:01:20,419 - hedge.hedge_manager - INFO - 对冲投注完全失败，不保存记录
2025-07-20 20:01:20,419 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:美国 - 澳大利亚, 锁ID: hedge:美国 - 澳大利亚:1753012868
2025-07-20 20:01:20,419 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:01:20,419 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:01:20,419 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:01:20,419 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:02:20,886 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 20:02:20,888 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 20:02:20,888 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:02:20,888 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:02:20,888 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 美国 - 澳大利亚 vs 美国 - 澳大利亚
2025-07-20 20:02:20,888 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 美国 - 澳大利亚 vs 美国 - 澳大利亚
2025-07-20 20:02:20,889 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:02:20,889 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:02:20,889 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:02:20,889 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:02:20,889 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:02:20,889 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:02:20,889 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:02:20,889 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:02:20,889 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:02:20,889 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:02:20,890 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:美国 - 澳大利亚, 锁ID: hedge:美国 - 澳大利亚:1753012940, 超时: 30秒
2025-07-20 20:02:20,890 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 20:02:20,890 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=726.61, 净利润=-286.61
2025-07-20 20:02:20,890 - hedge_system - INFO - 开始对冲投注: 美国 - 澳大利亚
2025-07-20 20:02:20,890 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=726.61, 总投资=1726.61
2025-07-20 20:02:20,890 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=726.61, 总投资=1726.61
2025-07-20 20:02:20,891 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:02:20,891 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:02:20,891 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:02:20,891 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:02:20,891 - platforms.hgbet_bk - INFO - 解析到投注信息: 美国 vs 澳大利亚, 市场类型: 18, 盘口: +19.5, 赔率: 1.80
2025-07-20 20:02:20,891 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.8
2025-07-20 20:02:20,891 - platforms.hgbet_bk - INFO - 处理市场类型 18
2025-07-20 20:02:20,891 - platforms.hgbet_bk - INFO - 解析为客队让球: 澳大利亚, 盘口: +19.5
2025-07-20 20:02:20,892 - platforms.hgbet_bk - INFO - 检查重复投注: 美国 vs 澳大利亚, 让球, 投注方向=澳大利亚, 时段=全场, 比赛ID=410145852, 盘口值=+19.5
2025-07-20 20:02:20,892 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 20:02:20,892 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 20:02:20,892 - platforms.hgbet_bk - INFO - 投注信息: 美国 vs 澳大利亚, 让球=澳大利亚, 盘口: +19.5, 赔率: 1.8
2025-07-20 20:02:20,892 - platforms.hgbet_bk - INFO - 开始执行投注流程
2025-07-20 20:02:20,892 - platforms.hgbet_bk - INFO - 投注信息: 美国 vs 澳大利亚, 让球=澳大利亚, 盘口: +19.5, 赔率: 1.8
2025-07-20 20:02:20,892 - platforms.hgbet_bk - INFO - 比赛投注次数检查: 澳大利亚_vs_美国, 当前次数: 0, 限制: 2
2025-07-20 20:02:20,892 - platforms.hgbet_bk - INFO - 比赛投注次数检查通过: 澳大利亚_vs_美国, 可继续投注
2025-07-20 20:02:21,102 - platforms.hgbet_bk - INFO - 等待3秒后执行投注...
2025-07-20 20:02:24,103 - platforms.hgbet_bk - INFO - 投注比赛: 美国 vs 澳大利亚
2025-07-20 20:02:24,103 - platforms.hgbet_bk - INFO - 投注类型: 让球, 投注队伍: 澳大利亚, 盘口: +19.5, 比赛阶段: 全场
2025-07-20 20:02:24,103 - platforms.hgbet_bk - INFO - 使用传入的投注金额: 1000.0
2025-07-20 20:02:24,103 - platforms.hgbet_bk - INFO - 最终确定的投注金额: 1000.0 (类型: <class 'float'>)
2025-07-20 20:02:24,104 - platforms.hgbet_bk - INFO - 确认投注金额: 1000.0, 赔率: 1.8
2025-07-20 20:02:24,104 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 20:02:24,398 - platforms.hgbet_bk - INFO - 获取到 24 场比赛
2025-07-20 20:02:24,399 - platforms.hgbet_bk - WARNING - 比赛ID 410145852 在today比赛列表中不存在，尝试在early中查找
2025-07-20 20:02:24,400 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 20:02:24,598 - platforms.hgbet_bk - INFO - 获取到 5 场比赛
2025-07-20 20:02:24,599 - platforms.hgbet_bk - WARNING - 比赛ID 410145852 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-20 20:02:24,602 - platforms.hgbet_bk - INFO - 队伍匹配良好: 美国 vs 澳大利亚 <-> 美国U19(女) vs 澳大利亚U19(女), 分数: 1.000 (匹配类型: forward)
2025-07-20 20:02:24,607 - platforms.hgbet_bk - INFO - 找到匹配的比赛ID: 9401653, 匹配分数: 1.00
2025-07-20 20:02:24,608 - platforms.hgbet_bk - INFO - 投注类型代码: R, 选择: C, 比赛阶段: 全场
2025-07-20 20:02:24,608 - platforms.hgbet_bk - INFO - 使用标准wtype格式: R, 选项: C, 时段: 全场
2025-07-20 20:02:24,813 - platforms.hgbet_bk - INFO - 盘口值验证 - 原始: 19.5, 表单: 19.5, 绝对值差异: 0.0, 阈值: 0
2025-07-20 20:02:24,813 - platforms.hgbet_bk - INFO - 盘口值验证通过，绝对值差异 0.0 在阈值 0 范围内，继续投注
2025-07-20 20:02:24,813 - platforms.hgbet_bk - INFO - 赔率值验证 - 原始: 1.8, 表单: 1.8, 差异: 0.0, 阈值: 0.08
2025-07-20 20:02:24,814 - platforms.hgbet_bk - INFO - 赔率值验证通过，差异 0.0 在阈值 0.08 范围内，继续投注
2025-07-20 20:02:24,814 - platforms.hgbet_bk - INFO - 投注金额详细信息: amount=1000.0, type=<class 'float'>, str(amount)=1000.0
2025-07-20 20:02:25,024 - platforms.hgbet_bk - INFO - 投注参数 - golds: '1000', amount: 1000.0 -> 1000, type: <class 'float'>
2025-07-20 20:02:25,248 - platforms.hgbet_bk - INFO - 【投注】成功，订单号: 22282608212，余额: 11025.40
2025-07-20 20:02:25,249 - platforms.hgbet_bk - INFO - 投注成功: 美国 vs 澳大利亚, 让球=澳大利亚, 盘口: +19.5, 赔率: 1.8
2025-07-20 20:02:25,252 - platforms.hgbet_bk - INFO - 成功保存投注记录: {'match_id': '9401653', 'home_team': '美国', 'away_team': '澳大利亚', 'bet_type': '让球', 'bet_team': '澳大利亚', 'handicap_value': '+19.5', 'odds': 1.8, 'amount': 1000.0, 'period': '全场', 'timestamp': 1753012945.250779, 'datetime': '2025-07-20 20:02:25', 'order_no': '22282608212', 'success': True}
2025-07-20 20:02:25,252 - platforms.hgbet_bk - INFO - 更新比赛投注次数: 澳大利亚_vs_美国, 新次数: 1
2025-07-20 20:02:25,253 - platforms.hgbet_bk - ERROR - 播放提示音失败: No module named 'betburger_fetcher'
2025-07-20 20:02:25,254 - hedge.hedge_manager - INFO - 基准平台(crown)投注成功，等待1.0秒后开始对冲平台投注
2025-07-20 20:02:25,254 - hedge.hedge_manager - INFO - 基准平台(crown)投注成功，等待1.0秒后开始对冲平台投注
2025-07-20 20:02:26,256 - PinBet - INFO - 解析结果: 美国 vs 澳大利亚, 让球=美国, 盘口=-19.0, 赔率=2.18
2025-07-20 20:02:26,257 - PinBet - INFO - 开始执行投注: 美国 vs 澳大利亚, 让球=美国
2025-07-20 20:02:26,258 - PinBet - INFO - 获取今日比赛数据
2025-07-20 20:02:26,258 - PinBet - INFO - 获取今日篮球比赛数据
2025-07-20 20:02:45,531 - PinBet - ERROR - 获取今日篮球比赛异常: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 20:02:45,531 - PinBet - ERROR - 获取今日比赛失败
2025-07-20 20:02:45,531 - hedge.hedge_manager - INFO - pinbet投注失败，1.0秒后进行第2次尝试: 投注失败
2025-07-20 20:02:45,531 - hedge.hedge_manager - INFO - pinbet投注失败，1.0秒后进行第2次尝试: 投注失败
2025-07-20 20:02:46,542 - PinBet - INFO - 解析结果: 美国 vs 澳大利亚, 让球=美国, 盘口=-19.0, 赔率=2.18
2025-07-20 20:02:46,543 - PinBet - INFO - 开始执行投注: 美国 vs 澳大利亚, 让球=美国
2025-07-20 20:02:46,543 - PinBet - INFO - 获取今日比赛数据
2025-07-20 20:02:46,544 - PinBet - INFO - 获取今日篮球比赛数据
2025-07-20 20:02:47,572 - PinBet - INFO - 今日篮球比赛数据获取成功
2025-07-20 20:02:47,572 - PinBet - INFO - 获取早场比赛数据
2025-07-20 20:02:47,572 - PinBet - INFO - 获取早场篮球比赛数据
2025-07-20 20:02:47,926 - PinBet - INFO - 早场篮球比赛数据获取成功
2025-07-20 20:02:47,926 - PinBet - INFO - 查找匹配比赛: 美国 vs 澳大利亚, 让球=美国, 盘口=-19.0
2025-07-20 20:02:47,927 - PinBet - INFO - 找到匹配比赛: 日本 vs 澳大利亚 (联赛: FIBA - 女子亚洲杯) (匹配分数: 0.50)
2025-07-20 20:02:47,927 - PinBet - INFO - 找到匹配比赛: 美国 vs 澳大利亚 (联赛: 国际篮联 - U19女子篮球世界杯) (匹配分数: 1.00)
2025-07-20 20:02:47,928 - PinBet - INFO - 可用的让球盘口: ['19.5', '19.0', '18.5', '18.0', '17.5', '17.0', '16.5', '16.0', '15.5', '15.0', '14.5']
2025-07-20 20:02:47,928 - PinBet - INFO - 标准化后的目标盘口值: -19.0
2025-07-20 20:02:47,928 - PinBet - INFO - 获取投注表单: event_id=1611860550, line_id=49607510791, period_num=0, bet_type=2, team_side=home, handicap=-19.0
2025-07-20 20:02:47,929 - PinBet - INFO - 会话空闲1753012968秒，执行主动刷新以保持活跃
2025-07-20 20:02:48,325 - PinBet - INFO - 余额获取成功: 26770.74
2025-07-20 20:02:48,326 - PinBet - INFO - 让分盘设置 - team_side: home, team_side_code: 0, selection_direction: 0
2025-07-20 20:02:48,326 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 20:02:48,326 - PinBet - INFO - 构建投注ID: odds_id=1611860550|0|2|0|1|-19, selection_id=49607510791|1611860550|0|2|0|1|-19|0
2025-07-20 20:02:48,327 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1753012968326', 'withCredentials': 'true'}
2025-07-20 20:02:48,327 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611860550|0|2|0|1|-19", "oddsSelectionsType": "NORMAL", "selectionId": "49607510791|1611860550|0|2|0|1|-19|0"}]}
2025-07-20 20:02:48,721 - PinBet - INFO - 投注表单获取成功: 1611860550, 赔率: 2.180
2025-07-20 20:02:48,721 - PinBet - INFO - API返回的selectionId: **********|***********|1611860550|0|2|0|1|-19.00|0
2025-07-20 20:02:48,721 - PinBet - INFO - API返回的oddsId: 1611860550|0|2|0|1|-19
2025-07-20 20:02:48,722 - PinBet - INFO - 将使用投注选项: selectionId=**********|***********|1611860550|0|2|0|1|-19.00|0, oddsId=1611860550|0|2|0|1|-19, odds=2.180
2025-07-20 20:02:48,722 - PinBet - INFO - 执行投注: 金额=726.61
2025-07-20 20:02:48,722 - PinBet - INFO - 从列表中提取投注信息: selection_id=**********|***********|1611860550|0|2|0|1|-19.00|0, odds_id=1611860550|0|2|0|1|-19, odds=2.180, stake=726.61
2025-07-20 20:02:49,484 - PinBet - INFO - 投注响应数据: {"response": [{"wagerId": *********, "status": "ACCEPTED", "selectionId": "**********|***********|1611860550|0|2|0|1|-19.00|0", "betId": **********, "uniqueRequestId": "bd12212f-054f-4cfd-8d23-3ff9f3822522", "wagerType": "NORMAL", "betterLineWasAccepted": false, "jsonString": "{\"bets\":[{\"status\":\"ACCEPTED\",\"errorCode\":null,\"uniqueRequestId\":\"bd12212f-054f-4cfd-8d23-3ff9f3822522\",\"lineId\":**********,\"altLineId\":***********,\"price\":2.18,\"minRiskStake\":0.07,\"maxRiskStake\":3588.98,\"minWinStake\":0.09,\"maxWinStake\":4234.99,\"straightBet\":{\"betId\":**********,\"uniqueRequestId\":\"bd12212f-054f-4cfd-8d23-3ff9f3822522\",\"wagerNumber\":1,\"placedAt\":\"2025-07-20T12:02:50Z\",\"betStatus\":\"ACCEPTED\",\"betStatus2\":\"ACCEPTED\",\"betType\":\"SPREAD\",\"win\":857.4,\"risk\":726.61,\"oddsFormat\":\"DECIMAL\",\"updateSequence\":**********,\"externalRef\":\"*********\",\"sportId\":4,\"sportName\":\"Basketball\",\"leagueId\":214756,\"leagueName\":\"FIBA - U19 World Cup Women\",\"eventId\":1611860550,\"handicap\":-19.0,\"price\":2.18,\"teamName\":\"USA\",\"team1\":\"USA\",\"team2\":\"Australia\",\"periodNumber\":0,\"isLive\":\"FALSE\",\"homeTeamType\":\"Team1\",\"eventStartTime\":\"2025-07-20T18:00:00Z\",\"resultingUnit\":\"Regular\"}}]}", "oddsId": "1611860550|0|2|0|1|-19", "psBetVO": {"betId": **********, "uniqueRequestId": "bd12212f-054f-4cfd-8d23-3ff9f3822522", "wagerNumber": 1, "placedAt": "2025-07-20T12:02:50Z", "betStatus": "ACCEPTED", "betType": "SPREAD", "win": 857.4, "risk": 726.61, "oddsFormat": "DECIMAL", "updateSequence": 1753012970460, "sportId": 4, "sportName": "Basketball", "leagueId": 214756, "leagueName": "FIBA - U19 World Cup Women", "eventId": 1611860550, "handicap": -19.0, "price": 2.18, "teamName": "USA", "team1": "USA", "team2": "Australia", "periodNumber": 0, "isLive": "FALSE", "eventStartTime": "2025-07-20T18:00:00Z", "resultingUnit": "Regular"}, "odds": 2.18}]}
2025-07-20 20:02:49,485 - PinBet - INFO - 投注成功: 注单ID *********, 投注ID **********
2025-07-20 20:02:49,486 - PinBet - INFO - 投注成功: {'response': [{'wagerId': *********, 'status': 'ACCEPTED', 'selectionId': '**********|***********|1611860550|0|2|0|1|-19.00|0', 'betId': **********, 'uniqueRequestId': 'bd12212f-054f-4cfd-8d23-3ff9f3822522', 'wagerType': 'NORMAL', 'betterLineWasAccepted': False, 'jsonString': '{"bets":[{"status":"ACCEPTED","errorCode":null,"uniqueRequestId":"bd12212f-054f-4cfd-8d23-3ff9f3822522","lineId":**********,"altLineId":***********,"price":2.18,"minRiskStake":0.07,"maxRiskStake":3588.98,"minWinStake":0.09,"maxWinStake":4234.99,"straightBet":{"betId":**********,"uniqueRequestId":"bd12212f-054f-4cfd-8d23-3ff9f3822522","wagerNumber":1,"placedAt":"2025-07-20T12:02:50Z","betStatus":"ACCEPTED","betStatus2":"ACCEPTED","betType":"SPREAD","win":857.4,"risk":726.61,"oddsFormat":"DECIMAL","updateSequence":**********,"externalRef":"*********","sportId":4,"sportName":"Basketball","leagueId":214756,"leagueName":"FIBA - U19 World Cup Women","eventId":1611860550,"handicap":-19.0,"price":2.18,"teamName":"USA","team1":"USA","team2":"Australia","periodNumber":0,"isLive":"FALSE","homeTeamType":"Team1","eventStartTime":"2025-07-20T18:00:00Z","resultingUnit":"Regular"}}]}', 'oddsId': '1611860550|0|2|0|1|-19', 'psBetVO': {'betId': **********, 'uniqueRequestId': 'bd12212f-054f-4cfd-8d23-3ff9f3822522', 'wagerNumber': 1, 'placedAt': '2025-07-20T12:02:50Z', 'betStatus': 'ACCEPTED', 'betType': 'SPREAD', 'win': 857.4, 'risk': 726.61, 'oddsFormat': 'DECIMAL', 'updateSequence': 1753012970460, 'sportId': 4, 'sportName': 'Basketball', 'leagueId': 214756, 'leagueName': 'FIBA - U19 World Cup Women', 'eventId': 1611860550, 'handicap': -19.0, 'price': 2.18, 'teamName': 'USA', 'team1': 'USA', 'team2': 'Australia', 'periodNumber': 0, 'isLive': 'FALSE', 'eventStartTime': '2025-07-20T18:00:00Z', 'resultingUnit': 'Regular'}, 'odds': 2.18}]}
2025-07-20 20:02:49,488 - PinBet - INFO - 投注记录已保存到: data\pinbet_records_2025-07-20.json
2025-07-20 20:02:51,489 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 20:02:51,489 - PinBet - INFO - 开始查询未结算注单
2025-07-20 20:02:51,861 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 20:02:51,861 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 20:02:51,861 - hedge.hedge_manager - INFO - 顺序对冲投注完全成功
2025-07-20 20:02:51,861 - hedge.hedge_manager - INFO - 顺序对冲投注完全成功
2025-07-20 20:02:51,861 - hedge_system - INFO - 对冲投注成功: 美国 - 澳大利亚
2025-07-20 20:02:51,862 - hedge.hedge_manager - INFO - 对冲投注完全成功，保存到对冲记录
2025-07-20 20:02:51,862 - hedge.hedge_manager - INFO - 对冲投注完全成功，保存到对冲记录
2025-07-20 20:02:51,864 - hedge.hedge_record_manager - INFO - 添加新对冲记录: hedge_1753012940
2025-07-20 20:02:51,864 - hedge.hedge_record_manager - INFO - 添加新对冲记录: hedge_1753012940
2025-07-20 20:02:51,867 - hedge.hedge_record_manager - INFO - 对冲记录保存成功: hedge_1753012940
2025-07-20 20:02:51,867 - hedge.hedge_record_manager - INFO - 对冲记录保存成功: hedge_1753012940
2025-07-20 20:02:51,867 - hedge.hedge_manager - INFO - 完全成功对冲记录保存成功: hedge_1753012940
2025-07-20 20:02:51,867 - hedge.hedge_manager - INFO - 完全成功对冲记录保存成功: hedge_1753012940
2025-07-20 20:02:51,867 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:美国 - 澳大利亚, 锁ID: hedge:美国 - 澳大利亚:1753012940
2025-07-20 20:02:51,867 - hedge.hedge_integration - INFO - 对冲投注执行成功
2025-07-20 20:02:51,867 - hedge.hedge_integration - INFO - 对冲投注执行成功
2025-07-20 20:02:51,914 - hedge.hedge_integration - INFO - 播放对冲成功提示音
2025-07-20 20:02:51,914 - hedge.hedge_integration - INFO - 播放对冲成功提示音
2025-07-20 20:02:51,915 - hedge.hedge_integration - INFO - 对冲投注成功，暂停监控以避免过度投注
2025-07-20 20:02:51,915 - hedge.hedge_integration - INFO - 对冲投注成功，暂停监控以避免过度投注
2025-07-20 20:03:03,973 - __main__ - INFO - [system] 正在查询对冲记录...
2025-07-20 20:03:03,974 - __main__ - INFO - [system] 优先加载当天对冲记录: hedge_records_2025-07-20.json
2025-07-20 20:03:03,975 - __main__ - INFO - [system] 显示当天对冲记录 2 条
2025-07-20 20:03:03,981 - __main__ - INFO - [system] 加载对冲记录 2 条 (来自 1 个文件)
2025-07-20 20:03:03,981 - __main__ - INFO - [system] 对冲记录查询完成
2025-07-20 20:03:50,970 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 20:03:51,297 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 20:03:51,298 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 20:03:51,299 - PinBet - INFO - 开始查询未结算注单
2025-07-20 20:03:51,670 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 20:03:51,670 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 20:03:51,677 - __main__ - INFO - [system] 加载平博记录 2 条
2025-07-20 20:03:51,677 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 20:03:53,530 - __main__ - INFO - [system] 切换到皇冠记录显示模式
2025-07-20 20:03:53,876 - __main__ - INFO - [system] 正在查询皇冠投注记录...
2025-07-20 20:03:53,877 - platforms.hgbet_bk - INFO - 直接获取今日BK注单
2025-07-20 20:03:54,120 - platforms.hgbet_bk - INFO - 今日总投注金额: 2,000
2025-07-20 20:03:54,120 - platforms.hgbet_bk - INFO - 找到 2 条注单
2025-07-20 20:03:54,123 - __main__ - INFO - [system] 加载皇冠记录 2 条
2025-07-20 20:03:54,123 - __main__ - INFO - [system] 皇冠投注记录查询完成
2025-07-20 20:04:01,837 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 20:04:02,154 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 20:04:02,155 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 20:04:02,155 - PinBet - INFO - 开始查询未结算注单
2025-07-20 20:04:02,523 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 20:04:02,523 - PinBet - INFO - 处理完成: 原始记录 2 条，处理后 2 条记录
2025-07-20 20:04:02,524 - __main__ - INFO - [system] 加载平博记录 2 条
2025-07-20 20:04:02,524 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 20:05:03,140 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 20:05:03,141 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 20:05:03,142 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:05:03,142 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:05:03,142 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:05:03,142 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:05:03,142 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:05:03,142 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:05:03,142 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:05:03,142 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:05:03,142 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:05:03,142 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:05:03,142 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:05:03,142 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:05:03,142 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:05:03,142 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:05:03,142 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013103, 超时: 30秒
2025-07-20 20:05:03,142 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 20:05:03,143 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=738.06, 净利润=-282.06
2025-07-20 20:05:03,143 - hedge_system - INFO - 开始对冲投注: 冰岛 - 德国
2025-07-20 20:05:03,143 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=738.06, 总投资=1738.06
2025-07-20 20:05:03,143 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=738.06, 总投资=1738.06
2025-07-20 20:05:03,143 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:05:03,143 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:05:03,143 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:05:03,143 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:05:03,143 - platforms.hgbet_bk - INFO - 解析到投注信息: 冰岛 vs 德国, 市场类型: 20, 盘口: 156.0, 赔率: 1.82
2025-07-20 20:05:03,143 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.82
2025-07-20 20:05:03,143 - platforms.hgbet_bk - INFO - 处理市场类型 20
2025-07-20 20:05:03,143 - platforms.hgbet_bk - INFO - 解析为小球: 156.0
2025-07-20 20:05:03,144 - platforms.hgbet_bk - INFO - 检查重复投注: 冰岛 vs 德国, 大小球, 投注方向=小, 时段=全场, 比赛ID=410143450, 盘口值=156.0
2025-07-20 20:05:03,144 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 20:05:03,144 - hedge.hedge_manager - INFO - 皇冠投注执行: amount=1000.0, type=<class 'float'>
2025-07-20 20:05:03,144 - platforms.hgbet_bk - INFO - 投注信息: 冰岛 vs 德国, 大小球=小, 盘口: 156.0, 赔率: 1.82
2025-07-20 20:05:03,145 - platforms.hgbet_bk - INFO - 开始执行投注流程
2025-07-20 20:05:03,145 - platforms.hgbet_bk - INFO - 投注信息: 冰岛 vs 德国, 大小球=小, 盘口: 156.0, 赔率: 1.82
2025-07-20 20:05:03,145 - platforms.hgbet_bk - INFO - 比赛投注次数检查: 冰岛_vs_德国, 当前次数: 0, 限制: 2
2025-07-20 20:05:03,145 - platforms.hgbet_bk - INFO - 比赛投注次数检查通过: 冰岛_vs_德国, 可继续投注
2025-07-20 20:05:03,352 - platforms.hgbet_bk - INFO - 等待3秒后执行投注...
2025-07-20 20:05:06,352 - platforms.hgbet_bk - INFO - 投注比赛: 冰岛 vs 德国
2025-07-20 20:05:06,352 - platforms.hgbet_bk - INFO - 投注类型: 大小球, 投注队伍: 小, 盘口: 156.0, 比赛阶段: 全场
2025-07-20 20:05:06,352 - platforms.hgbet_bk - INFO - 使用传入的投注金额: 1000.0
2025-07-20 20:05:06,352 - platforms.hgbet_bk - INFO - 最终确定的投注金额: 1000.0 (类型: <class 'float'>)
2025-07-20 20:05:06,352 - platforms.hgbet_bk - INFO - 确认投注金额: 1000.0, 赔率: 1.82
2025-07-20 20:05:06,352 - platforms.hgbet_bk - INFO - 获取today比赛数据，体育类型:bk，盘口类型:r
2025-07-20 20:05:06,594 - platforms.hgbet_bk - INFO - 获取到 24 场比赛
2025-07-20 20:05:06,595 - platforms.hgbet_bk - WARNING - 比赛ID 410143450 在today比赛列表中不存在，尝试在early中查找
2025-07-20 20:05:06,596 - platforms.hgbet_bk - INFO - 获取early比赛数据，体育类型:bk，盘口类型:r
2025-07-20 20:05:06,815 - platforms.hgbet_bk - INFO - 获取到 5 场比赛
2025-07-20 20:05:06,816 - platforms.hgbet_bk - WARNING - 比赛ID 410143450 在皇冠比赛列表中不存在，尝试查找匹配的比赛
2025-07-20 20:05:06,818 - platforms.hgbet_bk - INFO - 队伍匹配良好: 冰岛 vs 德国 <-> 冰岛U20 vs 德国U20, 分数: 1.000 (匹配类型: forward)
2025-07-20 20:05:06,825 - platforms.hgbet_bk - INFO - 找到匹配的比赛ID: 9401401, 匹配分数: 1.00
2025-07-20 20:05:06,825 - platforms.hgbet_bk - INFO - 根据市场类型20(小球)强制选择H选项
2025-07-20 20:05:06,825 - platforms.hgbet_bk - INFO - 投注类型代码: OU, 选择: H, 比赛阶段: 全场
2025-07-20 20:05:06,825 - platforms.hgbet_bk - INFO - 使用标准wtype格式: OU, 选项: H, 时段: 全场
2025-07-20 20:05:07,074 - platforms.hgbet_bk - INFO - 盘口值验证 - 原始: 156.0, 表单: 156.0, 绝对值差异: 0.0, 阈值: 0
2025-07-20 20:05:07,074 - platforms.hgbet_bk - INFO - 盘口值验证通过，绝对值差异 0.0 在阈值 0 范围内，继续投注
2025-07-20 20:05:07,074 - platforms.hgbet_bk - INFO - 赔率值验证 - 原始: 1.82, 表单: 1.83, 差异: 0.010000000000000009, 阈值: 0.08
2025-07-20 20:05:07,074 - platforms.hgbet_bk - INFO - 赔率值验证通过，差异 0.010000000000000009 在阈值 0.08 范围内，继续投注
2025-07-20 20:05:07,074 - platforms.hgbet_bk - INFO - 投注金额详细信息: amount=1000.0, type=<class 'float'>, str(amount)=1000.0
2025-07-20 20:05:07,275 - platforms.hgbet_bk - INFO - 投注参数 - golds: '1000', amount: 1000.0 -> 1000, type: <class 'float'>
2025-07-20 20:05:07,492 - platforms.hgbet_bk - INFO - 【投注】成功，订单号: 22282636999，余额: 10025.40
2025-07-20 20:05:07,493 - platforms.hgbet_bk - INFO - 投注成功: 冰岛 vs 德国, 大小球=小, 盘口: 156.0, 赔率: 1.82
2025-07-20 20:05:07,495 - platforms.hgbet_bk - INFO - 成功保存投注记录: {'match_id': '9401401', 'home_team': '冰岛', 'away_team': '德国', 'bet_type': '大小球', 'bet_team': '小', 'handicap_value': '156.0', 'odds': 1.82, 'amount': 1000.0, 'period': '全场', 'timestamp': 1753013107.4941227, 'datetime': '2025-07-20 20:05:07', 'order_no': '22282636999', 'success': True}
2025-07-20 20:05:07,495 - platforms.hgbet_bk - INFO - 更新比赛投注次数: 冰岛_vs_德国, 新次数: 1
2025-07-20 20:05:07,496 - platforms.hgbet_bk - ERROR - 播放提示音失败: No module named 'betburger_fetcher'
2025-07-20 20:05:07,496 - hedge.hedge_manager - INFO - 基准平台(crown)投注成功，等待1.0秒后开始对冲平台投注
2025-07-20 20:05:07,496 - hedge.hedge_manager - INFO - 基准平台(crown)投注成功，等待1.0秒后开始对冲平台投注
2025-07-20 20:05:08,496 - PinBet - INFO - 解析结果: 冰岛 vs 德国, 大小球=大, 盘口=155.5, 赔率=2.17
2025-07-20 20:05:08,496 - PinBet - INFO - 开始执行投注: 冰岛 vs 德国, 大小球=大
2025-07-20 20:05:08,496 - PinBet - INFO - 查找匹配比赛: 冰岛 vs 德国, 大小球=大, 盘口=155.5
2025-07-20 20:05:08,496 - PinBet - INFO - 找到匹配比赛: 冰岛 vs 德国 (联赛: 欧洲U20甲级锦标赛) (匹配分数: 1.00)
2025-07-20 20:05:08,497 - PinBet - INFO - 可用的大小球盘口: ['158.5', '158.0', '157.5', '157.0', '156.5', '156.0', '155.5', '155.0', '154.5', '154.0', '153.5']
2025-07-20 20:05:08,497 - PinBet - INFO - 标准化后的目标盘口值: 155.5
2025-07-20 20:05:08,497 - PinBet - INFO - 找到精确匹配的大小球盘口: 155.5, line_id=49606992144
2025-07-20 20:05:08,497 - PinBet - INFO - 获取投注表单: event_id=1611849798, line_id=49606992144, period_num=0, bet_type=3, team_side=over, handicap=155.5
2025-07-20 20:05:08,497 - PinBet - INFO - 大小球盘设置 - team_side: over, team_side_code: 3, selection_direction: 0
2025-07-20 20:05:08,497 - PinBet - INFO - 处理period_num: 原始值=0, API使用值=0
2025-07-20 20:05:08,497 - PinBet - INFO - 构建投注ID: odds_id=1611849798|0|3|3|1|155.5, selection_id=49606992144|1611849798|0|3|3|1|155.5|0
2025-07-20 20:05:08,497 - PinBet - INFO - 投注表单请求参数: {'locale': 'zh_CN', '_': '1753013108497', 'withCredentials': 'true'}
2025-07-20 20:05:08,497 - PinBet - INFO - 投注表单请求数据: {"oddsSelections": [{"oddsFormat": 1, "oddsId": "1611849798|0|3|3|1|155.5", "oddsSelectionsType": "NORMAL", "selectionId": "49606992144|1611849798|0|3|3|1|155.5|0"}]}
2025-07-20 20:05:08,902 - PinBet - INFO - 投注表单获取成功: 1611849798, 赔率: 2.170
2025-07-20 20:05:08,902 - PinBet - INFO - API返回的selectionId: 3184153411|49607567445|1611849798|0|3|3|1|155.50|0
2025-07-20 20:05:08,902 - PinBet - INFO - API返回的oddsId: 1611849798|0|3|3|1|155.5
2025-07-20 20:05:08,902 - PinBet - INFO - 将使用投注选项: selectionId=3184153411|49607567445|1611849798|0|3|3|1|155.50|0, oddsId=1611849798|0|3|3|1|155.5, odds=2.170
2025-07-20 20:05:08,902 - PinBet - INFO - 执行投注: 金额=738.06
2025-07-20 20:05:08,902 - PinBet - INFO - 从列表中提取投注信息: selection_id=3184153411|49607567445|1611849798|0|3|3|1|155.50|0, odds_id=1611849798|0|3|3|1|155.5, odds=2.170, stake=738.06
2025-07-20 20:05:09,604 - PinBet - INFO - 投注响应数据: {"response": [{"wagerId": 660909655, "status": "ACCEPTED", "selectionId": "3184153411|49607567445|1611849798|0|3|3|1|155.50|0", "betId": 2099487924, "uniqueRequestId": "c2cc61d5-1990-458d-a58b-b4b158197abe", "wagerType": "NORMAL", "betterLineWasAccepted": false, "jsonString": "{\"bets\":[{\"status\":\"ACCEPTED\",\"errorCode\":null,\"uniqueRequestId\":\"c2cc61d5-1990-458d-a58b-b4b158197abe\",\"lineId\":3184153411,\"altLineId\":49607567445,\"price\":2.17,\"minRiskStake\":0.07,\"maxRiskStake\":10766.93,\"minWinStake\":0.09,\"maxWinStake\":12597.30,\"straightBet\":{\"betId\":2099487924,\"uniqueRequestId\":\"c2cc61d5-1990-458d-a58b-b4b158197abe\",\"wagerNumber\":1,\"placedAt\":\"2025-07-20T12:05:10Z\",\"betStatus\":\"ACCEPTED\",\"betStatus2\":\"ACCEPTED\",\"betType\":\"TOTAL_POINTS\",\"win\":863.53,\"risk\":738.06,\"oddsFormat\":\"DECIMAL\",\"updateSequence\":2928900782,\"externalRef\":\"660909655\",\"sportId\":4,\"sportName\":\"Basketball\",\"leagueId\":357,\"leagueName\":\"European U20 Championship Division A\",\"eventId\":1611849798,\"handicap\":155.5,\"price\":2.17,\"side\":\"OVER\",\"team1\":\"Iceland\",\"team2\":\"Germany\",\"periodNumber\":0,\"isLive\":\"FALSE\",\"homeTeamType\":\"Team1\",\"eventStartTime\":\"2025-07-20T12:30:00Z\",\"resultingUnit\":\"Regular\"}}]}", "oddsId": "1611849798|0|3|3|1|155.5", "psBetVO": {"betId": 2099487924, "uniqueRequestId": "c2cc61d5-1990-458d-a58b-b4b158197abe", "wagerNumber": 1, "placedAt": "2025-07-20T12:05:10Z", "betStatus": "ACCEPTED", "betType": "TOTAL_POINTS", "win": 863.53, "risk": 738.06, "oddsFormat": "DECIMAL", "updateSequence": 1753013110588, "sportId": 4, "sportName": "Basketball", "leagueId": 357, "leagueName": "European U20 Championship Division A", "eventId": 1611849798, "handicap": 155.5, "price": 2.17, "side": "OVER", "team1": "Iceland", "team2": "Germany", "periodNumber": 0, "isLive": "FALSE", "eventStartTime": "2025-07-20T12:30:00Z", "resultingUnit": "Regular"}, "odds": 2.17}]}
2025-07-20 20:05:09,605 - PinBet - INFO - 投注成功: 注单ID 660909655, 投注ID 2099487924
2025-07-20 20:05:09,605 - PinBet - INFO - 投注成功: {'response': [{'wagerId': 660909655, 'status': 'ACCEPTED', 'selectionId': '3184153411|49607567445|1611849798|0|3|3|1|155.50|0', 'betId': 2099487924, 'uniqueRequestId': 'c2cc61d5-1990-458d-a58b-b4b158197abe', 'wagerType': 'NORMAL', 'betterLineWasAccepted': False, 'jsonString': '{"bets":[{"status":"ACCEPTED","errorCode":null,"uniqueRequestId":"c2cc61d5-1990-458d-a58b-b4b158197abe","lineId":3184153411,"altLineId":49607567445,"price":2.17,"minRiskStake":0.07,"maxRiskStake":10766.93,"minWinStake":0.09,"maxWinStake":12597.30,"straightBet":{"betId":2099487924,"uniqueRequestId":"c2cc61d5-1990-458d-a58b-b4b158197abe","wagerNumber":1,"placedAt":"2025-07-20T12:05:10Z","betStatus":"ACCEPTED","betStatus2":"ACCEPTED","betType":"TOTAL_POINTS","win":863.53,"risk":738.06,"oddsFormat":"DECIMAL","updateSequence":2928900782,"externalRef":"660909655","sportId":4,"sportName":"Basketball","leagueId":357,"leagueName":"European U20 Championship Division A","eventId":1611849798,"handicap":155.5,"price":2.17,"side":"OVER","team1":"Iceland","team2":"Germany","periodNumber":0,"isLive":"FALSE","homeTeamType":"Team1","eventStartTime":"2025-07-20T12:30:00Z","resultingUnit":"Regular"}}]}', 'oddsId': '1611849798|0|3|3|1|155.5', 'psBetVO': {'betId': 2099487924, 'uniqueRequestId': 'c2cc61d5-1990-458d-a58b-b4b158197abe', 'wagerNumber': 1, 'placedAt': '2025-07-20T12:05:10Z', 'betStatus': 'ACCEPTED', 'betType': 'TOTAL_POINTS', 'win': 863.53, 'risk': 738.06, 'oddsFormat': 'DECIMAL', 'updateSequence': 1753013110588, 'sportId': 4, 'sportName': 'Basketball', 'leagueId': 357, 'leagueName': 'European U20 Championship Division A', 'eventId': 1611849798, 'handicap': 155.5, 'price': 2.17, 'side': 'OVER', 'team1': 'Iceland', 'team2': 'Germany', 'periodNumber': 0, 'isLive': 'FALSE', 'eventStartTime': '2025-07-20T12:30:00Z', 'resultingUnit': 'Regular'}, 'odds': 2.17}]}
2025-07-20 20:05:09,606 - PinBet - INFO - 投注记录已保存到: data\pinbet_records_2025-07-20.json
2025-07-20 20:05:11,608 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 20:05:11,608 - PinBet - INFO - 开始查询未结算注单
2025-07-20 20:05:11,989 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 20:05:11,989 - PinBet - INFO - 处理完成: 原始记录 3 条，处理后 3 条记录
2025-07-20 20:05:11,989 - hedge.hedge_manager - INFO - 顺序对冲投注完全成功
2025-07-20 20:05:11,989 - hedge.hedge_manager - INFO - 顺序对冲投注完全成功
2025-07-20 20:05:11,989 - hedge_system - INFO - 对冲投注成功: 冰岛 - 德国
2025-07-20 20:05:11,989 - hedge.hedge_manager - INFO - 对冲投注完全成功，保存到对冲记录
2025-07-20 20:05:11,989 - hedge.hedge_manager - INFO - 对冲投注完全成功，保存到对冲记录
2025-07-20 20:05:11,990 - hedge.hedge_record_manager - INFO - 添加新对冲记录: hedge_1753013103
2025-07-20 20:05:11,990 - hedge.hedge_record_manager - INFO - 添加新对冲记录: hedge_1753013103
2025-07-20 20:05:11,993 - hedge.hedge_record_manager - INFO - 对冲记录保存成功: hedge_1753013103
2025-07-20 20:05:11,993 - hedge.hedge_record_manager - INFO - 对冲记录保存成功: hedge_1753013103
2025-07-20 20:05:11,993 - hedge.hedge_manager - INFO - 完全成功对冲记录保存成功: hedge_1753013103
2025-07-20 20:05:11,993 - hedge.hedge_manager - INFO - 完全成功对冲记录保存成功: hedge_1753013103
2025-07-20 20:05:11,993 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013103
2025-07-20 20:05:11,993 - hedge.hedge_integration - INFO - 对冲投注执行成功
2025-07-20 20:05:11,993 - hedge.hedge_integration - INFO - 对冲投注执行成功
2025-07-20 20:05:11,994 - hedge.hedge_integration - INFO - 播放对冲成功提示音
2025-07-20 20:05:11,994 - hedge.hedge_integration - INFO - 播放对冲成功提示音
2025-07-20 20:05:11,994 - hedge.hedge_integration - INFO - 对冲投注成功，暂停监控以避免过度投注
2025-07-20 20:05:11,994 - hedge.hedge_integration - INFO - 对冲投注成功，暂停监控以避免过度投注
2025-07-20 20:05:13,662 - __main__ - INFO - [system] 切换到对冲记录显示模式
2025-07-20 20:05:14,494 - __main__ - INFO - [system] 开启对冲记录自动刷新
2025-07-20 20:05:14,495 - __main__ - INFO - [system] 检测到新的对冲记录，自动刷新...
2025-07-20 20:05:14,495 - __main__ - INFO - [system] 优先加载当天对冲记录: hedge_records_2025-07-20.json
2025-07-20 20:05:14,496 - __main__ - INFO - [system] 显示当天对冲记录 3 条
2025-07-20 20:05:14,496 - __main__ - INFO - [system] 加载对冲记录 3 条 (来自 1 个文件)
2025-07-20 20:05:53,791 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 20:05:53,792 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 20:05:53,793 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:05:53,793 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:05:53,793 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:05:53,793 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:05:53,793 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:05:53,793 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:05:53,793 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:05:53,793 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:05:53,793 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:05:53,793 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:05:53,793 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:05:53,793 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:05:53,793 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:05:53,793 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:05:53,793 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013153, 超时: 30秒
2025-07-20 20:05:53,793 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 20:05:53,793 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=738.06, 净利润=-282.06
2025-07-20 20:05:53,794 - hedge_system - INFO - 开始对冲投注: 冰岛 - 德国
2025-07-20 20:05:53,794 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=738.06, 总投资=1738.06
2025-07-20 20:05:53,794 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=738.06, 总投资=1738.06
2025-07-20 20:05:53,794 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:05:53,794 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:05:53,794 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:05:53,794 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:05:53,794 - platforms.hgbet_bk - INFO - 解析到投注信息: 冰岛 vs 德国, 市场类型: 20, 盘口: 156.0, 赔率: 1.82
2025-07-20 20:05:53,794 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.82
2025-07-20 20:05:53,794 - platforms.hgbet_bk - INFO - 处理市场类型 20
2025-07-20 20:05:53,794 - platforms.hgbet_bk - INFO - 解析为小球: 156.0
2025-07-20 20:05:53,794 - platforms.hgbet_bk - INFO - 检查重复投注: 冰岛 vs 德国, 大小球, 投注方向=小, 时段=全场, 比赛ID=410143450, 盘口值=156.0
2025-07-20 20:05:53,795 - platforms.hgbet_bk - WARNING - 重复投注: 冰岛 vs 德国, 大小球盘口
2025-07-20 20:05:53,795 - platforms.hgbet_bk - WARNING - 已有记录: 2025-07-20 20:05:07, 全场/小/156.0, 当前尝试: 全场/小/156.0
2025-07-20 20:05:53,795 - platforms.hgbet_bk - WARNING - 重复投注检测: 同一场比赛的大小球盘口已有投注记录，不允许重复投注
2025-07-20 20:05:53,795 - platforms.hgbet_bk - WARNING - 比赛ID匹配: False, 队伍匹配: True, 盘口类型匹配: True
2025-07-20 20:05:53,795 - platforms.hgbet_bk - WARNING - 投注方向匹配: True, 盘口值匹配: True
2025-07-20 20:05:53,795 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:05:53,795 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:05:53,795 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:05:53,795 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:05:53,796 - hedge_system - WARNING - 对冲投注失败: 冰岛 - 德国
2025-07-20 20:05:53,796 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:05:53,796 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:05:53,796 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013153
2025-07-20 20:05:53,796 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:05:53,796 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:05:53,796 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:05:53,796 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:05:55,847 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 20:05:56,192 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 20:05:56,192 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 20:05:56,192 - PinBet - INFO - 开始查询未结算注单
2025-07-20 20:05:56,572 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 20:05:56,574 - PinBet - INFO - 处理完成: 原始记录 3 条，处理后 3 条记录
2025-07-20 20:05:56,583 - __main__ - INFO - [system] 加载平博记录 3 条
2025-07-20 20:05:56,584 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 20:05:58,768 - __main__ - INFO - [system] 切换到皇冠记录显示模式
2025-07-20 20:05:59,116 - __main__ - INFO - [system] 正在查询皇冠投注记录...
2025-07-20 20:05:59,117 - platforms.hgbet_bk - INFO - 直接获取今日BK注单
2025-07-20 20:05:59,340 - platforms.hgbet_bk - INFO - 今日总投注金额: 3,000
2025-07-20 20:05:59,340 - platforms.hgbet_bk - INFO - 找到 3 条注单
2025-07-20 20:05:59,351 - __main__ - INFO - [system] 加载皇冠记录 3 条
2025-07-20 20:05:59,351 - __main__ - INFO - [system] 皇冠投注记录查询完成
2025-07-20 20:05:59,535 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 20:05:59,536 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 20:05:59,536 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:05:59,536 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:05:59,536 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:05:59,536 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:05:59,536 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:05:59,536 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:05:59,536 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:05:59,536 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:05:59,537 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:05:59,537 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:05:59,537 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:05:59,537 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:05:59,537 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:05:59,537 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:05:59,537 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013159, 超时: 30秒
2025-07-20 20:05:59,537 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 20:05:59,537 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=738.06, 净利润=-282.06
2025-07-20 20:05:59,537 - hedge_system - INFO - 开始对冲投注: 冰岛 - 德国
2025-07-20 20:05:59,538 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=738.06, 总投资=1738.06
2025-07-20 20:05:59,538 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=738.06, 总投资=1738.06
2025-07-20 20:05:59,538 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:05:59,538 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:05:59,538 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:05:59,538 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:05:59,538 - platforms.hgbet_bk - INFO - 解析到投注信息: 冰岛 vs 德国, 市场类型: 20, 盘口: 156.0, 赔率: 1.82
2025-07-20 20:05:59,538 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.82
2025-07-20 20:05:59,538 - platforms.hgbet_bk - INFO - 处理市场类型 20
2025-07-20 20:05:59,538 - platforms.hgbet_bk - INFO - 解析为小球: 156.0
2025-07-20 20:05:59,538 - platforms.hgbet_bk - INFO - 检查重复投注: 冰岛 vs 德国, 大小球, 投注方向=小, 时段=全场, 比赛ID=410143450, 盘口值=156.0
2025-07-20 20:05:59,539 - platforms.hgbet_bk - WARNING - 重复投注: 冰岛 vs 德国, 大小球盘口
2025-07-20 20:05:59,539 - platforms.hgbet_bk - WARNING - 已有记录: 2025-07-20 20:05:07, 全场/小/156.0, 当前尝试: 全场/小/156.0
2025-07-20 20:05:59,539 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:05:59,539 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:05:59,539 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:05:59,539 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:05:59,539 - hedge_system - WARNING - 对冲投注失败: 冰岛 - 德国
2025-07-20 20:05:59,539 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:05:59,539 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:05:59,539 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013159
2025-07-20 20:05:59,540 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:05:59,540 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:05:59,540 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:05:59,540 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:02,741 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 20:06:03,039 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 20:06:03,039 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 20:06:03,039 - PinBet - INFO - 开始查询未结算注单
2025-07-20 20:06:03,404 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 20:06:03,404 - PinBet - INFO - 处理完成: 原始记录 3 条，处理后 3 条记录
2025-07-20 20:06:03,409 - __main__ - INFO - [system] 加载平博记录 3 条
2025-07-20 20:06:03,410 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 20:06:07,089 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 20:06:07,090 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 20:06:07,090 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:06:07,090 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:06:07,090 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:06:07,090 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:06:07,090 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:06:07,090 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:06:07,090 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:06:07,090 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:06:07,090 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:06:07,090 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:06:07,090 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:06:07,090 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:06:07,090 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:06:07,090 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:06:07,090 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013167, 超时: 30秒
2025-07-20 20:06:07,090 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 20:06:07,092 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=734.01, 净利润=-286.01
2025-07-20 20:06:07,092 - hedge_system - INFO - 开始对冲投注: 冰岛 - 德国
2025-07-20 20:06:07,092 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=734.01, 总投资=1734.01
2025-07-20 20:06:07,092 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=734.01, 总投资=1734.01
2025-07-20 20:06:07,092 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:06:07,092 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:06:07,092 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:06:07,092 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:06:07,092 - platforms.hgbet_bk - INFO - 解析到投注信息: 冰岛 vs 德国, 市场类型: 20, 盘口: 156.0, 赔率: 1.81
2025-07-20 20:06:07,092 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.81
2025-07-20 20:06:07,092 - platforms.hgbet_bk - INFO - 处理市场类型 20
2025-07-20 20:06:07,092 - platforms.hgbet_bk - INFO - 解析为小球: 156.0
2025-07-20 20:06:07,092 - platforms.hgbet_bk - INFO - 检查重复投注: 冰岛 vs 德国, 大小球, 投注方向=小, 时段=全场, 比赛ID=410143450, 盘口值=156.0
2025-07-20 20:06:07,093 - platforms.hgbet_bk - WARNING - 重复投注: 冰岛 vs 德国, 大小球盘口
2025-07-20 20:06:07,093 - platforms.hgbet_bk - WARNING - 已有记录: 2025-07-20 20:05:07, 全场/小/156.0, 当前尝试: 全场/小/156.0
2025-07-20 20:06:07,093 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:06:07,093 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:06:07,093 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:06:07,093 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:06:07,093 - hedge_system - WARNING - 对冲投注失败: 冰岛 - 德国
2025-07-20 20:06:07,093 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:06:07,093 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:06:07,093 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013167
2025-07-20 20:06:07,093 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:07,093 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:07,093 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:07,093 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:15,508 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 20:06:15,509 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 20:06:15,509 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:06:15,509 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:06:15,510 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:06:15,510 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:06:15,510 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:06:15,510 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:06:15,510 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:06:15,510 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:06:15,510 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:06:15,510 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:06:15,510 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:06:15,510 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:06:15,510 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:06:15,510 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:06:15,510 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013175, 超时: 30秒
2025-07-20 20:06:15,510 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 20:06:15,510 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=734.01, 净利润=-286.01
2025-07-20 20:06:15,510 - hedge_system - INFO - 开始对冲投注: 冰岛 - 德国
2025-07-20 20:06:15,511 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=734.01, 总投资=1734.01
2025-07-20 20:06:15,511 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=734.01, 总投资=1734.01
2025-07-20 20:06:15,511 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:06:15,511 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:06:15,511 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:06:15,511 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:06:15,511 - platforms.hgbet_bk - INFO - 解析到投注信息: 冰岛 vs 德国, 市场类型: 20, 盘口: 156.0, 赔率: 1.81
2025-07-20 20:06:15,511 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.81
2025-07-20 20:06:15,511 - platforms.hgbet_bk - INFO - 处理市场类型 20
2025-07-20 20:06:15,511 - platforms.hgbet_bk - INFO - 解析为小球: 156.0
2025-07-20 20:06:15,511 - platforms.hgbet_bk - INFO - 检查重复投注: 冰岛 vs 德国, 大小球, 投注方向=小, 时段=全场, 比赛ID=410143450, 盘口值=156.0
2025-07-20 20:06:15,512 - platforms.hgbet_bk - WARNING - 重复投注: 冰岛 vs 德国, 大小球盘口
2025-07-20 20:06:15,512 - platforms.hgbet_bk - WARNING - 已有记录: 2025-07-20 20:05:07, 全场/小/156.0, 当前尝试: 全场/小/156.0
2025-07-20 20:06:15,512 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:06:15,512 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:06:15,512 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:06:15,512 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:06:15,512 - hedge_system - WARNING - 对冲投注失败: 冰岛 - 德国
2025-07-20 20:06:15,512 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:06:15,512 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:06:15,513 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013175
2025-07-20 20:06:15,513 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:15,513 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:15,513 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:15,513 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:20,913 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 20:06:20,914 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 20:06:20,915 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:06:20,915 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:06:20,915 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:06:20,915 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:06:20,915 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:06:20,915 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:06:20,915 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:06:20,915 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:06:20,915 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:06:20,915 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:06:20,915 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:06:20,915 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:06:20,915 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:06:20,915 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:06:20,915 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013180, 超时: 30秒
2025-07-20 20:06:20,916 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 20:06:20,916 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=734.01, 净利润=-286.01
2025-07-20 20:06:20,916 - hedge_system - INFO - 开始对冲投注: 冰岛 - 德国
2025-07-20 20:06:20,916 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=734.01, 总投资=1734.01
2025-07-20 20:06:20,916 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=734.01, 总投资=1734.01
2025-07-20 20:06:20,916 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:06:20,916 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:06:20,916 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:06:20,916 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:06:20,916 - platforms.hgbet_bk - INFO - 解析到投注信息: 冰岛 vs 德国, 市场类型: 20, 盘口: 156.0, 赔率: 1.81
2025-07-20 20:06:20,916 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.81
2025-07-20 20:06:20,916 - platforms.hgbet_bk - INFO - 处理市场类型 20
2025-07-20 20:06:20,916 - platforms.hgbet_bk - INFO - 解析为小球: 156.0
2025-07-20 20:06:20,917 - platforms.hgbet_bk - INFO - 检查重复投注: 冰岛 vs 德国, 大小球, 投注方向=小, 时段=全场, 比赛ID=410143450, 盘口值=156.0
2025-07-20 20:06:20,917 - platforms.hgbet_bk - WARNING - 重复投注: 冰岛 vs 德国, 大小球盘口
2025-07-20 20:06:20,917 - platforms.hgbet_bk - WARNING - 已有记录: 2025-07-20 20:05:07, 全场/小/156.0, 当前尝试: 全场/小/156.0
2025-07-20 20:06:20,917 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:06:20,917 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:06:20,917 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:06:20,917 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:06:20,917 - hedge_system - WARNING - 对冲投注失败: 冰岛 - 德国
2025-07-20 20:06:20,918 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:06:20,918 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:06:20,918 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013180
2025-07-20 20:06:20,918 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:20,918 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:20,918 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:20,918 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:21,362 - __main__ - INFO - [system] 切换到对冲记录显示模式
2025-07-20 20:06:22,655 - __main__ - INFO - [system] 关闭对冲记录自动刷新
2025-07-20 20:06:22,962 - __main__ - INFO - [system] 开启对冲记录自动刷新
2025-07-20 20:06:23,689 - __main__ - INFO - [system] 关闭对冲记录自动刷新
2025-07-20 20:06:24,297 - __main__ - INFO - [system] 正在查询对冲记录...
2025-07-20 20:06:24,299 - __main__ - INFO - [system] 优先加载当天对冲记录: hedge_records_2025-07-20.json
2025-07-20 20:06:24,299 - __main__ - INFO - [system] 显示当天对冲记录 3 条
2025-07-20 20:06:24,307 - __main__ - INFO - [system] 加载对冲记录 3 条 (来自 1 个文件)
2025-07-20 20:06:24,307 - __main__ - INFO - [system] 对冲记录查询完成
2025-07-20 20:06:26,938 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 20:06:26,939 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 20:06:26,939 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:06:26,939 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:06:26,939 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:06:26,939 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:06:26,939 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:06:26,939 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:06:26,940 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:06:26,940 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:06:26,940 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:06:26,940 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:06:26,940 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:06:26,940 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:06:26,940 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:06:26,940 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:06:26,940 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013186, 超时: 30秒
2025-07-20 20:06:26,940 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 20:06:26,940 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=734.01, 净利润=-286.01
2025-07-20 20:06:26,940 - hedge_system - INFO - 开始对冲投注: 冰岛 - 德国
2025-07-20 20:06:26,940 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=734.01, 总投资=1734.01
2025-07-20 20:06:26,940 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=734.01, 总投资=1734.01
2025-07-20 20:06:26,940 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:06:26,940 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:06:26,940 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:06:26,940 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:06:26,940 - platforms.hgbet_bk - INFO - 解析到投注信息: 冰岛 vs 德国, 市场类型: 20, 盘口: 156.0, 赔率: 1.81
2025-07-20 20:06:26,940 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.81
2025-07-20 20:06:26,940 - platforms.hgbet_bk - INFO - 处理市场类型 20
2025-07-20 20:06:26,940 - platforms.hgbet_bk - INFO - 解析为小球: 156.0
2025-07-20 20:06:26,942 - platforms.hgbet_bk - INFO - 检查重复投注: 冰岛 vs 德国, 大小球, 投注方向=小, 时段=全场, 比赛ID=410143450, 盘口值=156.0
2025-07-20 20:06:26,942 - platforms.hgbet_bk - WARNING - 重复投注: 冰岛 vs 德国, 大小球盘口
2025-07-20 20:06:26,942 - platforms.hgbet_bk - WARNING - 已有记录: 2025-07-20 20:05:07, 全场/小/156.0, 当前尝试: 全场/小/156.0
2025-07-20 20:06:26,943 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:06:26,943 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:06:26,943 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:06:26,943 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:06:26,943 - hedge_system - WARNING - 对冲投注失败: 冰岛 - 德国
2025-07-20 20:06:26,943 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:06:26,943 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:06:26,943 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013186
2025-07-20 20:06:26,943 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:26,943 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:26,943 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:26,943 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:32,122 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 20:06:32,124 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 20:06:32,124 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:06:32,124 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:06:32,124 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:06:32,124 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:06:32,124 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:06:32,124 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:06:32,124 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:06:32,124 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:06:32,124 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:06:32,124 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:06:32,124 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:06:32,124 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:06:32,124 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:06:32,124 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:06:32,126 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013192, 超时: 30秒
2025-07-20 20:06:32,126 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 20:06:32,126 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=734.01, 净利润=-286.01
2025-07-20 20:06:32,126 - hedge_system - INFO - 开始对冲投注: 冰岛 - 德国
2025-07-20 20:06:32,126 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=734.01, 总投资=1734.01
2025-07-20 20:06:32,126 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=734.01, 总投资=1734.01
2025-07-20 20:06:32,126 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:06:32,126 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:06:32,126 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:06:32,126 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:06:32,126 - platforms.hgbet_bk - INFO - 解析到投注信息: 冰岛 vs 德国, 市场类型: 20, 盘口: 156.0, 赔率: 1.81
2025-07-20 20:06:32,126 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.81
2025-07-20 20:06:32,126 - platforms.hgbet_bk - INFO - 处理市场类型 20
2025-07-20 20:06:32,127 - platforms.hgbet_bk - INFO - 解析为小球: 156.0
2025-07-20 20:06:32,127 - platforms.hgbet_bk - INFO - 检查重复投注: 冰岛 vs 德国, 大小球, 投注方向=小, 时段=全场, 比赛ID=410143450, 盘口值=156.0
2025-07-20 20:06:32,127 - platforms.hgbet_bk - WARNING - 重复投注: 冰岛 vs 德国, 大小球盘口
2025-07-20 20:06:32,127 - platforms.hgbet_bk - WARNING - 已有记录: 2025-07-20 20:05:07, 全场/小/156.0, 当前尝试: 全场/小/156.0
2025-07-20 20:06:32,127 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:06:32,127 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:06:32,127 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:06:32,127 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:06:32,128 - hedge_system - WARNING - 对冲投注失败: 冰岛 - 德国
2025-07-20 20:06:32,128 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:06:32,128 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:06:32,128 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013192
2025-07-20 20:06:32,128 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:32,128 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:32,128 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:32,128 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:37,878 - platforms.betburger_fetcher - INFO - 获取到 2 个投注选项
2025-07-20 20:06:37,879 - platforms.betburger_fetcher - INFO - 成功获取并保存 2 条投注数据到 betdata-20250720.json
2025-07-20 20:06:37,879 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:06:37,879 - hedge.hedge_integration - INFO - 检查对冲对: pinbet vs crown
2025-07-20 20:06:37,879 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:06:37,879 - hedge.hedge_integration - INFO - ✓ 发现有效对冲机会: 冰岛 - 德国 vs 冰岛 - 德国
2025-07-20 20:06:37,879 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:06:37,879 - hedge.hedge_integration - INFO - 找到 1 个对冲机会
2025-07-20 20:06:37,879 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:06:37,879 - hedge.hedge_integration - INFO - 发现 1 个对冲机会
2025-07-20 20:06:37,879 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:06:37,879 - hedge.hedge_integration - INFO - 开始执行对冲投注
2025-07-20 20:06:37,879 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:06:37,879 - hedge.hedge_integration - INFO - 执行基本验证...
2025-07-20 20:06:37,879 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:06:37,879 - hedge.hedge_integration - INFO - 基本验证通过，直接执行对冲投注
2025-07-20 20:06:37,880 - hedge.execution_lock - INFO - 获取比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013197, 超时: 30秒
2025-07-20 20:06:37,880 - hedge.hedge_calculator - INFO - 皇冠基准: 有效投注800.0 -> 实际投注1000.00 (比例1.0, 折扣0.8)
2025-07-20 20:06:37,880 - hedge.hedge_calculator - INFO - 套利计算结果: 基准=1000.0, 对冲=734.01, 净利润=-286.01
2025-07-20 20:06:37,880 - hedge_system - INFO - 开始对冲投注: 冰岛 - 德国
2025-07-20 20:06:37,880 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=734.01, 总投资=1734.01
2025-07-20 20:06:37,880 - hedge.hedge_manager - INFO - 对冲投注计划: 基准平台=crown, 基准金额=1000.0, 对冲金额=734.01, 总投资=1734.01
2025-07-20 20:06:37,880 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:06:37,880 - hedge.hedge_manager - INFO - 使用顺序投注模式
2025-07-20 20:06:37,881 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:06:37,881 - hedge.hedge_manager - INFO - 开始顺序投注: 基准平台=crown, 基准金额=1000.0
2025-07-20 20:06:37,881 - platforms.hgbet_bk - INFO - 解析到投注信息: 冰岛 vs 德国, 市场类型: 20, 盘口: 156.0, 赔率: 1.81
2025-07-20 20:06:37,881 - platforms.hgbet_bk - INFO - 转换后的赔率值: 1.81
2025-07-20 20:06:37,881 - platforms.hgbet_bk - INFO - 处理市场类型 20
2025-07-20 20:06:37,881 - platforms.hgbet_bk - INFO - 解析为小球: 156.0
2025-07-20 20:06:37,881 - platforms.hgbet_bk - INFO - 检查重复投注: 冰岛 vs 德国, 大小球, 投注方向=小, 时段=全场, 比赛ID=410143450, 盘口值=156.0
2025-07-20 20:06:37,882 - platforms.hgbet_bk - WARNING - 重复投注: 冰岛 vs 德国, 大小球盘口
2025-07-20 20:06:37,882 - platforms.hgbet_bk - WARNING - 已有记录: 2025-07-20 20:05:07, 全场/小/156.0, 当前尝试: 全场/小/156.0
2025-07-20 20:06:37,882 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:06:37,882 - hedge.hedge_manager - WARNING - 皇冠平台检测到重复投注，跳过: 冰岛 vs 德国, 大小球=小
2025-07-20 20:06:37,882 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:06:37,882 - hedge.hedge_manager - ERROR - 基准平台(crown)投注失败: 检测到重复投注，已跳过
2025-07-20 20:06:37,882 - hedge_system - WARNING - 对冲投注失败: 冰岛 - 德国
2025-07-20 20:06:37,882 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:06:37,882 - hedge.hedge_manager - INFO - 检测到重复投注，不保存任何记录
2025-07-20 20:06:37,882 - hedge.execution_lock - INFO - 释放比赛锁成功: hedge:冰岛 - 德国, 锁ID: hedge:冰岛 - 德国:1753013197
2025-07-20 20:06:37,882 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:37,882 - hedge.hedge_integration - WARNING - 对冲投注执行失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:37,882 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:06:37,882 - hedge.hedge_integration - WARNING - 对冲投注失败: 基准平台(crown)投注失败，对冲取消
2025-07-20 20:10:48,656 - __main__ - INFO - [system] 切换到平博记录显示模式
2025-07-20 20:10:49,041 - __main__ - INFO - [system] 正在查询平博投注记录...
2025-07-20 20:10:49,042 - PinBet - INFO - 会话空闲481秒，执行主动刷新以保持活跃
2025-07-20 20:11:08,284 - PinBet - ERROR - 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 20:11:08,284 - PinBet - WARNING - 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 20:11:08,284 - PinBet - WARNING - 主动刷新失败: 会话刷新失败: 获取余额失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-20 20:11:08,284 - PinBet - INFO - 开始查询未结算注单（跳过登录状态检查）
2025-07-20 20:11:08,284 - PinBet - INFO - 开始查询未结算注单
2025-07-20 20:11:09,278 - PinBet - INFO - 成功获取未结算注单数据
2025-07-20 20:11:09,278 - PinBet - INFO - 处理完成: 原始记录 3 条，处理后 3 条记录
2025-07-20 20:11:09,287 - __main__ - INFO - [system] 加载平博记录 3 条
2025-07-20 20:11:09,287 - __main__ - INFO - [system] 平博投注记录查询完成
2025-07-20 20:11:11,981 - __main__ - INFO - [system] 切换到皇冠记录显示模式
2025-07-20 20:11:12,646 - __main__ - INFO - [system] 正在查询皇冠投注记录...
2025-07-20 20:11:12,646 - platforms.hgbet_bk - INFO - 直接获取今日BK注单
2025-07-20 20:11:12,889 - platforms.hgbet_bk - INFO - 今日总投注金额: 3,000
2025-07-20 20:11:12,889 - platforms.hgbet_bk - INFO - 找到 3 条注单
2025-07-20 20:11:12,899 - __main__ - INFO - [system] 加载皇冠记录 3 条
2025-07-20 20:11:12,899 - __main__ - INFO - [system] 皇冠投注记录查询完成
2025-07-20 20:52:58,461 - __main__ - INFO - [system] 正在停止对冲监控...
2025-07-20 20:52:58,461 - hedge.hedge_integration - INFO - 收到停止监控信号
2025-07-20 20:52:58,461 - hedge.hedge_integration - INFO - 收到停止监控信号
2025-07-20 20:52:59,186 - hedge.hedge_integration - INFO - 对冲监控已停止
2025-07-20 20:52:59,186 - hedge.hedge_integration - INFO - 对冲监控已停止
2025-07-20 20:52:59,188 - __main__ - INFO - [system] 对冲监控已成功停止
2025-07-20 21:10:37,054 - __main__ - INFO - GUI日志将保存到: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 21:19:25,907 - __main__ - INFO - GUI日志将保存到: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 21:33:18,542 - __main__ - INFO - GUI日志将保存到: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 21:34:15,493 - __main__ - INFO - GUI日志将保存到: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 21:34:16,044 - __main__ - INFO - [system] BetBurger对冲投注系统GUI已启动
2025-07-20 21:34:16,044 - __main__ - INFO - [system] 请先点击'初始化系统'，然后'登录系统'
2025-07-20 21:34:16,148 - __main__ - INFO - [system] 配置加载成功
2025-07-20 21:34:16,149 - __main__ - INFO - [GUI] GUI程序启动，日志文件: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 21:34:27,332 - __main__ - INFO - [system] 对冲记录显示已清空
2025-07-20 21:34:27,332 - __main__ - INFO - [system] 正在查询对冲记录...
2025-07-20 21:34:27,334 - __main__ - INFO - [system] 优先加载当天对冲记录: hedge_records_2025-07-20.json
2025-07-20 21:34:27,334 - __main__ - INFO - [system] 显示当天对冲记录 3 条
2025-07-20 21:34:27,350 - __main__ - INFO - [system] 加载对冲记录 3 条 (来自 1 个文件)
2025-07-20 21:34:27,350 - __main__ - INFO - [system] 对冲记录查询完成
2025-07-20 21:34:29,314 - __main__ - INFO - [system] 单边待补单记录显示已清空
2025-07-20 21:34:29,314 - __main__ - INFO - [system] 正在查询单边待补单记录...
2025-07-20 21:34:29,314 - hedge.partial_record_manager - INFO - 单边记录管理器初始化完成
2025-07-20 21:34:29,320 - __main__ - INFO - [system] 加载单边待补单记录 1 条
2025-07-20 21:34:29,320 - __main__ - INFO - [system] 单边待补单记录查询完成
2025-07-20 21:35:07,274 - __main__ - INFO - [system] 配置保存成功，对冲系统配置已更新
2025-07-20 21:35:26,415 - __main__ - INFO - GUI日志将保存到: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 21:35:26,970 - __main__ - INFO - [system] BetBurger对冲投注系统GUI已启动
2025-07-20 21:35:26,970 - __main__ - INFO - [system] 请先点击'初始化系统'，然后'登录系统'
2025-07-20 21:35:27,081 - __main__ - INFO - [system] 配置加载成功
2025-07-20 21:35:27,081 - __main__ - INFO - [GUI] GUI程序启动，日志文件: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 21:36:48,274 - __main__ - INFO - GUI日志将保存到: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 21:36:48,805 - __main__ - INFO - [system] BetBurger对冲投注系统GUI已启动
2025-07-20 21:36:48,806 - __main__ - INFO - [system] 请先点击'初始化系统'，然后'登录系统'
2025-07-20 21:36:48,917 - __main__ - INFO - [system] 配置加载成功
2025-07-20 21:36:48,917 - __main__ - INFO - [GUI] GUI程序启动，日志文件: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 21:53:13,058 - gui.main - INFO - GUI日志将保存到: D:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 22:06:27,832 - gui.main - INFO - GUI日志将保存到: D:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 22:07:29,568 - __main__ - INFO - GUI日志将保存到: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 22:07:30,239 - __main__ - INFO - [system] BetBurger对冲投注系统GUI已启动
2025-07-20 22:07:30,239 - __main__ - INFO - [system] 请先点击'初始化系统'，然后'登录系统'
2025-07-20 22:07:30,355 - __main__ - INFO - [system] 配置加载成功
2025-07-20 22:07:30,355 - __main__ - INFO - [GUI] GUI程序启动，日志文件: d:\betburgerbot\logs\gui_2025-07-20.log
2025-07-20 22:07:38,186 - __main__ - INFO - [system] 正在初始化系统...
2025-07-20 22:07:38,186 - hedge_main - INFO - 初始化对冲投注系统
2025-07-20 22:07:38,207 - utils.config_security - INFO - 加载现有加密密钥
2025-07-20 22:07:38,207 - utils.config_security - INFO - 加载未加密的配置文件
2025-07-20 22:07:38,207 - config.hedge_config - INFO - 对冲配置首次加载完成
2025-07-20 22:07:38,207 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 22:07:38,207 - config.hedge_config - WARNING - 参数 enable_rollback 不是布尔值, 使用默认值: False
2025-07-20 22:07:38,207 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 22:07:38,208 - utils.match_data_fetcher - INFO - 比赛数据获取器初始化完成，缓存目录: data/match_cache
2025-07-20 22:07:38,208 - utils.match_data_fetcher - INFO - 比赛数据获取器初始化完成，缓存目录: data/match_cache
2025-07-20 22:07:38,208 - utils.match_learning_enhanced - INFO - 增强匹配学习系统初始化完成，数据目录: data/match_learning
2025-07-20 22:07:38,209 - utils.alias_manager - INFO - 别名管理器初始化完成，配置目录: config
2025-07-20 22:07:38,209 - utils.match_learning_controller - INFO - 比赛匹配学习控制器初始化完成
2025-07-20 22:07:38,209 - hedge_main - INFO - 正在初始化投注系统...
2025-07-20 22:07:38,210 - hedge_main - INFO - 平博系统初始化完成
2025-07-20 22:07:38,211 - platforms.hgbet_bk - INFO - 初始化皇冠篮球投注模块
2025-07-20 22:07:38,212 - platforms.hgbet_bk - INFO - 投注记录将保存到: data\bet_records_2025-07-20.json
2025-07-20 22:07:38,212 - hedge_main - INFO - 皇冠系统初始化完成
2025-07-20 22:07:38,212 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 22:07:38,212 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 22:07:38,212 - validation.match_validator - INFO - 比赛匹配验证器初始化完成
2025-07-20 22:07:38,212 - validation.match_safety - INFO - 比赛匹配安全检查器初始化完成
2025-07-20 22:07:38,212 - config.hedge_config - INFO - 对冲配置加载完成
2025-07-20 22:07:38,213 - config.hedge_config - INFO - 对冲配置验证完成
2025-07-20 22:07:38,213 - hedge.hedge_calculator - INFO - 投注金额计算器初始化完成
2025-07-20 22:07:38,213 - hedge.hedge_monitor - INFO - 投注监控器初始化完成
2025-07-20 22:07:38,213 - hedge.hedge_record_manager - INFO - 对冲记录管理器初始化完成
2025-07-20 22:07:38,213 - hedge.hedge_manager - INFO - 对冲记录管理器初始化成功
2025-07-20 22:07:38,213 - hedge.partial_record_manager - INFO - 单边记录管理器初始化完成
2025-07-20 22:07:38,213 - hedge.hedge_manager - INFO - 单边记录管理器初始化成功
2025-07-20 22:07:38,213 - hedge.hedge_manager - INFO - 对冲管理器初始化完成
2025-07-20 22:07:38,213 - hedge.hedge_integration - INFO - 对冲集成器初始化完成（实时匹配模式）
2025-07-20 22:07:38,213 - hedge_main - INFO - 对冲集成器初始化完成
2025-07-20 22:07:38,213 - __main__ - INFO - [system] 系统初始化成功
2025-07-20 22:07:38,214 - __main__ - INFO - [GUI] 对冲系统日志转发设置完成
2025-07-20 22:07:38,214 - __main__ - INFO - [system] 启动日志监控...
2025-07-20 22:07:38,215 - __main__ - INFO - [system] 加载最近日志...
2025-07-20 22:07:38,216 - __main__ - INFO - [GUI] 正在加载最近20条日志...
2025-07-20 22:07:38,263 - __main__ - INFO - [GUI] 最近日志加载完成，共显示 20 条日志
2025-07-20 22:08:28,052 - __main__ - INFO - [GUI] 日志监控已停止
