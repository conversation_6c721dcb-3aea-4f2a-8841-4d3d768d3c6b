#!/usr/bin/env python
# -*- coding: utf-8 -*-

import threading
import time
import logging
from typing import Dict, Optional, Set
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class LockInfo:
    """锁信息数据类"""
    lock_id: str
    platform: str
    match_key: str
    created_at: datetime
    expires_at: datetime
    thread_id: int

class ExecutionLockManager:
    """投注执行锁管理器"""
    
    def __init__(self, default_timeout: int = 30):
        """
        初始化锁管理器
        
        参数:
            default_timeout: 默认锁超时时间（秒）
        """
        self.default_timeout = default_timeout
        self.locks: Dict[str, LockInfo] = {}
        self.global_locks: Set[str] = set()  # 全局锁（阻止所有投注）
        self._lock = threading.RLock()  # 内部锁保护数据结构
        
        # 启动清理线程
        self._cleanup_thread = threading.Thread(target=self._cleanup_expired_locks, daemon=True)
        self._cleanup_thread.start()
        
        logger.info("投注执行锁管理器初始化完成")
    
    def acquire_match_lock(self, platform: str, match_key: str, timeout: Optional[int] = None) -> Optional[str]:
        """
        获取比赛级别的投注锁
        
        参数:
            platform: 平台名称
            match_key: 比赛标识（通常是"主队 vs 客队"）
            timeout: 锁超时时间（秒），None使用默认值
        
        返回:
            锁ID，如果获取失败返回None
        """
        timeout = timeout or self.default_timeout
        lock_key = f"{platform}:{match_key}"
        
        with self._lock:
            # 检查是否有全局锁
            if self.global_locks:
                logger.warning(f"存在全局锁，无法获取比赛锁: {lock_key}")
                return None
            
            # 检查是否已有锁
            if lock_key in self.locks:
                existing_lock = self.locks[lock_key]
                if existing_lock.expires_at > datetime.now():
                    logger.warning(f"比赛锁已存在: {lock_key}, 过期时间: {existing_lock.expires_at}")
                    return None
                else:
                    # 锁已过期，清理
                    del self.locks[lock_key]
            
            # 创建新锁
            lock_id = f"{lock_key}:{int(time.time())}"
            lock_info = LockInfo(
                lock_id=lock_id,
                platform=platform,
                match_key=match_key,
                created_at=datetime.now(),
                expires_at=datetime.now() + timedelta(seconds=timeout),
                thread_id=threading.get_ident()
            )
            
            self.locks[lock_key] = lock_info
            logger.info(f"获取比赛锁成功: {lock_key}, 锁ID: {lock_id}, 超时: {timeout}秒")
            return lock_id
    
    def release_match_lock(self, lock_id: str) -> bool:
        """
        释放比赛级别的投注锁
        
        参数:
            lock_id: 锁ID
        
        返回:
            是否释放成功
        """
        with self._lock:
            # 查找并释放锁
            for lock_key, lock_info in list(self.locks.items()):
                if lock_info.lock_id == lock_id:
                    del self.locks[lock_key]
                    logger.info(f"释放比赛锁成功: {lock_key}, 锁ID: {lock_id}")
                    return True
            
            logger.warning(f"未找到要释放的锁: {lock_id}")
            return False
    
    def acquire_global_lock(self, reason: str, timeout: Optional[int] = None) -> Optional[str]:
        """
        获取全局锁（阻止所有投注）
        
        参数:
            reason: 获取锁的原因
            timeout: 锁超时时间（秒）
        
        返回:
            锁ID，如果获取失败返回None
        """
        timeout = timeout or self.default_timeout
        
        with self._lock:
            if self.global_locks:
                logger.warning(f"全局锁已存在，无法获取新的全局锁: {reason}")
                return None
            
            lock_id = f"global:{int(time.time())}"
            self.global_locks.add(lock_id)
            
            logger.warning(f"获取全局锁: {lock_id}, 原因: {reason}, 超时: {timeout}秒")
            
            # 设置定时释放
            def auto_release():
                time.sleep(timeout)
                self.release_global_lock(lock_id)
            
            threading.Thread(target=auto_release, daemon=True).start()
            return lock_id
    
    def release_global_lock(self, lock_id: str) -> bool:
        """
        释放全局锁
        
        参数:
            lock_id: 锁ID
        
        返回:
            是否释放成功
        """
        with self._lock:
            if lock_id in self.global_locks:
                self.global_locks.remove(lock_id)
                logger.info(f"释放全局锁: {lock_id}")
                return True
            else:
                logger.warning(f"未找到要释放的全局锁: {lock_id}")
                return False
    
    def is_match_locked(self, platform: str, match_key: str) -> bool:
        """
        检查比赛是否被锁定
        
        参数:
            platform: 平台名称
            match_key: 比赛标识
        
        返回:
            是否被锁定
        """
        lock_key = f"{platform}:{match_key}"
        
        with self._lock:
            # 检查全局锁
            if self.global_locks:
                return True
            
            # 检查比赛锁
            if lock_key in self.locks:
                lock_info = self.locks[lock_key]
                if lock_info.expires_at > datetime.now():
                    return True
                else:
                    # 锁已过期，清理
                    del self.locks[lock_key]
            
            return False
    
    def get_lock_status(self) -> Dict:
        """获取锁状态信息"""
        with self._lock:
            active_locks = []
            for lock_key, lock_info in self.locks.items():
                if lock_info.expires_at > datetime.now():
                    active_locks.append({
                        "lock_key": lock_key,
                        "lock_id": lock_info.lock_id,
                        "platform": lock_info.platform,
                        "match_key": lock_info.match_key,
                        "created_at": lock_info.created_at.isoformat(),
                        "expires_at": lock_info.expires_at.isoformat(),
                        "remaining_seconds": (lock_info.expires_at - datetime.now()).total_seconds()
                    })
            
            return {
                "global_locks": list(self.global_locks),
                "active_match_locks": active_locks,
                "total_locks": len(active_locks) + len(self.global_locks)
            }
    
    def _cleanup_expired_locks(self):
        """清理过期锁的后台线程"""
        while True:
            try:
                time.sleep(10)  # 每10秒清理一次
                
                with self._lock:
                    current_time = datetime.now()
                    expired_keys = []
                    
                    for lock_key, lock_info in self.locks.items():
                        if lock_info.expires_at <= current_time:
                            expired_keys.append(lock_key)
                    
                    for key in expired_keys:
                        del self.locks[key]
                        logger.debug(f"清理过期锁: {key}")
                        
            except Exception as e:
                logger.error(f"清理过期锁时出错: {e}")

# 全局锁管理器实例
_lock_manager = None

def get_lock_manager() -> ExecutionLockManager:
    """获取全局锁管理器实例"""
    global _lock_manager
    if _lock_manager is None:
        _lock_manager = ExecutionLockManager()
    return _lock_manager

class MatchLockContext:
    """比赛锁上下文管理器"""
    
    def __init__(self, platform: str, match_key: str, timeout: Optional[int] = None):
        self.platform = platform
        self.match_key = match_key
        self.timeout = timeout
        self.lock_id = None
        self.lock_manager = get_lock_manager()
    
    def __enter__(self):
        self.lock_id = self.lock_manager.acquire_match_lock(
            self.platform, self.match_key, self.timeout
        )
        if self.lock_id is None:
            raise RuntimeError(f"无法获取比赛锁: {self.platform}:{self.match_key}")
        return self.lock_id
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.lock_id:
            self.lock_manager.release_match_lock(self.lock_id)
