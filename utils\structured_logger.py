#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional
from enum import Enum

class EventType(Enum):
    """事件类型枚举"""
    HEDGE_START = "hedge_start"
    HEDGE_COMPLETE = "hedge_complete"
    HEDGE_FAILED = "hedge_failed"
    BET_EXECUTION = "bet_execution"
    BET_SUCCESS = "bet_success"
    BET_FAILED = "bet_failed"
    DUPLICATE_DETECTED = "duplicate_detected"
    LOCK_ACQUIRED = "lock_acquired"
    LOCK_RELEASED = "lock_released"
    RETRY_ATTEMPT = "retry_attempt"
    SYSTEM_HEALTH = "system_health"
    CONFIG_CHANGE = "config_change"
    ERROR_OCCURRED = "error_occurred"

class StructuredLogger:
    """结构化日志管理器"""
    
    def __init__(self, logger_name: str = "hedge_system"):
        """
        初始化结构化日志管理器
        
        参数:
            logger_name: 日志器名称
        """
        self.logger = logging.getLogger(logger_name)
        self.session_id = self._generate_session_id()
    
    def _generate_session_id(self) -> str:
        """生成会话ID"""
        return f"session_{int(time.time())}"
    
    def log_event(self, event_type: EventType, message: str, 
                  level: str = "info", **kwargs):
        """
        记录结构化事件日志
        
        参数:
            event_type: 事件类型
            message: 日志消息
            level: 日志级别
            **kwargs: 额外的结构化数据
        """
        # 构建结构化数据
        structured_data = {
            "timestamp": datetime.now().isoformat(),
            "session_id": self.session_id,
            "event_type": event_type.value,
            "message": message,
            **kwargs
        }
        
        # 根据级别记录日志
        log_method = getattr(self.logger, level.lower(), self.logger.info)
        log_method(message, extra={"structured_data": structured_data})
    
    def log_hedge_start(self, match_key: str, base_platform: str, 
                       base_amount: float, hedge_amount: float, **kwargs):
        """记录对冲开始事件"""
        self.log_event(
            EventType.HEDGE_START,
            f"开始对冲投注: {match_key}",
            level="info",
            match_key=match_key,
            base_platform=base_platform,
            base_amount=base_amount,
            hedge_amount=hedge_amount,
            total_investment=base_amount + hedge_amount,
            **kwargs
        )
    
    def log_hedge_complete(self, match_key: str, execution_time: float, 
                          success: bool, **kwargs):
        """记录对冲完成事件"""
        level = "info" if success else "warning"
        status = "成功" if success else "失败"
        
        self.log_event(
            EventType.HEDGE_COMPLETE,
            f"对冲投注{status}: {match_key}",
            level=level,
            match_key=match_key,
            execution_time=execution_time,
            success=success,
            **kwargs
        )
    
    def log_bet_execution(self, platform: str, match_key: str, 
                         bet_type: str, amount: float, odds: float, **kwargs):
        """记录投注执行事件"""
        self.log_event(
            EventType.BET_EXECUTION,
            f"执行投注: {platform} - {match_key}",
            level="info",
            platform=platform,
            match_key=match_key,
            bet_type=bet_type,
            amount=amount,
            odds=odds,
            **kwargs
        )
    
    def log_bet_result(self, platform: str, match_key: str, success: bool, 
                      execution_time: float, order_id: Optional[str] = None, 
                      error_message: Optional[str] = None, **kwargs):
        """记录投注结果事件"""
        event_type = EventType.BET_SUCCESS if success else EventType.BET_FAILED
        level = "info" if success else "warning"
        status = "成功" if success else "失败"
        
        log_data = {
            "platform": platform,
            "match_key": match_key,
            "success": success,
            "execution_time": execution_time,
            **kwargs
        }
        
        if order_id:
            log_data["order_id"] = order_id
        if error_message:
            log_data["error_message"] = error_message
        
        self.log_event(
            event_type,
            f"投注{status}: {platform} - {match_key}",
            level=level,
            **log_data
        )
    
    def log_duplicate_detected(self, platform: str, match_key: str, 
                             bet_type: str, **kwargs):
        """记录重复投注检测事件"""
        self.log_event(
            EventType.DUPLICATE_DETECTED,
            f"检测到重复投注: {platform} - {match_key}",
            level="warning",
            platform=platform,
            match_key=match_key,
            bet_type=bet_type,
            **kwargs
        )
    
    def log_lock_operation(self, operation: str, lock_type: str, 
                          lock_key: str, success: bool, **kwargs):
        """记录锁操作事件"""
        event_type = EventType.LOCK_ACQUIRED if operation == "acquire" else EventType.LOCK_RELEASED
        level = "debug"
        
        self.log_event(
            event_type,
            f"锁{operation}: {lock_type} - {lock_key}",
            level=level,
            operation=operation,
            lock_type=lock_type,
            lock_key=lock_key,
            success=success,
            **kwargs
        )
    
    def log_retry_attempt(self, platform: str, attempt: int, max_attempts: int, 
                         error_message: str, **kwargs):
        """记录重试尝试事件"""
        self.log_event(
            EventType.RETRY_ATTEMPT,
            f"重试投注: {platform} ({attempt}/{max_attempts})",
            level="warning",
            platform=platform,
            attempt=attempt,
            max_attempts=max_attempts,
            error_message=error_message,
            **kwargs
        )
    
    def log_system_health(self, status: str, success_rate: float, 
                         avg_duration: float, **kwargs):
        """记录系统健康状态事件"""
        level = "info" if status == "healthy" else "warning"
        
        self.log_event(
            EventType.SYSTEM_HEALTH,
            f"系统健康状态: {status}",
            level=level,
            status=status,
            success_rate=success_rate,
            avg_duration=avg_duration,
            **kwargs
        )
    
    def log_config_change(self, config_key: str, old_value: Any, 
                         new_value: Any, **kwargs):
        """记录配置变更事件"""
        self.log_event(
            EventType.CONFIG_CHANGE,
            f"配置变更: {config_key}",
            level="info",
            config_key=config_key,
            old_value=old_value,
            new_value=new_value,
            **kwargs
        )
    
    def log_error(self, error_type: str, error_message: str, 
                 context: Optional[Dict] = None, **kwargs):
        """记录错误事件"""
        log_data = {
            "error_type": error_type,
            "error_message": error_message,
            **kwargs
        }
        
        if context:
            log_data["context"] = context
        
        self.log_event(
            EventType.ERROR_OCCURRED,
            f"错误发生: {error_type}",
            level="error",
            **log_data
        )

class StructuredLogFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record):
        # 基础格式化
        formatted = super().format(record)
        
        # 如果有结构化数据，添加到日志中
        if hasattr(record, 'structured_data'):
            structured_json = json.dumps(
                record.structured_data, 
                ensure_ascii=False, 
                separators=(',', ':')
            )
            formatted += f" | STRUCTURED: {structured_json}"
        
        return formatted

def setup_structured_logging(logger_name: str = "hedge_system", 
                            log_file: Optional[str] = None) -> StructuredLogger:
    """
    设置结构化日志
    
    参数:
        logger_name: 日志器名称
        log_file: 日志文件路径
    
    返回:
        结构化日志管理器
    """
    # 创建结构化日志管理器
    structured_logger = StructuredLogger(logger_name)
    
    # 设置格式化器
    formatter = StructuredLogFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    structured_logger.logger.addHandler(console_handler)
    
    # 文件处理器（如果指定了文件）
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        structured_logger.logger.addHandler(file_handler)
    
    # 设置日志级别
    structured_logger.logger.setLevel(logging.INFO)
    
    return structured_logger

# 全局结构化日志管理器
_structured_logger = None

def get_structured_logger() -> StructuredLogger:
    """获取全局结构化日志管理器"""
    global _structured_logger
    if _structured_logger is None:
        _structured_logger = setup_structured_logging()
    return _structured_logger
