# 更新日志

## 🎉 v2.1.2 - 状态和开赛时间修复版本

### 🐛 问题修复

#### 皇冠记录状态和开赛时间修复
- ❌ **问题**: 皇冠记录的状态栏和开赛时间显示不正确
- ✅ **修复**: 参考web程序逻辑，使用正确的字段和转换方法
  - 开赛时间: 优先使用 `datetime`/`DATETIME` 字段（比赛开赛时间），回退到 `addtime`（投注时间）
  - 比赛信息: 使用 `team_h_show` 和 `team_c_show` 字段（与web程序一致）
  - 状态转换: 实现 `convert_crown_status()` 方法，空值=成功，非空=失败

#### 皇冠记录查询字段映射修复
- ❌ **问题**: 皇冠记录查询时只有金额显示，其他字段都为空
- ✅ **修复**: 使用正确的API字段名称映射
  - 开赛时间: `datetime`/`DATETIME` 字段（优先）或 `addtime` 字段
  - 比赛信息: `team_h_show` 和 `team_c_show` 字段
  - 投注信息: 组合 `bet_type_desc`、`bet_choice`/`chose_team`、`period` 字段
  - 赔率: `ioratio` 字段
  - 状态: `wager_status` 字段 + 状态转换

#### 平博记录查询字段映射修复
- ✅ **优化**: 使用正确的数组索引解析平博API返回数据
  - 投注时间: 索引14
  - 比赛信息: 索引9
  - 投注信息: 组合索引22和24
  - 投注金额: RISK字段后的第3个位置
  - 状态: 索引18

#### 调试功能增强
- ✅ **新增**: 记录解析失败时输出调试信息
- ✅ **新增**: 测试脚本 `test_records.py` 用于验证字段映射

### 🔧 技术改进

#### 皇冠记录解析逻辑
```python
# 修复前（错误的字段名和逻辑）
start_time = wager.get('addtime', '')
match_info = f"{wager.get('team_h_display', '')} vs {wager.get('team_c_display', '')}"
status = wager.get('wager_status', '未结算')

# 修复后（正确的字段名和状态转换）
# 开赛时间：优先使用比赛开赛时间，回退到投注时间
start_time = wager.get('datetime', wager.get('DATETIME', ''))
if not start_time:
    start_time = wager.get('addtime', '')

# 比赛信息：使用与web程序一致的字段
team_h = wager.get('team_h_show', wager.get('team_h', ''))
team_c = wager.get('team_c_show', wager.get('team_c', ''))

# 状态转换：实现与web程序一致的逻辑
def convert_crown_status(wager_status):
    if not wager_status or wager_status == "" or wager_status == "已成功":
        return '成功'
    elif wager_status == "未知":
        return '未知'
    else:
        return '失败'
```

#### 平博记录解析逻辑
```python
# 修复前（错误的字典访问）
start_time = record.get('eventStartTime', record.get('betTime', ''))

# 修复后（正确的数组索引）
start_time = str(record[14]) if len(record) > 14 and record[14] is not None else ""
```

---

## 🎉 v2.1.0 - 记录管理优化版本

### 🚀 重大改进

#### 1. 记录管理功能重构
- ❌ **移除"刷新记录"**: 改为"对冲记录获取"
- ✅ **新增记录类型选择**: 对冲记录、平博记录、皇冠记录
- ✅ **动态表格结构**: 根据记录类型自动调整表头和列
- ✅ **点击查询模式**: 选择类型后点击"查询记录"按钮才获取数据
- ✅ **数据清理机制**: 每次查询前自动清空现有记录，避免重复叠加

#### 2. 表格格式完全对应Web程序
- ✅ **对冲记录表头**: 对冲ID、创建时间、比赛、平博投注、皇冠投注、总投注额、预期盈利、实际盈利、状态
- ✅ **平博记录表头**: 开赛时间、比赛、投注信息、金额、赔率、状态
- ✅ **皇冠记录表头**: 开赛时间、比赛、投注信息、金额、赔率、状态

#### 3. 用户体验优化
- ✅ **操作流程**: 选择记录类型 → 点击查询按钮 → 获取对应数据
- ✅ **界面布局**: 记录类型选择器 + 查询按钮 + 导出/清空按钮
- ✅ **状态反馈**: 查询过程中显示详细日志信息

### 🔧 技术改进

#### 界面布局变更
```
原来: [刷新记录] [平博记录] [皇冠记录] [导出记录] [清空记录]
现在: 记录类型: [对冲记录 ▼] [🔍 查询记录] [📥 导出记录] [🗑️ 清空显示]
```

#### 表格结构动态调整
```python
# 对冲记录表格
columns = ("hedge_id", "create_time", "match", "pinbet_bet", "crown_bet",
          "total_amount", "expected_profit", "actual_profit", "status")

# 平博/皇冠记录表格
columns = ("start_time", "match", "bet_info", "amount", "odds", "status")
```

#### 查询流程优化
```python
def query_records(self):
    record_type = self.get_record_type_code()
    self.clear_records_display()  # 先清空

    if record_type == "hedge":
        self.query_hedge_records()
    elif record_type == "pinbet":
        self.query_pinbet_records()
    elif record_type == "crown":
        self.query_crown_records()
```

---

## 🎉 v2.0.0 - 真实API集成版本

### 🚀 重大改进

#### 1. 移除所有模拟数据，全部接入真实API
- ❌ **移除模拟余额数据**: 原来显示固定的"平博余额: ¥1000.00, 皇冠余额: ¥1500.00"
- ✅ **接入真实余额API**: 
  - 平博: 调用 `pinbet_system.api.get_balance()` 获取真实余额
  - 皇冠: 调用 `crown_system.get_account_balance()` 获取真实余额

#### 2. 新增平博记录查询功能
- ✅ **平博记录按钮**: 新增"平博记录"按钮
- ✅ **真实API调用**: 使用 `pinbet_system.api.get_pending_wagers()` 获取未结算注单
- ✅ **数据解析**: 解析投注时间、比赛信息、投注类型、金额、赔率、状态
- ✅ **表格显示**: 在记录管理页面表格中显示真实数据

#### 3. 新增皇冠记录查询功能
- ✅ **皇冠记录按钮**: 新增"皇冠记录"按钮
- ✅ **真实API调用**: 使用 `crown_system.get_today_wagers_direct(gtype="BK")` 获取今日篮球注单
- ✅ **数据解析**: 解析投注时间、比赛信息、投注类型、金额、赔率、状态
- ✅ **表格显示**: 在记录管理页面表格中显示真实数据

#### 4. 完善平台投注额比例配置
- ✅ **配置界面**: 平台投注比例配置已存在并完善
- ✅ **说明文字**: 添加"设置各平台的投注金额比例（1.0为正常比例）"说明
- ✅ **数据保存**: 配置数据正确保存到 `config.json` 的 `platform_bet_ratios` 字段

#### 5. 真实连接测试
- ❌ **移除模拟测试**: 原来固定返回 `success = True`
- ✅ **平博真实测试**: 
  - 检查登录状态
  - 调用余额API验证连接
  - 显示真实余额信息
- ✅ **皇冠真实测试**:
  - 检查登录状态
  - 调用账户余额API验证连接
  - 显示真实余额信息

### 🔧 技术改进

#### API集成详情
```python
# 平博余额查询
success, balance_data = pinbet_system.api.get_balance()
balance = balance_data.get('betCredit', 0)
currency = balance_data.get('currency', 'CNY')

# 平博记录查询
success, wagers_data = pinbet_system.api.get_pending_wagers()
records = wagers_data.get('page', {}).get('records', [])

# 皇冠余额查询
account_data = crown_system.get_account_balance()
balance = crown_system.credit

# 皇冠记录查询
wagers = crown_system.get_today_wagers_direct(gtype="BK")
```

#### 数据解析
- **平博记录解析**: 解析 `betTime`, `homeTeam`, `awayTeam`, `betType`, `stake`, `odds`
- **皇冠记录解析**: 解析 `bet_time`, `team_h`, `team_c`, `bet_type`, `gold`, `ioradio_r_h`
- **投注类型映射**: 将数字类型转换为中文描述（独赢、让分、大小、单队大小）

#### 错误处理
- ✅ **异常捕获**: 所有API调用都有完整的异常处理
- ✅ **状态检查**: 检查系统初始化状态和登录状态
- ✅ **用户提示**: 通过日志系统提供详细的操作反馈

### 📋 界面改进

#### 记录管理页面
```
原来: [刷新记录] [导出记录] [清空记录]
现在: [刷新记录] [平博记录] [皇冠记录] [导出记录] [清空记录]
```

#### 配置管理页面
```
平台投注比例配置
├── 说明: 设置各平台的投注金额比例（1.0为正常比例）
├── 平博比例: [1.0]
└── 皇冠比例: [1.0]
```

### 🛠️ 使用说明

#### 查看真实余额
1. 点击"初始化系统" → "登录系统"
2. 点击"查看账户余额"
3. 查看日志显示的真实余额信息

#### 查看投注记录
1. 切换到"记录管理"页面
2. 点击"平博记录"查看平博未结算注单
3. 点击"皇冠记录"查看皇冠今日注单
4. 点击"刷新记录"查看所有记录

#### 测试平台连接
1. 在"平台状态"页面
2. 点击"测试平博连接"或"测试皇冠连接"
3. 查看连接状态和余额信息

#### 配置投注比例
1. 切换到"配置管理"页面
2. 在"平台投注比例配置"区域设置比例
3. 点击"保存配置"

### 🐛 修复问题

#### 问题1: 模拟数据问题
- **问题**: 余额显示固定的模拟数据
- **解决**: 接入真实API，显示实际余额

#### 问题2: 记录查询缺失
- **问题**: 缺少平博和皇冠的记录查询功能
- **解决**: 新增专门的记录查询按钮和API调用

#### 问题3: 配置项缺失
- **问题**: 平台投注额比例配置不完整
- **解决**: 完善配置界面和说明文字

### 📈 性能优化

- ✅ **异步处理**: 所有API调用都在后台线程执行，不阻塞界面
- ✅ **错误恢复**: API调用失败时提供详细错误信息
- ✅ **状态管理**: 实时更新平台连接状态
- ✅ **数据缓存**: 避免重复的API调用

### 🔮 后续计划

- [ ] 添加历史记录查询（更长时间范围）
- [ ] 支持记录筛选和排序
- [ ] 添加投注统计分析
- [ ] 支持多账户余额查询
- [ ] 添加实时余额监控

---

## 📝 开发者注意事项

### API依赖
确保以下系统组件正常工作：
- `hedge_main.HedgeBettingSystem` - 主系统
- `platforms.pinbet_bk.PinBetSystem` - 平博系统
- `platforms.hgbet_bk.HGBetBK` - 皇冠系统

### 配置文件
配置保存在 `config/config.json` 的以下字段：
- `hedge.platform_bet_ratios.pinbet` - 平博投注比例
- `hedge.platform_bet_ratios.crown` - 皇冠投注比例

### 日志系统
所有操作都会记录到GUI日志系统，便于调试和监控。
