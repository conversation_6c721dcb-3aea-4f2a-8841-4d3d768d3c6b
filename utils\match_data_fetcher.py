#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class MatchInfo:
    """比赛信息数据类"""
    match_id: str
    home_team: str
    away_team: str
    league: str
    match_time: str
    platform: str
    match_type: str  # today 或 early
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return asdict(self)

@dataclass
class PlatformMatchData:
    """平台比赛数据"""
    platform: str
    today_matches: List[MatchInfo]
    early_matches: List[MatchInfo]
    fetch_time: str
    total_count: int
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "platform": self.platform,
            "today_matches": [match.to_dict() for match in self.today_matches],
            "early_matches": [match.to_dict() for match in self.early_matches],
            "fetch_time": self.fetch_time,
            "total_count": self.total_count
        }

class MatchDataFetcher:
    """比赛数据获取器"""
    
    def __init__(self, cache_dir: str = "data/match_cache"):
        """
        初始化数据获取器
        
        参数:
            cache_dir: 缓存目录
        """
        self.cache_dir = cache_dir
        self.cache_file = os.path.join(cache_dir, "platform_matches.json")
        self.team_names_file = os.path.join(cache_dir, "team_names.json")
        self.league_names_file = os.path.join(cache_dir, "league_names.json")
        
        # 确保目录存在
        os.makedirs(cache_dir, exist_ok=True)
        
        # 缓存数据
        self.platform_data: Dict[str, PlatformMatchData] = {}
        self.team_names: Dict[str, List[str]] = {}  # 平台 -> 队伍名列表
        self.league_names: Dict[str, List[str]] = {}  # 平台 -> 联赛名列表
        
        logger.info(f"比赛数据获取器初始化完成，缓存目录: {cache_dir}")
    
    def fetch_all_platform_data(self, pinbet_system=None, crown_system=None) -> bool:
        """
        获取所有平台的比赛数据
        
        参数:
            pinbet_system: 平博系统实例
            crown_system: 皇冠系统实例
            
        返回:
            是否成功获取数据
        """
        try:
            logger.info("开始获取所有平台比赛数据")
            success_count = 0
            
            # 获取平博数据
            if pinbet_system:
                if self._fetch_pinbet_data(pinbet_system):
                    success_count += 1
                    logger.info("平博数据获取成功")
                else:
                    logger.warning("平博数据获取失败")
            
            # 获取皇冠数据
            if crown_system:
                if self._fetch_crown_data(crown_system):
                    success_count += 1
                    logger.info("皇冠数据获取成功")
                else:
                    logger.warning("皇冠数据获取失败")
            
            # 保存数据
            if success_count > 0:
                self._save_cache_data()
                self._extract_and_save_names()
                logger.info(f"成功获取 {success_count} 个平台的数据")
                return True
            else:
                logger.error("所有平台数据获取失败")
                return False
                
        except Exception as e:
            logger.error(f"获取平台数据时出错: {e}")
            return False
    
    def _fetch_pinbet_data(self, pinbet_system) -> bool:
        """获取平博平台数据"""
        try:
            logger.info("获取平博平台比赛数据")
            
            today_matches = []
            early_matches = []
            
            # 获取今日比赛 - 使用多个mk参数获取更多数据
            try:
                if hasattr(pinbet_system, 'api') and pinbet_system.api:
                    # 使用不同的mk参数获取更多比赛数据
                    mk_params = [0, 1]  # mk=0和mk=1包含更多比赛，mk=2只有滚球

                    for mk in mk_params:
                        success, data = self._get_pinbet_matches_by_mk(pinbet_system.api, mk, "today")
                        if success and data:
                            matches = self._parse_pinbet_matches(data, "today")
                            today_matches.extend(matches)
                            logger.info(f"平博今日比赛 (mk={mk}): {len(matches)} 场")

                    logger.info(f"平博今日比赛总计: {len(today_matches)} 场")
                else:
                    logger.warning("平博API实例不可用")
            except Exception as e:
                logger.warning(f"获取平博今日比赛失败: {e}")

            # 获取早场比赛 - 使用多个mk参数获取更多数据
            try:
                if hasattr(pinbet_system, 'api') and pinbet_system.api:
                    # 使用不同的mk参数获取更多比赛数据
                    mk_params = [0, 1]  # mk=0和mk=1包含更多比赛

                    for mk in mk_params:
                        success, data = self._get_pinbet_matches_by_mk(pinbet_system.api, mk, "early")
                        if success and data:
                            matches = self._parse_pinbet_matches(data, "early")
                            early_matches.extend(matches)
                            logger.info(f"平博早场比赛 (mk={mk}): {len(matches)} 场")

                    logger.info(f"平博早场比赛总计: {len(early_matches)} 场")
                else:
                    logger.warning("平博API实例不可用")
            except Exception as e:
                logger.warning(f"获取平博早场比赛失败: {e}")
            
            # 创建平台数据
            platform_data = PlatformMatchData(
                platform="pinbet",
                today_matches=today_matches,
                early_matches=early_matches,
                fetch_time=datetime.now().isoformat(),
                total_count=len(today_matches) + len(early_matches)
            )
            
            self.platform_data["pinbet"] = platform_data
            return True
            
        except Exception as e:
            logger.error(f"获取平博数据失败: {e}")
            return False
    
    def _fetch_crown_data(self, crown_system) -> bool:
        """获取皇冠平台数据"""
        try:
            logger.info("获取皇冠平台比赛数据")
            
            today_matches = []
            early_matches = []
            
            # 获取今日比赛
            try:
                today_data = crown_system.get_basketball_matches(showtype="today")
                if today_data:
                    today_matches = self._parse_crown_matches(today_data, "today")
                    logger.info(f"皇冠今日比赛: {len(today_matches)} 场")
            except Exception as e:
                logger.warning(f"获取皇冠今日比赛失败: {e}")
            
            # 获取早场比赛
            try:
                early_data = crown_system.get_basketball_matches(showtype="early")
                if early_data:
                    early_matches = self._parse_crown_matches(early_data, "early")
                    logger.info(f"皇冠早场比赛: {len(early_matches)} 场")
            except Exception as e:
                logger.warning(f"获取皇冠早场比赛失败: {e}")
            
            # 创建平台数据
            platform_data = PlatformMatchData(
                platform="crown",
                today_matches=today_matches,
                early_matches=early_matches,
                fetch_time=datetime.now().isoformat(),
                total_count=len(today_matches) + len(early_matches)
            )
            
            self.platform_data["crown"] = platform_data
            return True
            
        except Exception as e:
            logger.error(f"获取皇冠数据失败: {e}")
            return False
    
    def _parse_pinbet_matches(self, match_data: Dict, match_type: str) -> List[MatchInfo]:
        """解析平博比赛数据"""
        matches = []
        try:
            # 平博API的实际响应结构：数据在"l"字段中
            leagues = match_data.get("l", [])

            for league_data in leagues:
                try:
                    if len(league_data) < 3:
                        continue

                    # league_data[0] = sport_id, league_data[1] = sport_name, league_data[2] = leagues
                    sport_leagues = league_data[2]

                    for league_info in sport_leagues:
                        try:
                            if len(league_info) < 3:
                                continue

                            # league_info[0] = league_id, league_info[1] = league_name, league_info[2] = matches
                            league_name = league_info[1]
                            league_matches = league_info[2]

                            for match_data_item in league_matches:
                                try:
                                    if len(match_data_item) < 3:
                                        continue

                                    # 提取比赛信息
                                    match_id = str(match_data_item[0])  # 比赛ID
                                    home_team = match_data_item[1].strip()  # 主队
                                    away_team = match_data_item[2].strip()  # 客队

                                    # 提取比赛时间（如果有的话）
                                    match_time = ""
                                    if len(match_data_item) > 4:
                                        match_time = str(match_data_item[4])

                                    if home_team and away_team:
                                        match_info = MatchInfo(
                                            match_id=match_id,
                                            home_team=home_team,
                                            away_team=away_team,
                                            league=league_name,
                                            match_time=match_time,
                                            platform="pinbet",
                                            match_type=match_type
                                        )
                                        matches.append(match_info)

                                except Exception as e:
                                    logger.debug(f"解析平博单场比赛失败: {e}")
                                    continue

                        except Exception as e:
                            logger.debug(f"解析平博联赛失败: {e}")
                            continue

                except Exception as e:
                    logger.debug(f"解析平博体育类别失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"解析平博比赛数据失败: {e}")

        return matches
    
    def _parse_crown_matches(self, match_data: List[Dict], match_type: str) -> List[MatchInfo]:
        """解析皇冠比赛数据"""
        matches = []
        try:
            for match in match_data:
                try:
                    # 提取比赛信息
                    match_id = str(match.get("GID", ""))
                    home_team = match.get("TEAM_H", "").strip()
                    away_team = match.get("TEAM_C", "").strip()
                    league = match.get("LEAGUE", "").strip()
                    match_time = match.get("DATETIME", "")
                    
                    if home_team and away_team:
                        match_info = MatchInfo(
                            match_id=match_id,
                            home_team=home_team,
                            away_team=away_team,
                            league=league,
                            match_time=match_time,
                            platform="crown",
                            match_type=match_type
                        )
                        matches.append(match_info)
                        
                except Exception as e:
                    logger.debug(f"解析皇冠单场比赛失败: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"解析皇冠比赛数据失败: {e}")
        
        return matches
    
    def _save_cache_data(self):
        """保存缓存数据"""
        try:
            cache_data = {
                platform: data.to_dict() 
                for platform, data in self.platform_data.items()
            }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"缓存数据已保存到: {self.cache_file}")
            
        except Exception as e:
            logger.error(f"保存缓存数据失败: {e}")
    
    def _extract_and_save_names(self):
        """提取并保存队伍名和联赛名"""
        try:
            # 提取队伍名和联赛名
            for platform, data in self.platform_data.items():
                team_names = set()
                league_names = set()
                
                # 处理今日比赛
                for match in data.today_matches:
                    team_names.add(match.home_team)
                    team_names.add(match.away_team)
                    if match.league:
                        league_names.add(match.league)
                
                # 处理早场比赛
                for match in data.early_matches:
                    team_names.add(match.home_team)
                    team_names.add(match.away_team)
                    if match.league:
                        league_names.add(match.league)
                
                self.team_names[platform] = sorted(list(team_names))
                self.league_names[platform] = sorted(list(league_names))
            
            # 保存队伍名
            with open(self.team_names_file, 'w', encoding='utf-8') as f:
                json.dump(self.team_names, f, ensure_ascii=False, indent=2)
            
            # 保存联赛名
            with open(self.league_names_file, 'w', encoding='utf-8') as f:
                json.dump(self.league_names, f, ensure_ascii=False, indent=2)
            
            logger.info(f"队伍名和联赛名已保存")
            
        except Exception as e:
            logger.error(f"提取和保存名称失败: {e}")
    
    def load_cached_data(self) -> bool:
        """加载缓存数据"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                # 重建平台数据
                for platform, data in cache_data.items():
                    today_matches = [
                        MatchInfo(**match) for match in data.get("today_matches", [])
                    ]
                    early_matches = [
                        MatchInfo(**match) for match in data.get("early_matches", [])
                    ]
                    
                    platform_data = PlatformMatchData(
                        platform=data["platform"],
                        today_matches=today_matches,
                        early_matches=early_matches,
                        fetch_time=data["fetch_time"],
                        total_count=data["total_count"]
                    )
                    
                    self.platform_data[platform] = platform_data
                
                logger.info(f"加载缓存数据成功，包含 {len(self.platform_data)} 个平台")
                return True
            else:
                logger.info("缓存文件不存在")
                return False
                
        except Exception as e:
            logger.error(f"加载缓存数据失败: {e}")
            return False
    
    def get_platform_matches(self, platform: str, match_type: str = "all") -> List[MatchInfo]:
        """
        获取指定平台的比赛数据
        
        参数:
            platform: 平台名称 (pinbet/crown)
            match_type: 比赛类型 (today/early/all)
            
        返回:
            比赛信息列表
        """
        if platform not in self.platform_data:
            return []
        
        data = self.platform_data[platform]
        
        if match_type == "today":
            return data.today_matches
        elif match_type == "early":
            return data.early_matches
        else:
            return data.today_matches + data.early_matches
    
    def find_similar_matches(self, target_home: str, target_away: str, 
                           platform: str = None) -> List[Tuple[MatchInfo, float]]:
        """
        查找相似的比赛
        
        参数:
            target_home: 目标主队名
            target_away: 目标客队名
            platform: 指定平台，None表示所有平台
            
        返回:
            (比赛信息, 相似度分数) 的列表
        """
        from utils.advanced_similarity import calculate_team_similarity
        
        similar_matches = []
        
        platforms = [platform] if platform else self.platform_data.keys()
        
        for plat in platforms:
            matches = self.get_platform_matches(plat)
            
            for match in matches:
                # 计算相似度
                score, _ = calculate_team_similarity(
                    target_home, target_away,
                    match.home_team, match.away_team,
                    {}  # 暂时不使用别名
                )
                
                if score > 0.3:  # 只保留有一定相似度的
                    similar_matches.append((match, score))
        
        # 按相似度排序
        similar_matches.sort(key=lambda x: x[1], reverse=True)
        
        return similar_matches

    def _get_pinbet_matches_by_mk(self, api, mk_value: int, match_type: str) -> tuple:
        """
        使用指定的mk参数获取平博比赛数据

        参数:
            api: 平博API实例
            mk_value: mk参数值 (0=早场, 1=今日, 2=滚球)
            match_type: 比赛类型标识

        返回:
            (success, data) 元组
        """
        try:
            import time

            url = f"{api.base_url}/sports-service/sv/am/events"

            params = {
                "_g": "0",
                "btg": "1",
                "c": "",
                "d": "",
                "ec": "",
                "ev": "",
                "g": "QQ%3D%3D",
                "ic": "false",
                "inl": "false",
                "l": "1",
                "lg": "",
                "lv": "",
                "me": "0",
                "mk": str(mk_value),  # 使用指定的mk值
                "more": "false",
                "o": "0",
                "ot": "1",
                "pa": "0",
                "pimo": "0%2C1%2C2",
                "pn": "-1",
                "sp": "4",  # 篮球
                "tm": "0",
                "v": "0",
                "wm": "ld",
                "locale": "zh_CN",
                "_": str(int(time.time() * 1000)),
                "withCredentials": "true"
            }

            response = api.session.get(url, params=params, headers=api.headers)

            if response.status_code == 200:
                data = response.json()
                return True, data
            else:
                logger.warning(f"平博API请求失败 (mk={mk_value}): HTTP {response.status_code}")
                return False, None

        except Exception as e:
            logger.error(f"获取平博数据失败 (mk={mk_value}): {e}")
            return False, None
