#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
import re
import json
import os
import sys
import requests
import logging
import msvcrt
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
# 导入pygame提示音模块
import pygame.mixer
import xml.etree.ElementTree as ET
import argparse

# 添加项目根目录到Python路径（用于单独运行此文件）
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入高级相似度算法
from utils.advanced_similarity import calculate_team_similarity
# 导入匹配学习系统
from utils.match_learning import match_learning_system

# 配置日志
logger = logging.getLogger(__name__)

def load_config():
    """加载配置文件"""
    try:
        # 获取项目根目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        config_path = os.path.join(project_root, 'config', 'config.json')

        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"无法加载配置文件: {e}")
        return {}

def setup_logging():
    """设置日志配置"""
    # 创建日志目录
    import os
    from datetime import datetime

    # 生成当前日期的时间戳 YYYY-MM-DD 格式
    today_str = datetime.now().strftime("%Y-%m-%d")
    log_dir = 'logs'
    os.makedirs(log_dir, exist_ok=True)

    # 生成带时间戳的日志文件名
    log_file = os.path.join(log_dir, f'hgbet_{today_str}.log')

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ],
        force=True  # 强制重新配置，覆盖已有配置
    )

    logger.info(f"皇冠日志将保存到: {log_file}")
    return log_file

def fetch_latest_arbs():
    """
    获取最新的套利数据
    
    返回:
        list: 套利数据列表，如果获取失败则返回空列表
    """
    try:
        from betburger_fetcher import fetch_and_save_data, process_betburger_data
        
        # 获取最新数据
        success, file_path = fetch_and_save_data()
        
        if not success:
            # 不打印日志，静默返回空列表
            return []
        
        # 加载JSON文件
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            logger.error(f"读取套利数据文件出错: {e}")
            return []
        
        # 检查数据格式并处理
        if isinstance(data, list):
            # 数组格式: [{"bets": [...多个投注选项...], ...}, ...]
            arb_items = []
            
            for item in data:
                if "bets" in item:
                    bets = item.get("bets", [])
                    if bets:
                        # 只提取皇冠的投注选项
                        for bet in bets:
                            bookmaker = bet.get("bookmaker", "")
                            if "皇冠" in bookmaker:
                                arb_items.append(bet)
            
            # 只有当找到套利数据时才记录日志
            if arb_items:
                logger.info(f"检测到数组格式的套利数据，共 {len(data)} 条，提取到 {len(arb_items)} 个皇冠投注选项")
            return arb_items
            
        elif isinstance(data, dict) and "bets" in data:
            # 字典格式: {"bets": [...多个投注选项...]}
            bets = data.get("bets", [])
            if not bets:
                return []
                
            # 处理套利数据
            matches = process_betburger_data(bets)
            
            if not matches:
                return []
            
            # 提取所有皇冠投注选项
            arb_items = []
            for match_id, bet_options in matches.items():
                for bet_option in bet_options:
                    bookmaker = bet_option.get("bookmaker", "")
                    if "皇冠" in bookmaker:
                        arb_items.append(bet_option)
            
            # 只有当找到套利数据时才记录日志
            if arb_items:
                logger.info(f"成功加载最新套利数据，共 {len(bets)} 条，提取到 {len(arb_items)} 个皇冠投注选项")
            return arb_items
        else:
            logger.error(f"套利数据格式错误")
            return []
            
    except Exception as e:
        logger.error(f"获取最新套利数据时出错: {e}", exc_info=True)
        return []

def play_notification_sound():
    """播放提示音（使用 pygame）"""
    config = load_config()
    notification_config = config.get("notification", {})
    
    if not notification_config.get("enable_sound", True):
        return
        
    sound_file = notification_config.get("sound_file", "alert.mp3")
    if not sound_file:
        logger.warning("未配置提示音文件路径")
        return
        
    try:
        logger.info(f"播放提示音: {sound_file}")
        pygame.mixer.init()  # 初始化音频模块
        pygame.mixer.music.load(sound_file)
        pygame.mixer.music.play()
        while pygame.mixer.music.get_busy():  # 等待播放完成
            time.sleep(0.1)
    except Exception as e:
        logger.error(f"播放提示音失败: {e}")

class HGBetBK:
    """
    皇冠篮球投注API客户端类
    提供篮球投注的所有功能
    """
    
    def __init__(self, username=None, password=None, base_url=None, account_config=None):
        """
        初始化HGBetBK对象，设置请求会话和参数
        
        参数:
            username: 用户名，如果为None则从配置文件中读取
            password: 密码，如果为None则从配置文件中读取
            base_url: 网站地址，如果为None则从配置文件中读取
            account_config: 账号配置字典，用于多账号配置
        """
        logger.info("初始化皇冠篮球投注模块")
        self.session = requests.Session()
        # 禁用SSL验证，避免证书验证错误
        self.session.verify = False
        
        # 禁用不安全请求警告
        try:
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        except ImportError:
            pass
        
        # 读取配置文件
        try:
            # 使用安全配置加载器
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)
            sys.path.append(project_root)
            from utils.utils import load_config
            self.config = load_config()
        except ImportError:
            # 备选方案：直接读取
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)
            config_path = os.path.join(project_root, 'config', 'config.json')

            if os.path.exists(config_path):
                try:
                    with open(config_path, "r", encoding="utf-8") as f:
                        self.config = json.load(f)
                except Exception as e:
                    logger.error(f"读取配置文件失败: {e}")
                    self.config = {}
            else:
                logger.warning("config/config.json文件不存在，使用默认配置")
                self.config = {}
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            self.config = {}
        
        # 处理账号配置，优先级：参数 > account_config > 配置文件
        if account_config is not None:
            # 使用传入的多账号配置
            self.username = account_config.get("username")
            self.password = account_config.get("password")
            self.base_url = account_config.get("base_url", "https://m518.mos077.com")
            self.account_name = account_config.get("account_name", self.username)
        else:
            # 使用单账号配置
            self.username = username or self.config.get("hgbet", {}).get("username")
            self.password = password or self.config.get("hgbet", {}).get("password")
            self.base_url = base_url or self.config.get("hgbet", {}).get("base_url", "https://m518.mos077.com")
            self.account_name = "Default"
        
        if not self.username or not self.password:
            raise ValueError("用户名和密码不能为空，请在config/config.json中配置或通过参数传递")
            
        self.uid = None
        self.transform_url = f"{self.base_url}/transform.php"
        self.is_logged_in = False
        self.version = None  # 版本号，登录后获取
        self.credit = 0.0    # 账户余额
        
        # 设置请求头
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Mobile Safari/537.36",
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "*/*",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Accept-Language": "zh-CN,zh;q=0.9"
        }
        
        # 生成当前日期的时间戳 YYYY-MM-DD 格式
        today_str = datetime.now().strftime("%Y-%m-%d")
        
        # 初始化投注记录保存文件名，使用时间戳
        self.bet_record_file = os.path.join("data", f"bet_records_{today_str}.json")
        # 确保data目录存在
        os.makedirs(os.path.dirname(self.bet_record_file), exist_ok=True)
        
        logger.info(f"投注记录将保存到: {self.bet_record_file}")
        
        # 注意：不要在初始化阶段自动执行任何投注操作
        # 投注操作应该由用户显式调用相关方法来执行
        
        self.log_counters = {
            'duplicate_bet': 0,
            'bet_attempt': 0,
            'bet_success': 0,
            'bet_failure': 0
        }

        # 比赛投注次数跟踪（按比赛ID统计）
        self.match_bet_counts = {}
        self.last_log_time = {
            'duplicate_bet': 0,
            'bet_attempt': 0,
            'bet_success': 0,
            'bet_failure': 0
        }
        self.log_interval = 60  # 日志记录间隔（秒）

    def login(self):
        """登录到HG网站"""
        logger.info("尝试登录...")
        
        # 获取登录页面获取cookie
        try:
            self.session.get(self.base_url, headers=self.headers)
        except Exception as e:
            logger.error(f"获取首页失败: {e}")
            return False

        # 构造登录请求 - 使用chk_login接口
        timestamp = int(time.time() * 1000)
        login_data = {
            "p": "chk_login",
            "langx": "zh-cn",
            "ver": self.version,
            "username": self.username,
            "password": self.password,
            "app": "N",
            "auto": "IEHAGD",
            "blackbox": "",
            "userAgent": "TW96aWxsYS81LjAgKExpbnV4OyBBbmRyb2lkIDYuMDsgTmV4dXMgNSBCdWlsZC9NUkE1OE4pIEFwcGxlV2ViS2l0LzUzNy4zNiAoS0hUTUwsIGxpa2UgR2Vja28pIENocm9tZS8xMTguMC4wLjAgTW9iaWxlIFNhZmFyaS81MzcuMzY="
        }
        
        try:
            # 发送POST请求进行登录
            response = self.session.post(
                url=f"{self.transform_url}?ver={self.version}",
                headers=self.headers,
                data=login_data
            )
            
            # 打印响应内容用于调试
            logger.info(f"登录状态码: {response.status_code}")
            logger.debug(f"登录响应内容: {response.text}")
            
            # 检查登录是否成功
            # 1. 检查响应状态码
            if response.status_code != 200:
                logger.error(f"登录失败，状态码: {response.status_code}")
                return False
                
            # 检查是否为维护状态
            response_text = response.text.lower()
            if any(keyword in response_text for keyword in ['维护', 'maintenance', '系统升级', '暂停服务', '服务器维护']):
                logger.warning("皇冠平台当前处于维护状态，请稍后再试")
                return False

            # 从XML响应中提取UID
            uid_match = re.search(r"<uid>(.*?)</uid>", response.text)

            if uid_match:
                self.uid = uid_match.group(1)
                logger.info(f"提取到UID: {self.uid}")
                self.is_logged_in = True

                # 登录后立即更新版本号
                self.version = self._get_latest_version()

                # 添加：登录成功后立即获取账户余额
                account_data = self.get_account_balance()
                balance = self.credit if account_data else 0.0

                logger.info(f"登录成功，当前余额：{balance}，版本号: {self.version}")

                return True
            else:
                # 检查响应内容，提供更详细的错误信息
                if len(response.text) < 100:
                    logger.error(f"皇冠平台可能处于维护状态，响应内容异常: {response.text[:200]}")
                else:
                    logger.error("登录失败，无法从响应中提取UID，可能是平台维护或网络问题")
                return False
                
        except Exception as e:
            logger.error(f"登录过程中出错: {e}")
            return False

    def _get_latest_version(self):
        """获取最新版本号"""
        # 此处可添加从页面中提取最新版本号的逻辑
        # 为了保证稳定性，目前直接返回抓包中的版本号
        return "2025-04-30-CRM-55_89"

    def get_account_balance(self):
        """
        获取账户余额
        
        返回:
            账户信息字典 或 None（失败时）
        """
        try:
            # 获取会员数据，其中包含账户余额
            account_data = self.get_member_data()
            
            if account_data and 'maxcredit' in account_data:
                self.credit = float(account_data.get('maxcredit', 0))
                # 返回账户数据
                return account_data
            else:
                logger.warning("获取账户余额失败，响应中没有余额信息")
                return None
                
        except Exception as e:
            logger.error(f"获取账户余额时出错: {e}")
            return None
            
    def get_member_data(self):
        """获取账户详细信息"""
        if not self.is_logged_in or not self.uid:
            logger.error("尚未登录，无法获取账户信息")
            return None
        
        try:
            # 构造查询账户信息的请求
            data = {
                "p": "get_member_data",
                "uid": self.uid,
                "ver": self.version,
                "langx": "zh-cn",
                "change": "all"
            }
            
            response = self.session.post(
                url=f"{self.transform_url}?ver={self.version}",
                headers=self.headers,
                data=data
            )
            
            if response.status_code == 200:
                # 解析XML响应
                account_data = {}
                
                # 提取常见字段
                fields = ["code", "enable", "pay_type", "currency", "maxcredit", "cash"]
                for field in fields:
                    match = re.search(f"<{field}>(.*?)</{field}>", response.text)
                    if match:
                        account_data[field] = match.group(1)
                
                # logger.info(f"账户信息: 余额={account_data.get('maxcredit', '未知')}, 货币={account_data.get('currency', '未知')}")
                return account_data
            else:
                logger.error(f"获取账户信息失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"请求账户信息时出错: {e}")
            return None

    def get_history_data(self, gtype="ALL", is_all="N"):
        """
        获取历史投注账单数据

        参数:
            gtype: 游戏类型，'ALL'=所有游戏，'BK'=篮球，'FT'=足球等
            is_all: 是否获取全部数据，'Y'=是，'N'=否

        返回:
            dict: 包含历史账单数据的字典，格式如下：
            {
                'success': bool,
                'total_gold': str,      # 总投注金额
                'total_vgold': str,     # 总有效投注
                'total_winloss': str,   # 总盈亏
                'history': [            # 历史记录列表
                    {
                        'date': str,        # 日期
                        'date_name': str,   # 日期名称
                        'gold': str,        # 投注金额
                        'vgold': str,       # 有效投注
                        'winloss': str,     # 盈亏金额
                        'winloss_class': str # 盈亏样式类
                    }
                ],
                'raw_response': str     # 原始响应数据
            }
        """
        logger.info(f"获取历史账单数据，游戏类型: {gtype}, 是否全部: {is_all}")

        if not self.is_logged_in or not self.uid:
            logger.error("尚未登录，无法获取历史账单")
            return {'success': False, 'error': '尚未登录'}

        try:
            # 构造请求参数
            data = {
                "p": "get_history_data",
                "uid": self.uid,
                "langx": "zh-cn",
                "gtype": gtype,
                "isAll": is_all
            }

            # 发送请求
            response = self.session.post(
                url=f"{self.transform_url}?ver={self.version}",
                headers=self.headers,
                data=data
            )

            if response.status_code != 200:
                logger.error(f"获取历史账单失败，状态码: {response.status_code}")
                return {'success': False, 'error': f'HTTP状态码: {response.status_code}'}

            # 解析XML响应
            xml_content = response.text
            logger.debug(f"历史账单响应: {xml_content[:500]}...")

            try:
                # 首先尝试清理XML内容
                cleaned_xml = self._clean_xml_content(xml_content)

                # 解析XML
                root = ET.fromstring(cleaned_xml)

                # 提取总计数据
                result = {
                    'success': True,
                    'total_gold': root.find('total_gold').text if root.find('total_gold') is not None else '0',
                    'total_vgold': root.find('total_vgold').text if root.find('total_vgold') is not None else '0',
                    'total_winloss': root.find('total_winloss').text if root.find('total_winloss') is not None else '0',
                    'total_winloss_class': root.find('total_winloss_calss').text if root.find('total_winloss_calss') is not None else '',
                    'history': [],
                    'raw_response': xml_content
                }

                # 提取历史记录
                for history_node in root.findall('history'):
                    history_item = {
                        'date': history_node.find('date').text if history_node.find('date') is not None else '',
                        'date_name': history_node.find('date_name').text if history_node.find('date_name') is not None else '',
                        'gold': history_node.find('gold').text if history_node.find('gold') is not None else '0',
                        'vgold': history_node.find('vgold').text if history_node.find('vgold') is not None else '0',
                        'winloss': history_node.find('winloss').text if history_node.find('winloss') is not None else '0',
                        'winloss_class': history_node.find('winloss_class').text if history_node.find('winloss_class') is not None else ''
                    }
                    result['history'].append(history_item)

                logger.info(f"成功获取历史账单，总投注: {result['total_gold']}, 总盈亏: {result['total_winloss']}, 记录数: {len(result['history'])}")
                return result

            except ET.ParseError as e:
                logger.warning(f"XML解析失败，尝试正则表达式解析: {e}")
                # 尝试使用正则表达式解析
                return self._parse_history_with_regex(xml_content)

        except Exception as e:
            logger.error(f"获取历史账单时出错: {e}")
            return {'success': False, 'error': str(e)}

    def _clean_xml_content(self, xml_content):
        """
        清理XML内容，修复常见的格式问题

        参数:
            xml_content: 原始XML内容

        返回:
            str: 清理后的XML内容
        """
        try:
            # 移除可能的BOM标记
            if xml_content.startswith('\ufeff'):
                xml_content = xml_content[1:]

            # 确保XML声明存在
            if not xml_content.strip().startswith('<?xml'):
                xml_content = '<?xml version="1.0" encoding="UTF-8"?>' + xml_content

            # 修复常见的XML问题
            # 1. 替换HTML实体
            xml_content = xml_content.replace('&nbsp;', ' ')
            xml_content = xml_content.replace('&amp;', '&')
            xml_content = xml_content.replace('&lt;', '<')
            xml_content = xml_content.replace('&gt;', '>')

            # 2. 移除可能的HTML标签
            import re
            xml_content = re.sub(r'<br\s*/?>', '', xml_content)
            xml_content = re.sub(r'<hr\s*/?>', '', xml_content)

            # 3. 确保所有标签都正确闭合
            # 这里可以添加更多的清理逻辑

            return xml_content.strip()

        except Exception as e:
            logger.warning(f"清理XML内容时出错: {e}")
            return xml_content

    def _parse_history_with_regex(self, xml_content):
        """
        使用正则表达式解析历史账单XML（备用方法）

        参数:
            xml_content: XML内容字符串

        返回:
            dict: 解析后的历史账单数据
        """
        try:
            result = {
                'success': True,
                'total_gold': '0',
                'total_vgold': '0',
                'total_winloss': '0',
                'total_winloss_class': '',
                'history': [],
                'raw_response': xml_content
            }

            # 提取总计数据
            total_patterns = {
                'total_gold': r'<total_gold>(.*?)</total_gold>',
                'total_vgold': r'<total_vgold>(.*?)</total_vgold>',
                'total_winloss': r'<total_winloss>(.*?)</total_winloss>',
                'total_winloss_class': r'<total_winloss_calss>(.*?)</total_winloss_calss>'
            }

            for key, pattern in total_patterns.items():
                match = re.search(pattern, xml_content)
                if match:
                    result[key] = match.group(1)

            # 提取历史记录
            history_pattern = r'<history>(.*?)</history>'
            history_matches = re.findall(history_pattern, xml_content, re.DOTALL)

            for history_content in history_matches:
                history_item = {}

                # 提取历史记录字段
                fields = {
                    'date': r'<date>(.*?)</date>',
                    'date_name': r'<date_name>(.*?)</date_name>',
                    'gold': r'<gold>(.*?)</gold>',
                    'vgold': r'<vgold>(.*?)</vgold>',
                    'winloss': r'<winloss>(.*?)</winloss>',
                    'winloss_class': r'<winloss_class>(.*?)</winloss_class>'
                }

                for field, pattern in fields.items():
                    match = re.search(pattern, history_content)
                    history_item[field] = match.group(1) if match else ''

                if history_item.get('date'):  # 只添加有日期的记录
                    result['history'].append(history_item)

            logger.info(f"正则表达式解析成功，记录数: {len(result['history'])}")
            return result

        except Exception as e:
            logger.error(f"正则表达式解析历史账单失败: {e}")
            return {'success': False, 'error': f'解析失败: {str(e)}'}

    def format_history_display(self, history_data):
        """
        格式化历史账单数据用于显示

        参数:
            history_data: get_history_data返回的数据

        返回:
            str: 格式化后的显示文本
        """
        if not history_data.get('success'):
            return f"获取历史账单失败: {history_data.get('error', '未知错误')}"

        lines = []
        lines.append("=" * 60)
        lines.append("皇冠历史投注账单")
        lines.append("=" * 60)

        # 显示总计信息
        lines.append(f"总投注金额: {history_data.get('total_gold', '0')}")
        lines.append(f"总有效投注: {history_data.get('total_vgold', '0')}")

        total_winloss = history_data.get('total_winloss', '0')
        winloss_class = history_data.get('total_winloss_class', '')

        if 'green' in winloss_class.lower():
            winloss_symbol = "+"
        elif 'red' in winloss_class.lower():
            winloss_symbol = ""
        else:
            winloss_symbol = ""

        lines.append(f"总盈亏金额: {winloss_symbol}{total_winloss}")
        lines.append("-" * 60)

        # 显示历史记录
        if history_data.get('history'):
            lines.append(f"{'日期':<12} {'投注金额':<10} {'有效投注':<10} {'盈亏金额':<12}")
            lines.append("-" * 60)

            for record in history_data['history']:
                date = record.get('date', '')
                gold = record.get('gold', '0')
                vgold = record.get('vgold', '0')
                winloss = record.get('winloss', '0')
                winloss_class = record.get('winloss_class', '')

                # 根据盈亏类型添加符号
                if 'green' in winloss_class.lower():
                    winloss_display = f"+{winloss}"
                elif 'red' in winloss_class.lower():
                    winloss_display = f"{winloss}"
                else:
                    winloss_display = winloss

                lines.append(f"{date:<12} {gold:<10} {vgold:<10} {winloss_display:<12}")
        else:
            lines.append("暂无历史记录")

        lines.append("=" * 60)
        return "\n".join(lines)
    
    def get_matches(self, showtype="soon", gtype="bk", rtype="r"):
        """
        获取比赛列表
        
        参数:
            showtype: 比赛类型，'soon'=即将开始，'today'=今日比赛，'early'=早盘
            gtype: 体育类型，'bk'=篮球，'fb'=足球等
            rtype: 盘口类型，'r'=让分盘，'ou'=大小盘，'m'=独赢盘等
            
        返回:
            比赛数据列表
        """
        logger.info(f"获取{showtype}比赛数据，体育类型:{gtype}，盘口类型:{rtype}")
        
        try:
            # 检查是否已登录
            if not self.is_logged_in:
                logger.warning("尚未登录，尝试登录")
                if not self.login():
                    logger.error("登录失败，无法获取比赛数据")
                    return []
            
            # 构造请求参数 - 使用正确的API参数格式
            params = {
                "uid": self.uid,
                "ver": self.version,
                "langx": "zh-cn",
                "p": "get_game_list",
                "p3type": "",
                "date": "",
                "gtype": gtype,
                "showtype": showtype,
                "rtype": rtype,
                "ltype": "3",
                "filter": "",
                "sorttype": "L",
                "specialClick": "",
                "ts": int(time.time() * 1000)
            }
            
            # 发送请求 - 使用POST方法而不是GET
            response = self.session.post(
                url=f"{self.transform_url}?ver={self.version}",
                headers=self.headers,
                data=params
            )
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"获取比赛列表失败，状态码: {response.status_code}")
                return []
                
            # 解析XML响应
            try:
                xml_content = response.text
                
                # 检查XML内容
                if not xml_content or len(xml_content) < 100:
                    logger.warning(f"获取到的XML内容过短或为空: {xml_content[:100]}")
                    return []
                
                # 使用正则表达式提取game节点，避免XML解析可能的问题
                game_pattern = r"<game id=\"(.*?)\">(.*?)</game>"
                games = re.findall(game_pattern, xml_content, re.DOTALL)
                
                # 提取比赛列表
                matches = []
                valid_count = 0
                
                for game_id, game_content in games:
                    if not game_id:
                        continue
                        
                    match_data = {"GID": game_id}
                    
                    # 提取其他字段
                    fields = {
                        "DATETIME": r"<DATETIME>(.*?)</DATETIME>",
                        "LEAGUE": r"<LEAGUE>(.*?)</LEAGUE>",
                        "TEAM_H": r"<TEAM_H>(.*?)</TEAM_H>",
                        "TEAM_C": r"<TEAM_C>(.*?)</TEAM_C>",
                        # 全场盘口
                        "RATIO_R": r"<RATIO_R>(.*?)</RATIO_R>",
                        "IOR_RH": r"<IOR_RH>(.*?)</IOR_RH>",
                        "IOR_RC": r"<IOR_RC>(.*?)</IOR_RC>",
                        "RATIO_OUO": r"<RATIO_OUO>(.*?)</RATIO_OUO>",
                        "IOR_OUH": r"<IOR_OUH>(.*?)</IOR_OUH>",
                        "IOR_OUC": r"<IOR_OUC>(.*?)</IOR_OUC>",
                        "IOR_MH": r"<IOR_MH>(.*?)</IOR_MH>",
                        "IOR_MC": r"<IOR_MC>(.*?)</IOR_MC>",
                        # 单队大小球盘口
                        "RATIO_OUHO": r"<RATIO_OUHO>(.*?)</RATIO_OUHO>",
                        "RATIO_OUCO": r"<RATIO_OUCO>(.*?)</RATIO_OUCO>",
                        "IOR_OUHO": r"<IOR_OUHO>(.*?)</IOR_OUHO>",
                        "IOR_OUHU": r"<IOR_OUHU>(.*?)</IOR_OUHU>",
                        "IOR_OUCO": r"<IOR_OUCO>(.*?)</IOR_OUCO>",
                        "IOR_OUCU": r"<IOR_OUCU>(.*?)</IOR_OUCU>",
                        # 半场盘口
                        "HALF_RATIO_R": r"<HALF_RATIO_R>(.*?)</HALF_RATIO_R>",
                        "HALF_IOR_RH": r"<HALF_IOR_RH>(.*?)</HALF_IOR_RH>",
                        "HALF_IOR_RC": r"<HALF_IOR_RC>(.*?)</HALF_IOR_RC>",
                        "HALF_RATIO_OUO": r"<HALF_RATIO_OUO>(.*?)</HALF_RATIO_OUO>",
                        "HALF_IOR_OUH": r"<HALF_IOR_OUH>(.*?)</HALF_IOR_OUH>",
                        "HALF_IOR_OUC": r"<HALF_IOR_OUC>(.*?)</HALF_IOR_OUC>",
                        "HALF_RATIO_OUHO": r"<HALF_RATIO_OUHO>(.*?)</HALF_RATIO_OUHO>",
                        "HALF_RATIO_OUCO": r"<HALF_RATIO_OUCO>(.*?)</HALF_RATIO_OUCO>",
                        "HALF_IOR_OUHO": r"<HALF_IOR_OUHO>(.*?)</HALF_IOR_OUHO>",
                        "HALF_IOR_OUHU": r"<HALF_IOR_OUHU>(.*?)</HALF_IOR_OUHU>",
                        "HALF_IOR_OUCO": r"<HALF_IOR_OUCO>(.*?)</HALF_IOR_OUCO>",
                        "HALF_IOR_OUCU": r"<HALF_IOR_OUCU>(.*?)</HALF_IOR_OUCU>"
                    }
                    
                    for field, pattern in fields.items():
                        match = re.search(pattern, game_content)
                        if match:
                            match_data[field] = match.group(1)
                        else:
                            match_data[field] = ""
                    
                    # 确保至少有队伍信息
                    if match_data.get("TEAM_H") and match_data.get("TEAM_C"):
                        valid_count += 1
                        matches.append(match_data)
                
                logger.info(f"获取到 {valid_count} 场比赛")
                return matches
                    
            except Exception as e:
                logger.error(f"解析比赛数据失败: {e}")
                logger.debug(f"响应内容预览: {response.text[:500]}")
                return []
                
        except Exception as e:
            logger.error(f"获取比赛列表时出错: {e}")
            return []

    # 已移除 place_bet() 方法 - 断层方法，从未被调用，功能已被 place_bet_simple() 替代
    


    def get_basketball_matches(self, showtype="today"):
        """
        获取篮球比赛列表
        
        参数:
            showtype: 比赛类型，'soon'=即将开始，'today'=今日比赛，'early'=早盘
            
        返回:
            返回篮球比赛数据列表
        """
        return self.get_matches(showtype=showtype, gtype="bk", rtype="r")
    
    def parse_basketball_match_data(self, match_data):
        """解析篮球比赛数据，从XML提取所需字段"""
        if not match_data:
            return {}
            
        # 篮球特定字段解析
        # 包括让分、大小、独赢等盘口数据
        result = {
            "match_id": match_data.get("id"),
            "league": match_data.get("LEAGUE", ""),
            "home_team": match_data.get("TEAM_H", ""),
            "away_team": match_data.get("TEAM_C", ""),
            "match_time": match_data.get("DATETIME", ""),
                
            # 让分盘口
            "handicap": match_data.get("RATIO_R", ""),
            "home_handicap_odds": match_data.get("IOR_RH", ""),
            "away_handicap_odds": match_data.get("IOR_RC", ""),
            
            # 大小盘口
            "total_points": match_data.get("RATIO_OUO", ""),
            "over_odds": match_data.get("IOR_OUH", ""),
            "under_odds": match_data.get("IOR_OUC", ""),
            
            # 独赢盘口
            "home_win_odds": match_data.get("IOR_MH", ""),
            "away_win_odds": match_data.get("IOR_MC", ""),
            
            # 单队大小球盘口
            "home_team_total": match_data.get("RATIO_OUHO", ""),
            "home_team_over_odds": match_data.get("IOR_OUHO", ""),
            "home_team_under_odds": match_data.get("IOR_OUHU", ""),
            
            "away_team_total": match_data.get("RATIO_OUCO", ""),
            "away_team_over_odds": match_data.get("IOR_OUCO", ""),
            "away_team_under_odds": match_data.get("IOR_OUCU", ""),
            
            # 单节盘口
            "ratio_rouo_1q": match_data.get("RATIO_ROUO_1Q", ""),  # 第一节大小球盘口
            "ior_rouh_1q": match_data.get("IOR_ROUH_1Q", ""),      # 第一节大球赔率
            "ior_rouc_1q": match_data.get("IOR_ROUC_1Q", ""),      # 第一节小球赔率
            
            "ratio_rr_1q": match_data.get("RATIO_RR_1Q", ""),      # 第一节让分盘口
            "ior_rrh_1q": match_data.get("IOR_RRH_1Q", ""),        # 第一节主队让分赔率
            "ior_rrc_1q": match_data.get("IOR_RRC_1Q", ""),        # 第一节客队让分赔率
            
            # 半场盘口
            "half_handicap": match_data.get("HALF_RATIO_R", ""),
            "half_home_handicap_odds": match_data.get("HALF_IOR_RH", ""),
            "half_away_handicap_odds": match_data.get("HALF_IOR_RC", ""),
            
            # 半场大小盘口
            "half_total_points": match_data.get("HALF_RATIO_OUO", ""),
            "half_over_odds": match_data.get("HALF_IOR_OUH", ""),
            "half_under_odds": match_data.get("HALF_IOR_OUC", ""),
            
            # 半场单队大小球盘口
            "half_home_team_total": match_data.get("HALF_RATIO_OUHO", ""),
            "half_home_team_over_odds": match_data.get("HALF_IOR_OUHO", ""),
            "half_home_team_under_odds": match_data.get("HALF_IOR_OUHU", ""),
            
            "half_away_team_total": match_data.get("HALF_RATIO_OUCO", ""),
            "half_away_team_over_odds": match_data.get("HALF_IOR_OUCO", ""),
            "half_away_team_under_odds": match_data.get("HALF_IOR_OUCU", "")
        }
        
        return result
    
    def get_basketball_bet_history(self):
        """获取篮球投注历史 - 使用改进后的get_today_wagers_direct方法"""
        # 直接使用get_today_wagers_direct方法获取注单
        wagers = self.get_today_wagers_direct(gtype="BK")

        if not wagers:
            logger.warning("未获取到任何注单")
            return []

        # 检查是否有重复注单
        if wagers:
            # 检查是否有重复的注单ID
            ticket_ids = [wager.get('ticket_id', '') for wager in wagers]
            unique_ids = set(ticket_ids)

            if len(unique_ids) < len(ticket_ids):
                logger.warning(f"发现重复的注单ID! 共{len(ticket_ids)}个ID，唯一ID数量为{len(unique_ids)}")

                # 去重处理
                unique_wagers = []
                seen_ids = set()

                for wager in wagers:
                    tid = wager.get('ticket_id', '')
                    if tid and tid not in seen_ids:
                        seen_ids.add(tid)
                        unique_wagers.append(wager)

                logger.info(f"注单去重: 原始数量 {len(wagers)}，去重后数量 {len(unique_wagers)}")
                wagers = unique_wagers

            # 按时间排序并只保留最新的10条
            if len(wagers) > 10:
                # 尝试按时间排序
                for wager in wagers:
                    # 确保有adddate和addtime字段用于排序
                    if 'adddate' not in wager and 'addtime' in wager:
                        wager['timestamp'] = f"2025-05-01 {wager.get('addtime', '00:00:00')}"
                    elif 'adddate' in wager and 'addtime' in wager:
                        wager['timestamp'] = f"{wager.get('adddate', '2025-05-01')} {wager.get('addtime', '00:00:00')}"
                    else:
                        wager['timestamp'] = "9999-99-99 99:99:99"

                # 按时间戳降序排序
                wagers.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

                # 只保留最新的10条
                wagers = wagers[:10]

        # 处理每个注单，确保让球盘口显示正确
        for wager in wagers:
            # 对让球盘口进行特殊处理
            if wager.get('bet_type_desc') == "全场让球" or "让球" in wager.get('bet_type_desc', ''):
                # 获取让分值
                ratio_value = ""
                if wager.get('team_h_ratio', '').strip():
                    ratio_value = wager.get('team_h_ratio', '').strip()
                elif wager.get('team_c_ratio', '').strip():
                    ratio_value = wager.get('team_c_ratio', '').strip()

                # 如果有让分值但是投注选择中没有，添加上去
                if ratio_value:
                    bet_choice = wager.get('bet_choice', '')
                    result = wager.get('result', '')
                    if ratio_value not in bet_choice and result:
                        bet_choice = f"{result.strip()} {ratio_value}"
                        wager['bet_choice'] = bet_choice

        logger.info(f"最终获取到 {len(wagers)} 条注单")
        return wagers
    
    def process_bet_data(self):
        """处理BetBurger数据，寻找投注机会"""
        logger.info("开始处理套利数据")
        try:
            # 获取配置
            config = load_config()
            timing_config = config.get("timing", {})
            
            # 获取相关时间间隔配置
            main_retry_interval = timing_config.get("main_retry_interval", 5)  # 主循环重试间隔
            data_monitor_interval = timing_config.get("data_monitor_interval", 5)  # 数据监控间隔
            error_retry_interval = timing_config.get("error_retry_interval", 10)  # 错误重试间隔
            
            # 初始化时间记录
            last_log_time = time.time()  # 添加日志时间控制变量
            last_path_update_time = time.time()  # 添加路径更新时间控制变量
            
            # 用于追踪处理过的投注选项，避免重复处理
            processed_bets = set()
            
            monitoring = True
            found_valid_bet = False
            
            # 确保登录状态
            if not self.is_logged_in:
                logger.warning("尚未登录，无法执行投注，尝试登录")
                if not self.login():
                    logger.error("登录失败，请检查账号密码或网络连接")
                    return False
            
            while monitoring:
                try:
                    # 每5分钟更新一次文件路径，确保跨日期时正确处理
                    current_time = time.time()
                    if current_time - last_path_update_time >= 300:  # 5分钟
                        old_path = self.bet_record_file
                        new_path = self.update_record_file_path()
                        if old_path != new_path:
                            logger.info(f"定期更新投注记录文件路径: {old_path} -> {new_path}")
                        last_path_update_time = current_time
                    
                    # 获取最新套利数据
                    arb_data = fetch_latest_arbs()
                    if not arb_data:
                        if last_log_time + 60 < time.time():  # 每分钟最多记录一次等待日志
                            logger.debug("等待新的套利数据...")
                            last_log_time = time.time()
                        time.sleep(data_monitor_interval)
                        continue
                    
                    # 记录获取到的套利数据数量
                    logger.info(f"获取到{len(arb_data)}条套利数据")
                    
                    processed_count = 0
                    found_valid_bet = False
                    
                    # 确保投注记录文件存在，如果不存在则创建空文件
                    if not os.path.exists(self.bet_record_file):
                        os.makedirs(os.path.dirname(self.bet_record_file), exist_ok=True)
                        with open(self.bet_record_file, 'w', encoding='utf-8') as f:
                            json.dump([], f, ensure_ascii=False, indent=2)
                        logger.info(f"创建新的投注记录文件: {self.bet_record_file}")
                    
                    for arb_item in arb_data:
                        try:
                            # 检查是否已经处理过该投注选项
                            arb_id = arb_item.get('id', '') or arb_item.get('match', '')
                            if arb_id in processed_bets:
                                logger.debug(f"跳过已处理的套利项: {arb_id}")
                                continue
                                
                            # 标记为已处理
                            processed_bets.add(arb_id)
                            
                            # 解析套利数据
                            bet_info = self._parse_bet_info_from_data(arb_item)
                            
                            if not bet_info:
                                logger.debug("无法解析套利数据，跳过")
                                continue
                            
                            # 记录当前处理的投注信息，便于调试
                            logger.info(f"处理投注信息: {bet_info.get('home_team')} vs {bet_info.get('away_team')}, {bet_info.get('bet_type')}={bet_info.get('bet_team')}, 盘口: {bet_info.get('handicap_value')}")
                            
                            # 检查是否已经下过注
                            if self.is_bet_duplicated(bet_info):
                                logger.info(f"检测到重复投注，跳过: {bet_info.get('home_team')} vs {bet_info.get('away_team')}, {bet_info.get('bet_type')}={bet_info.get('bet_team')}")
                                continue
                            
                            # 检查投注条件
                            if not self._should_bet_on_option(arb_item):
                                logger.info(f"投注条件不满足，跳过: {bet_info.get('home_team')} vs {bet_info.get('away_team')}")
                                continue
                            
                            # 执行投注
                            if self.place_bet_simple(bet_info):
                                found_valid_bet = True
                                logger.info(f"成功执行投注: {bet_info.get('home_team')} vs {bet_info.get('away_team')}, {bet_info.get('bet_type')}={bet_info.get('bet_team')}")
                            
                            processed_count += 1
                            
                        except Exception as e:
                            logger.error(f"处理套利项时出错: {e}", exc_info=True)
                            continue
                    
                    # 统计处理结果 - 只有当处理了数据或找到有效投注时才记录
                    if processed_count > 0 or found_valid_bet:
                        logger.info(f"本轮共处理了 {processed_count} 个套利项，{'找到' if found_valid_bet else '未找到'}有效的投注选项")
                    
                    # 处理完所有选项后，如果没有找到有效的投注选项，等待一段时间后重试
                    if not found_valid_bet:
                        if last_log_time + 60 < time.time():  # 每分钟最多记录一次等待日志
                            logger.debug("等待中...")
                            last_log_time = time.time()
                        
                        # 等待一段时间后继续
                        for _ in range(main_retry_interval):
                            if msvcrt.kbhit():
                                key = msvcrt.getch()
                                if key in (b'q', b'Q'):
                                    logger.info("用户请求退出监控")
                                    monitoring = False
                                    break
                            time.sleep(1)
                    
                    # 清理本轮已处理的投注选项集合，避免内存泄漏
                    if len(processed_bets) > 1000:  # 如果集合变得太大，清空它
                        logger.debug(f"清理已处理投注选项集合，当前大小: {len(processed_bets)}")
                        processed_bets.clear()
                        
                except Exception as e:
                    logger.error(f"处理套利数据时出错: {e}", exc_info=True)
                    time.sleep(error_retry_interval)
                    
            return found_valid_bet
        except KeyboardInterrupt:
            logger.info("用户中断监控")
            return found_valid_bet
        except Exception as e:
            logger.error(f"监控过程中发生错误: {e}", exc_info=True)
            return found_valid_bet

    def check_session_active(self):
        """检查会话是否仍然活跃，如果会话已过期则重新登录

        返回:
            bool: 表示会话是否活跃
        """
        # 首先尝试获取账户余额，如果失败就认为会话可能已经过期
        try:
            account_data = self.get_member_data()

            # 判断会话活跃性：通过账户余额是否大于0来判断，而不是通过code判断
            if not account_data or float(account_data.get('maxcredit', 0)) <= 0:
                logger.warning("会话可能已过期，尝试重新登录")

                # 重置登录状态
                self.is_logged_in = False

                # 重新登录
                if self.login():
                    logger.info("重新登录成功")

                    # 重新获取账户余额
                    self.get_account_balance()
                    return True
                else:
                    logger.error("重新登录失败")
                    return False

            # 更新账户余额
            self.credit = float(account_data.get('maxcredit', 0))
            # 不再每次都打印账户余额信息，除非是debug级别
            logger.debug(f"会话活跃，当前账户余额: {self.credit}")
            return True

        except Exception as e:
            logger.error(f"检查会话状态时出错: {e}")

            # 出错时尝试重新登录
            logger.info("尝试重新登录")
            self.is_logged_in = False

            if self.login():
                logger.info("重新登录成功")
                self.get_account_balance()
                return True
            else:
                logger.error("重新登录失败")
                return False
    
    def place_bet_simple(self, bet_info, amount=None):
        """
        执行简化版的投注操作

        参数:
            bet_info: 投注信息
            amount: 投注金额（可选，如果不提供则从配置读取）

        返回:
            成功返回True，失败返回False
        """
        current_time = time.time()
        self.log_counters['bet_attempt'] += 1
        
        # 更新投注记录文件路径，确保使用当前日期
        self.update_record_file_path()
        
        # 每60秒最多记录一次投注尝试日志
        if current_time - self.last_log_time['bet_attempt'] >= self.log_interval:
            logger.info(f"投注信息: {bet_info.get('home_team')} vs {bet_info.get('away_team')}, {bet_info.get('bet_type')}={bet_info.get('bet_team')}, 盘口: {bet_info.get('handicap_value')}, 赔率: {bet_info.get('odds')}")
            self.last_log_time['bet_attempt'] = current_time
            self.log_counters['bet_attempt'] = 0
        
        logger.info("开始执行投注流程")

        try:
            # 记录投注信息日志
            logger.info(f"投注信息: {bet_info.get('home_team')} vs {bet_info.get('away_team')}, {bet_info.get('bet_type')}={bet_info.get('bet_team')}, 盘口: {bet_info.get('handicap_value')}, 赔率: {bet_info.get('odds')}")

            # 检查比赛投注次数限制
            if not self._check_match_bet_limit(bet_info):
                logger.info(f"比赛投注次数已达上限，跳过: {bet_info.get('home_team')} vs {bet_info.get('away_team')}")
                return False

            # 确保会话活跃
            if not self.check_session_active():
                logger.warning("会话不活跃，尝试重新登录")
                if not self.login():
                    logger.error("重新登录失败，无法执行投注")
                    return False
                    
            # 等待一段时间确保不超速
            wait_time = 2  # 默认等待2秒
            # 尝试从配置获取等待时间
            try:
                config = load_config()
                timing_config = config.get("timing", {})
                wait_time = timing_config.get("bet_delay", 2)
            except:
                pass
                
            logger.info(f"等待{wait_time}秒后执行投注...")
            time.sleep(wait_time)
                
            # 获取必要的投注信息
            home_team = bet_info.get("home_team")
            away_team = bet_info.get("away_team")
            bet_type = bet_info.get("bet_type")
            bet_team = bet_info.get("bet_team")
            handicap_value = bet_info.get("handicap_value")
            period = bet_info.get("period", "全场")
            bet_option = bet_info.get("bet_option", {})
            
            # 日志记录
            logger.info(f"投注比赛: {home_team} vs {away_team}")
            logger.info(f"投注类型: {bet_type}, 投注队伍: {bet_team}, 盘口: {handicap_value}, 比赛阶段: {period}")
            
            # 获取赔率
            odds = bet_info.get("odds", 0.0)
            
            # 检查赔率是否有效
            if odds <= 0:
                logger.warning(f"投注赔率无效: {odds}，尝试获取实时赔率")
                
                # 尝试获取最新赔率
                debug_info = bet_option.get("debug_info", {})
                event_id = debug_info.get("event_id")
                
                if not event_id:
                    logger.warning("无法获取比赛ID，无法获取最新赔率")
                    return False
                
                # 首先尝试检查event_id是否是有效的皇冠比赛ID
                # 搜索当天的比赛列表，查找匹配的比赛
                current_matches = self.get_basketball_matches(showtype="today")
                found_match = False
                
                for match in current_matches:
                    if match.get('GID') == event_id:
                        found_match = True
                        logger.info(f"找到匹配的皇冠比赛: {match.get('TEAM_H')} vs {match.get('TEAM_C')}")
                        break
                
                # 如果在今日比赛中找不到，尝试在早盘比赛中查找
                if not found_match:
                    logger.warning(f"比赛ID {event_id} 在today比赛列表中不存在，尝试在early中查找")
                    early_matches = self.get_basketball_matches(showtype="early")
                    for match in early_matches:
                        if match.get('GID') == event_id:
                            found_match = True
                            logger.info(f"在early中找到匹配的皇冠比赛: {match.get('TEAM_H')} vs {match.get('TEAM_C')}")
                            break
                        
                if not found_match:
                    logger.warning(f"比赛ID {event_id} 在皇冠比赛列表中不存在，尝试查找匹配的比赛")
                    # 合并今日比赛和早盘比赛进行队伍匹配
                    all_matches = current_matches + early_matches if 'early_matches' in locals() else current_matches
                    
                    # 定义比赛名称黑名单关键词
                    blacklist_keywords = ["总三分球数", "-总三分", "三分球数", "total three"]
                    
                    # 过滤掉黑名单中的比赛
                    filtered_matches = []
                    for match in all_matches:
                        match_home = match.get('TEAM_H', '').strip()
                        match_away = match.get('TEAM_C', '').strip()
                        is_blacklisted = False
                        
                        # 检查比赛名称是否包含黑名单关键词
                        for keyword in blacklist_keywords:
                            if keyword.lower() in match_home.lower() or keyword.lower() in match_away.lower():
                                logger.info(f"过滤掉黑名单比赛: {match_home} vs {match_away}, 包含关键词 '{keyword}'")
                                is_blacklisted = True
                                break
                                
                        if not is_blacklisted:
                            filtered_matches.append(match)
                    
                    # 使用过滤后的比赛列表进行匹配
                    best_match = None
                    best_score = 0
                    
                    for match in filtered_matches:
                        match_home = match.get('TEAM_H', '').strip()
                        match_away = match.get('TEAM_C', '').strip()
                        
                        # 使用匹配函数计算相似度
                        match_score = self._improved_team_match(home_team, away_team, match_home, match_away)
                        
                        if match_score > best_score:
                            best_score = match_score
                            best_match = match
                    
                    # 使用配置的匹配阈值，减少误匹配
                    matching_config = self._load_matching_config()
                    min_match_threshold = matching_config.get("min_match_threshold", 0.6)
                    if best_match and best_score >= min_match_threshold:
                        event_id = best_match.get('GID', '')
                        logger.info(f"找到匹配的比赛ID: {event_id}, 匹配分数: {best_score:.2f}")

                        # 如果匹配分数较低，给出警告
                        if best_score < 0.8:
                            logger.warning(f"匹配分数较低 ({best_score:.2f})，请注意验证盘口是否正确")
                    else:
                        logger.warning(f"未找到匹配的比赛ID（最高分数: {best_score:.2f}，阈值: {min_match_threshold}），无法继续投注")

                        # 记录匹配失败用于学习改进
                        try:
                            arbitrage_data = {
                                "match": f"{home_team} - {away_team}",
                                "league": bet_info.get("league", ""),
                                "time": bet_info.get("time", "")
                            }

                            match_learning_system.record_failure(
                                source_platform="BetBurger",
                                target_platform="皇冠",
                                arbitrage_data=arbitrage_data,
                                candidate_matches=filtered_matches[:10],  # 保存前10个候选
                                best_match_score=best_score if best_match else 0.0,
                                best_match_data=best_match if best_match else {},
                                failure_reason=f"匹配分数{best_score:.2f}低于阈值{min_match_threshold}"
                            )
                        except Exception as e:
                            logger.error(f"记录匹配失败时出错: {e}")

                        return False
                    
                # 获取比赛详细信息
                match_info = self.get_basketball_match_info(event_id)
                if not match_info:
                    logger.warning(f"无法获取比赛 {event_id} 的详细信息，无法继续投注")
                    return False
                    
                # 根据投注类型获取对应的赔率
                if bet_type == "让球":
                    if bet_team == home_team:
                        odds = float(match_info.get('IOR_RH', '0') or '0')
                    else:
                        odds = float(match_info.get('IOR_RC', '0') or '0')
                elif bet_type == "大小球":
                    if bet_team == "大":
                        odds = float(match_info.get('IOR_OUC', '0') or '0')
                    else:  # 小球
                        odds = float(match_info.get('IOR_OUH', '0') or '0')
                elif bet_type == "独赢":
                    if bet_team == home_team:
                        odds = float(match_info.get('IOR_MH', '0') or '0')
                    else:
                        odds = float(match_info.get('IOR_MC', '0') or '0')
                        
                logger.info(f"获取到实时赔率: {odds}")
                
                # 更新bet_info中的赔率
                bet_info["odds"] = odds
                
                # 检查更新后的赔率是否仍然无效
                if odds <= 0:
                    logger.warning("即使更新后赔率仍然无效，无法继续投注")
                    return False
            
            # 获取投注金额 - 优先使用传入的amount参数，否则从配置读取
            if amount is not None:
                bet_amount = float(amount)
                logger.info(f"使用传入的投注金额: {bet_amount}")
            else:
                config = load_config()
                auto_bet_config = config.get("auto_bet", {})
                bet_amount = auto_bet_config.get("amount", 50)
                logger.info(f"使用配置文件中的投注金额: {bet_amount}")

            # 添加调试信息
            logger.info(f"最终确定的投注金额: {bet_amount} (类型: {type(bet_amount)})")

            # 确保投注金额不为0或None
            if bet_amount is None or bet_amount <= 0:
                logger.error(f"投注金额无效: {bet_amount}，使用默认值50")
                bet_amount = 50
            config = load_config()
            auto_bet_config = config.get("auto_bet", {})
            min_odds = auto_bet_config.get("min_odds", 1.5)
            
            # 确认赔率是否符合最低要求
            if odds < min_odds:
                logger.warning(f"赔率 {odds} 低于最低要求 {min_odds}，取消投注")
                return False
                
            logger.info(f"确认投注金额: {bet_amount}, 赔率: {odds}")
            
            # 根据投注类型找到对应的投注类型代码
            # 确定比赛阶段对应的投注代码
            period_code = "3"  # 默认全场
            if period == "全场":
                period_code = "3"
            elif period == "上半场":
                period_code = "10"
            elif period == "下半场":
                period_code = "13"
            elif period == "第一节":
                period_code = "5"
            elif period == "第二节":
                period_code = "6"
            elif period == "第三节":
                period_code = "7"
            elif period == "第四节":
                period_code = "8"
            else:
                logger.warning(f"未知的比赛阶段: {period}，使用默认全场")
                
            # 获取投注表单                
            # 确保有有效的比赛ID
            debug_info = bet_option.get("debug_info", {})
            match_id = debug_info.get("event_id")
            
            if not match_id:
                logger.warning("无法获取比赛ID，无法继续投注")
                return False
                
            # 同样检查比赛ID是否有效
            current_matches = self.get_basketball_matches(showtype="today")
            found_match = False
            
            for match in current_matches:
                if match.get('GID') == match_id:
                    found_match = True
                    logger.info(f"确认有效的皇冠比赛ID: {match_id}")
                    break
            
            # 如果在今日比赛中找不到，尝试在早盘比赛中查找
            if not found_match:
                logger.warning(f"比赛ID {match_id} 在today比赛列表中不存在，尝试在early中查找")
                early_matches = self.get_basketball_matches(showtype="early")
                for match in early_matches:
                    if match.get('GID') == match_id:
                        found_match = True
                        logger.info(f"在early中找到匹配的皇冠比赛ID: {match_id}")
                        break
            
            if not found_match:
                logger.warning(f"比赛ID {match_id} 在皇冠比赛列表中不存在，尝试查找匹配的比赛")
                # 合并今日比赛和早盘比赛进行队伍匹配
                all_matches = current_matches + early_matches if 'early_matches' in locals() else current_matches
                
                # 定义比赛名称黑名单关键词
                blacklist_keywords = auto_bet_config.get("blacklist_teams", [])
                
                # 过滤掉黑名单中的比赛
                filtered_matches = []
                for match in all_matches:
                    match_home = match.get('TEAM_H', '').strip()
                    match_away = match.get('TEAM_C', '').strip()
                    is_blacklisted = False
                    
                    # 检查比赛名称是否包含黑名单关键词
                    for keyword in blacklist_keywords:
                        if keyword.lower() in match_home.lower() or keyword.lower() in match_away.lower():
                            logger.info(f"过滤掉黑名单比赛: {match_home} vs {match_away}, 包含关键词 '{keyword}'")
                            is_blacklisted = True
                            break
                            
                    if not is_blacklisted:
                        filtered_matches.append(match)
                
                # 使用过滤后的比赛列表进行匹配
                best_match = None
                best_score = 0
                
                for match in filtered_matches:
                    match_home = match.get('TEAM_H', '').strip()
                    match_away = match.get('TEAM_C', '').strip()
                    
                    # 使用匹配函数计算相似度
                    match_score = self._improved_team_match(home_team, away_team, match_home, match_away)
                    
                    if match_score > best_score:
                        best_score = match_score
                        best_match = match
                
                # 使用配置的匹配阈值，减少误匹配
                matching_config = self._load_matching_config()
                min_match_threshold = matching_config.get("min_match_threshold", 0.6)
                if best_match and best_score >= min_match_threshold:
                    match_id = best_match.get('GID', '')
                    logger.info(f"找到匹配的比赛ID: {match_id}, 匹配分数: {best_score:.2f}")

                    # 如果匹配分数较低，给出警告
                    if best_score < 0.8:
                        logger.warning(f"匹配分数较低 ({best_score:.2f})，请注意验证盘口是否正确")
                else:
                    logger.warning(f"未找到匹配的比赛ID（最高分数: {best_score:.2f}，阈值: {min_match_threshold}），无法继续投注")

                    # 记录匹配失败用于学习改进
                    try:
                        arbitrage_data = {
                            "match": f"{home_team} - {away_team}",
                            "league": bet_info.get("league", ""),
                            "time": bet_info.get("time", "")
                        }

                        match_learning_system.record_failure(
                            source_platform="BetBurger",
                            target_platform="皇冠",
                            arbitrage_data=arbitrage_data,
                            candidate_matches=filtered_matches[:10],  # 保存前10个候选
                            best_match_score=best_score if best_match else 0.0,
                            best_match_data=best_match if best_match else {},
                            failure_reason=f"匹配分数{best_score:.2f}低于阈值{min_match_threshold}"
                        )
                    except Exception as e:
                        logger.error(f"记录匹配失败时出错: {e}")

                    return False
            
            # 确定投注类型代码
            wtype = "R"  # 默认让球
            choice = "H"  # 默认主队
            
            # 根据抓包分析修正投注类型代码
            if bet_type == "让球":
                # 让球的wtype统一使用R，不管是全场还是半场
                wtype = "R"
                # 选择主队还是客队
                if bet_team == home_team:
                    choice = "H"  # 主队
                else:
                    choice = "C"  # 客队
            elif bet_type == "大小球":
                # 大小球的wtype统一使用OU，不管是全场还是半场
                wtype = "OU"
                # 选择大还是小
                if bet_team == "大":
                    choice = "C"  # 大球选择C
                else:
                    choice = "H"  # 小球选择H
            elif bet_type == "主队大":
                wtype = "OUH"
                choice = "O"
            elif bet_type == "主队小":
                wtype = "OUH"
                choice = "U"
            elif bet_type == "客队大":
                wtype = "OUC"
                choice = "O"
            elif bet_type == "客队小":
                wtype = "OUC"
                choice = "U"
            elif bet_type == "独赢":
                wtype = "M"
                if bet_team == home_team:
                    choice = "H"  # 主队
                else:
                    choice = "C"  # 客队
                    
            # 处理半场投注，只修改rtype，保持wtype不变
            if period == "上半场":
                rtype_prefix = "H"
            elif period == "下半场":
                rtype_prefix = "D"
            elif period == "第一节":
                rtype_prefix = "1"
            elif period == "第二节":
                rtype_prefix = "2"
            elif period == "第三节":
                rtype_prefix = "3"
            elif period == "第四节":
                rtype_prefix = "4"
            else:
                rtype_prefix = ""
                
            # 检查市场类型以确保选择的正确性
            bet_option = bet_info.get("bet_option", {})
            market_and_bet_type = bet_option.get("market_and_bet_type")
            if market_and_bet_type:
                # 如果是市场类型19(大球)，强制选择C
                if market_and_bet_type == 19:
                    if wtype == "OU":
                        choice = "C"
                        logger.info("根据市场类型19(大球)强制选择C选项")
                # 如果是市场类型20(小球)，强制选择H
                elif market_and_bet_type == 20:
                    if wtype == "OU":
                        choice = "H"
                        logger.info("根据市场类型20(小球)强制选择H选项")
            
            logger.info(f"投注类型代码: {wtype}, 选择: {choice}, 比赛阶段: {period}")
            
            # 首先尝试获取主盘口的投注表单
            form_data = self.get_bet_form(match_id, wtype, choice, period)
            
            # 如果获取主盘口表单失败，尝试获取更多盘口并精确匹配
            if not form_data:
                logger.info("主盘口投注表单获取失败，尝试获取更多盘口数据...")
                
                # 使用match_alt_handicap函数获取更多盘口并尝试精确匹配
                alt_handicap = self.match_alt_handicap(match_id, bet_type, handicap_value, period)
                
                if alt_handicap:
                    # 找到完全匹配的盘口
                    # 记录实际投注的盘口值
                    actual_handicap = None
                    if bet_type == "让球" and alt_handicap.get("handicap"):
                        actual_handicap = alt_handicap["handicap"]["value"]
                        logger.info(f"找到完全匹配的让球盘口，比赛ID: {alt_handicap['gid']}，盘口值: {actual_handicap}")
                    elif bet_type == "大小球" and alt_handicap.get("total"):
                        actual_handicap = alt_handicap["total"]["value"]
                        logger.info(f"找到完全匹配的大小球盘口，比赛ID: {alt_handicap['gid']}，盘口值: {actual_handicap}")
                    
                    # 更新match_id为二级盘口的ID
                    alt_match_id = alt_handicap['gid']
                    
                    # 更新盘口值，以便记录正确的盘口
                    if actual_handicap is not None:
                        handicap_value = actual_handicap
                    
                    # 使用匹配到的盘口ID尝试获取投注表单
                    form_data = self.get_bet_form(alt_match_id, wtype, choice, period)
                    
                    if not form_data:
                        logger.warning("找到了匹配的盘口，但无法获取投注表单，跳过投注")
                        return False
                else:
                    logger.warning(f"没有找到与目标盘口 {handicap_value} 完全匹配的盘口，跳过投注")
                    return False
            
                
            # 验证表单中的盘口值与原始盘口值是否匹配
            if form_data and 'spread' in form_data:
                # 检查是否为独赢盘口（wtype=M 或 bet_type="独赢"）
                wtype = form_data.get('wtype', '')
                bet_type_check = bet_info.get('bet_type', '')

                if wtype == 'M' or bet_type_check == '独赢':
                    logger.info(f"检测到独赢盘口，跳过盘口值验证，继续投注")
                else:
                    # 仅对非独赢盘口进行盘口值验证
                    try:
                        # 从表单中获取盘口值
                        form_odds = float(form_data['spread'])
                        original_odds = float(handicap_value)

                        # 从配置中获取允许的盘口变化阈值，默认为1
                        config = load_config()
                        odds_change_threshold = config.get("auto_bet", {}).get("odds_change_threshold", 1.0)

                        # 计算差异 - 使用绝对值的差值，而不是直接差值
                        # 因为盘口的正负号表示让分方向，比较大小时我们关心的是数值的绝对差异
                        odds_diff = abs(abs(form_odds) - abs(original_odds))

                        logger.info(f"盘口值验证 - 原始: {original_odds}, 表单: {form_odds}, 绝对值差异: {odds_diff}, 阈值: {odds_change_threshold}")
                        logger.debug(f"盘口值绝对值: |{original_odds}| = {abs(original_odds)}, |{form_odds}| = {abs(form_odds)}")

                        # 如果差异超过阈值，取消投注
                        if odds_diff > odds_change_threshold:
                            logger.warning(f"盘口值变化过大: 原始值 {original_odds} vs 表单值 {form_odds}, 绝对值差异 {odds_diff} 超过阈值 {odds_change_threshold}, 取消投注")
                            return False
                        else:
                            logger.info(f"盘口值验证通过，绝对值差异 {odds_diff} 在阈值 {odds_change_threshold} 范围内，继续投注")
                    except ValueError as e:
                        # 捕获转换错误，记录错误并取消投注
                        logger.error(f"盘口值格式不正确: {form_data['spread']}, 错误: {e}")
                        return False
            else:
                logger.warning("表单数据中缺少盘口值(spread)，无法验证盘口变化，谨慎继续投注")

            # 新增：验证表单中的赔率值与原始赔率值是否匹配
            if form_data and 'ioratio' in form_data:
                try:
                    # 从表单中获取赔率值（ioratio字段）
                    form_ioratio = float(form_data['ioratio'])
                    original_ioratio = float(odds)

                    # 从配置中获取允许的赔率变化阈值，默认为0.1
                    config = load_config()
                    ioratio_change_threshold = config.get("auto_bet", {}).get("ioratio_change_threshold", 0.1)

                    # 计算赔率差异
                    ioratio_diff = abs(form_ioratio - original_ioratio)

                    logger.info(f"赔率值验证 - 原始: {original_ioratio}, 表单: {form_ioratio}, 差异: {ioratio_diff}, 阈值: {ioratio_change_threshold}")

                    # 如果差异超过阈值，取消投注
                    if ioratio_diff > ioratio_change_threshold:
                        logger.warning(f"赔率值变化过大: 原始值 {original_ioratio} vs 表单值 {form_ioratio}, 差异 {ioratio_diff} 超过阈值 {ioratio_change_threshold}, 取消投注")
                        return False
                    else:
                        logger.info(f"赔率值验证通过，差异 {ioratio_diff} 在阈值 {ioratio_change_threshold} 范围内，继续投注")
                except ValueError as e:
                    # 捕获转换错误，记录错误并取消投注
                    logger.error(f"赔率值格式不正确: 原始={odds}, 表单={form_data.get('ioratio')}, 错误: {e}")
                    return False
            else:
                logger.warning("表单数据中缺少赔率值(ioratio)，无法验证赔率变化，谨慎继续投注")
            
            # 发起投注请求
            bet_result = self.place_bet_with_form(form_data, bet_amount)
            
            # 记录更详细的投注结果日志
            # logger.info(f"投注详细结果: {json.dumps(bet_result, ensure_ascii=False, default=str)}")
            
            if bet_result.get("success"):
                self.log_counters['bet_success'] += 1
                if current_time - self.last_log_time['bet_success'] >= self.log_interval:
                    logger.info(f"投注成功: {bet_info.get('home_team')} vs {bet_info.get('away_team')}, {bet_info.get('bet_type')}={bet_info.get('bet_team')}, 盘口: {bet_info.get('handicap_value')}, 赔率: {bet_info.get('odds')}")
                    self.last_log_time['bet_success'] = current_time
                    self.log_counters['bet_success'] = 0
                
                # logger.info("投注成功！")
                
                # 保存投注记录 - 直接使用原始盘口值，不处理实际盘口值
                bet_record = {
                    "match_id": match_id,
                    "home_team": home_team,
                    "away_team": away_team,
                    "bet_type": bet_type,
                    "bet_team": bet_team, 
                    "handicap_value": handicap_value,  # 使用原始盘口值
                    "odds": odds,
                    "amount": bet_amount,
                    "period": period,
                    "timestamp": time.time(),
                    "datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "order_no": bet_result.get("order_no", ""),
                    "success": True
                }
                
                # 注意：开赛时间现在由前端显示时实时获取，不在投注时保存

                # 保存投注记录
                self._save_bet_record(bet_record)

                # 增加比赛投注次数计数
                self._increment_match_bet_count(bet_info)

                # 播放投注成功提示音
                self._play_notification_sound()

                return True
            else:
                self.log_counters['bet_failure'] += 1
                if current_time - self.last_log_time['bet_failure'] >= self.log_interval:
                    logger.warning(f"投注失败: {bet_result.get('message', '未知错误')}")
                    self.last_log_time['bet_failure'] = current_time
                    self.log_counters['bet_failure'] = 0
                logger.warning(f"投注失败: {bet_result.get('message', '未知错误')}")
                return False
                
        except Exception as e:
            logger.error(f"执行投注流程时出错: {e}", exc_info=True)
            return False

    def _improved_team_match(self, team1, team2, match_team1, match_team2):
        """
        改进的团队匹配算法，使用高级相似度计算

        参数:
            team1, team2: 目标队伍名称
            match_team1, match_team2: 皇冠平台中的队伍名称

        返回:
            匹配分数，范围0-1
        """
        # 加载队伍别名配置
        team_aliases = self._load_team_aliases()

        # 使用高级团队相似度计算（启用严格模式）
        final_score, details = calculate_team_similarity(
            team1, team2, match_team1, match_team2, team_aliases, strict_mode=True
        )

        # 记录详细匹配信息
        match_type = details.get("match_type", "unknown")

        # 处理性别不匹配的情况
        if match_type == "gender_mismatch":
            target_gender = details.get("target_gender", "unknown")
            match_gender = details.get("match_gender", "unknown")
            reason = details.get("reason", "性别不匹配")
            logger.debug(f"队伍性别不匹配被过滤: {team1} vs {team2} ({target_gender}) <-> {match_team1} vs {match_team2} ({match_gender}), 原因: {reason}")
            return 0.0

        home_match = details.get("home_match")
        away_match = details.get("away_match")

        if home_match and away_match:
            logger.debug(f"队伍匹配详情 ({match_type}): "
                       f"主队 {home_match.score:.3f} ({home_match.method}), "
                       f"客队 {away_match.score:.3f} ({away_match.method})")

        # 记录结果信息
        if final_score >= 0.8:
            logger.info(f"队伍匹配良好: {team1} vs {team2} <-> {match_team1} vs {match_team2}, "
                      f"分数: {final_score:.3f} (匹配类型: {match_type})")
        elif final_score >= 0.5:
            logger.info(f"队伍匹配一般: {team1} vs {team2} <-> {match_team1} vs {match_team2}, "
                      f"分数: {final_score:.3f} (匹配类型: {match_type})")
        elif final_score > 0:
            logger.debug(f"队伍匹配较差: {team1} vs {team2} <-> {match_team1} vs {match_team2}, "
                       f"分数: {final_score:.3f} (匹配类型: {match_type})")
        else:
            logger.debug(f"队伍匹配被严格模式过滤: {team1} vs {team2} <-> {match_team1} vs {match_team2}, "
                       f"分数: {final_score:.3f} (原因: 单个队伍匹配度过低)")

        return final_score

    # 已移除 _improved_team_match() 方法的剩余代码

    def _load_team_aliases(self):
        """加载队伍别名配置"""
        try:
            import json
            config_path = "config/team_aliases.json"
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get("team_aliases", {})
            else:
                logger.warning(f"队伍别名配置文件不存在: {config_path}")
                return {}
        except Exception as e:
            logger.error(f"加载队伍别名配置失败: {e}")
            return {}

    def _load_matching_config(self):
        """加载匹配配置"""
        try:
            import json
            config_path = "config/team_aliases.json"
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get("matching_config", {})
            else:
                return {}
        except Exception as e:
            logger.error(f"加载匹配配置失败: {e}")
            return {}



    def _is_meaningless_substring(self, substr):
        """检查子字符串是否无意义"""
        # 转换为小写进行检查
        substr_lower = substr.lower()

        # 常见的无意义模式
        meaningless_patterns = [
            'os d', 's de', ' de', 'de ', 'el ', 'la ', 'los', 'las', 'del',
            '印第安', 'ana', 'ian', 'iana', 'os ', 's d', 'e c', 'vs ', ' vs'
        ]

        # 检查是否匹配无意义模式
        for pattern in meaningless_patterns:
            if pattern in substr_lower:
                return True

        # 检查是否主要由空格和常见连接词组成
        common_words = ['de', 'del', 'la', 'el', 'los', 'las', 'vs', 'and', 'of', 'the']
        words = substr_lower.split()
        if len(words) <= 2 and all(word in common_words for word in words):
            return True

        # 检查是否主要由重复字符组成
        if len(set(substr_lower.replace(' ', ''))) <= 2:
            return True

        return False

    def save_bet_record(self, bet_record):
        """保存投注记录到文件"""
        try:
            # 更新投注记录文件路径，确保使用当前日期
            self.update_record_file_path()
            
            # 确保数据目录存在
            data_dir = "data"
            os.makedirs(data_dir, exist_ok=True)
            
            # 使用实例变量中的文件路径（已包含时间戳）
            file_path = self.bet_record_file
            
            # 读取已有记录
            records = []
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        records = json.load(f)
                except (json.JSONDecodeError, FileNotFoundError) as e:
                    logger.error(f"读取投注记录文件出错: {e}")
                    records = []
            
            # 添加新记录
            records.append(bet_record)
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
                
            logger.info(f"成功保存投注记录: {bet_record}")
            return True
        except Exception as e:
            logger.error(f"保存投注记录失败: {e}", exc_info=True)
            return False
    
    def _save_bet_record(self, bet_record):
        """内部方法：保存投注记录到文件，添加success标记"""
        # 更新投注记录文件路径，确保使用当前日期
        self.update_record_file_path()
        
        # 首先进行二次检查，确保不会保存重复记录
        try:
            # 提取关键信息进行检查
            match_id = bet_record.get("match_id", "")
            bet_type = bet_record.get("bet_type", "")
            home_team = bet_record.get("home_team", "")
            away_team = bet_record.get("away_team", "")
            bet_team = bet_record.get("bet_team", "")
            handicap_value = bet_record.get("handicap_value", "")
            
            # 尝试读取配置
            config = load_config()
            duplicate_config = config.get("duplicate_check", {})
            # 是否启用严格匹配（包括盘口值和投注方向）
            strict_matching = duplicate_config.get("strict_matching", False)
            # 大小球盘口值容差范围
            ou_handicap_tolerance = duplicate_config.get("ou_handicap_tolerance", 5)
            
            # 获取当前日期和时间
            now = datetime.now()
            
            # 需要检查的文件列表
            files_to_check = [self.bet_record_file]
            
            # 如果当前时间是凌晨（0-4点），也检查前一天的记录
            if now.hour < 4:
                yesterday = now - timedelta(days=1)
                yesterday_str = yesterday.strftime("%Y-%m-%d")
                yesterday_file = os.path.join("data", f"bet_records_{yesterday_str}.json")
                if os.path.exists(yesterday_file):
                    files_to_check.append(yesterday_file)
                    logger.info(f"保存记录时也将检查前一天的记录: {yesterday_file}")
            
            # 遍历所有需要检查的文件
            for file_path in files_to_check:
                if not os.path.exists(file_path):
                    continue
                
                # 读取记录文件
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        existing_records = json.load(f)
                except Exception as e:
                    logger.error(f"读取投注记录文件出错: {file_path}, 错误: {e}")
                    continue
                
                # 检查是否已有该比赛的相同类型盘口记录
                for record in existing_records:
                    # 比较比赛ID（如果有）
                    match_id_match = False
                    if match_id and record.get("match_id"):
                        match_id_match = (record.get("match_id") == match_id)
                    
                    # 比较队伍名称
                    teams_match = (record.get("home_team") == home_team and
                                  record.get("away_team") == away_team)
                    
                    # 比较盘口类型
                    bet_type_match = (record.get("bet_type") == bet_type)
                    
                    # 基本判定：比赛匹配且盘口类型匹配
                    is_duplicate = (teams_match or match_id_match) and bet_type_match
                    
                    # 如果启用严格匹配，还需要检查投注方向和盘口值
                    if strict_matching and is_duplicate:
                        # 投注方向匹配
                        direction_match = (record.get("bet_team") == bet_team)

                        # 盘口值匹配（对大小球盘口有容差）
                        record_handicap = record.get("handicap_value", "")
                        handicap_match = False

                        if bet_type == "大小球" and record_handicap and handicap_value:
                            try:
                                # 尝试将盘口值转换为浮点数进行比较
                                record_handicap_value = float(record_handicap)
                                current_handicap_value = float(handicap_value)
                                # 检查差值是否在容差范围内
                                handicap_match = abs(record_handicap_value - current_handicap_value) <= ou_handicap_tolerance
                            except:
                                # 转换失败，使用字符串比较
                                handicap_match = (record_handicap == handicap_value)
                        else:
                            # 其他盘口类型，直接比较字符串
                            handicap_match = (record_handicap == handicap_value)

                        # 更新重复判定
                        if bet_type in ["让球", "大小球", "独赢"]:
                            # 让球、大小球、独赢盘口：只要是同一场比赛的同类型盘口就认为重复，不考虑投注方向
                            # 这是因为：
                            # 1. 让球盘口：同一场比赛不应该既投主队让球又投客队让球
                            # 2. 大小球盘口：同一场比赛不应该既投大球又投小球
                            # 3. 独赢盘口：同一场比赛不应该既投主队胜又投客队胜
                            is_duplicate = is_duplicate
                        else:
                            # 其他盘口类型：需要投注方向和盘口值都匹配
                            is_duplicate = is_duplicate and direction_match and handicap_match
                    
                    if is_duplicate:
                        record_datetime = record.get("datetime", "")
                        logger.warning(f"保存记录时检测到重复：{record_datetime}, {home_team} vs {away_team}, {bet_type}盘口")
                        logger.warning(f"已有记录: {record.get('bet_team')}/{record.get('handicap_value')}, 当前尝试: {bet_team}/{handicap_value}")
                        logger.warning(f"跳过保存操作")
                        return True  # 认为已保存成功，避免重复写入
        except Exception as e:
            logger.error(f"二次检查重复记录时出错: {e}", exc_info=True)
            # 出错时继续执行，不阻止保存
        
        # 添加成功标记
        bet_record["success"] = True
        # 调用保存方法
        return self.save_bet_record(bet_record)
        


    def _convert_crown_time_to_local(self, crown_time: str) -> str:
        """将皇冠时间格式转换为本地时间"""
        try:
            if not crown_time:
                return ""

            # 解析皇冠时间格式: "06-15 07:30a" 或 "06-15 12:00p"
            import re
            from datetime import datetime, timedelta

            pattern = r'(\d{2})-(\d{2})\s+(\d{1,2}):(\d{2})([ap])'
            match = re.match(pattern, crown_time)

            if not match:
                logger.warning(f"无法解析皇冠时间格式: {crown_time}")
                return crown_time

            month, day, hour, minute, ampm = match.groups()

            # 转换为24小时制
            hour = int(hour)
            if ampm == 'p' and hour != 12:
                hour += 12
            elif ampm == 'a' and hour == 12:
                hour = 0

            # 构建当前年份的日期时间
            current_year = datetime.now().year
            dt = datetime(current_year, int(month), int(day), hour, int(minute))

            # 皇冠时间需要加12小时转换为本地时间
            local_dt = dt + timedelta(hours=12)

            # 返回格式化的本地时间
            return local_dt.strftime('%Y-%m-%d %H:%M:%S')

        except Exception as e:
            logger.error(f"转换皇冠时间失败: {e}")
            return crown_time

    def _play_notification_sound(self):
        """内部方法：播放投注成功提示音"""
        try:
            from betburger_fetcher import play_notification_sound
            play_notification_sound()
        except Exception as e:
            logger.error(f"播放提示音失败: {e}")

    def is_bet_duplicated(self, bet_key):
        """
        检查是否重复投注

        同一场比赛的让球、大小球或独赢盘口最多只能投注一次，无论是全场还是半场，无论投注方向如何

        重复检测规则：
        1. 让球盘口：同一场比赛不允许既投主队让球又投客队让球
        2. 大小球盘口：同一场比赛不允许既投大球又投小球
        3. 独赢盘口：同一场比赛不允许既投主队胜又投客队胜
        4. 其他盘口类型：需要投注方向和盘口值都完全匹配才算重复
        """
        try:
            current_time = time.time()
            self.log_counters['duplicate_bet'] += 1
            
            # 更新投注记录文件路径，确保使用当前日期
            self.update_record_file_path()
            
            # 从bet_key提取关键信息
            home_team = bet_key.get("home_team", "")
            away_team = bet_key.get("away_team", "")
            bet_type = bet_key.get("bet_type", "")
            bet_team = bet_key.get("bet_team", "")
            period = bet_key.get("period", "")
            match_id = bet_key.get("match_id", "")
            handicap_value = bet_key.get("handicap_value", "")
            debug_info = bet_key.get("bet_option", {}).get("debug_info", {}) if bet_key.get("bet_option") else {}
            event_id = debug_info.get("event_id", "")
            
            # 尝试读取配置
            config = load_config()
            duplicate_config = config.get("duplicate_check", {})
            # 是否启用严格匹配（包括盘口值和投注方向）
            strict_matching = duplicate_config.get("strict_matching", False)
            # 大小球盘口值容差范围
            ou_handicap_tolerance = duplicate_config.get("ou_handicap_tolerance", 5)
            # 是否启用时间窗口
            enable_time_window = duplicate_config.get("enable_time_window", False)
            # 时间窗口大小（分钟）
            time_window_minutes = duplicate_config.get("time_window_minutes", 60)
            
            # 记录日志，方便调试
            logger.info(f"检查重复投注: {home_team} vs {away_team}, {bet_type}, 投注方向={bet_team}, 时段={period}, 比赛ID={match_id or event_id}, 盘口值={handicap_value}")
            
            # 获取当前日期和时间
            now = datetime.now()
            
            # 计算时间窗口的开始时间
            if enable_time_window:
                window_start_time = now - timedelta(minutes=time_window_minutes)
                logger.debug(f"使用时间窗口: {window_start_time} 到 {now}")
            
            # 需要检查的文件列表
            files_to_check = [self.bet_record_file]
            
            # 如果当前时间是凌晨（0-4点），也检查前一天的记录
            if now.hour < 4:
                yesterday = now - timedelta(days=1)
                yesterday_str = yesterday.strftime("%Y-%m-%d")
                yesterday_file = os.path.join("data", f"bet_records_{yesterday_str}.json")
                if os.path.exists(yesterday_file):
                    files_to_check.append(yesterday_file)
                    logger.info(f"凌晨时段，也将检查前一天的记录: {yesterday_file}")
            
            # 遍历所有需要检查的文件
            for file_path in files_to_check:
                if not os.path.exists(file_path):
                    logger.debug(f"投注记录文件不存在: {file_path}")
                    continue
                    
                # 读取记录文件
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        records = json.load(f)
                except Exception as e:
                    logger.error(f"读取投注记录文件出错: {file_path}, 错误: {e}")
                    continue
                
                # 检查是否已经对该比赛的该类型盘口进行过投注
                for record in records:
                    # 如果启用时间窗口，检查记录时间是否在窗口内
                    if enable_time_window:
                        try:
                            record_time_str = record.get("datetime", "")
                            record_time = datetime.strptime(record_time_str, "%Y-%m-%d %H:%M:%S")
                            if record_time < window_start_time:
                                logger.debug(f"记录时间 {record_time_str} 早于窗口开始时间 {window_start_time}，跳过")
                                continue
                        except Exception as e:
                            logger.error(f"解析记录时间出错: {record.get('datetime', '')}, 错误: {e}")
                    
                    # 检查关键信息是否匹配
                    # 1. 比赛匹配 - 匹配主客队伍或比赛ID
                    teams_match = (record.get("home_team") == home_team and
                                  record.get("away_team") == away_team)
                    
                    match_id_match = False
                    record_match_id = str(record.get("match_id", ""))
                    if match_id and record_match_id:
                        match_id_match = (record_match_id == str(match_id))
                    elif event_id and record_match_id:
                        match_id_match = (record_match_id == str(event_id))
                    
                    # 2. 盘口类型匹配 - 只匹配盘口类型
                    bet_type_match = (record.get("bet_type") == bet_type)
                    
                    # 3. 如果启用严格匹配，还需要检查投注方向和盘口值
                    direction_match = True  # 默认方向匹配
                    handicap_match = True   # 默认盘口值匹配
                    
                    if strict_matching:
                        # 投注方向匹配
                        direction_match = (record.get("bet_team") == bet_team)
                        
                        # 盘口值匹配（对大小球盘口有容差）
                        record_handicap = record.get("handicap_value", "")
                        if bet_type == "大小球" and record_handicap and handicap_value:
                            try:
                                # 尝试将盘口值转换为浮点数进行比较
                                record_handicap_value = float(record_handicap)
                                current_handicap_value = float(handicap_value)
                                # 检查差值是否在容差范围内
                                handicap_match = abs(record_handicap_value - current_handicap_value) <= ou_handicap_tolerance
                                logger.debug(f"大小球盘口值比较: {record_handicap_value} vs {current_handicap_value}, 差值={abs(record_handicap_value - current_handicap_value)}, 容差={ou_handicap_tolerance}, 匹配={handicap_match}")
                            except:
                                # 转换失败，使用字符串比较
                                handicap_match = (record_handicap == handicap_value)
                        else:
                            # 其他盘口类型，直接比较字符串
                            handicap_match = (record_handicap == handicap_value)
                    
                    # 判断重复投注
                    # 基本判定：比赛匹配且盘口类型匹配
                    is_duplicate = (teams_match or match_id_match) and bet_type_match

                    # 如果启用严格匹配，还需要检查投注方向和盘口值
                    if strict_matching:
                        # 对于让球、大小球和独赢盘口：同一场比赛的同类型盘口，无论投注方向如何都视为重复
                        if bet_type in ["让球", "大小球", "独赢"]:
                            # 让球、大小球、独赢盘口：只要是同一场比赛的同类型盘口就认为重复，不考虑投注方向
                            # 这是因为：
                            # 1. 让球盘口：同一场比赛不应该既投主队让球又投客队让球
                            # 2. 大小球盘口：同一场比赛不应该既投大球又投小球
                            # 3. 独赢盘口：同一场比赛不应该既投主队胜又投客队胜
                            is_duplicate = is_duplicate
                        else:
                            # 其他盘口类型：需要投注方向和盘口值都匹配
                            is_duplicate = is_duplicate and direction_match and handicap_match
                    
                    if is_duplicate:
                        # 获取已有记录的详细信息用于日志
                        record_bet_team = record.get("bet_team", "")
                        record_period = record.get("period", "")
                        record_handicap = record.get("handicap_value", "")
                        record_datetime = record.get("datetime", "")
                        
                        # 统一简洁的日志格式
                        logger.warning(f"重复投注: {home_team} vs {away_team}, {bet_type}盘口")
                        logger.warning(f"已有记录: {record_datetime}, {record_period}/{record_bet_team}/{record_handicap}, 当前尝试: {period}/{bet_team}/{handicap_value}")
                        
                        # 每60秒最多记录一次重复投注日志
                        if current_time - self.last_log_time['duplicate_bet'] >= self.log_interval:
                            logger.warning(f"重复投注检测: 同一场比赛的{bet_type}盘口已有投注记录，不允许重复投注")
                            logger.warning(f"比赛ID匹配: {match_id_match}, 队伍匹配: {teams_match}, 盘口类型匹配: {bet_type_match}")
                            if strict_matching:
                                logger.warning(f"投注方向匹配: {direction_match}, 盘口值匹配: {handicap_match}")
                            self.last_log_time['duplicate_bet'] = current_time
                            self.log_counters['duplicate_bet'] = 0
                        
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查重复投注时出错: {e}", exc_info=True)
            return True  # 出错时保守处理，认为是重复投注

    def _parse_bet_info_from_data(self, bet_data):
        """从BetBurger数据中解析投注信息"""
        try:
            # 只记录关键信息
            match = bet_data.get("match", "")
            teams = match.split(" - ")
            if len(teams) != 2:
                logger.warning(f"无法解析比赛队伍: {match}")
                return None
                
            home_team, away_team = teams
            market_and_bet_type = bet_data.get("market_and_bet_type")
            market_param = bet_data.get("market_param", "0")
            koef = bet_data.get("koef", "0")
            period_id = bet_data.get("period_id", 0)  # 添加回period_id变量
            description = bet_data.get("description", "")  # 添加回description变量
            
            # 只记录最终解析结果
            logger.info(f"解析到投注信息: {home_team} vs {away_team}, 市场类型: {market_and_bet_type}, 盘口: {market_param}, 赔率: {koef}")
            
            # 尝试转换赔率为浮点数
            try:
                koef_value = float(koef)
                logger.info(f"转换后的赔率值: {koef_value}")
            except:
                logger.warning(f"赔率值{koef}无法转换为浮点数，使用默认值0.0")
                koef_value = 0.0
                
            # 尝试转换市场类型为整数
            try:
                if isinstance(market_and_bet_type, str):
                    market_and_bet_type = int(market_and_bet_type)
            except:
                logger.warning(f"市场类型{market_and_bet_type}无法转换为整数")
                
            # 根据盘口类型确定投注类型
            bet_type = "未知"
            bet_team = None
            handicap_value = None
            
            # 处理投注类型和盘口
            logger.info(f"处理市场类型 {market_and_bet_type}")
            
            # 支持更多市场类型
            if market_and_bet_type == 17:  # 主队让球
                bet_type = "让球"
                bet_team = home_team
                handicap_value = market_param
                logger.info(f"解析为主队让球: {home_team}, 盘口: {handicap_value}")
            elif market_and_bet_type == 18:  # 客队让球
                bet_type = "让球"
                bet_team = away_team
                handicap_value = market_param
                logger.info(f"解析为客队让球: {away_team}, 盘口: {handicap_value}")
            elif market_and_bet_type == 19:  # 大球
                bet_type = "大小球"
                bet_team = "大"  # 表示大球
                # 对于大小球，不需要符号，确保移除任何+号
                handicap_value = market_param.replace('+', '')
                logger.info(f"解析为大球: {handicap_value}")
            elif market_and_bet_type == 20:  # 小球
                bet_type = "大小球"
                bet_team = "小"  # 表示小球
                # 对于大小球，不需要符号，确保移除任何+号
                handicap_value = market_param.replace('+', '')
                logger.info(f"解析为小球: {handicap_value}")
            elif market_and_bet_type in [1, 2]:  # 独赢盘口: 1=主胜, 2=客胜
                bet_type = "独赢"
                bet_team = home_team if market_and_bet_type == 1 else away_team
                handicap_value = "0"
                logger.info(f"解析为独赢: {bet_team}")
            # 添加对更多描述信息的解析
            elif isinstance(description, str) and description:
                # 从描述中提取信息
                logger.info(f"尝试从描述中解析: {description}")
                if "让球" in description or "+" in market_param or "-" in market_param:
                    bet_type = "让球"
                    # 从描述中提取队伍
                    if home_team in description:
                        bet_team = home_team
                    elif away_team in description:
                        bet_team = away_team
                    else:
                        # 默认为主队
                        bet_team = home_team
                    handicap_value = market_param
                    logger.info(f"从描述解析为让球: {bet_team}, 盘口: {handicap_value}")
                elif "大" in description:
                    bet_type = "大小球"
                    bet_team = "大"
                    handicap_value = market_param.replace('+', '')
                    logger.info(f"从描述解析为大球: {handicap_value}")
                elif "小" in description:
                    bet_type = "大小球"
                    bet_team = "小"
                    handicap_value = market_param.replace('+', '')
                    logger.info(f"从描述解析为小球: {handicap_value}")
            else:
                logger.warning(f"不支持的投注类型: {market_and_bet_type}，描述: {description}")
                return None
                
            # 确定比赛阶段
            bet_period = "全场"
            if period_id == 3:
                bet_period = "全场"
            elif period_id == 5:
                bet_period = "第一节"
            elif period_id == 6:
                bet_period = "第二节"
            elif period_id == 7:
                bet_period = "第三节"
            elif period_id == 8:
                bet_period = "第四节"
            elif period_id == 10:
                bet_period = "上半场"
            elif period_id == 13:
                bet_period = "下半场"
            else:
                logger.warning(f"未知的比赛阶段ID: {period_id}")
            
            # 构建结果对象
            result = {
                "home_team": home_team,
                "away_team": away_team,
                "bet_type": bet_type,
                "bet_team": bet_team,
                "handicap_value": handicap_value,
                "odds": koef_value,
                "period": bet_period,
                "original_data": bet_data,
                "bet_option": bet_data  # 添加bet_option引用，方便在其他方法中直接操作原始数据
            }
            
            return result
        except Exception as e:
            logger.error(f"解析投注信息时出错: {e}")
            return None

    def _should_bet_on_option(self, bet_option):
        """
        判断是否应该对这个选项进行投注
        
        参数:
            bet_option: 投注选项数据
            
        返回:
            True表示应该投注，False表示不应该
        """
        try:
            # 记录完整的bet_option数据，用于调试
            logger.debug(f"完整的bet_option数据: {json.dumps(bet_option, ensure_ascii=False)}")
            
            # 从配置中获取投注条件
            config = load_config()
            auto_bet_config = config.get("auto_bet", {})
            
            # 如果自动投注功能未启用，直接返回False
            if not auto_bet_config.get("enable", False):
                logger.info("自动投注功能未启用，跳过投注")
                return False
                
            # 获取投注条件
            min_odds = auto_bet_config.get("min_odds", 1.5)
            blacklist_teams = auto_bet_config.get("blacklist_teams", [])
            
            # 检查皇冠投注选项 - 使用更宽松的检测
            bookmaker = bet_option.get("bookmaker", "").lower()
            if "皇冠" not in bookmaker and "crown" not in bookmaker and "hg" not in bookmaker:
                # 不是皇冠的选项，记录并跳过
                logger.debug(f"跳过非皇冠投注选项: {bookmaker}")
                return False
                
            logger.info(f"检测到皇冠投注选项: {bookmaker}")
            
            # 检查比赛ID - 必须提供有效的比赛ID
            debug_info = bet_option.get("debug_info", {})
            event_id = debug_info.get("event_id")
            if not event_id:
                logger.warning("投注选项缺少有效的比赛ID，无法投注")
                return False
            
            # 获取比赛队伍信息
            match = bet_option.get("match", "")
            # 分离主客队
            teams = match.split(" - ")
            if len(teams) != 2:
                logger.warning(f"无法解析比赛队伍: {match}")
                return False
                
            home_team, away_team = teams
            
            # 检查黑名单
            if any(team in match for team in blacklist_teams):
                logger.info(f"跳过黑名单中的比赛: {match}")
                return False
            
            # 检查盘口参数 - 必须有盘口值
            market_param = bet_option.get("market_param")
            
            # 修复：允许盘口为0或空字符串的情况，这在独赢盘口中是正常的
            if market_param is None:
                logger.warning("投注选项缺少盘口值，无法投注")
                return False
            
            # 检查赔率
            koef = bet_option.get("koef", "0")
            try:
                koef_value = float(koef)
                
                # 特殊处理：如果BetBurger赔率为0或者过低，尝试从皇冠平台直接获取赔率
                if koef_value < min_odds:
                    logger.warning(f"BetBurger赔率太低: {koef_value} < {min_odds}，尝试从皇冠平台获取最新赔率")
                    
                    # 解析投注信息
                    bet_info = self._parse_bet_info_from_data(bet_option)
                    if not bet_info:
                        logger.warning("无法解析投注信息，跳过")
                        return False
                    
                    # 获取最新比赛数据
                    current_matches = self.get_basketball_matches(showtype="today")
                    if not current_matches:
                        logger.warning("无法获取当前比赛列表，跳过")
                        return False
                    
                    # 尝试匹配比赛
                    best_match = None
                    best_score = 0
                    
                    for match in current_matches:
                        match_home = match.get('TEAM_H', '').strip()
                        match_away = match.get('TEAM_C', '').strip()
                        
                        # 使用改进的匹配方法
                        match_score = self._improved_team_match(home_team, away_team, match_home, match_away)
                        
                        if match_score > best_score:
                            best_score = match_score
                            best_match = match
                    
                    # 如果找到匹配的比赛，检查其赔率
                    if best_match and best_score >= 0.4:  # 使用较低的阈值以增加匹配可能性
                        # 获取正确的赔率字段
                        bet_type = bet_info.get("bet_type")
                        bet_team = bet_info.get("bet_team")
                        
                        crown_odds = 0.0
                        
                        # 根据不同的投注类型获取对应的赔率
                        if bet_type == "让球":
                            if bet_team == home_team:
                                crown_odds = float(best_match.get('IOR_RH', '0') or '0')
                            else:
                                crown_odds = float(best_match.get('IOR_RC', '0') or '0')
                        elif bet_type == "大小球":
                            if bet_team == "大":
                                crown_odds = float(best_match.get('IOR_OUH', '0') or '0')
                            else:  # 小球
                                crown_odds = float(best_match.get('IOR_OUC', '0') or '0')
                        elif bet_type == "独赢":
                            if bet_team == home_team:
                                crown_odds = float(best_match.get('IOR_MH', '0') or '0')
                            else:
                                crown_odds = float(best_match.get('IOR_MC', '0') or '0')
                        
                        logger.info(f"从皇冠平台获取到实际赔率: {crown_odds}")
                        
                        # 使用皇冠平台的赔率替代BetBurger的赔率
                        if crown_odds >= min_odds:
                            logger.info(f"皇冠平台赔率符合要求: {crown_odds} >= {min_odds}，允许投注")
                            # 更新bet_option中的赔率
                            bet_option["koef"] = str(crown_odds)
                            # 更新bet_info中的赔率
                            bet_info["odds"] = crown_odds
                            return True
                        else:
                            logger.info(f"皇冠平台赔率仍然太低: {crown_odds} < {min_odds}，跳过投注")
                            return False
                    else:
                        logger.warning(f"无法在皇冠平台找到匹配的比赛，跳过投注")
                        return False
                
                # 标准路径：BetBurger赔率符合要求
                if koef_value >= min_odds:
                    logger.info(f"赔率符合要求: {koef_value} >= {min_odds}")
                    return True
                else:
                    logger.info(f"赔率太低，跳过投注: {koef_value} < {min_odds}")
                    return False
            except Exception as e:
                logger.warning(f"处理赔率时出错: {e}")
                logger.warning(f"无效的赔率值: {koef}，尝试从皇冠平台获取")
                # 之前已经添加了从皇冠平台获取赔率的逻辑，这里不重复
                return False
                
        except Exception as e:
            logger.error(f"检查投注条件时出错: {e}", exc_info=True)
            return False

    def get_basketball_match_info(self, match_id):
        """
        获取篮球比赛的详细信息
        
        参数:
            match_id: 比赛ID
            
        返回:
            比赛详细信息字典
        """
        logger.info(f"获取比赛{match_id}的详细信息")
        
        try:
            # 检查会话是否活跃
            if not self.check_session_active():
                logger.warning("会话不活跃，尝试重新登录")
                if not self.login():
                    logger.error("重新登录失败，无法获取比赛信息")
                    return None
            
            # 构造请求参数
            params = {
                "uid": self.uid,
                "ver": self.version,
                "langx": "zh-cn",
                "p": "slip",
                "gtype": "BK",
                "showtype": "ft",
                "date": "live",
                "isMenuReset": "true",
                "isResetRefresh": "false",
                "gid": match_id,
                "timeer": int(time.time()),
                "ltype": 1
            }
            
            logger.debug(f"请求参数: {params}")
            
            # 发送请求
            response = self.session.get(
                url=f"{self.transform_url}?ver={self.version}",
                headers=self.headers,
                params=params
            )
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"获取比赛信息失败，状态码: {response.status_code}")
                return None
                
            # 提取HTML内容
            html_content = response.text
            
            # 提取比赛信息
            match_info = self._extract_match_info_from_html(html_content, match_id)
            
            # 记录提取到的信息
            if match_info:
                logger.info(f"成功获取比赛信息: {match_info.get('TEAM_H', '')} vs {match_info.get('TEAM_C', '')}")
                logger.debug(f"比赛详细信息: {match_info}")
            else:
                logger.warning(f"未提取到比赛{match_id}的信息")
                # 添加重试逻辑
                retry_count = 2
                while retry_count > 0 and not match_info:
                    logger.info(f"尝试重新获取比赛信息，剩余重试次数: {retry_count}")
                    time.sleep(2)  # 等待一段时间再重试
                    
                    # 重新发送请求
                    response = self.session.get(
                        url=f"{self.transform_url}?ver={self.version}",
                        headers=self.headers,
                        params=params
                    )
                    
                    if response.status_code == 200:
                        html_content = response.text
                        match_info = self._extract_match_info_from_html(html_content, match_id)
                        
                        if match_info:
                            logger.info(f"重试成功获取比赛信息: {match_info.get('TEAM_H', '')} vs {match_info.get('TEAM_C', '')}")
                            break
                            
                    retry_count -= 1
                    
                if not match_info:
                    logger.error(f"多次尝试后仍未获取到比赛{match_id}的信息")
                
            return match_info
        except Exception as e:
            logger.error(f"获取比赛信息时出错: {e}", exc_info=True)
            return None

    def _extract_match_info_from_html(self, html_content, match_id):
        """
        从HTML内容中提取比赛信息
        
        参数:
            html_content: HTML内容
            match_id: 比赛ID
            
        返回:
            比赛信息字典
        """
        try:
            # 查找比赛信息的JSON数据
            match_pattern = r'var\s+gameInfo\s*=\s*({.*?});'
            match = re.search(match_pattern, html_content, re.DOTALL)
            
            if not match:
                logger.warning("在HTML中未找到比赛信息")
                # 尝试查找错误信息
                error_pattern = r'<div class="error">(.*?)</div>'
                error_match = re.search(error_pattern, html_content, re.DOTALL)
                if error_match:
                    error_msg = error_match.group(1).strip()
                    logger.warning(f"页面显示错误: {error_msg}")
                return None
                
            # 提取JSON字符串
            json_str = match.group(1)
            
            # 清理JSON字符串
            json_str = re.sub(r'(\w+):', r'"\1":', json_str)  # 将属性名加上引号
            json_str = re.sub(r',\s*}', r'}', json_str)  # 移除对象末尾的逗号
            
            # 尝试解析JSON
            try:
                match_info = json.loads(json_str)
                
                # 转换属性为字符串
                match_info = {str(k): str(v) if v is not None else "" for k, v in match_info.items()}
                
                # 添加match_id
                match_info["MATCH_ID"] = str(match_id)
                
                return match_info
            except json.JSONDecodeError as e:
                logger.error(f"解析比赛信息JSON时出错: {e}")
                logger.debug(f"无法解析的JSON: {json_str[:200]}...")
                return None
        except Exception as e:
            logger.error(f"提取比赛信息时出错: {e}", exc_info=True)
            return None
    
    def get_bet_form(self, match_id, bet_type, choice, period="全场"):
        """获取投注表单信息
        
        参数:
            match_id (str): 比赛ID
            bet_type (str): 投注类型，如 'R'(让球), 'OU'(大小球)
            choice (str): 投注选择，'H'(主队/大) 或 'C'(客队/小)
            period (str): 比赛时段，"全场"、"上半场"等
            
        返回:
            dict: 投注表单参数，包含con, ratio, ioratio等，失败返回None
        """
        logger.debug(f"获取投注表单 - 比赛:{match_id}, 类型:{bet_type}, 选项:{choice}, 时段:{period}")
        
        try:
            # 不需要修改wtype，保持原始值
            # 根据用户提供的信息，正确的字段使用方式：
            # 全场大小球,半场大小球：wtype都是OU
            # 全场让球, 半场让球：wtype都是R
            
            logger.info(f"使用标准wtype格式: {bet_type}, 选项: {choice}, 时段: {period}")
            
            # 根据时段调整比赛ID
            original_match_id = match_id
            if period == "上半场":
                match_id = str(int(match_id) + 1)
                logger.info(f"上半场比赛，调整比赛ID: {original_match_id} -> {match_id}")
            
            # 构造获取表单的请求参数
            form_params = {
                "p": "Other_order_view",
                "uid": self.uid,
                "ver": self.version,
                "langx": "zh-cn",
                "odd_f_type": "E",
                "gid": match_id,
                "gtype": "BK",
                "wtype": bet_type,
                "chose_team": choice
            }
            
            logger.debug(f"投注表单请求参数: {form_params}")
            
            # 发送请求
            response = self.session.post(
                url=f"{self.transform_url}?ver={self.version}",
                headers=self.headers,
                data=form_params
            )
            
            if response.status_code != 200:
                logger.error(f"获取投注表单失败，状态码: {response.status_code}")
                return None
                
            # 解析响应
            response_text = response.text
            logger.debug(f"投注表单响应: {response_text[:200]}...")
            
            # 检查响应代码
            code_match = re.search(r"<code>(.*?)</code>", response_text)
            if not code_match or code_match.group(1) != "501":
                error_msg = "未知错误"
                errormsg_match = re.search(r"<errormsg>(.*?)</errormsg>", response_text)
                if errormsg_match:
                    error_msg = errormsg_match.group(1)
                
                logger.warning(f"获取投注表单失败，代码: {code_match.group(1) if code_match else '未知'}, 错误: {error_msg}")
                return None
            
            # 提取关键参数
            form_data = {}
            for field in ["con", "ratio", "ioratio", "spread"]:
                pattern = f"<{field}>(.*?)</{field}>"
                match = re.search(pattern, response_text)
                if match:
                    form_data[field] = match.group(1)
                else:
                    logger.debug(f"表单中未找到 {field} 字段")
            
            # 添加必要的固定参数
            form_data["chose_team"] = choice
            form_data["gid"] = match_id
            form_data["gtype"] = "BK"
            form_data["wtype"] = bet_type
            form_data["period"] = period  # 保存时段信息，便于后续处理
            
            # 检查是否获取到所有必要参数
            required_fields = ["con", "ratio", "ioratio", "spread"]
            for field in required_fields:
                if field not in form_data or not form_data[field]:
                    logger.warning(f"投注表单缺少必要参数: {field}")
                    return None
                    
            # 记录获取的表单数据
            logger.debug(f"成功获取投注表单: {json.dumps(form_data, ensure_ascii=False)}")
            
            return form_data
        except Exception as e:
            logger.error(f"获取投注表单时出错: {e}", exc_info=True)
            return None
    
    def place_bet_with_form(self, form_data, amount=50):
        """使用表单数据进行投注
        
        参数:
            form_data (dict): 从get_bet_form获取的表单数据
            amount (int): 投注金额
            
        返回:
            dict: 投注结果，包含success, order_no, credit等字段
        """
        logger.debug(f"使用表单数据投注 - 比赛:{form_data.get('gid')}, 类型:{form_data.get('wtype')}, 选项:{form_data.get('chose_team')}, 金额:{amount}")
        logger.info(f"投注金额详细信息: amount={amount}, type={type(amount)}, str(amount)={str(amount)}")

        # 投注前检查登录状态
        if not self.is_logged_in or not self.uid:
            logger.warning("投注前检测到未登录状态，尝试重新登录")
            if not self.login():
                logger.error("重新登录失败，无法执行投注")
                return {"success": False, "message": "登录状态异常，重新登录失败"}

        # 验证会话是否有效（通过获取账户余额）
        try:
            account_data = self.get_account_balance()
            if not account_data:
                logger.warning("无法获取账户余额，可能会话已过期，尝试重新登录")
                if not self.login():
                    logger.error("会话过期重新登录失败")
                    return {"success": False, "message": "会话过期，重新登录失败"}
        except Exception as e:
            logger.warning(f"验证会话时出错: {e}，尝试重新登录")
            if not self.login():
                logger.error("会话验证失败重新登录失败")
                return {"success": False, "message": "会话验证失败，重新登录失败"}
        
        try:
            # 构造rtype
            wtype = form_data.get('wtype', '')
            chose_team = form_data.get('chose_team', '')
            
            # 从place_bet_simple传递的rtype_prefix参数
            rtype_prefix = form_data.get('rtype_prefix', '')
            
            # 根据抓包结果修正rtype逻辑
            if wtype == 'R':
                rtype = f"{rtype_prefix}R{chose_team}"  # 如HR, DR, R1等
            elif wtype == 'OU':
                rtype = f"{rtype_prefix}OU{chose_team}"  # 如HOU, DOU, OU1等
            elif wtype == 'OUH':
                rtype = f"{rtype_prefix}OUH{chose_team}"  # 主队大小
            elif wtype == 'OUC':
                rtype = f"{rtype_prefix}OUC{chose_team}"  # 客队大小
            elif wtype == 'M':
                rtype = f"{rtype_prefix}M{chose_team}"  # 如HM, DM, M1等
            else:
                # 兼容其他类型
                rtype = f"{rtype_prefix}{wtype}{chose_team}"
                
            logger.debug(f"构造投注请求参数: wtype={wtype}, chose_team={chose_team}, rtype={rtype}")
            
            # 构造投注请求参数
            timestamp = str(int(time.time() * 1000))

            # 确保投注金额不为空或0
            if amount is None or amount <= 0:
                logger.error(f"投注金额无效: {amount}，无法继续投注")
                return {"success": False, "message": f"投注金额无效: {amount}"}

            # 确保投注金额是整数格式（皇冠可能不接受小数）
            amount_int = int(float(amount))
            golds_value = str(amount_int)
            logger.info(f"投注参数 - golds: '{golds_value}', amount: {amount} -> {amount_int}, type: {type(amount)}")

            bet_params = {
                "p": "Other_bet",
                "uid": self.uid,
                "ver": self.version,
                "langx": "zh-cn",
                "odd_f_type": "E",
                "golds": golds_value,
                "gid": form_data.get('gid', ''),
                "gtype": form_data.get('gtype', 'BK'),
                "wtype": form_data.get('wtype', ''),
                "rtype": rtype,
                "chose_team": form_data.get('chose_team', ''),
                "ioratio": form_data.get('ioratio', ''),
                "con": form_data.get('con', ''),
                "ratio": form_data.get('ratio', ''),  # 必须使用表单中获取的ratio值
                "autoOdd": "Y",
                "timestamp": timestamp,
                "timestamp2": "",
                "isRB": "N",
                "imp": "",
                "ptype": "",
                "isYesterday": "N",
                "f": "1R"
            }

            # 验证关键参数
            required_params = ['uid', 'gid', 'wtype', 'chose_team', 'ioratio']
            for param in required_params:
                if not bet_params.get(param):
                    logger.error(f"关键参数缺失: {param} = {bet_params.get(param)}")
                    return {"success": False, "message": f"关键参数缺失: {param}"}
            
            logger.debug(f"投注请求参数: {bet_params}")
            
            # 发送投注请求
            response = self.session.post(
                url=f"{self.transform_url}?ver={self.version}",
                headers=self.headers,
                data=bet_params
            )
            
            if response.status_code != 200:
                logger.error(f"投注请求失败，状态码: {response.status_code}")
                return {"success": False, "message": f"HTTP错误: {response.status_code}"}
            
            # 解析响应
            response_text = response.text
            logger.debug(f"投注响应: {response_text}")
            
            # 检查投注结果
            code_match = re.search(r"<code>(.*?)</code>", response_text)
            
            if not code_match:
                return {"success": False, "message": "无法解析响应代码"}
                
            error_code = code_match.group(1)
            
            if error_code == "560":  # 投注成功
                # 提取注单号和余额
                ticket_id_match = re.search(r"<ticket_id>(.*?)</ticket_id>", response_text)
                nowcredit_match = re.search(r"<nowcredit>(.*?)</nowcredit>", response_text)
                
                ticket_id = ticket_id_match.group(1) if ticket_id_match else "未知"
                nowcredit = nowcredit_match.group(1) if nowcredit_match else "未知"
                
                logger.info(f"【投注】成功，订单号: {ticket_id}，余额: {nowcredit}")
                
                return {
                    "success": True,
                    "order_no": ticket_id,
                    "credit": nowcredit,
                    "message": "投注成功"
                }
            else:
                # 投注失败，提取错误信息
                errormsg_match = re.search(r"<errormsg>(.*?)</errormsg>", response_text)
                error_msg = errormsg_match.group(1) if errormsg_match else "未知错误"

                logger.info(f"【投注】失败，代码: {error_code}，错误: {error_msg}")

                # 特殊处理0X007错误
                if error_msg == "0X007":
                    logger.error("0X007错误诊断:")
                    logger.error(f"  - 登录状态: {self.is_logged_in}")
                    logger.error(f"  - UID: {self.uid}")
                    logger.error(f"  - 投注金额: {amount} (类型: {type(amount)})")
                    logger.error(f"  - 账户余额: {getattr(self, 'credit', '未知')}")
                    logger.error(f"  - 投注参数: gid={bet_params.get('gid')}, wtype={bet_params.get('wtype')}, chose_team={bet_params.get('chose_team')}")

                    # 如果是0X007错误，尝试重新登录一次
                    logger.warning("检测到0X007错误，尝试重新登录后重试")
                    if self.login():
                        logger.info("重新登录成功，但本次投注已失败，请重试")
                        error_msg = "0X007错误，已重新登录，请重试投注"
                    else:
                        logger.error("重新登录失败")
                        error_msg = "0X007错误，重新登录失败"

                return {
                    "success": False,
                    "error_code": error_code,
                    "message": error_msg
                }
                
        except Exception as e:
            logger.error(f"投注过程中出错: {e}", exc_info=True)
            return {"success": False, "message": f"投注异常: {str(e)}"}
            
    def place_basketball_bet_two_step(self, match_id, bet_type, choice, amount=50):
        """使用两步骤进行篮球投注(先获取表单，再投注)
        
        参数:
            match_id (str): 比赛ID
            bet_type (str): 投注类型，如 'R'(让球), 'OU'(大小球)
            choice (str): 投注选择，'H'(主队/大) 或 'C'(客队/小)
            amount (int): 投注金额
            
        返回:
            dict: 投注结果，包含success, order_no, credit等字段
        """
        logger.info(f"两步骤投注 - 比赛:{match_id}, 类型:{bet_type}, 选项:{choice}, 金额:{amount}")
        
        # 获取配置中的投注前等待时间
        config = load_config()
        timing_config = config.get("timing", {})
        bet_delay = timing_config.get("bet_delay")
        
        # 第一步：获取投注表单
        form_data = self.get_bet_form(match_id, bet_type, choice)
        if not form_data:
            return {"success": False, "message": "获取投注表单失败"}
            
        # 投注前等待一段时间，防止非正常投注
        if bet_delay > 0:
            logger.info(f"投注前等待 {bet_delay} 秒...")
            time.sleep(bet_delay)
            
        # 第二步：使用表单数据投注
        return self.place_bet_with_form(form_data, amount)

    def get_today_wagers_direct(self, gtype="BK"):
        """直接调用API获取今日注单数据
        
        参数:
            gtype (str): 游戏类型，BK表示篮球，ALL表示所有体育
            
        返回:
            list: 注单列表，每个元素是一个包含注单信息的字典
        """
        logger.info(f"直接获取今日{gtype}注单")
        
        try:
            import time

            # 构造请求参数 - 根据新抓包添加缺失的参数
            current_timestamp = str(int(time.time() * 1000))
            data = {
                "p": "get_today_wagers",
                "uid": self.uid,
                "langx": "zh-cn",
                "LS": "g",
                "selGtype": gtype,
                "chk_cw": "N",
                "ts": current_timestamp,  # 添加时间戳
                "format": "xml",          # 添加格式参数
                "db_slow": "N"
            }
            
            logger.debug(f"今日注单请求参数: {data}")
            
            # 发送请求
            response = self.session.post(
                url=f"{self.transform_url}?ver={self.version}",
                headers=self.headers,
                data=data
            )
            
            if response.status_code != 200:
                logger.error(f"获取今日注单失败，状态码: {response.status_code}")
                return []
                
            # 解析响应
            response_text = response.text
            logger.debug(f"今日注单响应内容长度: {len(response_text)} 字节")
            
            # 检查返回代码
            code_match = re.search(r"<code>(.*?)</code>", response_text)
            if not code_match:
                logger.error("无法从响应中提取返回代码")
                return []
                
            return_code = code_match.group(1)
            # API可能返回"todaywagers"作为成功代码
            if return_code != "0" and return_code != "todaywagers":
                logger.error(f"获取今日注单失败，错误代码: {return_code}")
                return []
            
            # 提取总投注金额
            amout_gold_match = re.search(r"<amout_gold>(.*?)</amout_gold>", response_text)
            if amout_gold_match:
                amout_gold = amout_gold_match.group(1)
                logger.info(f"今日总投注金额: {amout_gold}")
            
            # 使用正则表达式匹配所有注单
            wagers_matches = re.findall(r"<wagers\s+tid=\"([^\"]+)\">(.*?)</wagers>", response_text, re.DOTALL)
            
            logger.info(f"找到 {len(wagers_matches)} 条注单")
            
            if not wagers_matches:
                logger.warning("注单匹配失败，返回空列表")
                return []
            
            # 确保注单ID不重复
            seen_tids = set()
            unique_wagers = []
            
            for tid, wager_content in wagers_matches:
                if tid not in seen_tids:
                    seen_tids.add(tid)
                    unique_wagers.append((tid, wager_content))
            
            if len(unique_wagers) != len(wagers_matches):
                logger.warning(f"发现重复注单ID! 原始数量={len(wagers_matches)}，去重后={len(unique_wagers)}")
            
            # 创建带有时间戳的注单列表，用于排序
            wagers_with_timestamp = []
            
            for tid, wager_content in unique_wagers:
                # 提取下注时间
                addtime_match = re.search(r"<addtime>(.*?)</addtime>", wager_content)
                adddate_match = re.search(r"<adddate>(.*?)</adddate>", wager_content)
                
                if adddate_match and addtime_match:
                    adddate = adddate_match.group(1)
                    addtime = addtime_match.group(1)
                    timestamp = f"{adddate} {addtime}"
                else:
                    # 如果没有时间信息，放在最后
                    timestamp = "9999-99-99 99:99:99"
                
                wagers_with_timestamp.append((tid, wager_content, timestamp))
            
            # 按照时间戳从新到旧排序
            wagers_with_timestamp.sort(key=lambda x: x[2], reverse=True)
            
            # 只保留最近的20条注单
            max_wagers = 20
            if len(wagers_with_timestamp) > max_wagers:
                logger.info(f"注单总数为{len(wagers_with_timestamp)}，限制为最近的{max_wagers}条")
                wagers_with_timestamp = wagers_with_timestamp[:max_wagers]
            
            # 解析每个注单
            wagers_list = []
            
            for tid, wager_content, _ in wagers_with_timestamp:
                # 提取必要的字段
                fields = [
                    "w_id", "addtime", "oddf_type", "team_h", "team_c", "team_h_show", "team_c_show",
                    "team_h_ratio", "team_c_ratio", "league", "gold", "win_gold", "ioratio",
                    "wtype", "bet_wtype", "result", "gtype", "ticket_id", "con", "chose_team", "type", "rtype", "w_ms"
                ]
                
                wager_info = {"ticket_id": tid, "tid": tid}
                for field in fields:
                    pattern = f"<{field}>(.*?)</{field}>"
                    match = re.search(pattern, wager_content)
                    if match:
                        wager_info[field] = match.group(1)
                
                # 格式化队伍信息
                team_h_show = wager_info.get('team_h_show', wager_info.get('team_h', ''))
                team_c_show = wager_info.get('team_c_show', wager_info.get('team_c', ''))
                team_h_ratio = wager_info.get('team_h_ratio', '')
                team_c_ratio = wager_info.get('team_c_ratio', '')
                
                # 格式化显示队伍信息，包含让分数据
                if team_h_ratio:
                    team_h_display = f"{team_h_show} ({team_h_ratio})"
                else:
                    team_h_display = team_h_show
                    
                if team_c_ratio:
                    team_c_display = f"{team_c_show} ({team_c_ratio})"
                else:
                    team_c_display = team_c_show
                
                # 保存格式化后的队伍信息
                wager_info['team_h_display'] = team_h_display
                wager_info['team_c_display'] = team_c_display
                
                # 解析投注类型
                bet_wtype = wager_info.get('bet_wtype', '')
                result = wager_info.get('result', '')
                con = wager_info.get('con', '')  # 盘口值
                
                # 获取投注方向，尝试多种方法：
                chose_team = wager_info.get('chose_team', '')
                type_val = wager_info.get('type', '')  # 有时候是type字段
                
                # 如果chose_team为空，尝试从其他字段获取
                if not chose_team:
                    # 1. 从type字段获取
                    if type_val in ['H', 'C']:
                        chose_team = type_val
                        logger.debug(f"从type字段获取到chose_team={chose_team}")

                    # 2. 从rtype字段提取，通常rtype格式为"XY"，Y部分是H或C
                    elif "rtype" in wager_info:
                        rtype = wager_info.get('rtype', '')
                        if rtype and len(rtype) > 1:
                            last_char = rtype[-1]
                            if last_char in ['H', 'C']:
                                chose_team = last_char
                                logger.debug(f"从rtype字段提取到chose_team={chose_team}")
                            elif 'H' in rtype:
                                chose_team = 'H'
                                logger.debug(f"从rtype字段(包含H)提取到chose_team={chose_team}")
                            elif 'C' in rtype:
                                chose_team = 'C'
                                logger.debug(f"从rtype字段(包含C)提取到chose_team={chose_team}")

                    # 3. 尝试从原始XML数据中直接查找
                    if not chose_team:
                        chose_team_match = re.search(r"<chose_team>(.*?)</chose_team>", wager_content)
                        if chose_team_match:
                            chose_team = chose_team_match.group(1)
                            logger.debug(f"从XML原始数据中获取到chose_team={chose_team}")

                    # 4. 根据bet_type设置默认值
                    if not chose_team and bet_wtype:
                        if "大" in result:
                            chose_team = "H"  # 大球通常用H表示
                            logger.debug(f"根据大球结果设置默认chose_team={chose_team}")
                        elif "小" in result:
                            chose_team = "C"  # 小球通常用C表示
                            logger.debug(f"根据小球结果设置默认chose_team={chose_team}")
                        else:
                            # 最后的默认值
                            chose_team = "H"  # 默认为H
                            logger.warning(f"无法确定投注方向，使用默认值chose_team=H")

                # 将最终确定的chose_team存回wager_info
                wager_info['chose_team'] = chose_team

                # 投注类型描述 - 结合w_ms字段和bet_wtype判断
                w_ms = wager_info.get("w_ms", "").strip()
                if bet_wtype == "R":
                    if w_ms:
                        bet_type_desc = "上半场让球"
                    else:
                        bet_type_desc = "全场让球"
                elif bet_wtype == "OU":
                    if w_ms:
                        bet_type_desc = "上半场大小球"
                    else:
                        bet_type_desc = "全场大小球"
                elif bet_wtype == "HOU":
                    bet_type_desc = "上半场大小球"
                elif bet_wtype == "HR":
                    bet_type_desc = "上半场让球"
                elif bet_wtype.startswith("R") and len(bet_wtype) > 1:
                    bet_type_desc = "单节让球"
                elif bet_wtype.startswith("OU") and len(bet_wtype) > 2:
                    bet_type_desc = "单节大小球"
                else:
                    bet_type_desc = wager_info.get('wtype', bet_wtype)

                # 投注选择
                if "大" in result or "小" in result:
                    if con:
                        bet_choice = f"{result} {con}"  # 大 212.5 或 小 212.5
                    else:
                        bet_choice = result
                elif result:
                    # 获取让分值
                    ratio_value = ""
                    if team_h_ratio and team_h_ratio.strip():
                        ratio_value = team_h_ratio.strip()
                    elif team_c_ratio and team_c_ratio.strip():
                        ratio_value = team_c_ratio.strip()

                    # 始终拼接让分值
                    if ratio_value:
                        bet_choice = f"{result.strip()} {ratio_value}"
                    else:
                        bet_choice = result.strip()
                else:
                    bet_choice = "未知选项"

                # 保存投注类型和选择信息
                wager_info['bet_type_desc'] = bet_type_desc
                wager_info['bet_choice'] = bet_choice

                # 特别处理时段信息 - 优先使用w_ms字段
                w_ms = wager_info.get("w_ms", "").strip()
                if w_ms:
                    # w_ms字段不为空，表示是上半场
                    wager_info["period"] = "上半场"
                    logger.debug(f"从w_ms字段识别为上半场: {w_ms}")
                elif gtype == "BK":
                    # w_ms为空时，根据bet_wtype判断时段
                    if bet_wtype == "HOU" or bet_wtype == "HR":
                        wager_info["period"] = "上半场"
                    elif bet_wtype == "DOU" or bet_wtype == "DR":
                        wager_info["period"] = "下半场"
                    # 然后处理单节情况
                    elif bet_wtype.startswith("R") and bet_wtype != "R":
                        if "_1Q" in wager_info.get("type", ""):
                            wager_info["period"] = "第1节"
                        elif "_2Q" in wager_info.get("type", ""):
                            wager_info["period"] = "第2节"
                        elif "_3Q" in wager_info.get("type", ""):
                            wager_info["period"] = "第3节"
                        elif "_4Q" in wager_info.get("type", ""):
                            wager_info["period"] = "第4节"
                        else:
                            wager_info["period"] = "全场"
                    # 根据rtype辅助判断
                    elif wager_info.get("rtype", "").startswith("H") and len(wager_info.get("rtype", "")) > 1:
                        wager_info["period"] = "上半场"
                    elif wager_info.get("rtype", "").startswith("D") and len(wager_info.get("rtype", "")) > 1:
                        wager_info["period"] = "下半场"
                    else:
                        wager_info["period"] = "全场"
                else:
                    wager_info["period"] = "全场"

                # 添加投注状态字段
                status_match = re.search(r"<ball_act_ret>(.*?)</ball_act_ret>", wager_content)
                if status_match:
                    wager_info["wager_status"] = status_match.group(1)
                else:
                    wager_info["wager_status"] = "未知"

                wagers_list.append(wager_info)
            
            return wagers_list
                
        except Exception as e:
            logger.error(f"获取今日注单时出错: {e}", exc_info=True)
            return []

    def get_game_more(self, match_id):
        """
        获取单场比赛的更多盘口选项
        
        参数:
            match_id: 比赛ID
            
        返回:
            包含该比赛各种盘口的字典，如果失败则返回None
        """
        logger.info(f"获取比赛{match_id}的更多盘口选项")
        
        try:
            # 确保会话活跃
            if not self.check_session_active():
                logger.warning("会话不活跃，尝试重新登录")
                if not self.login():
                    logger.error("重新登录失败，无法获取比赛详情")
                    return None
            
            # 构造请求参数
            data = {
                "uid": self.uid,
                "ver": self.version,
                "langx": "zh-cn",
                "p": "get_game_more",
                "gtype": "bk",
                "showtype": "today",
                "ltype": "3",
                "isRB": "N",
                "lid": "",  # 联赛ID，可以为空
                "specialClick": "",
                "mode": "NORMAL",
                "filter": "Main",
                "ts": str(int(time.time() * 1000)),
                "gid": match_id
            }
            
            # 发送请求
            response = self.session.post(
                url=f"{self.transform_url}?ver={self.version}",
                headers=self.headers,
                data=data
            )
            
            if response.status_code != 200:
                logger.error(f"获取比赛{match_id}详情失败，状态码: {response.status_code}")
                return None
                
            # 解析XML响应
            xml_content = response.text
            
            # 检查返回代码
            code_match = re.search(r"<code>(.*?)</code>", xml_content)
            if not code_match or code_match.group(1) != "615":  # 615是正常返回代码
                logger.warning(f"获取比赛详情返回异常代码: {code_match.group(1) if code_match else '未知'}")
                # 即使代码异常，仍然尝试解析
            
            # 提取所有比赛节点
            games = re.findall(r'<game id="(.*?)">(.*?)</game>', xml_content, re.DOTALL)
            
            if not games:
                logger.warning(f"未找到比赛{match_id}的信息")
                return None
                
            # 解析所有盘口选项
            alt_handicaps = {}
            
            for gid, game_content in games:
                # 去掉前缀"gid"
                if gid.startswith("gid"):
                    gid = gid[3:]
                    
                # 提取比赛信息
                game_info = {}
                
                # 提取基本信息
                fields = ["gtype", "gid", "league", "team_h", "team_c", "session"]
                for field in fields:
                    match = re.search(f"<{field}>(.*?)</{field}>", game_content)
                    if match:
                        game_info[field] = match.group(1)
                
                # 提取让分盘口
                ratio_match = re.search(r"<ratio>(.*?)</ratio>", game_content)
                ior_rh_match = re.search(r"<ior_RH>(.*?)</ior_RH>", game_content)
                ior_rc_match = re.search(r"<ior_RC>(.*?)</ior_RC>", game_content)
                
                if ratio_match and ior_rh_match and ior_rc_match:
                    handicap_value = ratio_match.group(1)
                    game_info["handicap"] = {
                        "value": handicap_value,
                        "ior_h": ior_rh_match.group(1),
                        "ior_c": ior_rc_match.group(1)
                    }
                
                # 提取大小球盘口
                ratio_o_match = re.search(r"<ratio_o>(.*?)</ratio_o>", game_content)
                ratio_u_match = re.search(r"<ratio_u>(.*?)</ratio_u>", game_content)
                ior_ouh_match = re.search(r"<ior_OUH>(.*?)</ior_OUH>", game_content)
                ior_ouc_match = re.search(r"<ior_OUC>(.*?)</ior_OUC>", game_content)
                
                if ratio_o_match and ratio_u_match and ior_ouh_match and ior_ouc_match:
                    # 确保ratio_o和ratio_u一致，否则使用ratio_o
                    total_value = ratio_o_match.group(1)
                    
                    game_info["total"] = {
                        "value": total_value,
                        "ior_h": ior_ouh_match.group(1),  # 大球赔率
                        "ior_c": ior_ouc_match.group(1)   # 小球赔率
                    }
                
                # 添加比赛时段
                session = game_info.get("session", "")
                
                # 根据session确定比赛时段
                period = "全场"
                if "上半场" in session:
                    period = "上半场"
                elif "下半场" in session:
                    period = "下半场"
                elif "第一节" in session:
                    period = "第一节"
                elif "第二节" in session:
                    period = "第二节"
                elif "第三节" in session:
                    period = "第三节"
                elif "第四节" in session:
                    period = "第四节"
                
                # 同样时段的比赛，也需要按不同的让分/大小盘口进行区分
                key = f"{period}"
                
                if key not in alt_handicaps:
                    alt_handicaps[key] = []
                    
                # 只添加有效的盘口（有让分或大小球盘口）
                if "handicap" in game_info or "total" in game_info:
                    alt_handicaps[key].append({
                        "gid": gid,
                        "period": period,
                        "handicap": game_info.get("handicap"),
                        "total": game_info.get("total"),
                        "is_open": "Y" == re.search(r"<gopen>(.*?)</gopen>", game_content).group(1) if re.search(r"<gopen>(.*?)</gopen>", game_content) else False
                    })
            
            logger.info(f"成功获取比赛{match_id}的盘口选项，共{sum(len(options) for options in alt_handicaps.values())}个盘口")
            return alt_handicaps
            
        except Exception as e:
            logger.error(f"获取比赛更多盘口时出错: {e}", exc_info=True)
            return None

    def match_alt_handicap(self, match_id, bet_type, target_handicap, period="全场"):
        """
        寻找和目标盘口最接近的可用盘口

        参数:
            match_id: 比赛ID
            bet_type: 投注类型，"让球" 或 "大小球"
            target_handicap: 目标盘口值
            period: 比赛时段，默认全场

        返回:
            最匹配的盘口信息，如果没找到匹配的则返回None
        """
        logger.info(f"尝试匹配盘口 - 比赛:{match_id}, 类型:{bet_type}, 目标盘口:{target_handicap}, 时段:{period}")

        try:
            # 获取比赛的所有盘口
            all_handicaps = self.get_game_more(match_id)
            if not all_handicaps:
                logger.warning(f"无法获取比赛{match_id}的盘口信息")
                return None

            # 获取指定时段的盘口列表
            period_handicaps = all_handicaps.get(period, [])
            if not period_handicaps:
                logger.warning(f"比赛{match_id}没有{period}的盘口信息")
                return None

            # 将目标盘口转换为浮点数
            try:
                target_value = float(target_handicap)
            except ValueError:
                logger.warning(f"盘口值{target_handicap}无法转换为浮点数")
                return None

            # 记录所有可用的盘口值
            available_handicaps = []

            # 首先寻找完全匹配的盘口
            for handicap_info in period_handicaps:
                # 如果盘口未开放，跳过
                if not handicap_info.get("is_open", False):
                    continue

                if bet_type == "让球" and handicap_info.get("handicap"):
                    try:
                        value = float(handicap_info["handicap"]["value"])
                        available_handicaps.append(f"{value}")

                        # 只返回完全匹配的盘口
                        if value == target_value:
                            logger.info(f"找到完全匹配的让球盘口 {value}")
                            return handicap_info
                    except (ValueError, KeyError):
                        continue

                elif bet_type == "大小球" and handicap_info.get("total"):
                    try:
                        value = float(handicap_info["total"]["value"])
                        available_handicaps.append(f"{value}")

                        # 只返回完全匹配的盘口
                        if value == target_value:
                            logger.info(f"找到完全匹配的大小球盘口 {value}")
                            return handicap_info
                    except (ValueError, KeyError):
                        continue

            # 如果没有找到完全匹配的盘口，记录可用的盘口并返回None
            if available_handicaps:
                logger.info(f"可用的{bet_type}盘口列表: {', '.join(available_handicaps)}，但没有找到与目标盘口 {target_handicap} 完全匹配的盘口")
            else:
                logger.warning(f"没有找到任何可用的{bet_type}盘口")

            return None

        except Exception as e:
            logger.error(f"匹配替代盘口时出错: {e}", exc_info=True)
            return None

    def update_record_file_path(self):
        """更新投注记录文件路径，确保使用当前日期"""
        # 生成当前日期的时间戳 YYYY-MM-DD 格式
        today_str = datetime.now().strftime("%Y-%m-%d")

        # 更新投注记录保存文件名
        old_path = self.bet_record_file
        self.bet_record_file = os.path.join("data", f"bet_records_{today_str}.json")

        # 确保data目录存在
        os.makedirs(os.path.dirname(self.bet_record_file), exist_ok=True)

        # 如果文件路径发生变化，记录日志
        if old_path != self.bet_record_file:
            logger.info(f"更新投注记录文件路径: {old_path} -> {self.bet_record_file}")

        return self.bet_record_file

    def _get_match_identifier(self, bet_info):
        """
        获取比赛的唯一标识符

        参数:
            bet_info: 投注信息字典

        返回:
            比赛唯一标识符字符串
        """
        try:
            # 使用主队和客队名称组合作为比赛标识符
            home_team = bet_info.get('home_team', '').strip()
            away_team = bet_info.get('away_team', '').strip()

            if not home_team or not away_team:
                # 如果没有队伍信息，尝试使用比赛ID
                match_id = bet_info.get('match_id', '')
                if match_id:
                    return f"match_{match_id}"
                else:
                    # 最后尝试使用其他可用信息
                    return f"unknown_{int(time.time())}"

            # 标准化队伍名称（去除空格，转小写）
            home_normalized = home_team.lower().replace(' ', '')
            away_normalized = away_team.lower().replace(' ', '')

            # 按字母顺序排序，确保同一场比赛的标识符一致
            teams = sorted([home_normalized, away_normalized])
            match_identifier = f"{teams[0]}_vs_{teams[1]}"

            logger.debug(f"生成比赛标识符: {home_team} vs {away_team} -> {match_identifier}")
            return match_identifier

        except Exception as e:
            logger.error(f"生成比赛标识符时出错: {e}")
            return f"error_{int(time.time())}"

    def _check_match_bet_limit(self, bet_info):
        """
        检查比赛投注次数是否超过限制

        参数:
            bet_info: 投注信息字典

        返回:
            True: 可以投注，False: 超过限制
        """
        try:
            # 从配置中获取每场比赛最大投注次数
            config = load_config()
            max_bets_per_match = config.get("auto_bet", {}).get("max_bets_per_match", 3)

            # 获取比赛标识符
            match_identifier = self._get_match_identifier(bet_info)

            # 获取当前比赛的投注次数
            current_count = self.match_bet_counts.get(match_identifier, 0)

            logger.info(f"比赛投注次数检查: {match_identifier}, 当前次数: {current_count}, 限制: {max_bets_per_match}")

            # 检查是否超过限制
            if current_count >= max_bets_per_match:
                logger.warning(f"比赛投注次数已达上限: {match_identifier}, 当前: {current_count}, 限制: {max_bets_per_match}")
                return False

            logger.info(f"比赛投注次数检查通过: {match_identifier}, 可继续投注")
            return True

        except Exception as e:
            logger.error(f"检查比赛投注次数限制时出错: {e}")
            # 出错时允许投注，避免影响正常功能
            return True

    def _increment_match_bet_count(self, bet_info):
        """
        增加比赛投注次数计数

        参数:
            bet_info: 投注信息字典
        """
        try:
            match_identifier = self._get_match_identifier(bet_info)

            # 增加计数
            if match_identifier in self.match_bet_counts:
                self.match_bet_counts[match_identifier] += 1
            else:
                self.match_bet_counts[match_identifier] = 1

            new_count = self.match_bet_counts[match_identifier]
            logger.info(f"更新比赛投注次数: {match_identifier}, 新次数: {new_count}")

        except Exception as e:
            logger.error(f"更新比赛投注次数时出错: {e}")

def print_basketball_match_info(match):
    """打印篮球比赛信息"""
    print("="*80)
    print(f"比赛时间: {match['DATETIME']}")
    print(f"联赛: {match['LEAGUE']}")
    print(f"对阵: {match['TEAM_H']} VS {match['TEAM_C']}")
    print("-"*80)
    print(f"让分盘口: {match['RATIO_R']}")
    print(f"主队让分赔率: {match['IOR_RH']}")
    print(f"客队让分赔率: {match['IOR_RC']}")
    print("-"*80)
    print(f"大小球盘口: {match['RATIO_OUO']}")
    print(f"大球赔率: {match['IOR_OUH']}")
    print(f"小球赔率: {match['IOR_OUC']}")
    print("-"*80)
    print(f"独赢盘: 主队({match['IOR_MH']}) 客队({match['IOR_MC']})")
    print("-"*80)
    print(f"主队大小球: {match.get('RATIO_OUHO', '无')}")
    print(f"主队大球赔率: {match.get('IOR_OUHO', '无')}")
    print(f"主队小球赔率: {match.get('IOR_OUHU', '无')}")
    print("-"*80)
    print(f"客队大小球: {match.get('RATIO_OUCO', '无')}")
    print(f"客队大球赔率: {match.get('IOR_OUCO', '无')}")
    print(f"客队小球赔率: {match.get('IOR_OUCU', '无')}")
    print("-"*80)
    print("半场盘口信息：")
    print(f"半场让分盘口: {match.get('HALF_RATIO_R', '无')}")
    print(f"半场主队让分赔率: {match.get('HALF_IOR_RH', '无')}")
    print(f"半场客队让分赔率: {match.get('HALF_IOR_RC', '无')}")
    print("-"*80)
    print(f"半场大小球盘口: {match.get('HALF_RATIO_OUO', '无')}")
    print(f"半场大球赔率: {match.get('HALF_IOR_OUH', '无')}")
    print(f"半场小球赔率: {match.get('HALF_IOR_OUC', '无')}")
    print("-"*80)
    print(f"半场主队大小球: {match.get('HALF_RATIO_OUHO', '无')}")
    print(f"半场主队大球赔率: {match.get('HALF_IOR_OUHO', '无')}")
    print(f"半场主队小球赔率: {match.get('HALF_IOR_OUHU', '无')}")
    print("-"*80)
    print(f"半场客队大小球: {match.get('HALF_RATIO_OUCO', '无')}")
    print(f"半场客队大球赔率: {match.get('HALF_IOR_OUCO', '无')}")
    print(f"半场客队小球赔率: {match.get('HALF_IOR_OUCU', '无')}")
    print("="*80)

def show_history_bills_menu(hgbet_system):
    """显示历史账单查询菜单"""
    while True:
        print("\n===== 皇冠历史账单查询 =====")
        print("1. 查询所有游戏历史账单")
        print("2. 查询篮球历史账单")
        print("3. 查询足球历史账单")
        print("4. 查询详细历史账单")
        print("5. 导出历史账单数据")
        print("0. 返回主菜单")

        choice = input("\n请选择操作: ")

        try:
            if choice == "1":
                query_crown_history(hgbet_system, "ALL", "N")
            elif choice == "2":
                query_crown_history(hgbet_system, "BK", "N")
            elif choice == "3":
                query_crown_history(hgbet_system, "FT", "N")
            elif choice == "4":
                query_crown_history(hgbet_system, "ALL", "Y")
            elif choice == "5":
                export_crown_history(hgbet_system)
            elif choice == "0":
                return
            else:
                print("无效选择，请重新输入")

        except ValueError as e:
            print(f"输入格式错误: {e}")
        except Exception as e:
            logger.error(f"历史账单查询操作异常: {e}")
            print(f"操作失败: {e}")

def query_crown_history(hgbet_system, gtype="ALL", is_all="N"):
    """查询皇冠历史账单"""
    try:
        print(f"\n===== 查询{'详细' if is_all == 'Y' else ''}历史账单 =====")

        if not hgbet_system.is_logged_in:
            print("皇冠系统未登录，请先登录")
            return

        game_type_names = {
            "ALL": "所有游戏",
            "BK": "篮球",
            "FT": "足球"
        }

        print(f"正在查询{game_type_names.get(gtype, gtype)}历史账单...")

        # 调用皇冠系统的历史账单查询方法
        history_data = hgbet_system.get_history_data(gtype=gtype, is_all=is_all)

        if history_data.get('success'):
            # 使用格式化显示方法
            formatted_display = hgbet_system.format_history_display(history_data)
            print(formatted_display)

            # 显示统计信息
            history_records = history_data.get('history', [])
            if history_records:
                print(f"\n📊 统计信息:")
                print(f"记录天数: {len(history_records)} 天")

                # 计算盈利天数和亏损天数
                profit_days = 0
                loss_days = 0

                for record in history_records:
                    winloss = record.get('winloss', '0')
                    try:
                        winloss_float = float(winloss.replace(',', ''))
                        if winloss_float > 0:
                            profit_days += 1
                        elif winloss_float < 0:
                            loss_days += 1
                    except:
                        pass

                print(f"盈利天数: {profit_days} 天")
                print(f"亏损天数: {loss_days} 天")

                if profit_days + loss_days > 0:
                    win_rate = profit_days / (profit_days + loss_days) * 100
                    print(f"胜率: {win_rate:.1f}%")

        else:
            error_msg = history_data.get('error', '未知错误')
            print(f"查询失败: {error_msg}")

    except Exception as e:
        logger.error(f"查询皇冠历史账单失败: {e}")
        print(f"查询失败: {e}")

def export_crown_history(hgbet_system):
    """导出皇冠历史账单数据"""
    try:
        print(f"\n===== 导出历史账单数据 =====")

        if not hgbet_system.is_logged_in:
            print("皇冠系统未登录，请先登录")
            return

        print("正在获取完整历史账单数据...")

        # 获取详细历史数据
        history_data = hgbet_system.get_history_data(gtype="ALL", is_all="Y")

        if not history_data.get('success'):
            print(f"获取数据失败: {history_data.get('error', '未知错误')}")
            return

        # 生成文件名
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"data/crown_history_{timestamp}.json"

        # 确保data目录存在
        import os
        os.makedirs("data", exist_ok=True)

        # 保存数据到JSON文件
        import json
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(history_data, f, ensure_ascii=False, indent=2)

        print(f"✓ 历史账单数据已导出到: {filename}")
        print(f"  总投注金额: {history_data.get('total_gold', '0')}")
        print(f"  总盈亏金额: {history_data.get('total_winloss', '0')}")
        print(f"  记录天数: {len(history_data.get('history', []))} 天")

        # 询问是否生成CSV格式
        choice = input("\n是否同时生成CSV格式文件? (y/n): ")
        if choice.lower() == 'y':
            export_crown_history_csv(history_data, timestamp)

    except Exception as e:
        logger.error(f"导出皇冠历史账单失败: {e}")
        print(f"导出失败: {e}")

def export_crown_history_csv(history_data, timestamp):
    """导出CSV格式的历史账单"""
    try:
        import csv

        csv_filename = f"data/crown_history_{timestamp}.csv"

        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['日期', '日期名称', '投注金额', '有效投注', '盈亏金额', '盈亏状态']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # 写入表头
            writer.writeheader()

            # 写入数据
            for record in history_data.get('history', []):
                winloss_class = record.get('winloss_class', '')
                winloss_status = "盈利" if 'green' in winloss_class.lower() else "亏损" if 'red' in winloss_class.lower() else "平局"

                writer.writerow({
                    '日期': record.get('date', ''),
                    '日期名称': record.get('date_name', ''),
                    '投注金额': record.get('gold', '0'),
                    '有效投注': record.get('vgold', '0'),
                    '盈亏金额': record.get('winloss', '0'),
                    '盈亏状态': winloss_status
                })

            # 写入总计行
            writer.writerow({
                '日期': '总计',
                '日期名称': '',
                '投注金额': history_data.get('total_gold', '0'),
                '有效投注': history_data.get('total_vgold', '0'),
                '盈亏金额': history_data.get('total_winloss', '0'),
                '盈亏状态': ''
            })

        print(f"✓ CSV格式文件已生成: {csv_filename}")

    except Exception as e:
        logger.error(f"导出CSV文件失败: {e}")
        print(f"CSV导出失败: {e}")

def main():
    """主程序入口"""
    # 初始化日志
    setup_logging()
    
        # 单账号模式 - 原始逻辑
        # 加载配置
    config = load_config()
    hgbet_config = config.get("hgbet", {})
    
    username = None
    password = None
    base_url = None
    
    # 如果没有在配置文件中设置，提示用户输入
    if not hgbet_config.get("username"):
        username = input("请输入账号: ")
    
    if not hgbet_config.get("password"):
        password = input("请输入密码: ")
    
    if not hgbet_config.get("base_url"):
        base_url = input("请输入网站地址(直接回车使用默认地址): ")
    
    # 创建HGBetBK实例
    hgbet_bk = HGBetBK(username, password, base_url)
    
    # 登录
    if not hgbet_bk.login():
        logger.error("登录失败，程序退出")
        return
    
    # 菜单循环
    while True:
        print("\n===== 皇冠篮球投注系统 =====")
        print("1. 获取今日篮球比赛")
        print("2. 查询账户余额")
        print("3. 查询今日篮球注单")
        print("4. 处理套利数据并自动投注")
        print("5. 历史账单查询")
        print("0. 退出")
        
        choice = input("请选择操作: ")
        
        if choice == "1":
            # 获取今日篮球比赛4
            matches = hgbet_bk.get_basketball_matches(showtype="today")
            
            # 打印比赛信息
            print(f"\n找到 {len(matches)} 场今日篮球比赛")
            for i, match in enumerate(matches):
                print(f"\n【{i+1}】")
                print_basketball_match_info(match)
                
                
        elif choice == "2":
            # 查询账户余额
            hgbet_bk.get_account_balance()
            print(f"当前账户余额: {hgbet_bk.credit}")
            
        elif choice == "3":
            # 查询今日篮球注单
            wagers = hgbet_bk.get_basketball_bet_history()
            if wagers:
                print("\n===== 今日篮球注单列表 =====")
                for i, wager in enumerate(wagers):
                    print(f"{i+1}. 注单号: {wager.get('ticket_id', '未知')}")
                    print(f"比赛: {wager.get('team_h_display', wager.get('team_h', '未知'))} VS {wager.get('team_c_display', wager.get('team_c', '未知'))}")
                    print(f"投注类型: {wager.get('bet_type_desc', wager.get('wtype', '未知'))}")
                    
                    # 特殊处理让球盘口投注选择显示
                    bet_choice = wager.get('bet_choice', '未知')
                    if wager.get('bet_type_desc') == "全场让球" or "让球" in wager.get('bet_type_desc', ''):
                        # 获取让分值
                        ratio_value = ""
                        if wager.get('team_h_ratio') and wager.get('team_h_ratio').strip():
                            ratio_value = wager.get('team_h_ratio').strip()
                        elif wager.get('team_c_ratio') and wager.get('team_c_ratio').strip():
                            ratio_value = wager.get('team_c_ratio').strip()
                            
                        # 如果有让分值但是投注选择中没有，添加上去
                        if ratio_value and ratio_value not in bet_choice:
                            result = wager.get('result', '')
                            bet_choice = f"{result.strip()} {ratio_value}"
                    
                    print(f"投注选择: {bet_choice}---赔率：{wager.get('ioratio', '未知')}")
                    print(f"投注金额: {wager.get('gold', '0')}")
                    print(f"投注时间: {wager.get('addtime', '未知')}")
                    print(f"投注状态: {wager.get('wager_status', '已成功')}")
                    print("-"*80)
            else:
                print("\n今日没有篮球注单")
                # 尝试直接获取注单，用于调试
                try:
                    direct_wagers = hgbet_bk.get_today_wagers_direct(gtype="BK")
                    if direct_wagers:
                        print(f"直接获取注单成功，找到 {len(direct_wagers)} 条")
                    else:
                        print("直接获取注单也返回空")
                except Exception as e:
                    print(f"尝试直接获取注单时出错: {e}")
        
        elif choice == "4":
            # 处理套利数据
            hgbet_bk.process_bet_data()

        elif choice == "5":
            # 历史账单查询
            show_history_bills_menu(hgbet_bk)

        elif choice == "0":
            print("谢谢使用，再见!")
            break
            
        else:
            print("无效的选择，请重新输入")

if __name__ == "__main__":
    main()