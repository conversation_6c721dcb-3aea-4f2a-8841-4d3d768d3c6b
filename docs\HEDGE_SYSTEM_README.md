# 对冲投注系统使用说明

## 概述

对冲投注系统是在现有平博和皇冠投注系统基础上开发的同时对冲功能，能够自动识别套利机会并在两个平台同时执行对冲投注，实现风险对冲和稳定收益。

## 核心功能

### 1. 基准投注策略
- **自动选择基准平台**：根据赔率优势自动选择基准投注平台
- **手动指定基准平台**：可在配置中指定固定的基准平台
- **金额分配计算**：根据赔率自动计算两个平台的投注金额分配

### 2. 漏单补单功能
- **实时监控**：监控两个平台的投注状态
- **自动重试**：失败平台自动重试投注，最多重试3次
- **异步补单**：不阻塞主流程的后台补单机制
- **状态跟踪**：详细记录每次投注的状态和结果

### 3. 风险控制
- **余额验证**：投注前验证两个平台是否有足够余额
- **超时控制**：设置合理的投注超时时间（默认30秒）
- **并发限制**：控制同时进行的对冲投注数量
- **金额限制**：设置最大对冲投注金额上限

## 系统架构

```
hedge_main.py           # 主程序入口
├── hedge_manager.py    # 对冲管理器（核心逻辑）
├── hedge_config.py     # 配置管理
├── hedge_calculator.py # 投注金额计算
├── hedge_monitor.py    # 投注状态监控
├── hedge_integration.py # 系统集成
└── test_hedge_*.py     # 测试文件
```
{
  "enable": false,                              // ✅ 对冲开关
  "base_platform": "crown",                     // ✅ 基准平台
  "base_amount": 50,                            // ✅ 基准金额
  "allocation_method": "fixed",                 // ⚠️ 只支持fixed
  "max_hedge_amount": 500,                      // ✅ 最大金额
  "retry_attempts": 3,                          // ✅ 重试次数
  "retry_delay": 2,                             // ✅ 重试延迟
  "timeout_seconds": 30,                        // ✅ 超时时间
  "min_arbitrage_profit": 0.1,                  // ✅ 最小收益
  "max_concurrent_hedges": 1,                   // ✅ 最大并发
  "enable_notifications": true,                 // ✅ 通知开关
  "duplicate_check_interval": 1,                // ✅ 重复检测间隔
  "enable_cross_platform_duplicate_check": true, // ✅ 跨平台检测
  "execution_lock_timeout": 30,                 // ✅ 锁超时
  "base_first_delay": 1,                        // ✅ 基准延迟
  "execution_mode": "sequential"                // ✅ 执行模式
}

## 配置说明

在 `config.json` 中添加以下配置：

```json
{
  "hedge": {
    "enable": true,                    // 是否启用对冲功能
    "base_platform": "auto",           // 基准平台: auto/pinbet/crown
    "base_amount": 100,                // 基准投注金额
    "allocation_method": "fixed",      // 分配方法: fixed/kelly/balance
    "max_hedge_amount": 500,           // 最大对冲金额
    "retry_attempts": 3,               // 重试次数
    "retry_delay": 2,                  // 重试延迟（秒）
    "timeout_seconds": 30,             // 超时时间（秒）
    "enable_rollback": false,          // 是否启用回滚
    "min_arbitrage_profit": 2.0,       // 最小套利收益率（%）
    "max_concurrent_hedges": 1,        // 最大并发对冲数
    "enable_notifications": true       // 是否启用通知
  }
}
```

## 使用方法

### 1. 启动系统

```bash
# 交互式菜单模式
python hedge_main.py

# 自动监控模式
python hedge_main.py --auto --interval 10
```

### 2. 菜单操作

系统提供以下菜单选项：

1. **显示系统状态** - 查看登录状态和对冲功能可用性
2. **显示账户余额** - 查看两个平台的账户余额
3. **平博单独投注** - 仅在平博平台执行投注
4. **皇冠单独投注** - 仅在皇冠平台执行投注
5. **执行对冲投注** - 手动执行一次对冲投注
6. **启动自动对冲监控** - 持续监控并自动执行对冲
7. **显示对冲配置** - 查看当前配置
8. **更新对冲配置** - 修改配置参数
9. **重新登录系统** - 重新登录两个平台

### 3. 对冲计算示例

以皇冠赔率1.88，平博赔率2.06，基准金额1000为例：

```
基准投注金额: 1000 (皇冠)
对冲投注金额: 912.62 (平博)
总投资: 1912.62
保证收益: 1880.00
净利润: -32.62 (-1.71%)
```

计算公式：`对冲金额 = (基准金额 × 基准赔率) ÷ 对冲赔率`

## 重要注意事项

### 1. 风险提示
- 对冲投注并非无风险，可能存在负收益情况
- 网络延迟可能导致盘口变化，影响对冲效果
- 平台规则变化可能影响投注成功率

### 2. 使用建议
- 首次使用建议设置较小的基准金额进行测试
- 定期检查两个平台的账户余额
- 关注平台的投注限额和规则变化
- 建议在网络稳定的环境下使用

### 3. 故障排除
- 如果登录失败，检查账号密码和网络连接
- 如果投注失败，检查账户余额和盘口状态
- 如果对冲功能不可用，检查配置文件设置

## 技术特性

### 1. 并发投注
- 使用异步编程实现两个平台的并发投注
- 最大化投注速度，减少盘口变化风险

### 2. 异常处理
- 完善的异常处理机制，确保系统稳定性
- 详细的日志记录，便于问题诊断

### 3. 配置灵活性
- 支持运行时配置更新
- 多种投注策略可选
- 丰富的参数调节选项

## 测试和验证

系统提供了完整的测试套件：

```bash
# 运行单元测试
python test_hedge_system.py

# 运行功能演示
python test_hedge_demo.py
```

## 更新日志

### v1.0.0 (当前版本)
- ✅ 基础对冲功能实现
- ✅ 配置管理系统
- ✅ 投注金额计算器
- ✅ 状态监控和补单
- ✅ 系统集成和测试
- ✅ 用户界面和文档

### 计划功能
- 📋 更多投注策略支持
- 📋 历史数据分析
- 📋 收益统计报告
- 📋 Web界面支持

## 技术支持

如遇到问题，请检查：
1. 日志文件中的错误信息
2. 配置文件是否正确
3. 网络连接是否稳定
4. 账户状态是否正常

## 免责声明

本系统仅供学习和研究使用，使用者需要自行承担投注风险。开发者不对任何投注损失承担责任。请在使用前充分了解相关风险，并在法律允许的范围内使用。
