#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置文件迁移工具
用于将现有的明文配置转换为加密配置
"""

import os
import sys
import json
import logging
from typing import Dict, Any

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

from config_security import get_config_security

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ConfigMigration:
    """配置文件迁移工具"""
    
    def __init__(self):
        self.config_path = os.path.join(project_root, 'config', 'config.json')
        self.config_security = get_config_security(self.config_path)
    
    def check_config_status(self) -> Dict[str, Any]:
        """检查配置文件状态"""
        try:
            if not os.path.exists(self.config_path):
                return {
                    "exists": False,
                    "encrypted": False,
                    "valid": False,
                    "message": "配置文件不存在"
                }
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            is_encrypted = config.get('_encrypted', False)
            is_valid = True
            
            if is_encrypted:
                # 验证加密配置的完整性
                is_valid = self.config_security.verify_integrity()
            
            # 统计敏感字段
            sensitive_count = self._count_sensitive_fields(config)
            
            return {
                "exists": True,
                "encrypted": is_encrypted,
                "valid": is_valid,
                "sensitive_fields": sensitive_count,
                "message": "配置文件状态正常" if is_valid else "配置文件可能已损坏"
            }
            
        except Exception as e:
            logger.error(f"检查配置文件状态失败: {e}")
            return {
                "exists": True,
                "encrypted": False,
                "valid": False,
                "message": f"配置文件检查失败: {str(e)}"
            }
    
    def _count_sensitive_fields(self, config: Dict[str, Any], path: str = "") -> int:
        """递归统计敏感字段数量"""
        count = 0
        sensitive_fields = ['password']  # 只统计密码字段

        for key, value in config.items():
            current_path = f"{path}.{key}" if path else key

            if isinstance(value, dict):
                count += self._count_sensitive_fields(value, current_path)
            elif key.lower() in sensitive_fields and isinstance(value, str):
                count += 1

        return count
    
    def migrate_to_encrypted(self) -> bool:
        """将明文配置迁移为加密配置"""
        try:
            status = self.check_config_status()
            
            if not status["exists"]:
                logger.error("配置文件不存在，无法迁移")
                return False
            
            if status["encrypted"]:
                logger.info("配置文件已经是加密格式，无需迁移")
                return True
            
            logger.info(f"开始迁移配置文件，发现 {status['sensitive_fields']} 个敏感字段")
            
            # 执行加密
            success = self.config_security.encrypt_config_file()
            
            if success:
                logger.info("配置文件迁移完成，敏感信息已加密")
                
                # 验证迁移结果
                new_status = self.check_config_status()
                if new_status["encrypted"] and new_status["valid"]:
                    logger.info("迁移验证成功")
                    return True
                else:
                    logger.error("迁移验证失败")
                    return False
            else:
                logger.error("配置文件迁移失败")
                return False
                
        except Exception as e:
            logger.error(f"迁移过程中发生错误: {e}")
            return False
    
    def migrate_to_plaintext(self) -> bool:
        """将加密配置迁移为明文配置（仅用于调试）"""
        try:
            status = self.check_config_status()
            
            if not status["exists"]:
                logger.error("配置文件不存在，无法迁移")
                return False
            
            if not status["encrypted"]:
                logger.info("配置文件已经是明文格式，无需迁移")
                return True
            
            logger.warning("开始将加密配置转换为明文格式（不推荐用于生产环境）")
            
            # 加载并解密配置
            config = self.config_security.load_config()
            if config is None:
                logger.error("无法加载加密配置")
                return False
            
            # 创建备份
            self.config_security.create_backup()
            
            # 保存为明文格式
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info("配置文件已转换为明文格式")
            return True
            
        except Exception as e:
            logger.error(f"转换过程中发生错误: {e}")
            # 尝试恢复备份
            self.config_security.restore_backup()
            return False
    
    def verify_migration(self) -> bool:
        """验证迁移结果"""
        try:
            status = self.check_config_status()
            
            if not status["exists"]:
                logger.error("配置文件不存在")
                return False
            
            if not status["encrypted"]:
                logger.warning("配置文件未加密")
                return False
            
            if not status["valid"]:
                logger.error("配置文件完整性验证失败")
                return False
            
            # 尝试加载配置
            config = self.config_security.load_config()
            if config is None:
                logger.error("无法加载配置文件")
                return False
            
            logger.info("配置文件验证通过")
            return True
            
        except Exception as e:
            logger.error(f"验证过程中发生错误: {e}")
            return False
    
    def show_status(self):
        """显示配置文件状态"""
        status = self.check_config_status()
        
        print("=" * 50)
        print("配置文件状态报告")
        print("=" * 50)
        print(f"文件路径: {self.config_path}")
        print(f"文件存在: {'是' if status['exists'] else '否'}")
        
        if status['exists']:
            print(f"加密状态: {'已加密' if status['encrypted'] else '明文'}")
            print(f"文件有效: {'是' if status['valid'] else '否'}")
            print(f"敏感字段: {status.get('sensitive_fields', 0)} 个")
            print(f"状态信息: {status['message']}")
        
        print("=" * 50)


def main():
    """主函数"""
    migration = ConfigMigration()
    
    if len(sys.argv) < 2:
        print("配置文件迁移工具")
        print("用法:")
        print("  python config_migration.py status    - 显示配置文件状态")
        print("  python config_migration.py encrypt   - 迁移到加密格式")
        print("  python config_migration.py decrypt   - 迁移到明文格式（调试用）")
        print("  python config_migration.py verify    - 验证配置文件")
        return
    
    command = sys.argv[1].lower()
    
    if command == "status":
        migration.show_status()
    
    elif command == "encrypt":
        print("开始迁移配置文件到加密格式...")
        if migration.migrate_to_encrypted():
            print("✅ 迁移成功！配置文件已加密")
        else:
            print("❌ 迁移失败！请检查日志")
    
    elif command == "decrypt":
        print("⚠️  警告：即将将加密配置转换为明文格式")
        confirm = input("确认继续？(y/N): ")
        if confirm.lower() == 'y':
            if migration.migrate_to_plaintext():
                print("✅ 转换成功！配置文件已转为明文")
            else:
                print("❌ 转换失败！请检查日志")
        else:
            print("操作已取消")
    
    elif command == "verify":
        print("验证配置文件...")
        if migration.verify_migration():
            print("✅ 配置文件验证通过")
        else:
            print("❌ 配置文件验证失败")
    
    else:
        print(f"未知命令: {command}")


if __name__ == "__main__":
    main()
