#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from utils.json_data_manager import get_data_manager

# 设置日志
logger = logging.getLogger(__name__)

class HedgeRecordManager:
    """对冲记录管理器"""
    
    def __init__(self, data_dir: str = "data"):
        """
        初始化对冲记录管理器
        
        参数:
            data_dir: 数据目录
        """
        self.data_manager = get_data_manager(data_dir)
        self.records_file = "hedge_records"
        logger.info("对冲记录管理器初始化完成")
    
    def save_hedge_log(self, log_data: Dict) -> bool:
        """
        保存对冲记录
        
        参数:
            log_data: 对冲记录数据
            
        返回:
            是否保存成功
        """
        try:
            # 验证必要字段
            if not log_data.get("hedge_id"):
                logger.error("对冲记录缺少hedge_id字段")
                return False
            
            # 添加时间戳
            current_time = datetime.now().isoformat()
            log_data["created_at"] = current_time
            log_data["updated_at"] = current_time
            
            # 获取今天的日期字符串用于分片存储
            date_str = datetime.now().strftime("%Y-%m-%d")
            
            # 加载现有记录
            existing_records = self.data_manager.load_data(self.records_file, date_str, [])
            
            # 检查是否已存在相同的hedge_id
            hedge_id = log_data["hedge_id"]
            for i, record in enumerate(existing_records):
                if record.get("hedge_id") == hedge_id:
                    # 更新现有记录
                    log_data["updated_at"] = current_time
                    existing_records[i] = log_data
                    logger.info(f"更新现有对冲记录: {hedge_id}")
                    break
            else:
                # 添加新记录
                existing_records.append(log_data)
                logger.info(f"添加新对冲记录: {hedge_id}")
            
            # 保存记录
            success = self.data_manager.save_data(self.records_file, existing_records, date_str)
            
            if success:
                logger.info(f"对冲记录保存成功: {hedge_id}")
                return True
            else:
                logger.error(f"对冲记录保存失败: {hedge_id}")
                return False
                
        except Exception as e:
            logger.error(f"保存对冲记录时出错: {e}", exc_info=True)
            return False
    
    def get_hedge_records(self, date_str: Optional[str] = None, hours: int = 24) -> List[Dict]:
        """
        获取对冲记录
        
        参数:
            date_str: 日期字符串，格式为YYYY-MM-DD，None表示今天
            hours: 获取最近多少小时的记录
            
        返回:
            对冲记录列表
        """
        try:
            if date_str is None:
                date_str = datetime.now().strftime("%Y-%m-%d")
            
            records = self.data_manager.load_data(self.records_file, date_str, [])
            
            # 如果指定了小时数，过滤记录
            if hours < 24:
                cutoff_time = datetime.now().timestamp() - (hours * 3600)
                filtered_records = []
                
                for record in records:
                    try:
                        record_time = datetime.fromisoformat(record.get("created_at", "")).timestamp()
                        if record_time >= cutoff_time:
                            filtered_records.append(record)
                    except (ValueError, TypeError):
                        # 如果时间解析失败，保留记录
                        filtered_records.append(record)
                
                return filtered_records
            
            return records
            
        except Exception as e:
            logger.error(f"获取对冲记录失败: {e}")
            return []
    
    def get_hedge_record_by_id(self, hedge_id: str, date_str: Optional[str] = None) -> Optional[Dict]:
        """
        根据ID获取对冲记录
        
        参数:
            hedge_id: 对冲ID
            date_str: 日期字符串，None表示搜索最近几天
            
        返回:
            对冲记录或None
        """
        try:
            # 如果没有指定日期，搜索最近7天
            if date_str is None:
                from datetime import timedelta
                for i in range(7):
                    search_date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                    records = self.data_manager.load_data(self.records_file, search_date, [])
                    
                    for record in records:
                        if record.get("hedge_id") == hedge_id:
                            return record
                return None
            else:
                records = self.data_manager.load_data(self.records_file, date_str, [])
                for record in records:
                    if record.get("hedge_id") == hedge_id:
                        return record
                return None
                
        except Exception as e:
            logger.error(f"根据ID获取对冲记录失败: {e}")
            return None
    
    def delete_hedge_record(self, hedge_id: str, date_str: Optional[str] = None) -> bool:
        """
        删除对冲记录
        
        参数:
            hedge_id: 对冲ID
            date_str: 日期字符串，None表示搜索最近几天
            
        返回:
            是否删除成功
        """
        try:
            # 如果没有指定日期，搜索最近7天
            if date_str is None:
                from datetime import timedelta
                for i in range(7):
                    search_date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                    records = self.data_manager.load_data(self.records_file, search_date, [])
                    
                    for j, record in enumerate(records):
                        if record.get("hedge_id") == hedge_id:
                            del records[j]
                            success = self.data_manager.save_data(self.records_file, records, search_date)
                            if success:
                                logger.info(f"删除对冲记录成功: {hedge_id}")
                            return success
                return False
            else:
                records = self.data_manager.load_data(self.records_file, date_str, [])
                for i, record in enumerate(records):
                    if record.get("hedge_id") == hedge_id:
                        del records[i]
                        success = self.data_manager.save_data(self.records_file, records, date_str)
                        if success:
                            logger.info(f"删除对冲记录成功: {hedge_id}")
                        return success
                return False
                
        except Exception as e:
            logger.error(f"删除对冲记录失败: {e}")
            return False
    
    def get_partial_success_records(self, days: int = 7) -> List[Dict]:
        """
        获取单边待补单记录（部分成功的对冲记录）

        参数:
            days: 查询天数

        返回:
            单边待补单记录列表
        """
        try:
            from datetime import timedelta

            partial_records = []

            for i in range(days):
                date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                records = self.data_manager.load_data(self.records_file, date, [])

                for record in records:
                    status = record.get("status", "")
                    if status == "partial_success":
                        # 添加额外信息用于显示
                        record["date"] = date
                        record["risk_type"] = self._analyze_risk_type(record)
                        partial_records.append(record)

            # 按时间倒序排列
            partial_records.sort(key=lambda x: x.get("created_at", ""), reverse=True)
            return partial_records

        except Exception as e:
            logger.error(f"获取单边待补单记录失败: {e}")
            return []

    def _analyze_risk_type(self, record: Dict) -> str:
        """
        分析单边风险类型

        参数:
            record: 对冲记录

        返回:
            风险类型描述
        """
        try:
            pinbet_bet = record.get("pinbet_bet", {})
            crown_bet = record.get("crown_bet", {})

            pinbet_success = pinbet_bet.get("success", False)
            crown_success = crown_bet.get("success", False)

            if pinbet_success and not crown_success:
                return "平博成功，皇冠失败"
            elif crown_success and not pinbet_success:
                return "皇冠成功，平博失败"
            else:
                return "状态不明"

        except Exception as e:
            logger.error(f"分析风险类型失败: {e}")
            return "分析失败"

    def update_partial_record_status(self, hedge_id: str, new_status: str,
                                   补单_result: Optional[Dict] = None) -> bool:
        """
        更新单边待补单记录状态

        参数:
            hedge_id: 对冲ID
            new_status: 新状态 (completed, failed, retry_pending)
            补单_result: 补单结果

        返回:
            是否更新成功
        """
        try:
            # 查找记录
            record = self.get_hedge_record_by_id(hedge_id)
            if not record:
                logger.error(f"未找到对冲记录: {hedge_id}")
                return False

            # 更新状态
            record["status"] = new_status
            record["updated_at"] = datetime.now().isoformat()

            # 如果有补单结果，更新相关信息
            if 补单_result:
                record["补单_result"] = 补单_result
                record["补单_time"] = datetime.now().isoformat()

            # 保存更新
            date_str = datetime.fromisoformat(record["created_at"]).strftime("%Y-%m-%d")
            records = self.data_manager.load_data(self.records_file, date_str, [])

            for i, existing_record in enumerate(records):
                if existing_record.get("hedge_id") == hedge_id:
                    records[i] = record
                    break

            success = self.data_manager.save_data(self.records_file, records, date_str)
            if success:
                logger.info(f"更新单边待补单记录状态成功: {hedge_id} -> {new_status}")

            return success

        except Exception as e:
            logger.error(f"更新单边待补单记录状态失败: {e}")
            return False

    def get_statistics(self, days: int = 7) -> Dict:
        """
        获取对冲记录统计信息

        参数:
            days: 统计天数

        返回:
            统计信息字典
        """
        try:
            from datetime import timedelta

            total_records = 0
            completed_records = 0
            failed_records = 0
            partial_records = 0
            total_amount = 0.0
            total_profit = 0.0

            for i in range(days):
                date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                records = self.data_manager.load_data(self.records_file, date, [])

                for record in records:
                    total_records += 1
                    status = record.get("status", "")

                    if status == "completed":
                        completed_records += 1
                    elif status in ["failed", "error"]:
                        failed_records += 1
                    elif status == "partial_success":
                        partial_records += 1

                    total_amount += record.get("total_amount", 0)
                    total_profit += record.get("actual_profit", 0)

            return {
                "total_records": total_records,
                "completed_records": completed_records,
                "failed_records": failed_records,
                "partial_records": partial_records,
                "success_rate": (completed_records / total_records * 100) if total_records > 0 else 0,
                "partial_rate": (partial_records / total_records * 100) if total_records > 0 else 0,
                "total_amount": total_amount,
                "total_profit": total_profit,
                "average_profit": (total_profit / completed_records) if completed_records > 0 else 0,
                "days": days
            }

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
