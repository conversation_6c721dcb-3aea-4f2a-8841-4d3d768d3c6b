#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import logging
import shutil
from datetime import datetime
from typing import Dict, List, Optional, Set

logger = logging.getLogger(__name__)

class AliasManager:
    """别名管理器 - 负责自动生成和保存队伍别名"""
    
    def __init__(self, config_dir: str = "config", data_dir: str = "data"):
        """
        初始化别名管理器
        
        参数:
            config_dir: 配置文件目录
            data_dir: 数据目录
        """
        self.config_dir = config_dir
        self.data_dir = data_dir
        
        # 配置文件路径
        self.team_aliases_file = os.path.join(config_dir, "team_aliases.json")
        self.backup_dir = os.path.join(data_dir, "alias_backups")
        
        # 确保目录存在
        os.makedirs(config_dir, exist_ok=True)
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # 加载现有别名
        self.current_aliases = self._load_current_aliases()
        
        logger.info(f"别名管理器初始化完成，配置目录: {config_dir}")
    
    def _load_current_aliases(self) -> Dict:
        """加载当前的别名配置"""
        try:
            if os.path.exists(self.team_aliases_file):
                with open(self.team_aliases_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get("team_aliases", {})
            else:
                logger.info("team_aliases.json文件不存在，将创建新文件")
                return {}
        except Exception as e:
            logger.error(f"加载当前别名配置失败: {e}")
            return {}
    
    def merge_learned_aliases(self, learned_aliases: Dict, 
                            confidence_threshold: float = 0.7) -> Dict:
        """
        合并学习到的别名到现有配置
        
        参数:
            learned_aliases: 学习到的别名字典
            confidence_threshold: 置信度阈值
            
        返回:
            合并结果统计
        """
        try:
            logger.info("开始合并学习到的别名")
            
            merge_stats = {
                "new_leagues": 0,
                "new_teams": 0,
                "new_aliases": 0,
                "skipped_duplicates": 0,
                "total_processed": 0
            }
            
            for league, teams in learned_aliases.items():
                if not league:
                    continue
                
                # 确保联赛存在
                if league not in self.current_aliases:
                    self.current_aliases[league] = {}
                    merge_stats["new_leagues"] += 1
                    logger.info(f"添加新联赛: {league}")
                
                for team, aliases in teams.items():
                    if not team or not aliases:
                        continue
                    
                    merge_stats["total_processed"] += 1
                    
                    # 确保队伍存在
                    if team not in self.current_aliases[league]:
                        self.current_aliases[league][team] = []
                        merge_stats["new_teams"] += 1
                        logger.info(f"添加新队伍: {league} - {team}")
                    
                    # 添加新别名
                    existing_aliases = set(self.current_aliases[league][team])
                    
                    for alias in aliases:
                        if alias and alias not in existing_aliases:
                            # 检查别名质量
                            if self._is_valid_alias(team, alias):
                                self.current_aliases[league][team].append(alias)
                                merge_stats["new_aliases"] += 1
                                logger.info(f"添加新别名: {team} -> {alias}")
                            else:
                                logger.debug(f"跳过低质量别名: {team} -> {alias}")
                        else:
                            merge_stats["skipped_duplicates"] += 1
            
            logger.info(f"别名合并完成: {merge_stats}")
            return merge_stats
            
        except Exception as e:
            logger.error(f"合并学习别名失败: {e}")
            return {}
    
    def _is_valid_alias(self, original_team: str, alias: str) -> bool:
        """
        验证别名是否有效
        
        参数:
            original_team: 原始队伍名
            alias: 别名
            
        返回:
            是否有效
        """
        try:
            # 基本检查
            if not alias or len(alias.strip()) < 2:
                return False
            
            alias = alias.strip()
            original_team = original_team.strip()
            
            # 不能与原名完全相同
            if alias.lower() == original_team.lower():
                return False
            
            # 长度检查
            if len(alias) > 50:  # 别名不应该太长
                return False
            
            # 检查是否包含有意义的字符
            if alias.isdigit():  # 纯数字不是好别名
                return False
            
            # 检查特殊字符比例
            special_chars = sum(1 for c in alias if not c.isalnum() and c != ' ')
            if special_chars / len(alias) > 0.5:  # 特殊字符不应超过50%
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证别名时出错: {e}")
            return False
    
    def save_aliases(self, create_backup: bool = True) -> bool:
        """
        保存别名配置到文件
        
        参数:
            create_backup: 是否创建备份
            
        返回:
            是否保存成功
        """
        try:
            # 创建备份
            if create_backup and os.path.exists(self.team_aliases_file):
                self._create_backup()
            
            # 构建完整配置
            full_config = {
                "team_aliases": self.current_aliases,
                "matching_config": self._get_default_matching_config(),
                "metadata": {
                    "last_updated": datetime.now().isoformat(),
                    "total_leagues": len(self.current_aliases),
                    "total_teams": sum(len(teams) for teams in self.current_aliases.values()),
                    "auto_generated": True
                }
            }
            
            # 保存到文件
            with open(self.team_aliases_file, 'w', encoding='utf-8') as f:
                json.dump(full_config, f, ensure_ascii=False, indent=2)
            
            logger.info(f"别名配置已保存到: {self.team_aliases_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存别名配置失败: {e}")
            return False
    
    def _create_backup(self):
        """创建配置文件备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(
                self.backup_dir, 
                f"team_aliases_backup_{timestamp}.json"
            )
            
            shutil.copy2(self.team_aliases_file, backup_file)
            logger.info(f"创建备份文件: {backup_file}")
            
            # 清理旧备份（保留最近10个）
            self._cleanup_old_backups()
            
        except Exception as e:
            logger.error(f"创建备份失败: {e}")
    
    def _cleanup_old_backups(self, keep_count: int = 10):
        """清理旧备份文件"""
        try:
            backup_files = []
            for file in os.listdir(self.backup_dir):
                if file.startswith("team_aliases_backup_") and file.endswith(".json"):
                    file_path = os.path.join(self.backup_dir, file)
                    backup_files.append((file_path, os.path.getmtime(file_path)))
            
            # 按修改时间排序
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # 删除多余的备份
            for file_path, _ in backup_files[keep_count:]:
                os.remove(file_path)
                logger.debug(f"删除旧备份: {file_path}")
                
        except Exception as e:
            logger.error(f"清理旧备份失败: {e}")
    
    def _get_default_matching_config(self) -> Dict:
        """获取默认匹配配置"""
        return {
            "similarity_threshold": 0.6,
            "min_char_length": 5,
            "enable_fuzzy_matching": True,
            "case_sensitive": False,
            "ignore_patterns": [
                "\\(.*\\)",  # 括号内容
                "\\[.*\\]",  # 方括号内容
                "\\s+",      # 多余空格
            ]
        }
    
    def add_manual_alias(self, league: str, team: str, alias: str) -> bool:
        """
        手动添加别名
        
        参数:
            league: 联赛名
            team: 队伍名
            alias: 别名
            
        返回:
            是否添加成功
        """
        try:
            if not self._is_valid_alias(team, alias):
                logger.warning(f"无效别名: {team} -> {alias}")
                return False
            
            # 确保结构存在
            if league not in self.current_aliases:
                self.current_aliases[league] = {}
            
            if team not in self.current_aliases[league]:
                self.current_aliases[league][team] = []
            
            # 检查是否已存在
            if alias in self.current_aliases[league][team]:
                logger.info(f"别名已存在: {team} -> {alias}")
                return True
            
            # 添加别名
            self.current_aliases[league][team].append(alias)
            logger.info(f"手动添加别名: {league} - {team} -> {alias}")
            
            return True
            
        except Exception as e:
            logger.error(f"手动添加别名失败: {e}")
            return False
    
    def remove_alias(self, league: str, team: str, alias: str) -> bool:
        """
        移除别名
        
        参数:
            league: 联赛名
            team: 队伍名
            alias: 别名
            
        返回:
            是否移除成功
        """
        try:
            if (league in self.current_aliases and 
                team in self.current_aliases[league] and
                alias in self.current_aliases[league][team]):
                
                self.current_aliases[league][team].remove(alias)
                logger.info(f"移除别名: {league} - {team} -> {alias}")
                
                # 如果队伍没有别名了，可以选择保留空列表或删除
                if not self.current_aliases[league][team]:
                    # 保留空列表，便于后续添加
                    pass
                
                return True
            else:
                logger.warning(f"别名不存在: {league} - {team} -> {alias}")
                return False
                
        except Exception as e:
            logger.error(f"移除别名失败: {e}")
            return False
    
    def get_aliases_summary(self) -> Dict:
        """获取别名摘要信息"""
        try:
            summary = {
                "total_leagues": len(self.current_aliases),
                "total_teams": 0,
                "total_aliases": 0,
                "leagues": {}
            }
            
            for league, teams in self.current_aliases.items():
                league_info = {
                    "team_count": len(teams),
                    "alias_count": 0,
                    "teams": {}
                }
                
                for team, aliases in teams.items():
                    alias_count = len(aliases)
                    league_info["alias_count"] += alias_count
                    league_info["teams"][team] = alias_count
                
                summary["leagues"][league] = league_info
                summary["total_teams"] += league_info["team_count"]
                summary["total_aliases"] += league_info["alias_count"]
            
            return summary
            
        except Exception as e:
            logger.error(f"获取别名摘要失败: {e}")
            return {}
    
    def validate_aliases(self) -> Dict:
        """验证所有别名的有效性"""
        try:
            validation_result = {
                "valid_count": 0,
                "invalid_count": 0,
                "invalid_aliases": [],
                "suggestions": []
            }
            
            for league, teams in self.current_aliases.items():
                for team, aliases in teams.items():
                    for alias in aliases:
                        if self._is_valid_alias(team, alias):
                            validation_result["valid_count"] += 1
                        else:
                            validation_result["invalid_count"] += 1
                            validation_result["invalid_aliases"].append({
                                "league": league,
                                "team": team,
                                "alias": alias
                            })
            
            return validation_result
            
        except Exception as e:
            logger.error(f"验证别名失败: {e}")
            return {}
    
    def export_aliases(self, export_path: str) -> bool:
        """
        导出别名配置
        
        参数:
            export_path: 导出文件路径
            
        返回:
            是否导出成功
        """
        try:
            export_data = {
                "export_time": datetime.now().isoformat(),
                "source_file": self.team_aliases_file,
                "aliases": self.current_aliases,
                "summary": self.get_aliases_summary()
            }
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"别名配置已导出到: {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出别名配置失败: {e}")
            return False
