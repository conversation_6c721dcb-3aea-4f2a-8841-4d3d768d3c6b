#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试XHR过滤功能
"""

import json
import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def create_test_har():
    """创建测试HAR文件"""
    test_har = {
        "log": {
            "version": "1.2",
            "creator": {
                "name": "Test",
                "version": "1.0"
            },
            "entries": [
                {
                    "startedDateTime": "2025-01-01T00:00:00.000Z",
                    "time": 100,
                    "request": {
                        "method": "GET",
                        "url": "https://example.com/api/sports/events",
                        "headers": []
                    },
                    "response": {
                        "status": 200,
                        "statusText": "OK",
                        "headers": [],
                        "content": {
                            "size": 1000,
                            "mimeType": "application/json",
                            "text": '{"n": [["league1", "League 1", [["match1", "Team A", "Team B"]]]]}'
                        }
                    },
                    "_resourceType": "xhr"
                },
                {
                    "startedDateTime": "2025-01-01T00:00:01.000Z",
                    "time": 50,
                    "request": {
                        "method": "GET",
                        "url": "https://example.com/style.css",
                        "headers": []
                    },
                    "response": {
                        "status": 200,
                        "statusText": "OK",
                        "headers": [],
                        "content": {
                            "size": 500,
                            "mimeType": "text/css",
                            "text": "body { margin: 0; }"
                        }
                    },
                    "_resourceType": "stylesheet"
                },
                {
                    "startedDateTime": "2025-01-01T00:00:02.000Z",
                    "time": 200,
                    "request": {
                        "method": "POST",
                        "url": "https://example.com/api/bet",
                        "headers": []
                    },
                    "response": {
                        "status": 200,
                        "statusText": "OK",
                        "headers": [],
                        "content": {
                            "size": 300,
                            "mimeType": "application/json",
                            "text": '{"success": true}'
                        }
                    },
                    "_resourceType": "xhr"
                },
                {
                    "startedDateTime": "2025-01-01T00:00:03.000Z",
                    "time": 30,
                    "request": {
                        "method": "GET",
                        "url": "https://example.com/image.png",
                        "headers": []
                    },
                    "response": {
                        "status": 200,
                        "statusText": "OK",
                        "headers": [],
                        "content": {
                            "size": 2000,
                            "mimeType": "image/png"
                        }
                    },
                    "_resourceType": "image"
                },
                {
                    "startedDateTime": "2025-01-01T00:00:04.000Z",
                    "time": 150,
                    "request": {
                        "method": "GET",
                        "url": "https://example.com/api/balance",
                        "headers": []
                    },
                    "response": {
                        "status": 200,
                        "statusText": "OK",
                        "headers": [],
                        "content": {
                            "size": 100,
                            "mimeType": "application/json",
                            "text": '{"balance": 1000}'
                        }
                    },
                    "_resourceType": "fetch"
                }
            ]
        }
    }
    
    test_file = "test_data.har"
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_har, f, indent=2)
    
    return test_file

def test_har_simplifier():
    """测试HAR简化工具"""
    print("=== 测试HAR简化工具 ===")
    
    # 创建测试文件
    test_file = create_test_har()
    
    try:
        from tools.har_simplifier import HARSimplifier
        
        # 加载原始数据
        with open(test_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        original_entries = original_data['log']['entries']
        print(f"原始请求数: {len(original_entries)}")
        
        for i, entry in enumerate(original_entries):
            resource_type = entry.get('_resourceType', 'unknown')
            url = entry['request']['url']
            print(f"  {i+1}. {resource_type}: {url}")
        
        # 使用简化工具
        simplifier = HARSimplifier()
        simplified_data = simplifier.simplify_har(original_data)
        
        simplified_entries = simplified_data['log']['entries']
        print(f"\n简化后请求数: {len(simplified_entries)}")
        
        for i, entry in enumerate(simplified_entries):
            resource_type = entry.get('_resourceType', 'unknown')
            url = entry['request']['url']
            print(f"  {i+1}. {resource_type}: {url}")
        
        # 验证结果
        xhr_count = sum(1 for entry in simplified_entries if entry.get('_resourceType') == 'xhr')
        print(f"\nXHR请求数: {xhr_count}")
        
        if xhr_count == len(simplified_entries):
            print("✅ 测试通过：只保留了XHR请求")
        else:
            print("❌ 测试失败：包含了非XHR请求")
        
        # 保存简化后的文件
        output_file = "test_data_simplified.har"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(simplified_data, f, indent=2)
        
        print(f"\n简化后的文件已保存: {output_file}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
        if os.path.exists("test_data_simplified.har"):
            os.remove("test_data_simplified.har")

def test_match_analyzer():
    """测试比赛数据分析工具"""
    print("\n=== 测试比赛数据分析工具 ===")
    
    # 创建包含比赛数据的测试文件
    test_har = {
        "log": {
            "version": "1.2",
            "creator": {"name": "Test", "version": "1.0"},
            "entries": [
                {
                    "startedDateTime": "2025-01-01T00:00:00.000Z",
                    "time": 100,
                    "request": {
                        "method": "GET",
                        "url": "https://example.com/api/sports/events",
                        "headers": []
                    },
                    "response": {
                        "status": 200,
                        "statusText": "OK",
                        "headers": [],
                        "content": {
                            "size": 1000,
                            "mimeType": "application/json",
                            "text": json.dumps({
                                "n": [
                                    [1, "NBA", [
                                        [101, "Lakers", "Warriors", "2025-01-01 20:00"],
                                        [102, "Celtics", "Heat", "2025-01-01 21:00"],
                                        [103, "Bulls", "Knicks", "2025-01-01 22:00"]
                                    ]],
                                    [2, "WNBA", [
                                        [201, "Storm", "Mercury", "2025-01-02 19:00"],
                                        [202, "Lynx", "Sun", "2025-01-02 20:00"]
                                    ]]
                                ]
                            })
                        }
                    },
                    "_resourceType": "xhr"
                },
                {
                    "startedDateTime": "2025-01-01T00:00:01.000Z",
                    "time": 50,
                    "request": {
                        "method": "GET",
                        "url": "https://example.com/style.css",
                        "headers": []
                    },
                    "response": {
                        "status": 200,
                        "statusText": "OK",
                        "headers": [],
                        "content": {
                            "size": 500,
                            "mimeType": "text/css",
                            "text": "body { margin: 0; }"
                        }
                    },
                    "_resourceType": "stylesheet"
                }
            ]
        }
    }
    
    test_file = "test_match_data.har"
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_har, f, indent=2)
    
    try:
        from tools.har_match_analyzer import HARMatchAnalyzer
        
        analyzer = HARMatchAnalyzer(test_file)
        
        # 查找比赛响应
        match_responses = analyzer.find_match_responses()
        print(f"找到比赛响应数: {len(match_responses)}")
        
        if len(match_responses) == 1:
            print("✅ 测试通过：正确识别了XHR比赛数据响应")
            
            # 测试路径提取
            result = analyzer.extract_path_data("n-0-2")
            if result:
                print("✅ 路径提取测试通过")
                print(f"提取的数据: {result['data']}")
            else:
                print("❌ 路径提取测试失败")
        else:
            print("❌ 测试失败：比赛响应识别错误")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)

def main():
    """主函数"""
    print("开始测试XHR过滤功能...")
    
    test_har_simplifier()
    test_match_analyzer()
    
    print("\n测试完成！")

if __name__ == '__main__':
    main()
