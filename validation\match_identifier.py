#!/usr/bin/env python
# -*- coding: utf-8 -*-

import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json

from utils.advanced_similarity import similarity_calculator

logger = logging.getLogger(__name__)

class MatchIdentifier:
    """比赛唯一标识生成器"""
    
    def __init__(self):
        """初始化标识生成器"""
        self.match_cache = {}  # 缓存已生成的比赛标识
        self.cache_expiry = timedelta(hours=24)  # 缓存过期时间
        logger.info("比赛标识生成器初始化完成")
    
    def generate_match_id(self, match_info: Dict) -> str:
        """
        生成比赛唯一标识
        
        参数:
            match_info: 比赛信息字典
        
        返回:
            比赛唯一标识字符串
        """
        try:
            # 提取关键信息
            home_team = self._normalize_team_name(match_info.get("home_team", ""))
            away_team = self._normalize_team_name(match_info.get("away_team", ""))
            league = self._normalize_league_name(match_info.get("league", ""))
            match_time = match_info.get("match_time")
            
            # 确保主客队顺序一致（按字母顺序排序）
            if home_team > away_team:
                home_team, away_team = away_team, home_team
            
            # 构建标识字符串
            id_components = [
                home_team,
                away_team,
                league,
                self._format_match_time(match_time)
            ]
            
            # 过滤空值
            id_components = [comp for comp in id_components if comp]
            
            # 生成哈希
            id_string = "|".join(id_components)
            match_id = hashlib.md5(id_string.encode('utf-8')).hexdigest()[:12]
            
            logger.debug(f"生成比赛ID: {match_id} <- {id_string}")
            return match_id
            
        except Exception as e:
            logger.error(f"生成比赛ID失败: {e}")
            return ""
    
    def generate_market_id(self, match_info: Dict, market_type: str, market_param: str = "") -> str:
        """
        生成盘口唯一标识
        
        参数:
            match_info: 比赛信息
            market_type: 盘口类型
            market_param: 盘口参数
        
        返回:
            盘口唯一标识
        """
        try:
            match_id = self.generate_match_id(match_info)
            
            # 标准化盘口信息
            market_type_norm = self._normalize_market_type(market_type)
            market_param_norm = self._normalize_market_param(market_param)
            
            # 构建盘口标识
            market_components = [
                match_id,
                market_type_norm,
                market_param_norm
            ]
            
            # 过滤空值
            market_components = [comp for comp in market_components if comp]
            
            # 生成哈希
            market_string = "|".join(market_components)
            market_id = hashlib.md5(market_string.encode('utf-8')).hexdigest()[:16]
            
            logger.debug(f"生成盘口ID: {market_id} <- {market_string}")
            return market_id
            
        except Exception as e:
            logger.error(f"生成盘口ID失败: {e}")
            return ""
    
    def find_matching_matches(self, target_match: Dict, candidate_matches: List[Dict], 
                            threshold: float = 0.8) -> List[Tuple[Dict, float]]:
        """
        在候选比赛列表中查找匹配的比赛
        
        参数:
            target_match: 目标比赛信息
            candidate_matches: 候选比赛列表
            threshold: 匹配阈值
        
        返回:
            匹配的比赛列表，包含匹配分数
        """
        try:
            target_id = self.generate_match_id(target_match)
            if not target_id:
                return []
            
            matches = []
            
            for candidate in candidate_matches:
                candidate_id = self.generate_match_id(candidate)
                if not candidate_id:
                    continue
                
                # 计算匹配分数
                score = self._calculate_match_similarity(target_match, candidate)
                
                if score >= threshold:
                    matches.append((candidate, score))
            
            # 按分数降序排序
            matches.sort(key=lambda x: x[1], reverse=True)
            
            logger.info(f"找到 {len(matches)} 个匹配的比赛（阈值: {threshold}）")
            return matches
            
        except Exception as e:
            logger.error(f"查找匹配比赛失败: {e}")
            return []
    
    def is_same_match(self, match1: Dict, match2: Dict) -> bool:
        """
        判断两个比赛是否为同一场比赛
        
        参数:
            match1: 比赛1信息
            match2: 比赛2信息
        
        返回:
            是否为同一场比赛
        """
        try:
            id1 = self.generate_match_id(match1)
            id2 = self.generate_match_id(match2)
            
            if not id1 or not id2:
                return False
            
            # 如果ID完全相同，则为同一场比赛
            if id1 == id2:
                return True
            
            # 如果ID不同，进一步检查相似度
            similarity = self._calculate_match_similarity(match1, match2)
            return similarity >= 0.9  # 高相似度阈值
            
        except Exception as e:
            logger.error(f"比较比赛失败: {e}")
            return False
    
    def _normalize_team_name(self, team_name: str) -> str:
        """标准化队伍名称"""
        if not team_name:
            return ""
        
        # 转换为小写，去除空格和特殊字符
        normalized = team_name.lower().strip()
        
        # 移除常见的后缀
        suffixes_to_remove = ["队", "俱乐部", "club", "fc", "basketball", "篮球"]
        for suffix in suffixes_to_remove:
            if normalized.endswith(suffix):
                normalized = normalized[:-len(suffix)].strip()
        
        # 移除常见的前缀
        prefixes_to_remove = ["basketball", "篮球"]
        for prefix in prefixes_to_remove:
            if normalized.startswith(prefix):
                normalized = normalized[len(prefix):].strip()
        
        return normalized
    
    def _normalize_league_name(self, league_name: str) -> str:
        """标准化联赛名称"""
        if not league_name:
            return ""
        
        # 转换为小写并去除空格
        normalized = league_name.lower().strip()
        
        # 标准化常见联赛名称
        league_mappings = {
            "nba": ["美国职业篮球联赛", "national basketball association"],
            "cba": ["中国男子篮球职业联赛", "chinese basketball association"],
            "euroleague": ["欧洲篮球联赛", "european basketball league"],
        }
        
        for standard_name, variants in league_mappings.items():
            if normalized in variants or any(variant in normalized for variant in variants):
                return standard_name
        
        return normalized
    
    def _normalize_market_type(self, market_type: str) -> str:
        """标准化盘口类型"""
        if not market_type:
            return ""
        
        market_type_lower = market_type.lower()
        
        # 标准化盘口类型
        if any(keyword in market_type_lower for keyword in ["让球", "让分", "handicap", "spread"]):
            return "handicap"
        elif any(keyword in market_type_lower for keyword in ["大小球", "总分", "total", "over", "under"]):
            return "total"
        elif any(keyword in market_type_lower for keyword in ["独赢", "胜负", "moneyline", "1x2"]):
            return "moneyline"
        
        return market_type_lower
    
    def _normalize_market_param(self, market_param: str) -> str:
        """标准化盘口参数"""
        if not market_param:
            return ""
        
        try:
            # 尝试转换为浮点数并格式化
            param_value = float(market_param)
            return f"{param_value:.1f}"
        except:
            return market_param.strip()
    
    def _format_match_time(self, match_time) -> str:
        """格式化比赛时间"""
        if not match_time:
            return ""
        
        try:
            if isinstance(match_time, datetime):
                # 只保留日期，忽略具体时间（允许一定的时间误差）
                return match_time.strftime("%Y-%m-%d")
            elif isinstance(match_time, (int, float)):
                # 时间戳转换
                dt = datetime.fromtimestamp(match_time)
                return dt.strftime("%Y-%m-%d")
            else:
                # 使用灵活的时间解析
                dt = self._parse_time_flexible(match_time)
                if dt:
                    return dt.strftime("%Y-%m-%d")
                else:
                    return ""
        except:
            logger.debug(f"无法格式化比赛时间: {match_time}")
            return ""
    
    def _calculate_match_similarity(self, match1: Dict, match2: Dict) -> float:
        """计算比赛相似度"""
        try:
            # 队伍名称相似度
            home1 = self._normalize_team_name(match1.get("home_team", ""))
            away1 = self._normalize_team_name(match1.get("away_team", ""))
            home2 = self._normalize_team_name(match2.get("home_team", ""))
            away2 = self._normalize_team_name(match2.get("away_team", ""))
            
            # 计算队伍匹配分数（考虑主客队可能颠倒）
            forward_score = (self._string_similarity(home1, home2) + 
                           self._string_similarity(away1, away2)) / 2
            cross_score = (self._string_similarity(home1, away2) + 
                         self._string_similarity(away1, home2)) / 2
            team_score = max(forward_score, cross_score)
            
            # 联赛相似度
            league1 = self._normalize_league_name(match1.get("league", ""))
            league2 = self._normalize_league_name(match2.get("league", ""))
            league_score = self._string_similarity(league1, league2) if league1 and league2 else 0.5
            
            # 时间相似度
            time1 = match1.get("match_time")
            time2 = match2.get("match_time")
            time_score = self._time_similarity(time1, time2)
            
            # 综合分数（权重：队伍50%，联赛30%，时间20%）
            total_score = team_score * 0.5 + league_score * 0.3 + time_score * 0.2
            
            return total_score
            
        except Exception as e:
            logger.error(f"计算比赛相似度失败: {e}")
            return 0.0
    
    def _string_similarity(self, str1: str, str2: str) -> float:
        """计算字符串相似度 - 使用高级相似度算法"""
        if not str1 or not str2:
            return 0.0

        # 使用高级相似度计算器
        result = similarity_calculator.calculate_similarity(str1, str2)

        # 记录详细信息用于调试
        logger.debug(f"字符串相似度: '{str1}' vs '{str2}' = {result.score:.3f} "
                    f"(方法: {result.method})")

        return result.score
    
    def _time_similarity(self, time1, time2) -> float:
        """计算时间相似度"""
        if not time1 or not time2:
            return 0.5  # 时间信息缺失时给予中等分数
        
        try:
            # 转换为datetime对象
            dt1 = self._parse_time_flexible(time1)
            dt2 = self._parse_time_flexible(time2)

            if not dt1 or not dt2:
                logger.debug(f"时间解析失败: '{time1}' -> {dt1}, '{time2}' -> {dt2}")
                return 0.5
            
            # 计算时间差（分钟）
            time_diff = abs((dt1 - dt2).total_seconds()) / 60
            
            # 根据时间差计算相似度
            if time_diff <= 5:
                return 1.0
            elif time_diff <= 30:
                return 0.8
            elif time_diff <= 120:
                return 0.5
            else:
                return 0.0
                
        except Exception as e:
            logger.warning(f"时间相似度计算失败: {e}")
            return 0.5

    def _parse_time_flexible(self, time_str) -> Optional[datetime]:
        """灵活解析时间字符串，支持多种格式"""
        if not time_str:
            return None

        try:
            # 如果是数字类型（时间戳）
            if isinstance(time_str, (int, float)):
                # 判断是秒级还是毫秒级时间戳
                if time_str > 1e10:  # 毫秒级
                    return datetime.fromtimestamp(time_str / 1000)
                else:  # 秒级
                    return datetime.fromtimestamp(time_str)

            # 如果已经是datetime对象
            if isinstance(time_str, datetime):
                return time_str

            # 字符串格式处理
            time_str = str(time_str).strip()

            # 尝试ISO格式 (平博格式: "2025-06-05 00:00:00")
            try:
                return datetime.fromisoformat(time_str)
            except:
                pass

            # 尝试皇冠格式 ("06-04 11:30a", "06-04 08:00p")
            try:
                return self._parse_crown_time_format(time_str)
            except:
                pass

            # 尝试其他常见格式
            common_formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d %H:%M",
                "%m-%d %H:%M",
                "%m/%d %H:%M",
                "%Y/%m/%d %H:%M:%S",
                "%Y/%m/%d %H:%M"
            ]

            for fmt in common_formats:
                try:
                    return datetime.strptime(time_str, fmt)
                except:
                    continue

            logger.debug(f"无法解析时间格式: {time_str}")
            return None

        except Exception as e:
            logger.debug(f"时间解析异常: {e}")
            return None

    def _parse_crown_time_format(self, time_str: str) -> datetime:
        """解析皇冠时间格式 (例: "06-04 11:30a", "06-04 08:00p")"""
        import re
        from datetime import datetime

        # 匹配格式: MM-DD HH:MMa/p
        pattern = r'(\d{2})-(\d{2})\s+(\d{1,2}):(\d{2})([ap])'
        match = re.match(pattern, time_str)

        if not match:
            raise ValueError(f"无法匹配皇冠时间格式: {time_str}")

        month, day, hour, minute, ampm = match.groups()

        # 转换为24小时制
        hour = int(hour)
        if ampm == 'p' and hour != 12:
            hour += 12
        elif ampm == 'a' and hour == 12:
            hour = 0

        # 假设是当前年份
        current_year = datetime.now().year

        try:
            return datetime(current_year, int(month), int(day), hour, int(minute))
        except ValueError as e:
            # 如果日期无效，可能是跨年情况
            try:
                return datetime(current_year + 1, int(month), int(day), hour, int(minute))
            except ValueError:
                raise ValueError(f"无效的日期: {time_str}")

    def clear_cache(self):
        """清理过期缓存"""
        current_time = datetime.now()
        expired_keys = []
        
        for key, (timestamp, _) in self.match_cache.items():
            if current_time - timestamp > self.cache_expiry:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.match_cache[key]
        
        if expired_keys:
            logger.info(f"清理了 {len(expired_keys)} 个过期缓存项")
    
    def get_cache_stats(self) -> Dict:
        """获取缓存统计信息"""
        return {
            "cache_size": len(self.match_cache),
            "cache_expiry_hours": self.cache_expiry.total_seconds() / 3600
        }
