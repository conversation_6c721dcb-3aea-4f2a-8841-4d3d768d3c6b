#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HAR文件精简工具
只保留重要的XHR、Fetch、Document等请求，移除图片、CSS、JS等静态资源
"""

import json
import sys
import os
from typing import Dict, List, Any
import argparse

class HARSimplifier:
    """HAR文件精简器"""
    
    def __init__(self):
        # 只保留XHR类型的请求
        self.keep_resource_types = {
            'xhr',           # 只保留AJAX请求
        }

        # 不再基于请求方法过滤
        self.keep_methods = set()

        # 不再基于URL关键词过滤
        self.important_url_keywords = set()

        # 排除所有非XHR的资源类型
        self.exclude_resource_types = {
            'fetch', 'document', 'websocket', 'preflight',
            'image', 'stylesheet', 'script', 'font', 'media',
            'texttrack', 'eventsource', 'manifest', 'ping',
            'other'
        }
    
    def is_important_request(self, entry: Dict[str, Any]) -> bool:
        """判断请求是否重要 - 只保留XHR类型"""
        resource_type = entry.get('_resourceType', '').lower()

        # 只保留XHR类型的请求
        return resource_type == 'xhr'
    
    def simplify_entry(self, entry: Dict[str, Any]) -> Dict[str, Any]:
        """精简单个请求条目"""
        simplified = {
            'startedDateTime': entry.get('startedDateTime'),
            'time': entry.get('time'),
            'request': self.simplify_request(entry.get('request', {})),
            'response': self.simplify_response(entry.get('response', {})),
            '_resourceType': entry.get('_resourceType'),
            'serverIPAddress': entry.get('serverIPAddress'),
            'connection': entry.get('connection'),
            'pageref': entry.get('pageref')
        }
        
        # 移除空值
        return {k: v for k, v in simplified.items() if v is not None}
    
    def simplify_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """精简请求信息"""
        simplified = {
            'method': request.get('method'),
            'url': request.get('url'),
            'httpVersion': request.get('httpVersion'),
            'headers': self.filter_headers(request.get('headers', [])),
            'queryString': request.get('queryString', []),
            'cookies': request.get('cookies', []),
            'headersSize': request.get('headersSize'),
            'bodySize': request.get('bodySize')
        }
        
        # 保留POST数据
        if 'postData' in request:
            simplified['postData'] = request['postData']
        
        return {k: v for k, v in simplified.items() if v is not None}
    
    def simplify_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """精简响应信息"""
        simplified = {
            'status': response.get('status'),
            'statusText': response.get('statusText'),
            'httpVersion': response.get('httpVersion'),
            'headers': self.filter_headers(response.get('headers', [])),
            'cookies': response.get('cookies', []),
            'content': self.simplify_content(response.get('content', {})),
            'redirectURL': response.get('redirectURL'),
            'headersSize': response.get('headersSize'),
            'bodySize': response.get('bodySize'),
            '_transferSize': response.get('_transferSize'),
            '_error': response.get('_error')
        }
        
        return {k: v for k, v in simplified.items() if v is not None}
    
    def filter_headers(self, headers: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """过滤重要的请求头"""
        important_headers = {
            'authorization', 'cookie', 'set-cookie', 'content-type',
            'accept', 'user-agent', 'referer', 'origin', 'x-requested-with',
            'x-csrf-token', 'x-auth-token', 'sessionid', 'location'
        }
        
        return [
            header for header in headers
            if header.get('name', '').lower() in important_headers
        ]
    
    def simplify_content(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """精简响应内容"""
        simplified = {
            'size': content.get('size'),
            'mimeType': content.get('mimeType')
        }
        
        # 只保留JSON和文本内容的实际数据
        mime_type = content.get('mimeType', '').lower()
        if any(t in mime_type for t in ['json', 'text', 'xml', 'html']):
            if 'text' in content:
                simplified['text'] = content['text']
        
        return {k: v for k, v in simplified.items() if v is not None}
    
    def simplify_har(self, har_data: Dict[str, Any]) -> Dict[str, Any]:
        """精简整个HAR文件"""
        log = har_data.get('log', {})
        entries = log.get('entries', [])
        
        # 过滤重要请求
        important_entries = [
            entry for entry in entries
            if self.is_important_request(entry)
        ]
        
        # 精简每个条目
        simplified_entries = [
            self.simplify_entry(entry)
            for entry in important_entries
        ]
        
        # 构建精简后的HAR
        simplified_har = {
            'log': {
                'version': log.get('version'),
                'creator': log.get('creator'),
                'pages': log.get('pages', []),
                'entries': simplified_entries
            }
        }
        
        return simplified_har
    
    def process_file(self, input_file: str, output_file: str = None) -> bool:
        """处理HAR文件"""
        try:
            # 读取原始HAR文件
            with open(input_file, 'r', encoding='utf-8') as f:
                har_data = json.load(f)
            
            # 精简HAR数据
            simplified_har = self.simplify_har(har_data)
            
            # 生成输出文件名
            if not output_file:
                base_name = os.path.splitext(input_file)[0]
                output_file = f"{base_name}_simplified.har"
            
            # 保存精简后的HAR文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(simplified_har, f, indent=2, ensure_ascii=False)
            
            # 统计信息
            original_count = len(har_data.get('log', {}).get('entries', []))
            simplified_count = len(simplified_har['log']['entries'])
            
            print(f"HAR文件精简完成:")
            print(f"  输入文件: {input_file}")
            print(f"  输出文件: {output_file}")
            print(f"  原始请求数: {original_count}")
            print(f"  精简后请求数: {simplified_count}")
            print(f"  压缩比: {simplified_count/original_count*100:.1f}%")
            
            return True
            
        except Exception as e:
            print(f"处理HAR文件失败: {e}")
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='HAR文件精简工具')
    parser.add_argument('input', help='输入的HAR文件路径')
    parser.add_argument('-o', '--output', help='输出的HAR文件路径')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        return 1
    
    simplifier = HARSimplifier()
    success = simplifier.process_file(args.input, args.output)
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
