#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import logging
import os
import time
from typing import Dict, Any, Optional
from utils.utils import load_config

logger = logging.getLogger(__name__)

class HedgeConfig:
    """对冲配置管理器"""

    # 类级别的配置缓存
    _cached_config = None
    _cache_time = 0
    _cache_duration = 300  # 缓存5分钟，减少重复加载
    _config_loaded_once = False  # 标记是否已经加载过配置

    def __init__(self):
        """初始化配置管理器"""
        self.config = self._load_hedge_config()
        self._validate_config()
    
    def _load_hedge_config(self) -> Dict[str, Any]:
        """加载对冲配置（使用缓存）"""
        try:
            # 检查缓存是否有效
            current_time = time.time()
            if (HedgeConfig._cached_config is not None and
                (current_time - HedgeConfig._cache_time) < HedgeConfig._cache_duration):
                # 使用缓存的配置
                full_config = HedgeConfig._cached_config
            else:
                # 重新加载配置并缓存
                full_config = load_config()
                HedgeConfig._cached_config = full_config
                HedgeConfig._cache_time = current_time

                # 只在第一次加载时记录日志
                if not HedgeConfig._config_loaded_once:
                    HedgeConfig._config_loaded_once = True
                    logger.info("对冲配置首次加载完成")
                else:
                    logger.debug("对冲配置已重新加载（缓存过期）")

            hedge_config = full_config.get("hedge", {})
            
            # 设置默认值
            default_config = {
                "enable": False,
                "base_platform": "auto",  # auto, pinbet, crown
                "base_amount": 100,
                "allocation_method": "fixed",  # kelly, fixed, balance
                "max_hedge_amount": 500,
                "retry_attempts": 3,
                "retry_delay": 2,
                "timeout_seconds": 30,
                "min_arbitrage_profit": 2.0,
                "max_concurrent_hedges": 1,
                "enable_notifications": True,
                "execution_mode": "sequential",  # sequential, concurrent
                "base_first_delay": 1.0,  # 基准平台投注成功后等待时间（秒）
                "duplicate_check_interval": 1,  # 重复检测间隔（秒）
                "enable_cross_platform_duplicate_check": True,  # 跨平台重复检测
                "execution_lock_timeout": 30,  # 执行锁超时时间（秒）
                "odds_change_threshold": 0.0,  # 盘口值变化阈值
                "ioratio_change_threshold": 0.08,  # 赔率值变化阈值
                "platform_bet_ratios": {  # 平台投注额比例配置
                    "pinbet": 1.0,  # 平博投注比例（1.0为基准）
                    "crown": 1.0   # 皇冠投注比例（1.0为基准）
                },
                "crown_discount_rate": 0.8,  # 皇冠折扣率（0.8=8折盘，1.0=原盘，0.5=5折盘）
                "monitor_check_interval": 10  # 监控检查间隔（秒）
            }
            
            # 合并配置
            for key, default_value in default_config.items():
                if key not in hedge_config:
                    hedge_config[key] = default_value
            
            logger.info("对冲配置加载完成")
            return hedge_config
            
        except Exception as e:
            logger.error(f"加载对冲配置失败: {e}")
            # 返回默认配置
            return {
                "enable": False,
                "base_platform": "auto",
                "base_amount": 100,
                "allocation_method": "fixed",
                "max_hedge_amount": 500,
                "retry_attempts": 3,
                "retry_delay": 2,
                "timeout_seconds": 30,
                "min_arbitrage_profit": 2.0,
                "max_concurrent_hedges": 1,
                "enable_notifications": True,
                "execution_mode": "sequential",
                "base_first_delay": 1.0,
                "duplicate_check_interval": 1,
                "enable_cross_platform_duplicate_check": True,
                "execution_lock_timeout": 30,
                "odds_change_threshold": 0.0,
                "ioratio_change_threshold": 0.08,
                "platform_bet_ratios": {
                    "pinbet": 1.0,
                    "crown": 1.0
                },
                "crown_discount_rate": 0.8,  # 皇冠折扣率（0.8=8折盘，1.0=原盘，0.5=5折盘）
                "monitor_check_interval": 10  # 监控检查间隔（秒）
            }
    
    def _validate_config(self):
        """验证配置参数的有效性"""
        try:
            # 验证基准平台
            valid_platforms = ["auto", "pinbet", "crown"]
            if self.config["base_platform"] not in valid_platforms:
                logger.warning(f"无效的基准平台配置: {self.config['base_platform']}, 使用默认值: auto")
                self.config["base_platform"] = "auto"
            
            # 验证分配方法
            valid_methods = ["kelly", "fixed", "balance"]
            if self.config["allocation_method"] not in valid_methods:
                logger.warning(f"无效的分配方法配置: {self.config['allocation_method']}, 使用默认值: fixed")
                self.config["allocation_method"] = "fixed"

            # 验证执行模式
            valid_modes = ["sequential", "concurrent"]
            if self.config["execution_mode"] not in valid_modes:
                logger.warning(f"无效的执行模式配置: {self.config['execution_mode']}, 使用默认值: sequential")
                self.config["execution_mode"] = "sequential"
            
            # 验证数值参数
            numeric_params = {
                "base_amount": (10, 10000),
                "max_hedge_amount": (50, 50000),
                "retry_attempts": (1, 10),
                "retry_delay": (1, 60),
                "timeout_seconds": (10, 300),
                "min_arbitrage_profit": (0.1, 50.0),
                "max_concurrent_hedges": (1, 10),
                "base_first_delay": (0.1, 10.0),
                "duplicate_check_interval": (0.1, 10.0),
                "execution_lock_timeout": (5, 300),
                "odds_change_threshold": (0.0, 10.0),
                "ioratio_change_threshold": (0.01, 1.0)
            }
            
            for param, (min_val, max_val) in numeric_params.items():
                value = self.config.get(param, 0)
                if not isinstance(value, (int, float)) or value < min_val or value > max_val:
                    default_value = min_val if value < min_val else max_val
                    logger.warning(f"参数 {param} 值 {value} 超出范围 [{min_val}, {max_val}], 使用默认值: {default_value}")
                    self.config[param] = default_value
            
            # 验证布尔参数
            bool_params = ["enable", "enable_rollback", "enable_notifications", "enable_cross_platform_duplicate_check"]
            for param in bool_params:
                if not isinstance(self.config.get(param), bool):
                    logger.warning(f"参数 {param} 不是布尔值, 使用默认值: False")
                    self.config[param] = False
            
            logger.info("对冲配置验证完成")
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
    
    def is_enabled(self) -> bool:
        """检查对冲功能是否启用"""
        return self.config.get("enable", False)
    
    def get_base_platform(self) -> str:
        """获取基准平台"""
        return self.config.get("base_platform", "auto")
    
    def get_base_amount(self) -> float:
        """获取基准投注金额"""
        return float(self.config.get("base_amount", 100))
    
    def get_allocation_method(self) -> str:
        """获取资金分配方法"""
        return self.config.get("allocation_method", "fixed")
    
    def get_max_hedge_amount(self) -> float:
        """获取最大对冲金额"""
        return float(self.config.get("max_hedge_amount", 500))
    
    def get_retry_attempts(self) -> int:
        """获取重试次数"""
        return int(self.config.get("retry_attempts", 3))
    
    def get_retry_delay(self) -> int:
        """获取重试延迟（秒）"""
        return int(self.config.get("retry_delay", 2))
    
    def get_timeout_seconds(self) -> int:
        """获取超时时间（秒）"""
        return int(self.config.get("timeout_seconds", 30))

    def get_min_arbitrage_profit(self) -> float:
        """获取最小套利收益率"""
        return float(self.config.get("min_arbitrage_profit", 2.0))
    
    def get_max_concurrent_hedges(self) -> int:
        """获取最大并发对冲数量"""
        return int(self.config.get("max_concurrent_hedges", 1))
    
    def is_notifications_enabled(self) -> bool:
        """检查是否启用通知"""
        return self.config.get("enable_notifications", True)

    def get_execution_mode(self) -> str:
        """获取执行模式"""
        return self.config.get("execution_mode", "sequential")

    def get_base_first_delay(self) -> float:
        """获取基准平台投注成功后的等待时间（秒）"""
        return float(self.config.get("base_first_delay", 1.0))

    def get_duplicate_check_interval(self) -> float:
        """获取重复检测间隔（秒）"""
        return float(self.config.get("duplicate_check_interval", 1.0))

    def is_cross_platform_duplicate_check_enabled(self) -> bool:
        """检查是否启用跨平台重复检测"""
        return self.config.get("enable_cross_platform_duplicate_check", True)

    def get_execution_lock_timeout(self) -> int:
        """获取执行锁超时时间（秒）"""
        return int(self.config.get("execution_lock_timeout", 30))

    def get_platform_bet_ratios(self) -> Dict[str, float]:
        """获取平台投注额比例配置"""
        default_ratios = {"pinbet": 1.0, "crown": 1.0}
        return self.config.get("platform_bet_ratios", default_ratios)

    def get_platform_bet_ratio(self, platform: str) -> float:
        """获取指定平台的投注比例"""
        ratios = self.get_platform_bet_ratios()
        return float(ratios.get(platform, 1.0))

    def set_platform_bet_ratio(self, platform: str, ratio: float):
        """设置指定平台的投注比例"""
        if "platform_bet_ratios" not in self.config:
            self.config["platform_bet_ratios"] = {"pinbet": 1.0, "crown": 1.0}

        # 验证比例范围
        if ratio < 0.1 or ratio > 5.0:
            logger.warning(f"平台 {platform} 投注比例 {ratio} 超出范围 [0.1, 5.0]，使用默认值 1.0")
            ratio = 1.0

        self.config["platform_bet_ratios"][platform] = float(ratio)
        logger.info(f"设置平台 {platform} 投注比例为 {ratio}")

    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self.config.copy()

    def get_odds_change_threshold(self) -> float:
        """获取盘口值变化阈值"""
        return float(self.config.get("odds_change_threshold", 0.0))

    def get_ioratio_change_threshold(self) -> float:
        """获取赔率值变化阈值"""
        return float(self.config.get("ioratio_change_threshold", 0.08))

    def get_crown_discount_rate(self) -> float:
        """获取皇冠折扣率"""
        return float(self.config.get("crown_discount_rate", 0.8))

    def update_config(self, updates: Dict[str, Any]) -> bool:
        """
        更新配置
        
        参数:
            updates: 要更新的配置项
            
        返回:
            是否更新成功
        """
        try:
            # 备份当前配置
            backup_config = self.config.copy()
            
            # 更新配置
            self.config.update(updates)
            
            # 重新验证配置
            self._validate_config()

            # 清除缓存，确保其他实例能获取到最新配置
            HedgeConfig.clear_cache()

            logger.info(f"配置更新成功: {updates}")
            return True
            
        except Exception as e:
            # 恢复备份配置
            self.config = backup_config
            logger.error(f"配置更新失败: {e}")
            return False
    
    def save_config_to_file(self) -> bool:
        """
        保存配置到文件
        
        返回:
            是否保存成功
        """
        try:
            # 读取完整配置文件
            full_config = load_config()
            
            # 更新对冲配置部分
            full_config["hedge"] = self.config
            
            # 保存到文件
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(current_dir, "config.json")
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(full_config, f, ensure_ascii=False, indent=2)
            
            logger.info("对冲配置已保存到文件")
            return True
            
        except Exception as e:
            logger.error(f"保存配置到文件失败: {e}")
            return False
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        self.config = {
            "enable": False,
            "base_platform": "auto",
            "base_amount": 100,
            "allocation_method": "fixed",
            "max_hedge_amount": 500,
            "retry_attempts": 3,
            "retry_delay": 2,
            "timeout_seconds": 30,
            "min_arbitrage_profit": 2.0,
            "max_concurrent_hedges": 1,
            "enable_notifications": True,
            "execution_mode": "sequential",
            "base_first_delay": 1.0,
            "duplicate_check_interval": 1,
            "enable_cross_platform_duplicate_check": True,
            "execution_lock_timeout": 30,
            "odds_change_threshold": 1.0,
            "ioratio_change_threshold": 0.1,
            "platform_bet_ratios": {
                "pinbet": 1.0,
                "crown": 1.0
            },
            "monitor_check_interval": 10  # 监控检查间隔（秒）
        }
        logger.info("对冲配置已重置为默认值")

    @classmethod
    def clear_cache(cls):
        """清除配置缓存，强制下次重新加载"""
        cls._cached_config = None
        cls._cache_time = 0

        # 同时清除ConfigSecurity的缓存
        try:
            from utils.config_security import ConfigSecurity
            ConfigSecurity._cached_config = None
            ConfigSecurity._cache_time = 0
            logger.debug("ConfigSecurity缓存已清除")
        except Exception as e:
            logger.warning(f"清除ConfigSecurity缓存失败: {e}")

        logger.debug("对冲配置缓存已清除")

    def force_reload_config(self):
        """强制重新加载配置（忽略缓存）"""
        try:
            # 清除类级别缓存
            HedgeConfig.clear_cache()

            # 重新加载配置
            self.config = self._load_hedge_config()
            self._validate_config()

            logger.info("配置已强制重新加载")
            return True
        except Exception as e:
            logger.error(f"强制重新加载配置失败: {e}")
            return False

    def get_config_summary(self) -> str:
        """获取配置摘要"""
        ratios = self.get_platform_bet_ratios()
        return f"""对冲配置摘要:
- 启用状态: {'是' if self.is_enabled() else '否'}
- 基准平台: {self.get_base_platform()}
- 基准金额: {self.get_base_amount()}
- 分配方法: {self.get_allocation_method()}
- 最大对冲金额: {self.get_max_hedge_amount()}
- 重试次数: {self.get_retry_attempts()}
- 超时时间: {self.get_timeout_seconds()}秒
- 最小套利收益: {self.get_min_arbitrage_profit()}%
- 最大并发数: {self.get_max_concurrent_hedges()}
- 启用通知: {'是' if self.is_notifications_enabled() else '否'}
- 执行模式: {self.get_execution_mode()}
- 基准延迟: {self.get_base_first_delay()}秒
- 平台投注比例: 平博={ratios.get('pinbet', 1.0)}, 皇冠={ratios.get('crown', 1.0)}"""
