# 小金平台JWT Token获取指南

## 📋 概述

本指南将帮助您获取小金平台的JWT Token，用于API自动化操作。

## 🎯 获取步骤

### 1. 登录小金平台

1. 打开浏览器，访问: https://www.taptap.asia/zh-cn
2. 输入您的用户名和密码
3. 点击登录按钮
4. 等待登录成功

### 2. 进入体育平台

1. 登录成功后，查找"体育"或"Sports"链接
2. 点击进入体育平台
3. 等待页面完全加载

### 3. 打开开发者工具

1. 按 `F12` 键打开开发者工具
2. 或者右键点击页面，选择"检查"或"Inspect"
3. 切换到 `Network` (网络) 标签

### 4. 捕获网络请求

1. 在开发者工具的Network标签中，点击"清除"按钮清空现有请求
2. 刷新页面 (按F5) 或进行任何操作 (如点击余额查询)
3. 观察网络请求列表

### 5. 查找API请求

在网络请求列表中查找以下类型的请求：
- `getbalance` - 余额查询
- `getmybet` - 注单查询
- `getfreebettoken` - 免费投注令牌
- 任何以 `sports-api.sbk-188-sports.com` 开头的请求

### 6. 提取JWT Token

1. 点击任意一个API请求
2. 在右侧面板中查看 `Request Headers` (请求头)
3. 找到 `Authorization` 头
4. 复制 `Bearer ` 后面的完整Token

**示例:**
```
Authorization: Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JZMznZmook3OqOK2GWJcWW8EH4-FuC6e_2SivirQCHs
```

**只需要复制这部分:**
```
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JZMznZmook3OqOK2GWJcWW8EH4-FuC6e_2SivirQCHs
```

## 🔧 使用JWT Token

### 方法1: 直接在代码中使用

```python
from platforms.xiaojin_api import XiaojinAPI

# 使用JWT Token初始化API
jwt_token = "your_jwt_token_here"
api = XiaojinAPI(jwt_token)

# 测试API调用
success, balance_data = api.get_balance()
if success:
    print("余额查询成功:", balance_data)
else:
    print("余额查询失败:", balance_data)
```

### 方法2: 运行测试脚本

```bash
python test_xiaojin_manual_jwt.py
```

按照提示输入JWT Token进行测试。

## ⚠️ 重要注意事项

### 1. Token有效期
- JWT Token有时间限制，通常几小时后会过期
- 过期后需要重新登录获取新的Token
- 建议定期更新Token

### 2. 安全性
- 不要在公共场所或不安全的网络环境下获取Token
- 不要将Token分享给他人
- 不要在代码中硬编码Token

### 3. 使用限制
- 不要频繁调用API，避免触发风控
- 建议在API调用之间添加适当的延时
- 遵守平台的使用条款

## 🛠️ 故障排除

### 问题1: 找不到Authorization头
**解决方案:**
- 确保已经完全登录到体育平台
- 尝试刷新页面或进行其他操作
- 检查是否在正确的网络请求中查找

### 问题2: Token格式错误
**解决方案:**
- 确保复制了完整的Token
- 不要包含"Bearer "前缀
- 检查Token中是否有换行符或空格

### 问题3: Token验证失败
**解决方案:**
- 检查Token是否已过期
- 重新登录获取新的Token
- 确保在正确的平台上获取Token

### 问题4: API调用返回401错误
**解决方案:**
- Token可能已过期，重新获取
- 检查API URL是否正确
- 确认账户状态正常

## 📞 技术支持

如果在获取或使用JWT Token过程中遇到问题，请：

1. 检查本指南的故障排除部分
2. 确认按照步骤正确操作
3. 检查浏览器控制台是否有错误信息
4. 尝试使用不同的浏览器或清除缓存

## 🔄 自动化更新

为了实现Token的自动化更新，建议：

1. 定期检查Token有效性
2. 在Token即将过期时提醒用户更新
3. 实现Token刷新机制（如果平台支持）

## 📝 更新日志

- **2025-07-01**: 初始版本，支持手动JWT Token获取和使用
- 后续版本将添加更多自动化功能
