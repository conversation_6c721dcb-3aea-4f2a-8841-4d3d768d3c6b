# 记录查询功能使用说明

## 🎯 功能概述

记录管理页面现已完全修复，支持三种类型的记录查询：
- **对冲记录**: 显示系统执行的对冲投注记录
- **平博记录**: 显示平博平台的未结算注单
- **皇冠记录**: 显示皇冠平台的今日注单

## 🚀 使用步骤

### 1. 系统准备
1. 启动GUI应用
2. 点击"初始化系统"
3. 点击"登录系统"
4. 确保平博和皇冠状态显示为"已登录"

### 2. 查询记录
1. 切换到"📋 记录管理"页面
2. 在"记录类型"下拉框中选择要查询的类型：
   - **对冲记录**: 查看系统对冲投注历史
   - **平博记录**: 查看平博未结算注单
   - **皇冠记录**: 查看皇冠今日注单
3. 点击"🔍 查询记录"按钮
4. 等待数据加载完成

### 3. 查看结果
- 每次查询会自动清空之前的记录，避免数据重复
- 表格会根据记录类型自动调整列结构
- 查询过程和结果会在日志中显示

## 📋 表格结构说明

### 对冲记录表格
| 列名 | 说明 |
|------|------|
| 对冲ID | 对冲操作的唯一标识 |
| 创建时间 | 对冲创建的时间 |
| 比赛 | 对冲涉及的比赛 |
| 平博投注 | 在平博的投注详情 |
| 皇冠投注 | 在皇冠的投注详情 |
| 总投注额 | 两平台投注金额总和 |
| 预期盈利 | 理论盈利金额 |
| 实际盈利 | 实际盈利金额 |
| 状态 | 对冲状态（已完成/进行中/失败） |

### 平博记录表格
| 列名 | 说明 |
|------|------|
| 开赛时间 | 比赛开始时间 |
| 比赛 | 比赛队伍信息 |
| 投注信息 | 投注类型和选择 |
| 金额 | 投注金额 |
| 赔率 | 投注赔率 |
| 状态 | 注单状态（OPEN/PENDING/CLOSED） |

### 皇冠记录表格
| 列名 | 说明 |
|------|------|
| 开赛时间 | 比赛开始时间 |
| 比赛 | 比赛队伍信息 |
| 投注信息 | 投注类型、时段和选择 |
| 金额 | 投注金额 |
| 赔率 | 投注赔率 |
| 状态 | 注单状态 |

## 🔧 功能特性

### 智能字段映射
- **皇冠记录**: 使用 `addtime`、`team_h_display`、`bet_type_desc` 等正确字段
- **平博记录**: 使用数组索引14、9、22、24等正确解析API返回数据
- **自动组合**: 投注信息会自动组合投注类型、时段、选择等信息

### 数据清理机制
- 每次查询前自动清空现有记录
- 避免数据重复叠加
- 确保显示的都是最新查询结果

### 错误处理
- 完善的异常捕获和错误提示
- 调试信息输出，便于问题排查
- 优雅的降级处理

## 🐛 故障排除

### 常见问题

**1. 查询结果为空**
- 检查系统是否已登录
- 确认网络连接正常
- 查看日志中的错误信息

**2. 字段显示为空**
- 已修复字段映射问题
- 如仍有问题，查看日志中的调试信息

**3. 查询失败**
- 检查平台登录状态
- 重新初始化和登录系统
- 查看详细错误日志

### 调试工具

**测试脚本**
```bash
cd gui
python test_records.py
```
此脚本会测试平博和皇冠记录查询，显示字段映射情况。

**日志查看**
- 切换到"📄 日志查看"页面
- 查看详细的操作日志和错误信息
- 使用过滤功能定位特定问题

## 📈 性能说明

### 查询速度
- **平博记录**: 通常1-3秒
- **皇冠记录**: 通常2-5秒
- **对冲记录**: 通常<1秒（本地数据）

### 数据量限制
- **平博**: 显示所有未结算注单
- **皇冠**: 显示今日注单（可能较多）
- **对冲**: 显示历史对冲记录

## 🔄 更新历史

### v2.1.2 - 状态和开赛时间修复
- ✅ 修复皇冠记录状态显示问题
- ✅ 修复皇冠记录开赛时间显示问题
- ✅ 实现与web程序一致的状态转换逻辑

### v2.1.1 - 字段映射修复
- ✅ 修复皇冠记录字段映射问题
- ✅ 修复平博记录数组索引解析
- ✅ 增强调试功能

### v2.1.0 - 功能重构
- ✅ 重构记录管理界面
- ✅ 实现动态表格结构
- ✅ 添加点击查询模式

## 💡 使用技巧

1. **定期查询**: 建议每隔一段时间查询一次，获取最新数据
2. **类型切换**: 可以快速切换不同记录类型，无需重新登录
3. **数据导出**: 查询后可以导出为CSV文件进行进一步分析
4. **日志监控**: 关注日志信息，及时发现和解决问题

## 📞 技术支持

如遇到问题，请提供：
1. 错误截图
2. 日志信息
3. 操作步骤
4. 系统环境信息

这将帮助快速定位和解决问题。
