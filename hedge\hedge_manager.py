#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import time
import asyncio
import threading
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import json

from config.hedge_config import HedgeConfig
from hedge.hedge_calculator import BetAmountCalculator
from hedge.hedge_monitor import BetMonitor
from hedge.execution_lock import get_lock_manager, MatchLockContext
from hedge.hedge_metrics import get_metrics_manager
from utils.structured_logger import get_structured_logger
from utils.utils import load_config

# 导入对冲记录管理器
try:
    from hedge.hedge_record_manager import HedgeRecordManager
    from hedge.partial_record_manager import PartialRecordManager
except ImportError:
    # 如果导入失败，创建一个简单的占位符类
    class HedgeRecordManager:
        def __init__(self, *args, **kwargs):
            pass
        def save_hedge_log(self, log_data):
            return False

    class PartialRecordManager:
        def __init__(self, *args, **kwargs):
            pass
        def save_partial_record(self, log_data):
            return False

# 设置日志
logger = logging.getLogger(__name__)

class HedgeManager:
    """对冲投注管理器"""
    
    def __init__(self, pinbet_instance=None, crown_instance=None):
        """
        初始化对冲管理器

        参数:
            pinbet_instance: 平博投注实例
            crown_instance: 皇冠投注实例
        """
        self.pinbet = pinbet_instance
        self.crown = crown_instance
        self.config = HedgeConfig()
        self.calculator = BetAmountCalculator()
        self.monitor = BetMonitor()

        # 投注状态跟踪
        self.active_hedges = {}  # 活跃的对冲投注
        self.hedge_history = []  # 对冲历史记录

        # 初始化对冲记录管理器
        try:
            self.record_manager = HedgeRecordManager()
            logger.info("对冲记录管理器初始化成功")
        except Exception as e:
            logger.warning(f"对冲记录管理器初始化失败: {e}")
            self.record_manager = None

        # 初始化单边记录管理器
        try:
            self.partial_manager = PartialRecordManager()
            logger.info("单边记录管理器初始化成功")
        except Exception as e:
            logger.warning(f"单边记录管理器初始化失败: {e}")
            self.partial_manager = None

        logger.info("对冲管理器初始化完成")
    
    def is_hedge_enabled(self) -> bool:
        """检查对冲功能是否启用"""
        return self.config.is_enabled()
    
    def can_execute_hedge(self, arbitrage_data: Dict) -> Tuple[bool, str]:
        """
        检查是否可以执行对冲投注

        参数:
            arbitrage_data: 套利数据

        返回:
            (是否可以执行, 原因说明)
        """
        if not self.is_hedge_enabled():
            return False, "对冲功能未启用"

        if not self.pinbet or not self.crown:
            return False, "缺少投注平台实例"

        # 检查平博登录状态
        pinbet_logged_in = self._check_pinbet_login_status()
        if not pinbet_logged_in:
            return False, "平博平台未登录"

        # 检查皇冠登录状态
        crown_logged_in = self._check_crown_login_status()
        if not crown_logged_in:
            return False, "皇冠平台未登录"
        
        # 检查是否有对应的投注选项
        pinbet_option = None
        crown_option = None
        
        for option in arbitrage_data:
            bookmaker = option.get("bookmaker", "").lower()
            if "平博" in bookmaker or "pinbet" in bookmaker:
                pinbet_option = option
            elif "皇冠" in bookmaker or "crown" in bookmaker:
                crown_option = option
        
        if not pinbet_option or not crown_option:
            return False, "缺少完整的对冲投注选项"
        
        return True, "可以执行对冲投注"

    def _check_pinbet_login_status(self) -> bool:
        """检查平博登录状态"""
        try:
            if not self.pinbet:
                return False

            # 平博可能是PinBetSystem类型，需要检查api属性
            if hasattr(self.pinbet, 'api'):
                api = self.pinbet.api
                if hasattr(api, 'is_logged_in'):
                    return bool(api.is_logged_in)

            # 直接检查is_logged_in属性
            if hasattr(self.pinbet, 'is_logged_in'):
                return bool(self.pinbet.is_logged_in)

            logger.warning("平博系统缺少登录状态属性")
            return False

        except Exception as e:
            logger.error(f"检查平博登录状态失败: {e}")
            return False

    def _check_crown_login_status(self) -> bool:
        """检查皇冠登录状态"""
        try:
            if not self.crown:
                return False

            # 皇冠直接检查is_logged_in属性
            if hasattr(self.crown, 'is_logged_in'):
                return bool(self.crown.is_logged_in)

            logger.warning("皇冠系统缺少登录状态属性")
            return False

        except Exception as e:
            logger.error(f"检查皇冠登录状态失败: {e}")
            return False
    
    def determine_base_platform(self, pinbet_option: Dict, crown_option: Dict) -> str:
        """
        确定基准投注平台
        
        参数:
            pinbet_option: 平博投注选项
            crown_option: 皇冠投注选项
            
        返回:
            基准平台名称 ('pinbet' 或 'crown')
        """
        base_platform = self.config.get_base_platform()
        
        if base_platform == "auto":
            # 自动选择：优先选择赔率更高的平台作为基准
            try:
                pinbet_odds = float(pinbet_option.get("koef", "0"))
                crown_odds = float(crown_option.get("koef", "0"))
                
                if pinbet_odds > crown_odds:
                    return "pinbet"
                else:
                    return "crown"
            except (ValueError, TypeError):
                # 如果赔率解析失败，默认使用平博
                logger.warning("无法解析赔率，使用默认基准平台: crown")
                return "crown"
        
        return base_platform
    
    async def execute_hedge_bet_async(self, arbitrage_data: List[Dict]) -> Dict:
        """
        异步执行对冲投注

        参数:
            arbitrage_data: 套利数据列表

        返回:
            投注结果字典
        """
        # 检查是否可以执行对冲
        can_execute, reason = self.can_execute_hedge(arbitrage_data)
        if not can_execute:
            logger.warning(f"无法执行对冲投注: {reason}")
            return {"success": False, "message": reason}

        # 获取比赛信息用于锁定
        match_key = self._extract_match_key(arbitrage_data)
        if not match_key:
            logger.error("无法提取比赛信息，无法执行对冲")
            return {"success": False, "message": "无法提取比赛信息"}

        # 获取锁管理器
        lock_manager = get_lock_manager()
        lock_timeout = self.config.get_execution_lock_timeout()

        # 检查是否已被锁定
        if lock_manager.is_match_locked("hedge", match_key):
            logger.warning(f"比赛已被锁定，无法执行对冲: {match_key}")
            return {"success": False, "message": f"比赛已被锁定: {match_key}"}

        # 获取执行锁
        lock_id = lock_manager.acquire_match_lock("hedge", match_key, lock_timeout)
        if not lock_id:
            logger.warning(f"无法获取执行锁: {match_key}")
            return {"success": False, "message": f"无法获取执行锁: {match_key}"}

        try:
            return await self._execute_hedge_with_lock(arbitrage_data, match_key)
        finally:
            # 确保释放锁
            lock_manager.release_match_lock(lock_id)

    def _extract_match_key(self, arbitrage_data: List[Dict]) -> Optional[str]:
        """从套利数据中提取比赛标识"""
        try:
            for option in arbitrage_data:
                match = option.get("match", "")
                if match:
                    return match
            return None
        except Exception as e:
            logger.error(f"提取比赛标识失败: {e}")
            return None

    async def _execute_hedge_with_lock(self, arbitrage_data: List[Dict], match_key: str) -> Dict:
        """在锁保护下执行对冲投注"""
        start_time = time.time()
        structured_logger = get_structured_logger()

        # 分离平博和皇冠的投注选项
        pinbet_option = None
        crown_option = None

        for option in arbitrage_data:
            bookmaker = option.get("bookmaker", "").lower()
            if "平博" in bookmaker or "pinbet" in bookmaker:
                pinbet_option = option
            elif "皇冠" in bookmaker or "crown" in bookmaker:
                crown_option = option

        # 确定基准平台
        base_platform = self.determine_base_platform(pinbet_option, crown_option)

        # 获取基准投注金额
        base_amount = self.config.get_base_amount()

        # 计算投注金额分配
        if base_platform == "pinbet":
            base_odds = float(pinbet_option.get("koef", "0"))
            hedge_odds = float(crown_option.get("koef", "0"))
            base_ratio = self.config.get_platform_bet_ratio("pinbet")
            hedge_ratio = self.config.get_platform_bet_ratio("crown")
        else:
            base_odds = float(crown_option.get("koef", "0"))
            hedge_odds = float(pinbet_option.get("koef", "0"))
            base_ratio = self.config.get_platform_bet_ratio("crown")
            hedge_ratio = self.config.get_platform_bet_ratio("pinbet")

        # 获取皇冠折扣率
        crown_discount_rate = self.config.get_crown_discount_rate()

        amounts = self.calculator.calculate_hedge_amounts(
            base_amount, base_odds, hedge_odds, base_ratio, hedge_ratio,
            crown_discount_rate, base_platform
        )

        # 记录对冲开始事件
        structured_logger.log_hedge_start(
            match_key=match_key,
            base_platform=base_platform,
            base_amount=amounts['base_amount'],
            hedge_amount=amounts['hedge_amount'],
            base_odds=base_odds,
            hedge_odds=hedge_odds,
            execution_mode=self.config.get_execution_mode()
        )

        logger.info(f"对冲投注计划: 基准平台={base_platform}, 基准金额={amounts['base_amount']}, "
                   f"对冲金额={amounts['hedge_amount']}, 总投资={amounts['total_investment']}")
        
        # 创建对冲记录
        hedge_id = f"hedge_{int(time.time())}"
        hedge_record = {
            "hedge_id": hedge_id,
            "timestamp": datetime.now().isoformat(),
            "base_platform": base_platform,
            "amounts": amounts,
            "pinbet_option": pinbet_option,
            "crown_option": crown_option,
            "status": "executing"
        }
        
        self.active_hedges[hedge_id] = hedge_record
        
        try:
            # 根据配置选择执行模式
            execution_mode = self.config.get_execution_mode()

            if execution_mode == "sequential":
                # 顺序投注：基准平台先投注，成功后再投注另一个平台
                logger.info("使用顺序投注模式")
                result = await self._execute_sequential_bets(
                    pinbet_option, crown_option, amounts, base_platform
                )
            else:
                # 并发投注：同时投注两个平台（保留原有逻辑）
                logger.info("使用并发投注模式")
                result = await self._execute_concurrent_bets(
                    pinbet_option, crown_option, amounts, base_platform
                )
            
            # 更新对冲记录
            hedge_record["status"] = "completed" if result["success"] else "failed"
            hedge_record["result"] = result

            # 记录对冲完成事件
            execution_time = time.time() - start_time
            structured_logger.log_hedge_complete(
                match_key=match_key,
                execution_time=execution_time,
                success=result["success"],
                execution_mode=self.config.get_execution_mode(),
                base_platform=base_platform,
                hedge_status=result.get("hedge_status", "unknown")
            )

            # 保存对冲记录到持久化存储
            self._save_hedge_record(hedge_record, result)

            # 移动到历史记录
            self.hedge_history.append(hedge_record)
            del self.active_hedges[hedge_id]

            return result
            
        except Exception as e:
            logger.error(f"执行对冲投注时出错: {e}", exc_info=True)
            hedge_record["status"] = "error"
            hedge_record["error"] = str(e)

            # 记录对冲失败事件
            execution_time = time.time() - start_time
            structured_logger.log_hedge_complete(
                match_key=match_key,
                execution_time=execution_time,
                success=False,
                execution_mode=self.config.get_execution_mode(),
                base_platform=base_platform,
                error_message=str(e),
                hedge_status="error"
            )

            # 保存失败的对冲记录到持久化存储
            self._save_hedge_record(hedge_record, {"success": False, "error": str(e)})

            # 移动到历史记录
            self.hedge_history.append(hedge_record)
            del self.active_hedges[hedge_id]

            return {"success": False, "message": f"对冲投注执行失败: {str(e)}"}

    async def _execute_sequential_bets(self, pinbet_option: Dict, crown_option: Dict,
                                      amounts: Dict, base_platform: str) -> Dict:
        """
        顺序执行两个平台的投注（基准平台先投注，成功后再投注另一个平台）

        参数:
            pinbet_option: 平博投注选项
            crown_option: 皇冠投注选项
            amounts: 投注金额分配
            base_platform: 基准平台

        返回:
            投注结果
        """
        # 确定投注金额和顺序
        if base_platform == "pinbet":
            base_amount = amounts["base_amount"]
            hedge_amount = amounts["hedge_amount"]
            base_option = pinbet_option
            hedge_option = crown_option
            base_executor = self._execute_pinbet_bet
            hedge_executor = self._execute_crown_bet
        else:
            base_amount = amounts["base_amount"]
            hedge_amount = amounts["hedge_amount"]
            base_option = crown_option
            hedge_option = pinbet_option
            base_executor = self._execute_crown_bet
            hedge_executor = self._execute_pinbet_bet

        logger.info(f"开始顺序投注: 基准平台={base_platform}, 基准金额={base_amount}")

        # 第一步：执行基准平台投注
        timeout = self.config.get_timeout_seconds()
        try:
            base_result = await asyncio.wait_for(
                base_executor(base_option, base_amount),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            logger.error(f"基准平台投注超时 ({timeout}秒)")
            return {"success": False, "message": f"基准平台({base_platform})投注超时"}

        # 检查基准平台投注结果
        if not base_result.get("success", False):
            # 只记录一次错误日志，避免重复
            logger.error(f"基准平台({base_platform})投注失败: {base_result.get('message', '未知错误')}")
            return {
                "success": False,
                "message": f"基准平台({base_platform})投注失败，对冲取消",
                "base_result": base_result,
                "hedge_result": None,
                "hedge_status": "cancelled"
            }

        logger.info(f"基准平台({base_platform})投注成功，等待{self.config.get_base_first_delay()}秒后开始对冲平台投注")

        # 等待一段时间，确保基准平台投注完全确认
        await asyncio.sleep(self.config.get_base_first_delay())

        # 第二步：执行对冲平台投注
        hedge_platform = "crown" if base_platform == "pinbet" else "pinbet"
        hedge_result = None  # 初始化为None，确保变量存在

        try:
            hedge_result = await asyncio.wait_for(
                hedge_executor(hedge_option, hedge_amount),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            logger.error(f"对冲平台投注超时 ({timeout}秒)")
            # 基准平台已投注成功，但对冲平台超时，需要补单
            self._handle_hedge_failure(base_result, hedge_platform, hedge_option, hedge_amount)
            return {
                "success": False,
                "message": f"对冲平台({hedge_platform})投注超时，已启动补单流程",
                "base_result": base_result,
                "hedge_result": {"success": False, "platform": hedge_platform, "message": "投注超时"},
                "hedge_status": "timeout_retry"
            }
        except Exception as e:
            logger.error(f"对冲平台投注异常: {e}")
            # 基准平台已投注成功，但对冲平台异常，需要补单
            self._handle_hedge_failure(base_result, hedge_platform, hedge_option, hedge_amount)
            return {
                "success": False,
                "message": f"对冲平台({hedge_platform})投注异常: {str(e)}，已启动补单流程",
                "base_result": base_result,
                "hedge_result": {"success": False, "platform": hedge_platform, "message": f"投注异常: {str(e)}"},
                "hedge_status": "exception_retry"
            }

        # 确保hedge_result不为None
        if hedge_result is None:
            logger.error("对冲平台投注返回None结果")
            hedge_result = {"success": False, "platform": hedge_platform, "message": "投注返回None结果"}

        # 处理最终结果
        return self._process_sequential_bet_results(base_result, hedge_result, base_platform)

    async def _execute_concurrent_bets(self, pinbet_option: Dict, crown_option: Dict,
                                     amounts: Dict, base_platform: str) -> Dict:
        """
        并发执行两个平台的投注（原有逻辑）

        参数:
            pinbet_option: 平博投注选项
            crown_option: 皇冠投注选项
            amounts: 投注金额分配
            base_platform: 基准平台

        返回:
            投注结果
        """
        # 确定投注金额
        if base_platform == "pinbet":
            pinbet_amount = amounts["base_amount"]
            crown_amount = amounts["hedge_amount"]
        else:
            crown_amount = amounts["base_amount"]
            pinbet_amount = amounts["hedge_amount"]

        logger.info(f"开始并发投注: 平博={pinbet_amount}, 皇冠={crown_amount}")

        # 创建投注任务
        pinbet_task = asyncio.create_task(
            self._execute_pinbet_bet(pinbet_option, pinbet_amount)
        )
        crown_task = asyncio.create_task(
            self._execute_crown_bet(crown_option, crown_amount)
        )

        # 等待投注完成
        timeout = self.config.get_timeout_seconds()
        try:
            pinbet_result, crown_result = await asyncio.wait_for(
                asyncio.gather(pinbet_task, crown_task, return_exceptions=True),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            logger.error(f"投注超时 ({timeout}秒)")
            return {"success": False, "message": "投注超时"}

        # 处理投注结果
        return self._process_bet_results(pinbet_result, crown_result, base_platform)

    async def _execute_pinbet_bet(self, option: Dict, amount: float) -> Dict:
        """执行平博投注"""
        return await self._execute_with_retry(
            self._execute_pinbet_bet_once, option, amount, "pinbet"
        )

    async def _execute_pinbet_bet_once(self, option: Dict, amount: float) -> Dict:
        """执行单次平博投注"""
        start_time = time.time()
        match_key = option.get("match", "unknown")
        metrics = get_metrics_manager()

        try:
            # 解析平博投注信息
            bet_info = self.pinbet._parse_bet_info_from_data(option)
            if not bet_info:
                duration = time.time() - start_time
                metrics.record_execution(False, duration, "pinbet", match_key, "解析失败")
                return {"success": False, "platform": "pinbet", "message": "无法解析投注信息"}

            # 在执行投注前检查是否重复投注
            if hasattr(self.pinbet, 'is_bet_duplicated') and self.pinbet.is_bet_duplicated(bet_info):
                duration = time.time() - start_time
                metrics.record_execution(False, duration, "pinbet", match_key, "重复投注", is_duplicate=True)
                logger.warning(f"平博平台检测到重复投注，跳过: {bet_info.get('home_team')} vs {bet_info.get('away_team')}, {bet_info.get('bet_type')}={bet_info.get('bet_team')}")
                return {
                    "success": False,
                    "platform": "pinbet",
                    "amount": amount,
                    "bet_info": bet_info,
                    "message": "检测到重复投注，已跳过",
                    "no_retry": True,  # 标记不需要重试
                    "is_duplicate": True  # 标记为重复投注，不保存记录
                }

            # 执行投注 - 使用正确的方法名和参数
            success = self.pinbet._execute_bet(bet_info, amount)
            duration = time.time() - start_time

            # 记录指标
            error_type = None if success else "投注失败"
            metrics.record_execution(success, duration, "pinbet", match_key, error_type)

            return {
                "success": success,
                "platform": "pinbet",
                "amount": amount,
                "bet_info": bet_info,
                "message": "投注成功" if success else "投注失败"
            }
        except Exception as e:
            duration = time.time() - start_time
            metrics.record_execution(False, duration, "pinbet", match_key, f"异常: {type(e).__name__}")
            logger.error(f"平博投注异常: {e}")
            return {"success": False, "platform": "pinbet", "message": str(e)}

    async def _execute_crown_bet(self, option: Dict, amount: float) -> Dict:
        """执行皇冠投注"""
        return await self._execute_with_retry(
            self._execute_crown_bet_once, option, amount, "crown"
        )

    async def _execute_crown_bet_once(self, option: Dict, amount: float) -> Dict:
        """执行单次皇冠投注"""
        start_time = time.time()
        match_key = option.get("match", "unknown")
        metrics = get_metrics_manager()

        try:
            # 解析皇冠投注信息
            bet_info = self.crown._parse_bet_info_from_data(option)
            if not bet_info:
                duration = time.time() - start_time
                metrics.record_execution(False, duration, "crown", match_key, "解析失败")
                return {"success": False, "platform": "crown", "message": "无法解析投注信息"}

            # 在执行投注前检查是否重复投注
            if self.crown.is_bet_duplicated(bet_info):
                duration = time.time() - start_time
                metrics.record_execution(False, duration, "crown", match_key, "重复投注", is_duplicate=True)
                logger.warning(f"皇冠平台检测到重复投注，跳过: {bet_info.get('home_team')} vs {bet_info.get('away_team')}, {bet_info.get('bet_type')}={bet_info.get('bet_team')}")
                return {
                    "success": False,
                    "platform": "crown",
                    "amount": amount,
                    "bet_info": bet_info,
                    "message": "检测到重复投注，已跳过",
                    "no_retry": True,  # 标记不需要重试
                    "is_duplicate": True  # 标记为重复投注，不保存记录
                }

            # 执行投注 - 传递金额参数
            logger.info(f"皇冠投注执行: amount={amount}, type={type(amount)}")
            success = self.crown.place_bet_simple(bet_info, amount)
            duration = time.time() - start_time

            # 记录指标
            error_type = None if success else "投注失败"
            metrics.record_execution(success, duration, "crown", match_key, error_type)

            return {
                "success": success,
                "platform": "crown",
                "amount": amount,
                "bet_info": bet_info,
                "message": "投注成功" if success else "投注失败"
            }
        except Exception as e:
            duration = time.time() - start_time
            metrics.record_execution(False, duration, "crown", match_key, f"异常: {type(e).__name__}")
            logger.error(f"皇冠投注异常: {e}")
            return {"success": False, "platform": "crown", "message": str(e)}

    async def _execute_with_retry(self, platform_func, option: Dict, amount: float,
                                 platform: str, max_retries: int = 3) -> Dict:
        """
        带智能重试的投注执行

        参数:
            platform_func: 平台投注函数
            option: 投注选项
            amount: 投注金额
            platform: 平台名称
            max_retries: 最大重试次数

        返回:
            投注结果
        """
        last_result = None

        for attempt in range(max_retries):
            try:
                result = await platform_func(option, amount)

                # 如果成功或明确标记不需要重试，直接返回
                if result.get("success") or result.get("no_retry"):
                    return result

                last_result = result
                error_msg = result.get("message", "")

                # 根据错误类型决定是否重试
                should_retry = self._should_retry_error(error_msg, attempt, max_retries)

                if not should_retry:
                    # 只在调试模式下记录详细的重试信息，避免重复日志
                    logger.debug(f"{platform}投注失败，不适合重试: {error_msg}")
                    break

                # 计算退避延迟
                delay = self._calculate_backoff_delay(attempt)
                logger.info(f"{platform}投注失败，{delay}秒后进行第{attempt + 2}次尝试: {error_msg}")
                await asyncio.sleep(delay)

            except Exception as e:
                logger.error(f"{platform}投注异常，尝试{attempt + 1}/{max_retries}: {e}")
                last_result = {"success": False, "platform": platform, "message": str(e)}

                if attempt == max_retries - 1:
                    break

                delay = self._calculate_backoff_delay(attempt)
                await asyncio.sleep(delay)

        # 返回最后一次的结果
        return last_result or {"success": False, "platform": platform, "message": "重试失败"}

    def _should_retry_error(self, error_msg: str, attempt: int, max_retries: int) -> bool:
        """判断错误是否应该重试"""
        # 不重试的错误类型
        no_retry_keywords = [
            "重复投注", "余额不足", "投注已关闭", "盘口已关闭",
            "无法解析", "参数错误", "账号被锁定"
        ]

        for keyword in no_retry_keywords:
            if keyword in error_msg:
                return False

        # 可重试的错误类型
        retry_keywords = [
            "网络", "超时", "连接", "服务器", "临时", "繁忙", "请稍后"
        ]

        for keyword in retry_keywords:
            if keyword in error_msg:
                return attempt < max_retries - 1

        # 默认重试一次
        return attempt < 1

    def _calculate_backoff_delay(self, attempt: int) -> float:
        """计算指数退避延迟"""
        base_delay = 1.0
        max_delay = 8.0
        delay = min(base_delay * (2 ** attempt), max_delay)
        return delay

    def _process_sequential_bet_results(self, base_result: Dict, hedge_result: Dict,
                                      base_platform: str) -> Dict:
        """
        处理顺序投注结果

        参数:
            base_result: 基准平台投注结果
            hedge_result: 对冲平台投注结果
            base_platform: 基准平台

        返回:
            处理后的结果
        """
        # 确保结果不为None
        base_result = base_result or {}
        hedge_result = hedge_result or {}

        base_success = base_result.get("success", False)
        hedge_success = hedge_result.get("success", False)
        hedge_platform = "crown" if base_platform == "pinbet" else "pinbet"

        if base_success and hedge_success:
            # 两个平台都成功
            logger.info("顺序对冲投注完全成功")
            return {
                "success": True,
                "message": "顺序对冲投注完全成功",
                "base_result": base_result,
                "hedge_result": hedge_result,
                "hedge_status": "complete",
                "execution_mode": "sequential"
            }
        elif base_success and not hedge_success:
            # 基准平台成功，对冲平台失败，需要补单
            logger.warning(f"基准平台({base_platform})成功，对冲平台({hedge_platform})失败，启动补单流程")

            # 启动补单流程
            self._handle_hedge_failure(base_result, hedge_platform,
                                     hedge_result.get("bet_info"),
                                     hedge_result.get("amount"))

            return {
                "success": False,
                "message": f"基准平台成功，对冲平台({hedge_platform})失败，已启动补单流程",
                "base_result": base_result,
                "hedge_result": hedge_result,
                "hedge_status": "hedge_failed_retry",
                "execution_mode": "sequential"
            }
        else:
            # 基准平台失败（这种情况在前面已经处理，这里是保险）
            logger.error("基准平台投注失败，对冲已取消")
            return {
                "success": False,
                "message": "基准平台投注失败，对冲已取消",
                "base_result": base_result,
                "hedge_result": hedge_result,
                "hedge_status": "cancelled",
                "execution_mode": "sequential"
            }

    def _handle_hedge_failure(self, base_result: Dict, failed_platform: str,
                            failed_option: Dict, failed_amount: float):
        """
        处理对冲平台投注失败的情况，启动补单流程

        参数:
            base_result: 基准平台投注结果
            failed_platform: 失败的平台名称
            failed_option: 失败的投注选项
            failed_amount: 失败的投注金额
        """
        logger.info(f"启动{failed_platform}平台补单流程")

        # 启动异步补单任务
        threading.Thread(
            target=self._retry_hedge_bet,
            args=(base_result, failed_platform, failed_option, failed_amount),
            daemon=True
        ).start()

    def _retry_hedge_bet(self, base_result: Dict, failed_platform: str,
                        failed_option: Dict, failed_amount: float):
        """
        重试对冲平台投注

        参数:
            base_result: 基准平台投注结果
            failed_platform: 失败的平台名称
            failed_option: 失败的投注选项
            failed_amount: 失败的投注金额
        """
        max_retries = self.config.get_retry_attempts()
        retry_delay = self.config.get_retry_delay()

        for attempt in range(max_retries):
            logger.info(f"尝试补单 {failed_platform}，第 {attempt + 1}/{max_retries} 次")

            try:
                success = False
                if failed_platform == "pinbet":
                    # 重试平博投注
                    bet_info = self.pinbet._parse_bet_info_from_data(failed_option)
                    if bet_info:
                        success = self.pinbet._execute_bet(bet_info, failed_amount)
                elif failed_platform == "crown":
                    # 重试皇冠投注
                    bet_info = self.crown._parse_bet_info_from_data(failed_option)
                    if bet_info:
                        success = self.crown.place_bet_simple(bet_info, failed_amount)

                if success:
                    logger.info(f"{failed_platform}补单成功，对冲完成")
                    return
                else:
                    logger.warning(f"{failed_platform}补单失败，第 {attempt + 1} 次尝试")

                # 如果重试失败，等待后继续
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)

            except Exception as e:
                logger.error(f"补单重试异常: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)

        logger.error(f"{failed_platform} 补单失败，已达到最大重试次数")
        logger.warning(f"注意：基准平台投注已成功，但{failed_platform}补单失败，存在单边风险")

    def _save_hedge_record(self, hedge_record: Dict, result: Dict):
        """
        保存对冲记录到持久化存储
        - 完全成功的记录保存到对冲记录
        - 单边风险记录保存到单边记录管理器
        - 完全失败和重复投注不保存

        参数:
            hedge_record: 对冲记录数据
            result: 执行结果
        """
        # 检查是否为重复投注导致的失败
        if not result.get("success"):
            # 检查基准平台和对冲平台的结果，确保不为None
            base_result = result.get("base_result") or {}
            hedge_result = result.get("hedge_result") or {}

            # 如果任一平台是重复投注，不保存记录
            if (base_result.get("is_duplicate") or hedge_result.get("is_duplicate") or
                "重复投注" in str(base_result.get("message", "")) or
                "重复投注" in str(hedge_result.get("message", ""))):
                logger.info("检测到重复投注，不保存任何记录")
                return

            # 检查是否有部分成功的投注（一个平台成功，另一个失败）
            base_success = base_result.get("success", False)
            hedge_success = hedge_result.get("success", False)

            if base_success or hedge_success:
                # 有部分成功，保存为单边风险记录
                logger.info(f"检测到单边风险，保存到单边记录: 基准={base_success}, 对冲={hedge_success}")
                self._save_partial_record(hedge_record, result)
                return
            else:
                # 完全失败，不保存记录
                logger.info("对冲投注完全失败，不保存记录")
                return

        # 完全成功的情况，保存到对冲记录
        logger.info("对冲投注完全成功，保存到对冲记录")
        self._save_success_record(hedge_record, result)

    def _save_success_record(self, hedge_record: Dict, result: Dict):
        """
        保存完全成功的对冲记录

        参数:
            hedge_record: 对冲记录数据
            result: 执行结果
        """
        if not self.record_manager:
            logger.warning("对冲记录管理器未初始化，无法保存记录")
            return

        try:
            # 提取投注类型信息
            bet_type = ""
            pinbet_option = hedge_record.get("pinbet_option", {})
            crown_option = hedge_record.get("crown_option", {})

            # 从任一选项中提取投注类型
            for option in [pinbet_option, crown_option]:
                if option:
                    wtype = option.get("wtype", "")
                    if wtype == "R":
                        bet_type = "让球"
                        break
                    elif wtype == "OU":
                        bet_type = "大小球"
                        break
                    elif wtype == "M":
                        bet_type = "独赢"
                        break
                    else:
                        bet_type = option.get("bet_type", option.get("gtype", ""))
                        if bet_type:
                            break

            # 构建保存数据 - 只保存完全成功的记录
            log_data = {
                "hedge_id": hedge_record.get("hedge_id"),
                "match_info": self._extract_match_info(hedge_record),
                "status": "completed",  # 完全成功状态
                "total_amount": hedge_record.get("amounts", {}).get("total_investment", 0),
                "expected_profit": self._calculate_expected_profit(hedge_record),
                "actual_profit": self._calculate_actual_profit(result),
                "start_time": hedge_record.get("timestamp"),
                "end_time": datetime.now().isoformat(),
                "execution_time": self._calculate_execution_time(hedge_record),
                "stage": "completed",
                "base_platform": hedge_record.get("base_platform"),
                "execution_mode": self.config.get_execution_mode(),
                "bet_type": bet_type
            }

            # 添加平博投注信息
            if hedge_record.get("pinbet_option"):
                pinbet_result = result.get("pinbet_result") or result.get("base_result") if hedge_record.get("base_platform") == "pinbet" else result.get("hedge_result")
                log_data["pinbet_bet"] = self._format_bet_info("pinbet", hedge_record, pinbet_result)

            # 添加皇冠投注信息
            if hedge_record.get("crown_option"):
                crown_result = result.get("crown_result") or result.get("base_result") if hedge_record.get("base_platform") == "crown" else result.get("hedge_result")
                log_data["crown_bet"] = self._format_bet_info("crown", hedge_record, crown_result)

            # 保存完全成功的对冲记录
            success = self.record_manager.save_hedge_log(log_data)
            if success:
                logger.info(f"完全成功对冲记录保存成功: {hedge_record.get('hedge_id')}")
            else:
                logger.warning(f"完全成功对冲记录保存失败: {hedge_record.get('hedge_id')}")

        except Exception as e:
            logger.error(f"保存完全成功对冲记录时出错: {e}")

    def _save_partial_record(self, hedge_record: Dict, result: Dict):
        """
        保存单边风险记录

        参数:
            hedge_record: 对冲记录数据
            result: 执行结果
        """
        if not self.partial_manager:
            logger.warning("单边记录管理器未初始化，无法保存记录")
            return

        try:
            # 分析哪个平台成功，哪个平台失败
            base_result = result.get("base_result") or {}
            hedge_result = result.get("hedge_result") or {}

            base_success = base_result.get("success", False)
            hedge_success = hedge_result.get("success", False)

            # 提取投注类型信息
            bet_type = ""
            pinbet_option = hedge_record.get("pinbet_option", {})
            crown_option = hedge_record.get("crown_option", {})

            for option in [pinbet_option, crown_option]:
                if option:
                    wtype = option.get("wtype", "")
                    if wtype == "R":
                        bet_type = "让球"
                        break
                    elif wtype == "OU":
                        bet_type = "大小球"
                        break
                    elif wtype == "M":
                        bet_type = "独赢"
                        break
                    else:
                        bet_type = option.get("bet_type", option.get("gtype", ""))
                        if bet_type:
                            break

            # 构建单边风险记录数据
            partial_data = {
                "hedge_id": hedge_record.get("hedge_id"),
                "match_info": self._extract_match_info(hedge_record),
                "status": "pending",  # 待补单状态
                "total_amount": hedge_record.get("amounts", {}).get("total_investment", 0),
                "expected_profit": self._calculate_expected_profit(hedge_record),
                "actual_profit": self._calculate_actual_profit(result),
                "start_time": hedge_record.get("timestamp"),
                "end_time": datetime.now().isoformat(),
                "execution_time": self._calculate_execution_time(hedge_record),
                "stage": "partial_risk",
                "base_platform": hedge_record.get("base_platform"),
                "execution_mode": self.config.get_execution_mode(),
                "bet_type": bet_type,
                "risk_warning": f"注意：基准平台投注已成功，但对冲平台失败，存在单边风险"
            }

            # 添加平博投注信息
            if hedge_record.get("pinbet_option"):
                pinbet_result = result.get("pinbet_result") or result.get("base_result") if hedge_record.get("base_platform") == "pinbet" else result.get("hedge_result")
                partial_data["pinbet_bet"] = self._format_bet_info("pinbet", hedge_record, pinbet_result)

            # 添加皇冠投注信息
            if hedge_record.get("crown_option"):
                crown_result = result.get("crown_result") or result.get("base_result") if hedge_record.get("base_platform") == "crown" else result.get("hedge_result")
                partial_data["crown_bet"] = self._format_bet_info("crown", hedge_record, crown_result)

            # 保存单边风险记录
            success = self.partial_manager.save_partial_record(partial_data)
            if success:
                logger.info(f"单边风险记录保存成功: {hedge_record.get('hedge_id')}")
            else:
                logger.warning(f"单边风险记录保存失败: {hedge_record.get('hedge_id')}")

        except Exception as e:
            logger.error(f"保存单边风险记录时出错: {e}")

    def _extract_match_info(self, hedge_record: Dict) -> str:
        """从对冲记录中提取比赛信息"""
        try:
            pinbet_option = hedge_record.get("pinbet_option", {})
            crown_option = hedge_record.get("crown_option", {})

            # 尝试从任一选项中获取比赛信息
            match_info = pinbet_option.get("match") or crown_option.get("match")
            if match_info:
                return match_info

            # 如果没有直接的比赛信息，尝试构建
            home_team = pinbet_option.get("home_team") or crown_option.get("home_team")
            away_team = pinbet_option.get("away_team") or crown_option.get("away_team")

            if home_team and away_team:
                return f"{home_team} vs {away_team}"

            return "未知比赛"
        except Exception:
            return "未知比赛"

    def _calculate_expected_profit(self, hedge_record: Dict) -> float:
        """计算预期盈利"""
        try:
            amounts = hedge_record.get("amounts", {})
            return amounts.get("expected_profit", 0.0)
        except Exception:
            return 0.0

    def _calculate_actual_profit(self, result: Dict) -> float:
        """计算实际盈利"""
        try:
            if not result.get("success", False):
                return 0.0

            # 这里可以根据实际的投注结果计算盈利
            # 目前返回预期盈利作为占位符
            return 0.0
        except Exception:
            return 0.0

    def _calculate_execution_time(self, hedge_record: Dict) -> str:
        """计算执行时间"""
        try:
            start_time_str = hedge_record.get("timestamp")
            if start_time_str:
                start_time = datetime.fromisoformat(start_time_str)
                end_time = datetime.now()
                execution_time = end_time - start_time
                return str(execution_time)
            return "未知"
        except Exception:
            return "未知"

    def _format_bet_info(self, platform: str, hedge_record: Dict, bet_result: Dict) -> Dict:
        """格式化投注信息"""
        try:
            option_key = f"{platform}_option"
            option = hedge_record.get(option_key, {})
            amounts = hedge_record.get("amounts", {})

            # 确定投注金额
            if hedge_record.get("base_platform") == platform:
                amount = amounts.get("base_amount", 0)
            else:
                amount = amounts.get("hedge_amount", 0)

            # 提取投注详情信息 - 增强字段映射和数据解析
            bet_type = option.get("bet_type", option.get("wtype", option.get("gtype", "")))
            team = option.get("team", option.get("chose_team", option.get("bet_team", "")))
            handicap_value = option.get("handicap", option.get("handicap_value", option.get("team_h_ratio", "")))

            # 从比赛信息中提取队伍名称
            match_info = option.get("match", option.get("event", ""))
            if not team and match_info:
                # 尝试从比赛信息中提取队伍名称
                if " vs " in match_info:
                    teams = match_info.split(" vs ")
                    if len(teams) >= 2:
                        # 根据选择确定队伍
                        selection = option.get("selection", option.get("chose_team", ""))
                        if "H" in selection or "主" in selection or "0" in selection:
                            team = teams[0].strip()
                        elif "C" in selection or "客" in selection or "1" in selection:
                            team = teams[1].strip()
                        else:
                            team = teams[0].strip()  # 默认主队
                elif " - " in match_info:
                    teams = match_info.split(" - ")
                    if len(teams) >= 2:
                        selection = option.get("selection", option.get("chose_team", ""))
                        if "H" in selection or "主" in selection or "0" in selection:
                            team = teams[0].strip()
                        elif "C" in selection or "客" in selection or "1" in selection:
                            team = teams[1].strip()
                        else:
                            team = teams[0].strip()

            # 增强投注类型识别 - 从betburger数据正确提取
            if not bet_type or bet_type == "未知":
                # 首先尝试从betburger的market_and_bet_type字段获取
                market_and_bet_type = option.get("market_and_bet_type", 0)
                try:
                    market_and_bet_type = int(market_and_bet_type)
                    if market_and_bet_type in [17, 18]:  # 让球盘口
                        bet_type = "让球"
                    elif market_and_bet_type in [19, 20]:  # 大小球盘口
                        bet_type = "大小球"
                    elif market_and_bet_type in [23, 24]:  # 主客队大小球
                        bet_type = "大小球"
                    elif market_and_bet_type in [1, 2]:  # 独赢
                        bet_type = "独赢"
                except (ValueError, TypeError):
                    pass

                # 如果还是未知，尝试从wtype转换
                if not bet_type or bet_type == "未知":
                    wtype = option.get("wtype", "")
                    if wtype == "R":
                        bet_type = "让球"
                    elif wtype == "OU":
                        bet_type = "大小球"
                    elif wtype == "M":
                        bet_type = "独赢"
                    elif wtype == "OUH":
                        bet_type = "主队大小球"
                    elif wtype == "OUC":
                        bet_type = "客队大小球"
                    else:
                        # 从其他字段获取
                        bet_type = option.get("type", option.get("market", ""))

                        # 如果还是空的，尝试从投注描述中推断
                        description = option.get("description", option.get("name", ""))
                        if "让球" in description or "handicap" in description.lower():
                            bet_type = "让球"
                        elif "大小" in description or "total" in description.lower() or "over" in description.lower() or "under" in description.lower():
                            bet_type = "大小球"
                        elif "独赢" in description or "moneyline" in description.lower() or "winner" in description.lower():
                            bet_type = "独赢"
                        else:
                            bet_type = "未知类型"

            # 增强队伍信息获取
            if not team:
                team = option.get("selection", option.get("side", option.get("choice", "")))

                # 如果还是空的，尝试从选择代码推断
                chose_team = option.get("chose_team", "")
                if chose_team == "H":
                    team = "主队"
                elif chose_team == "C":
                    team = "客队"
                elif chose_team == "O":
                    team = "大"
                elif chose_team == "U":
                    team = "小"

            # 增强盘口值获取 - 从betburger数据中正确提取
            if not handicap_value:
                # 首先尝试从betburger的标准字段获取
                handicap_value = option.get("market_and_bet_type_param", "")

                # 如果还是空的，尝试其他可能的字段
                if not handicap_value:
                    handicap_value = option.get("market_param", option.get("ratio", option.get("line", option.get("spread", option.get("con", "")))))

                # 最后尝试从其他可能的字段获取
                if not handicap_value:
                    handicap_value = option.get("handicap_line", option.get("point", option.get("total", "")))

            bet_info = {
                "platform": platform,
                "amount": amount,
                "odds": float(option.get("koef", option.get("odds", "0"))),
                "team": team,
                "bet_type": bet_type,
                "handicap_value": str(handicap_value),
                "bet_id": f"{platform}_hedge_{hedge_record.get('hedge_id', int(time.time()))}"
            }

            # 添加投注结果信息
            if bet_result:
                bet_info["success"] = bet_result.get("success", False)
                bet_info["message"] = bet_result.get("message", "")

            return bet_info
        except Exception as e:
            logger.error(f"格式化{platform}投注信息失败: {e}")
            return {}

    def _process_bet_results(self, pinbet_result: Dict, crown_result: Dict,
                           base_platform: str) -> Dict:
        """
        处理投注结果

        参数:
            pinbet_result: 平博投注结果
            crown_result: 皇冠投注结果
            base_platform: 基准平台

        返回:
            处理后的结果
        """
        pinbet_success = pinbet_result.get("success", False)
        crown_success = crown_result.get("success", False)

        if pinbet_success and crown_success:
            # 两个平台都成功
            logger.info("对冲投注完全成功")
            return {
                "success": True,
                "message": "对冲投注完全成功",
                "pinbet_result": pinbet_result,
                "crown_result": crown_result,
                "hedge_status": "complete"
            }
        elif pinbet_success or crown_success:
            # 只有一个平台成功，需要补单
            failed_platform = "crown" if pinbet_success else "pinbet"
            success_platform = "pinbet" if pinbet_success else "crown"

            logger.warning(f"部分投注成功，{success_platform}成功，{failed_platform}失败，需要补单")

            # 启动补单流程
            self._handle_partial_success(pinbet_result, crown_result, failed_platform)

            return {
                "success": False,
                "message": f"部分投注成功，{failed_platform}需要补单",
                "pinbet_result": pinbet_result,
                "crown_result": crown_result,
                "hedge_status": "partial",
                "failed_platform": failed_platform
            }
        else:
            # 两个平台都失败
            logger.error("对冲投注完全失败")
            return {
                "success": False,
                "message": "对冲投注完全失败",
                "pinbet_result": pinbet_result,
                "crown_result": crown_result,
                "hedge_status": "failed"
            }

    def _handle_partial_success(self, pinbet_result: Dict, crown_result: Dict,
                              failed_platform: str):
        """
        处理部分成功的情况，启动补单流程

        参数:
            pinbet_result: 平博投注结果
            crown_result: 皇冠投注结果
            failed_platform: 失败的平台
        """
        # 这里可以启动异步补单任务
        threading.Thread(
            target=self._retry_failed_bet,
            args=(pinbet_result, crown_result, failed_platform),
            daemon=True
        ).start()

    def _retry_failed_bet(self, pinbet_result: Dict, crown_result: Dict,
                         failed_platform: str):
        """
        重试失败的投注

        参数:
            pinbet_result: 平博投注结果
            crown_result: 皇冠投注结果
            failed_platform: 失败的平台
        """
        max_retries = self.config.get_retry_attempts()
        retry_delay = self.config.get_retry_delay()

        for attempt in range(max_retries):
            logger.info(f"尝试补单 {failed_platform}，第 {attempt + 1}/{max_retries} 次")

            try:
                if failed_platform == "pinbet":
                    # 重试平博投注
                    bet_info = pinbet_result.get("bet_info")
                    amount = pinbet_result.get("amount", 50)  # 获取投注金额，默认50
                    if bet_info:
                        success = self.pinbet._execute_bet(bet_info, amount)
                        if success:
                            logger.info("平博补单成功")
                            return
                elif failed_platform == "crown":
                    # 重试皇冠投注
                    bet_info = crown_result.get("bet_info")
                    amount = crown_result.get("amount", 50)  # 获取投注金额，默认50
                    if bet_info:
                        success = self.crown.place_bet_simple(bet_info, amount)
                        if success:
                            logger.info("皇冠补单成功")
                            return

                # 如果重试失败，等待后继续
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)

            except Exception as e:
                logger.error(f"补单重试异常: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)

        logger.error(f"{failed_platform} 补单失败，已达到最大重试次数")

    def execute_hedge_bet(self, arbitrage_data: List[Dict]) -> Dict:
        """
        同步执行对冲投注（对外接口）

        参数:
            arbitrage_data: 套利数据列表

        返回:
            投注结果字典
        """
        # 使用asyncio运行异步方法
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        return loop.run_until_complete(self.execute_hedge_bet_async(arbitrage_data))

    def get_hedge_status(self) -> Dict:
        """获取对冲状态信息"""
        metrics = get_metrics_manager()
        health_status = metrics.get_health_status()
        lock_manager = get_lock_manager()
        lock_status = lock_manager.get_lock_status()

        return {
            "active_hedges": len(self.active_hedges),
            "total_history": len(self.hedge_history),
            "config": self.config.get_all_config(),
            "health": health_status,
            "locks": lock_status,
            "metrics_summary": metrics.get_metrics_summary(time_window_hours=24).__dict__
        }

    def get_system_health(self) -> Dict:
        """获取系统健康状态"""
        metrics = get_metrics_manager()
        return metrics.get_health_status()

    def get_recent_errors(self, limit: int = 10) -> List[Dict]:
        """获取最近的错误记录"""
        metrics = get_metrics_manager()
        return metrics.get_recent_errors(limit)

    def get_latest_hedge_records(self, limit: int = 10) -> List[Dict]:
        """
        获取最新的对冲记录（只返回成功记录和需补单记录）

        参数:
            limit: 返回记录数量限制

        返回:
            最新的对冲记录列表
        """
        if not self.record_manager:
            logger.warning("对冲记录管理器未初始化")
            return []

        try:
            # 从记录管理器获取最新记录
            all_records = self.record_manager.records

            # 过滤记录：只保留成功记录和需补单记录
            filtered_records = []
            for record in all_records:
                status = getattr(record, 'status', '')
                if status in ['completed', 'partial_success', 'pending_manual', 'pending']:
                    filtered_records.append(record)

            # 按创建时间倒序排列，返回最新的记录
            sorted_records = sorted(filtered_records,
                                  key=lambda x: x.created_at,
                                  reverse=True)

            # 转换为字典格式并限制数量
            latest_records = []
            for record in sorted_records[:limit]:
                latest_records.append(record.to_dict())

            return latest_records

        except Exception as e:
            logger.error(f"获取最新对冲记录失败: {e}")
            return []

    def get_hedge_record_by_id(self, hedge_id: str) -> Optional[Dict]:
        """
        根据ID获取对冲记录

        参数:
            hedge_id: 对冲记录ID

        返回:
            对冲记录字典或None
        """
        if not self.record_manager:
            logger.warning("对冲记录管理器未初始化")
            return None

        try:
            record = self.record_manager.get_record_by_id(hedge_id)
            return record.to_dict() if record else None
        except Exception as e:
            logger.error(f"获取对冲记录失败: {e}")
            return None

    def get_hedge_statistics(self, hours: int = 24) -> Dict:
        """
        获取对冲统计信息

        参数:
            hours: 统计时间范围（小时）

        返回:
            统计信息字典
        """
        if not self.record_manager:
            logger.warning("对冲记录管理器未初始化")
            return {}

        try:
            return self.record_manager.get_hedge_statistics(hours)
        except Exception as e:
            logger.error(f"获取对冲统计信息失败: {e}")
            return {}

    def save_manual_hedge_record(self, match_info: str, pinbet_bet: Dict = None,
                                crown_bet: Dict = None, status: str = "completed") -> bool:
        """
        手动保存对冲记录（用于补录或测试）

        参数:
            match_info: 比赛信息
            pinbet_bet: 平博投注信息
            crown_bet: 皇冠投注信息
            status: 记录状态

        返回:
            是否保存成功
        """
        if not self.record_manager:
            logger.warning("对冲记录管理器未初始化")
            return False

        try:
            log_data = {
                "match_info": match_info,
                "status": status,
                "total_amount": (pinbet_bet.get("amount", 0) if pinbet_bet else 0) +
                               (crown_bet.get("amount", 0) if crown_bet else 0),
                "expected_profit": 0,
                "actual_profit": 0,
                "start_time": datetime.now().isoformat(),
                "end_time": datetime.now().isoformat(),
                "stage": "手动录入",
                "pinbet_bet": pinbet_bet,
                "crown_bet": crown_bet
            }

            return self.record_manager.save_hedge_log(log_data)
        except Exception as e:
            logger.error(f"手动保存对冲记录失败: {e}")
            return False
