#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HAR分析工具启动脚本
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def main():
    """主函数"""
    print("启动HAR文件分析工具...")
    
    try:
        from tools.har_analyzer_gui import HARAnalyzerGUI
        
        print("✅ 成功导入GUI模块")
        
        # 检查拖拽支持
        try:
            import tkinterdnd2
            print("✅ 支持拖拽功能")
        except ImportError:
            print("⚠️  未安装tkinterdnd2，拖拽功能不可用")
            print("   可以通过 pip install tkinterdnd2 安装")
        
        # 启动GUI
        app = HARAnalyzerGUI()
        print("🚀 启动GUI界面...")
        app.run()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保在项目根目录运行此脚本")
        return 1
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
