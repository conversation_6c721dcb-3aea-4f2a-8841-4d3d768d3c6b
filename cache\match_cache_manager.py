#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
比赛缓存管理器
实现比赛ID映射的缓存管理，提高对冲匹配效率
"""

import json
import os
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from threading import Lock

logger = logging.getLogger(__name__)

@dataclass
class MatchMapping:
    """比赛映射信息"""
    pinbet_id: str                    # 平博比赛ID
    crown_id: str                     # 皇冠比赛ID
    match_info: Dict[str, Any]        # 标准化比赛信息
    confidence_score: float           # 匹配置信度
    created_time: datetime            # 创建时间
    last_verified: datetime           # 最后验证时间
    verification_count: int = 0       # 验证次数
    is_active: bool = True           # 是否活跃
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        data = asdict(self)
        # 转换datetime为字符串
        data['created_time'] = self.created_time.isoformat()
        data['last_verified'] = self.last_verified.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'MatchMapping':
        """从字典创建实例"""
        # 转换字符串为datetime
        data['created_time'] = datetime.fromisoformat(data['created_time'])
        data['last_verified'] = datetime.fromisoformat(data['last_verified'])
        return cls(**data)

@dataclass
class CacheStats:
    """缓存统计信息"""
    total_mappings: int = 0
    active_mappings: int = 0
    today_mappings: int = 0
    cache_hit_rate: float = 0.0
    last_update_time: Optional[datetime] = None
    
class MatchCacheManager:
    """比赛缓存管理器"""
    
    def __init__(self, cache_dir: str = "cache"):
        """
        初始化缓存管理器
        
        参数:
            cache_dir: 缓存目录
        """
        self.cache_dir = cache_dir
        self.cache_file = os.path.join(cache_dir, "match_mappings.json")
        self.stats_file = os.path.join(cache_dir, "cache_stats.json")
        self.lock = Lock()
        
        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)
        
        # 加载缓存数据
        self.mappings: Dict[str, MatchMapping] = {}
        self.stats = CacheStats()
        self._load_cache()
        
        logger.info(f"比赛缓存管理器初始化完成，加载了 {len(self.mappings)} 个映射")
    
    def add_mapping(self, pinbet_id: str, crown_id: str, match_info: Dict, 
                   confidence_score: float) -> bool:
        """
        添加比赛映射
        
        参数:
            pinbet_id: 平博比赛ID
            crown_id: 皇冠比赛ID
            match_info: 比赛信息
            confidence_score: 匹配置信度
        
        返回:
            是否添加成功
        """
        try:
            with self.lock:
                # 生成映射键
                mapping_key = f"{pinbet_id}_{crown_id}"
                
                # 检查是否已存在
                if mapping_key in self.mappings:
                    logger.debug(f"映射已存在，更新置信度: {mapping_key}")
                    existing = self.mappings[mapping_key]
                    existing.confidence_score = max(existing.confidence_score, confidence_score)
                    existing.last_verified = datetime.now()
                    existing.verification_count += 1
                else:
                    # 创建新映射
                    now = datetime.now()
                    mapping = MatchMapping(
                        pinbet_id=pinbet_id,
                        crown_id=crown_id,
                        match_info=match_info,
                        confidence_score=confidence_score,
                        created_time=now,
                        last_verified=now,
                        verification_count=1
                    )
                    self.mappings[mapping_key] = mapping
                    logger.info(f"添加新的比赛映射: {mapping_key}, 置信度: {confidence_score:.3f}")
                
                # 保存到文件
                self._save_cache()
                self._update_stats()
                
                return True
                
        except Exception as e:
            logger.error(f"添加比赛映射失败: {e}")
            return False
    
    def find_mapping_by_pinbet_id(self, pinbet_id: str) -> Optional[MatchMapping]:
        """
        通过平博ID查找映射
        
        参数:
            pinbet_id: 平博比赛ID
        
        返回:
            匹配的映射，如果未找到返回None
        """
        try:
            with self.lock:
                for mapping in self.mappings.values():
                    if mapping.pinbet_id == pinbet_id and mapping.is_active:
                        # 更新最后验证时间
                        mapping.last_verified = datetime.now()
                        mapping.verification_count += 1
                        logger.debug(f"缓存命中: 平博ID {pinbet_id} -> 皇冠ID {mapping.crown_id}")
                        return mapping
                
                logger.debug(f"缓存未命中: 平博ID {pinbet_id}")
                return None
                
        except Exception as e:
            logger.error(f"查找映射失败: {e}")
            return None
    
    def find_mapping_by_crown_id(self, crown_id: str) -> Optional[MatchMapping]:
        """
        通过皇冠ID查找映射
        
        参数:
            crown_id: 皇冠比赛ID
        
        返回:
            匹配的映射，如果未找到返回None
        """
        try:
            with self.lock:
                for mapping in self.mappings.values():
                    if mapping.crown_id == crown_id and mapping.is_active:
                        # 更新最后验证时间
                        mapping.last_verified = datetime.now()
                        mapping.verification_count += 1
                        logger.debug(f"缓存命中: 皇冠ID {crown_id} -> 平博ID {mapping.pinbet_id}")
                        return mapping
                
                logger.debug(f"缓存未命中: 皇冠ID {crown_id}")
                return None
                
        except Exception as e:
            logger.error(f"查找映射失败: {e}")
            return None
    
    def get_all_active_mappings(self) -> List[MatchMapping]:
        """获取所有活跃的映射"""
        try:
            with self.lock:
                return [mapping for mapping in self.mappings.values() if mapping.is_active]
        except Exception as e:
            logger.error(f"获取活跃映射失败: {e}")
            return []
    
    def cleanup_expired_mappings(self, max_age_hours: int = 24) -> int:
        """
        清理过期的映射
        
        参数:
            max_age_hours: 最大保留时间（小时）
        
        返回:
            清理的映射数量
        """
        try:
            with self.lock:
                cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
                expired_keys = []
                
                for key, mapping in self.mappings.items():
                    if mapping.last_verified < cutoff_time:
                        expired_keys.append(key)
                        mapping.is_active = False
                
                # 移除过期映射
                for key in expired_keys:
                    del self.mappings[key]
                
                if expired_keys:
                    logger.info(f"清理了 {len(expired_keys)} 个过期映射")
                    self._save_cache()
                    self._update_stats()
                
                return len(expired_keys)
                
        except Exception as e:
            logger.error(f"清理过期映射失败: {e}")
            return 0
    
    def get_cache_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        try:
            with self.lock:
                self._update_stats()
                return self.stats
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return CacheStats()
    
    def clear_cache(self) -> bool:
        """清空缓存"""
        try:
            with self.lock:
                self.mappings.clear()
                self._save_cache()
                self._update_stats()
                logger.info("缓存已清空")
                return True
        except Exception as e:
            logger.error(f"清空缓存失败: {e}")
            return False
    
    def _load_cache(self):
        """加载缓存数据"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 转换为MatchMapping对象
                for key, mapping_data in data.items():
                    try:
                        mapping = MatchMapping.from_dict(mapping_data)
                        self.mappings[key] = mapping
                    except Exception as e:
                        logger.warning(f"加载映射 {key} 失败: {e}")
                
                logger.info(f"从缓存文件加载了 {len(self.mappings)} 个映射")
            
            # 加载统计信息
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    stats_data = json.load(f)
                    if 'last_update_time' in stats_data and stats_data['last_update_time']:
                        stats_data['last_update_time'] = datetime.fromisoformat(stats_data['last_update_time'])
                    else:
                        stats_data['last_update_time'] = None
                    self.stats = CacheStats(**stats_data)
            
        except Exception as e:
            logger.error(f"加载缓存失败: {e}")
            self.mappings = {}
            self.stats = CacheStats()
    
    def _save_cache(self):
        """保存缓存数据"""
        try:
            # 保存映射数据
            cache_data = {}
            for key, mapping in self.mappings.items():
                cache_data[key] = mapping.to_dict()
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            # 保存统计信息
            stats_data = asdict(self.stats)
            if stats_data['last_update_time']:
                stats_data['last_update_time'] = stats_data['last_update_time'].isoformat()
            
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats_data, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"保存缓存失败: {e}")
    
    def _update_stats(self):
        """更新统计信息"""
        try:
            active_mappings = [m for m in self.mappings.values() if m.is_active]
            today = datetime.now().date()
            today_mappings = [m for m in active_mappings if m.created_time.date() == today]
            
            self.stats.total_mappings = len(self.mappings)
            self.stats.active_mappings = len(active_mappings)
            self.stats.today_mappings = len(today_mappings)
            self.stats.last_update_time = datetime.now()
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
