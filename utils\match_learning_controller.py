#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

from utils.match_data_fetcher import MatchDataFetcher
from utils.match_learning_enhanced import MatchLearningEnhanced
from utils.alias_manager import AliasManager

logger = logging.getLogger(__name__)

class MatchLearningController:
    """比赛匹配学习系统控制器"""
    
    def __init__(self, config_dir: str = "config", data_dir: str = "data"):
        """
        初始化学习控制器
        
        参数:
            config_dir: 配置目录
            data_dir: 数据目录
        """
        self.config_dir = config_dir
        self.data_dir = data_dir
        
        # 初始化组件
        self.data_fetcher = MatchDataFetcher()
        self.learning_system = MatchLearningEnhanced()
        self.alias_manager = AliasManager(config_dir, data_dir)
        
        # 状态标志
        self.is_initialized = False
        self.last_data_fetch = None
        self.auto_save_enabled = True
        
        # 线程锁
        self._lock = threading.Lock()
        
        logger.info("比赛匹配学习控制器初始化完成")
    
    def initialize_system(self, pinbet_system=None, crown_system=None, 
                         force_refresh: bool = False) -> bool:
        """
        初始化学习系统
        
        参数:
            pinbet_system: 平博系统实例
            crown_system: 皇冠系统实例
            force_refresh: 是否强制刷新数据
            
        返回:
            是否初始化成功
        """
        try:
            logger.info("开始初始化比赛匹配学习系统")
            
            with self._lock:
                # 检查是否需要获取新数据
                need_fetch = force_refresh or self._should_fetch_new_data()
                
                if need_fetch:
                    logger.info("需要获取新的平台数据")
                    
                    # 获取平台数据
                    if not self.data_fetcher.fetch_all_platform_data(pinbet_system, crown_system):
                        logger.warning("获取平台数据失败，尝试加载缓存数据")
                        if not self.data_fetcher.load_cached_data():
                            logger.error("加载缓存数据也失败")
                            return False
                    else:
                        self.last_data_fetch = datetime.now()
                        logger.info("平台数据获取成功")
                else:
                    logger.info("使用缓存的平台数据")
                    if not self.data_fetcher.load_cached_data():
                        logger.warning("加载缓存数据失败，尝试获取新数据")
                        if not self.data_fetcher.fetch_all_platform_data(pinbet_system, crown_system):
                            logger.error("获取新数据也失败")
                            return False
                
                self.is_initialized = True
                logger.info("比赛匹配学习系统初始化完成")
                
                # 显示初始化摘要
                self._log_initialization_summary()
                
                return True
                
        except Exception as e:
            logger.error(f"初始化学习系统失败: {e}")
            return False
    
    def _should_fetch_new_data(self) -> bool:
        """检查是否需要获取新数据"""
        try:
            # 如果从未获取过数据
            if self.last_data_fetch is None:
                return True
            
            # 如果距离上次获取超过6小时
            time_threshold = timedelta(hours=6)
            if datetime.now() - self.last_data_fetch > time_threshold:
                return True
            
            # 检查缓存文件是否存在
            cache_file = os.path.join(self.data_fetcher.cache_dir, "platform_matches.json")
            if not os.path.exists(cache_file):
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查数据获取需求失败: {e}")
            return True
    
    def handle_match_failure(self, arbitrage_data: Dict, target_platform: str, 
                           failure_reason: str = "匹配失败") -> bool:
        """
        处理匹配失败事件
        
        参数:
            arbitrage_data: 套利数据
            target_platform: 目标平台
            failure_reason: 失败原因
            
        返回:
            是否处理成功
        """
        try:
            if not self.is_initialized:
                logger.warning("学习系统未初始化，无法处理匹配失败")
                return False
            
            logger.info(f"处理匹配失败: {target_platform} - {failure_reason}")
            
            # 记录失败并尝试学习
            failure_id = self.learning_system.record_match_failure(
                arbitrage_data, target_platform, failure_reason
            )
            
            if failure_id:
                logger.info(f"匹配失败已记录: {failure_id}")
                
                # 如果启用自动保存，尝试更新别名配置
                if self.auto_save_enabled:
                    self._try_auto_save_aliases()
                
                return True
            else:
                logger.error("记录匹配失败失败")
                return False
                
        except Exception as e:
            logger.error(f"处理匹配失败时出错: {e}")
            return False
    
    def _try_auto_save_aliases(self):
        """尝试自动保存学习到的别名"""
        try:
            # 获取学习到的别名
            learned_aliases = self.learning_system.get_learned_aliases_for_team_aliases()
            
            if learned_aliases:
                # 合并到现有配置
                merge_stats = self.alias_manager.merge_learned_aliases(learned_aliases)
                
                if merge_stats.get("new_aliases", 0) > 0:
                    # 保存配置
                    if self.alias_manager.save_aliases():
                        logger.info(f"自动保存别名成功: {merge_stats}")
                    else:
                        logger.error("自动保存别名失败")
                else:
                    logger.debug("没有新的别名需要保存")
            
        except Exception as e:
            logger.error(f"自动保存别名时出错: {e}")
    
    def manual_save_aliases(self) -> bool:
        """手动保存学习到的别名"""
        try:
            logger.info("开始手动保存学习别名")
            
            # 获取学习到的别名
            learned_aliases = self.learning_system.get_learned_aliases_for_team_aliases()
            
            if not learned_aliases:
                logger.info("没有学习到的别名需要保存")
                return True
            
            # 合并到现有配置
            merge_stats = self.alias_manager.merge_learned_aliases(learned_aliases)
            
            # 保存配置
            if self.alias_manager.save_aliases():
                logger.info(f"手动保存别名成功: {merge_stats}")
                return True
            else:
                logger.error("手动保存别名失败")
                return False
                
        except Exception as e:
            logger.error(f"手动保存别名时出错: {e}")
            return False
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        try:
            # 获取学习摘要
            learning_summary = self.learning_system.get_learning_summary()
            
            # 获取别名摘要
            alias_summary = self.alias_manager.get_aliases_summary()
            
            # 获取数据获取状态
            platform_data_status = {}
            for platform in ["pinbet", "crown"]:
                matches = self.data_fetcher.get_platform_matches(platform)
                platform_data_status[platform] = {
                    "total_matches": len(matches),
                    "today_matches": len(self.data_fetcher.get_platform_matches(platform, "today")),
                    "early_matches": len(self.data_fetcher.get_platform_matches(platform, "early"))
                }
            
            return {
                "initialized": self.is_initialized,
                "last_data_fetch": self.last_data_fetch.isoformat() if self.last_data_fetch else None,
                "auto_save_enabled": self.auto_save_enabled,
                "learning_summary": learning_summary,
                "alias_summary": alias_summary,
                "platform_data": platform_data_status
            }
            
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {"error": str(e)}
    
    def _log_initialization_summary(self):
        """记录初始化摘要"""
        try:
            status = self.get_system_status()
            
            logger.info("=== 比赛匹配学习系统初始化摘要 ===")
            logger.info(f"系统状态: {'已初始化' if status['initialized'] else '未初始化'}")
            
            if status.get('platform_data'):
                for platform, data in status['platform_data'].items():
                    logger.info(f"{platform}平台: 总计{data['total_matches']}场比赛 "
                              f"(今日{data['today_matches']}场, 早场{data['early_matches']}场)")
            
            if status.get('alias_summary'):
                alias_info = status['alias_summary']
                logger.info(f"当前别名配置: {alias_info['total_leagues']}个联赛, "
                          f"{alias_info['total_teams']}支队伍, "
                          f"{alias_info['total_aliases']}个别名")
            
            if status.get('learning_summary'):
                learning_info = status['learning_summary']
                if learning_info.get('learning_stats'):
                    stats = learning_info['learning_stats']
                    logger.info(f"学习统计: 总失败{stats.get('total_failures', 0)}次, "
                              f"成功学习{stats.get('successful_learning', 0)}次, "
                              f"学习率{stats.get('learning_rate', 0):.1%}")
            
            logger.info("=== 初始化摘要结束 ===")
            
        except Exception as e:
            logger.error(f"记录初始化摘要失败: {e}")
    
    def refresh_platform_data(self, pinbet_system=None, crown_system=None) -> bool:
        """刷新平台数据"""
        try:
            logger.info("开始刷新平台数据")
            
            success = self.data_fetcher.fetch_all_platform_data(pinbet_system, crown_system)
            
            if success:
                self.last_data_fetch = datetime.now()
                logger.info("平台数据刷新成功")
            else:
                logger.error("平台数据刷新失败")
            
            return success
            
        except Exception as e:
            logger.error(f"刷新平台数据时出错: {e}")
            return False
    
    def set_auto_save(self, enabled: bool):
        """设置自动保存状态"""
        self.auto_save_enabled = enabled
        logger.info(f"自动保存别名: {'启用' if enabled else '禁用'}")
    
    def export_learning_data(self, export_dir: str) -> bool:
        """
        导出学习数据
        
        参数:
            export_dir: 导出目录
            
        返回:
            是否导出成功
        """
        try:
            os.makedirs(export_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 导出别名配置
            alias_file = os.path.join(export_dir, f"aliases_export_{timestamp}.json")
            if not self.alias_manager.export_aliases(alias_file):
                return False
            
            # 导出系统状态
            status_file = os.path.join(export_dir, f"system_status_{timestamp}.json")
            status = self.get_system_status()
            
            import json
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status, f, ensure_ascii=False, indent=2)
            
            logger.info(f"学习数据已导出到: {export_dir}")
            return True
            
        except Exception as e:
            logger.error(f"导出学习数据失败: {e}")
            return False
    
    def cleanup_old_data(self, days_to_keep: int = 30) -> bool:
        """
        清理旧数据
        
        参数:
            days_to_keep: 保留天数
            
        返回:
            是否清理成功
        """
        try:
            logger.info(f"开始清理{days_to_keep}天前的旧数据")
            
            cutoff_time = datetime.now() - timedelta(days=days_to_keep)
            
            # 清理失败记录文件中的旧记录
            failures_file = self.learning_system.failures_file
            if os.path.exists(failures_file):
                # 读取所有记录
                valid_records = []
                
                with open(failures_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            import json
                            record = json.loads(line.strip())
                            record_time = datetime.fromisoformat(record.get('timestamp', ''))
                            
                            if record_time > cutoff_time:
                                valid_records.append(line.strip())
                        except:
                            continue
                
                # 重写文件
                with open(failures_file, 'w', encoding='utf-8') as f:
                    for record in valid_records:
                        f.write(record + '\n')
                
                logger.info(f"清理失败记录完成，保留{len(valid_records)}条记录")
            
            return True
            
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
            return False
