#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
异步操作管理器 - 遵循SOLID原则的异步处理优化

功能：
1. 统一的线程池管理
2. 操作进度反馈
3. 异常处理和重试机制
4. 资源自动清理
"""

import threading
import queue
import time
import logging
from concurrent.futures import ThreadPoolExecutor, Future
from typing import Callable, Optional, Any, Dict
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class OperationStatus(Enum):
    """操作状态枚举"""
    PENDING = "pending"
    RUNNING = "running" 
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class OperationResult:
    """操作结果数据类"""
    operation_id: str
    status: OperationStatus
    result: Any = None
    error: Optional[Exception] = None
    start_time: float = 0
    end_time: float = 0
    
    @property
    def duration(self) -> float:
        """获取操作耗时"""
        if self.end_time > 0:
            return self.end_time - self.start_time
        return time.time() - self.start_time


class AsyncOperationManager:
    """异步操作管理器 - 单一职责原则"""
    
    def __init__(self, max_workers: int = 4, max_queue_size: int = 100):
        """
        初始化异步操作管理器
        
        Args:
            max_workers: 最大工作线程数
            max_queue_size: 最大队列大小
        """
        self.max_workers = max_workers
        self.max_queue_size = max_queue_size
        
        # 线程池
        self.executor = ThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="AsyncOp"
        )
        
        # 操作跟踪
        self.operations: Dict[str, OperationResult] = {}
        self.operation_counter = 0
        self.lock = threading.RLock()
        
        # 结果队列（用于GUI通信）
        self.result_queue = queue.Queue(maxsize=max_queue_size)
        
        # 清理线程
        self.cleanup_thread = threading.Thread(
            target=self._cleanup_worker,
            daemon=True,
            name="AsyncCleanup"
        )
        self.cleanup_thread.start()
        
        logger.info(f"异步操作管理器已启动，最大工作线程数: {max_workers}")
    
    def submit_operation(self, 
                        func: Callable,
                        callback: Optional[Callable] = None,
                        error_callback: Optional[Callable] = None,
                        operation_name: str = "未知操作",
                        timeout: Optional[float] = None,
                        retry_count: int = 0,
                        **kwargs) -> str:
        """
        提交异步操作
        
        Args:
            func: 要执行的函数
            callback: 成功回调函数
            error_callback: 错误回调函数
            operation_name: 操作名称
            timeout: 超时时间（秒）
            retry_count: 重试次数
            **kwargs: 传递给func的参数
            
        Returns:
            操作ID
        """
        with self.lock:
            self.operation_counter += 1
            operation_id = f"op_{self.operation_counter}_{int(time.time())}"
        
        # 创建操作结果对象
        operation_result = OperationResult(
            operation_id=operation_id,
            status=OperationStatus.PENDING,
            start_time=time.time()
        )
        
        self.operations[operation_id] = operation_result
        
        # 包装执行函数
        def wrapped_execution():
            return self._execute_with_retry(
                operation_id, func, retry_count, **kwargs
            )
        
        # 提交到线程池
        future = self.executor.submit(wrapped_execution)
        
        # 添加完成回调
        future.add_done_callback(
            lambda f: self._handle_completion(
                operation_id, f, callback, error_callback, operation_name
            )
        )
        
        logger.debug(f"已提交异步操作: {operation_name} (ID: {operation_id})")
        return operation_id
    
    def _execute_with_retry(self, operation_id: str, func: Callable, 
                           retry_count: int, **kwargs) -> Any:
        """执行函数并处理重试逻辑"""
        operation = self.operations[operation_id]
        operation.status = OperationStatus.RUNNING
        
        last_exception = None
        
        for attempt in range(retry_count + 1):
            try:
                if attempt > 0:
                    logger.info(f"操作 {operation_id} 第 {attempt} 次重试")
                    time.sleep(min(2 ** attempt, 10))  # 指数退避
                
                result = func(**kwargs)
                operation.status = OperationStatus.SUCCESS
                operation.result = result
                return result
                
            except Exception as e:
                last_exception = e
                logger.warning(f"操作 {operation_id} 第 {attempt + 1} 次尝试失败: {e}")
                
                if attempt == retry_count:
                    operation.status = OperationStatus.FAILED
                    operation.error = e
                    raise e
        
        return None
    
    def _handle_completion(self, operation_id: str, future: Future,
                          callback: Optional[Callable],
                          error_callback: Optional[Callable],
                          operation_name: str):
        """处理操作完成"""
        operation = self.operations.get(operation_id)
        if not operation:
            return
        
        operation.end_time = time.time()
        
        try:
            result = future.result()
            
            # 放入结果队列供GUI处理
            try:
                self.result_queue.put_nowait({
                    'operation_id': operation_id,
                    'operation_name': operation_name,
                    'status': 'success',
                    'result': result,
                    'duration': operation.duration
                })
            except queue.Full:
                logger.warning(f"结果队列已满，丢弃操作结果: {operation_id}")
            
            # 执行成功回调
            if callback:
                try:
                    callback(result)
                except Exception as e:
                    logger.error(f"成功回调执行失败: {e}")
            
            logger.info(f"异步操作完成: {operation_name} "
                       f"(耗时: {operation.duration:.2f}秒)")
                       
        except Exception as e:
            # 放入错误结果
            try:
                self.result_queue.put_nowait({
                    'operation_id': operation_id,
                    'operation_name': operation_name,
                    'status': 'error',
                    'error': str(e),
                    'duration': operation.duration
                })
            except queue.Full:
                logger.warning(f"结果队列已满，丢弃错误结果: {operation_id}")
            
            # 执行错误回调
            if error_callback:
                try:
                    error_callback(e)
                except Exception as callback_error:
                    logger.error(f"错误回调执行失败: {callback_error}")
            
            logger.error(f"异步操作失败: {operation_name} - {e} "
                        f"(耗时: {operation.duration:.2f}秒)")
    
    def get_operation_status(self, operation_id: str) -> Optional[OperationResult]:
        """获取操作状态"""
        return self.operations.get(operation_id)
    
    def cancel_operation(self, operation_id: str) -> bool:
        """取消操作（尽力而为）"""
        operation = self.operations.get(operation_id)
        if operation and operation.status in [OperationStatus.PENDING, OperationStatus.RUNNING]:
            operation.status = OperationStatus.CANCELLED
            logger.info(f"已标记操作为取消: {operation_id}")
            return True
        return False
    
    def get_result(self, timeout: Optional[float] = None) -> Optional[Dict]:
        """获取操作结果（阻塞）"""
        try:
            return self.result_queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def get_result_nowait(self) -> Optional[Dict]:
        """获取操作结果（非阻塞）"""
        try:
            return self.result_queue.get_nowait()
        except queue.Empty:
            return None
    
    def _cleanup_worker(self):
        """清理工作线程 - 定期清理过期操作"""
        while True:
            try:
                time.sleep(300)  # 每5分钟清理一次
                
                current_time = time.time()
                expired_operations = []
                
                with self.lock:
                    for op_id, operation in self.operations.items():
                        # 清理1小时前的已完成操作
                        if (operation.status in [OperationStatus.SUCCESS, 
                                               OperationStatus.FAILED, 
                                               OperationStatus.CANCELLED] and
                            current_time - operation.end_time > 3600):
                            expired_operations.append(op_id)
                    
                    # 删除过期操作
                    for op_id in expired_operations:
                        del self.operations[op_id]
                
                if expired_operations:
                    logger.debug(f"清理了 {len(expired_operations)} 个过期操作")
                    
            except Exception as e:
                logger.error(f"清理工作线程异常: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            stats = {
                'total_operations': len(self.operations),
                'pending': sum(1 for op in self.operations.values() 
                              if op.status == OperationStatus.PENDING),
                'running': sum(1 for op in self.operations.values() 
                              if op.status == OperationStatus.RUNNING),
                'success': sum(1 for op in self.operations.values() 
                              if op.status == OperationStatus.SUCCESS),
                'failed': sum(1 for op in self.operations.values() 
                             if op.status == OperationStatus.FAILED),
                'cancelled': sum(1 for op in self.operations.values() 
                                if op.status == OperationStatus.CANCELLED),
                'queue_size': self.result_queue.qsize(),
                'max_workers': self.max_workers
            }
        return stats
    
    def shutdown(self, wait: bool = True):
        """关闭异步操作管理器"""
        logger.info("正在关闭异步操作管理器...")
        self.executor.shutdown(wait=wait)
        logger.info("异步操作管理器已关闭")


# 全局单例实例
_async_manager = None


def get_async_manager() -> AsyncOperationManager:
    """获取全局异步操作管理器实例"""
    global _async_manager
    if _async_manager is None:
        _async_manager = AsyncOperationManager()
    return _async_manager


def shutdown_async_manager():
    """关闭全局异步操作管理器"""
    global _async_manager
    if _async_manager:
        _async_manager.shutdown()
        _async_manager = None