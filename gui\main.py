#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
对冲投注系统 - Tkinter GUI

"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
import sys
import os
import json
import logging
from datetime import datetime, timedelta
import queue
import time
import requests
import subprocess
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # 导入现有系统
    from hedge_main import HedgeBettingSystem
    from utils.utils import load_config
    print("✅ 成功导入现有系统模块")
    
    # 导入优化管理器
    try:
        from gui.async_manager import get_async_manager, shutdown_async_manager
        from gui.memory_manager import get_memory_manager, cleanup_memory_manager
        OPTIMIZATION_AVAILABLE = True
        print("✅ 优化管理器导入成功")
    except ImportError as e:
        print(f"⚠️ 优化管理器导入失败: {e}")
        OPTIMIZATION_AVAILABLE = False
    SYSTEM_AVAILABLE = True
except ImportError as e:
    print(f"❌ 导入系统模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    HedgeBettingSystem = None
    SYSTEM_AVAILABLE = False

def setup_gui_logging():
    """设置GUI程序的日志配置"""
    # 创建日志目录
    today_str = datetime.now().strftime("%Y-%m-%d")
    log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
    os.makedirs(log_dir, exist_ok=True)

    # 生成带时间戳的日志文件名
    log_file = os.path.join(log_dir, f'gui_{today_str}.log')

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ],
        force=True  # 强制重新配置，覆盖已有配置
    )

    logger = logging.getLogger(__name__)
    logger.info(f"GUI日志将保存到: {log_file}")
    return log_file, logger



class BetBurgerGUI:
    """BetBurger对冲投注系统GUI主类 - 完全复刻Web版本"""
    
    def __init__(self):
        # 初始化日志系统
        self.log_file, self.logger = setup_gui_logging()

        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.create_notebook()
        self.setup_pages()

        # 系统相关
        self.hedge_system = None
        self.monitoring_thread = None
        self.config_data = {}

        # 检查系统可用性
        if not SYSTEM_AVAILABLE:
            self.log_message("系统模块导入失败，部分功能不可用", "WARNING")

        # 消息队列（用于线程间通信）
        self.message_queue = queue.Queue()

        # 启动消息处理
        self.process_messages()

        # 对冲记录自动刷新相关
        self.auto_refresh_enabled = False
        self.last_hedge_file_size = 0
        self.last_hedge_file_path = ""
        self.log_file_positions = {}  # 记录各个日志文件的读取位置

        # 日志重复过滤
        self.displayed_logs = set()  # 存储已显示的日志内容哈希
        self.max_displayed_logs = 10000  # 最大缓存数量，防止内存泄漏

        # 日志加载状态
        self.logs_initialized = False  # 标记日志是否已初始化
        self.log_monitoring_active = False  # 标记日志监控是否激活

        # 初始化优化管理器
        if OPTIMIZATION_AVAILABLE:
            self.async_manager = get_async_manager()
            self.memory_manager = get_memory_manager()
            self.optimization_enabled = True
            self.log_message("优化管理器已启用", "INFO", "GUI")
        else:
            self.async_manager = None
            self.memory_manager = None
            self.optimization_enabled = False
            self.log_message("优化管理器未启用，使用原有功能", "WARNING", "GUI")

        # 延迟加载配置和日志，避免启动卡顿
        self.root.after(100, self.delayed_initialization)

    def is_duplicate_log(self, log_content):
        """检查是否为重复日志 - 优化版本"""
        # 使用内存管理器检查重复（如果可用）
        if self.optimization_enabled and self.memory_manager:
            return self.memory_manager.is_duplicate_log(log_content)
        
        # 回退到原有逻辑
        import hashlib
        log_hash = hashlib.md5(log_content.encode('utf-8')).hexdigest()

        if log_hash in self.displayed_logs:
            return True

        self.displayed_logs.add(log_hash)

        # 防止内存泄漏，限制缓存大小
        if len(self.displayed_logs) > self.max_displayed_logs:
            old_logs = list(self.displayed_logs)[:self.max_displayed_logs // 2]
            for old_log in old_logs:
                self.displayed_logs.discard(old_log)

        return False

    def delayed_initialization(self):
        """延迟初始化，避免启动卡顿"""
        try:
            # 加载配置
            self.load_config()

            # 显示日志文件位置
            self.log_message(f"GUI程序启动，日志文件: {self.log_file}", "INFO", "GUI")

            # 添加日志加载状态标记
            self.logs_initialized = False
            self.log_monitoring_active = False

            # 不再自动启动日志监控和加载，等待用户点击"初始化系统"

        except Exception as e:
            self.log_message(f"延迟初始化失败: {e}", "ERROR", "GUI")

    def start_log_monitoring(self):
        """启动日志文件监控"""
        # 检查是否已经激活监控
        if self.log_monitoring_active:
            return

        def monitor_logs():
            try:
                # 只有在监控激活时才执行
                if self.log_monitoring_active:
                    self.monitor_log_files()
            except Exception as e:
                self.log_message(f"日志监控异常: {e}", "ERROR")
            finally:
                # 只有在监控激活时才继续调度
                if self.log_monitoring_active:
                    # 降低日志监控频率，减少I/O操作
                    self.root.after(2000, self.start_log_monitoring)

        self.root.after(500, monitor_logs)

    def stop_log_monitoring(self):
        """停止日志文件监控"""
        self.log_monitoring_active = False
        self.log_message("日志监控已停止", "INFO", "GUI")



    def setup_hedge_log_forwarding(self):
        """设置对冲系统日志转发到GUI日志文件"""
        try:
            # 创建一个文件处理器，将对冲系统的日志也写入GUI日志文件
            gui_file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
            gui_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            gui_file_handler.setFormatter(gui_formatter)

            # 获取需要转发的对冲系统日志器
            hedge_loggers = [
                'hedge.hedge_manager',
                'hedge.hedge_integration',
                'hedge.hedge_record_manager',
                'hedge.partial_record_manager'
            ]

            # 为每个对冲日志器添加GUI文件处理器
            for logger_name in hedge_loggers:
                logger = logging.getLogger(logger_name)
                # 检查是否已经添加过处理器，避免重复
                if gui_file_handler not in logger.handlers:
                    logger.addHandler(gui_file_handler)
                    logger.setLevel(logging.INFO)

            self.log_message("对冲系统日志转发设置完成", "INFO", "GUI")

        except Exception as e:
            self.log_message(f"设置对冲系统日志转发失败: {e}", "ERROR", "GUI")

    def monitor_log_files(self):
        """监控GUI日志文件变化，在系统监控页面显示所有日志"""
        try:
            # 只监控当前GUI日志文件
            gui_log_file = self.log_file

            if not os.path.exists(gui_log_file):
                return

            # 获取文件当前大小
            current_size = os.path.getsize(gui_log_file)
            last_position = self.log_file_positions.get(gui_log_file, 0)

            # 如果文件有新内容
            if current_size > last_position:
                try:
                    with open(gui_log_file, 'r', encoding='utf-8') as f:
                        f.seek(last_position)
                        new_lines = f.readlines()

                    # 处理新的日志行，直接显示在系统监控页面
                    for line in new_lines:
                        line = line.strip()
                        if line:
                            self.process_gui_log_line(line)

                    # 更新文件位置
                    self.log_file_positions[gui_log_file] = current_size

                except Exception as e:
                    print(f"读取GUI日志文件失败: {e}")

        except Exception as e:
            print(f"监控GUI日志文件异常: {e}")

    def process_gui_log_line(self, line):
        """处理GUI日志文件的新日志行，直接显示在系统监控页面"""
        try:
            # 解析日志行
            log_entry = self.parse_log_line(line, "gui.log")
            if log_entry:
                # 格式化显示
                timestamp = log_entry.get('timestamp', '')
                level = log_entry.get('level', 'info').upper()
                source = log_entry.get('source', 'unknown')
                message = log_entry.get('message', '')

                # 格式化时间显示
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    time_str = dt.strftime('%H:%M:%S')
                except:
                    time_str = timestamp

                # 格式化日志条目
                log_line = f"[{time_str}] [{level}] [{source}] {message}\n"

                # 检查是否为重复日志
                if self.is_duplicate_log(log_line.strip()):
                    return  # 跳过重复日志

                # 获取颜色标签
                color_tag = self.get_log_color_tag(message, level)

                # 在主线程中更新系统监控页面的日志显示
                self.root.after(0, lambda: self.append_to_platform_logs(log_line, color_tag))

        except Exception as e:
            print(f"处理GUI日志行失败: {e}")



    def append_to_logs(self, log_line):
        """在主线程中添加日志到显示区域"""
        try:
            if hasattr(self, 'logs_text'):
                # 解析日志条目以获取级别和消息
                log_entry = self.parse_log_entry_from_text(log_line)
                level = log_entry.get('level', 'INFO') if log_entry else 'INFO'
                message = log_entry.get('message', log_line) if log_entry else log_line

                # 获取颜色标签
                color_tag = self.get_log_color_tag(message, level)

                # 使用insert with tags方法插入日志
                self.logs_text.insert(tk.END, log_line, color_tag)

                if self.auto_scroll_var.get():
                    self.logs_text.see(tk.END)
        except Exception as e:
            print(f"添加日志到显示区域失败: {e}")

    def append_to_platform_logs(self, log_line, color_tag):
        """在主线程中添加日志到系统监控页面"""
        try:
            if hasattr(self, 'platform_log_text'):
                # 使用insert with tags方法插入日志
                self.platform_log_text.insert(tk.END, log_line, color_tag)
                self.platform_log_text.see(tk.END)
        except Exception as e:
            print(f"添加日志到系统监控页面失败: {e}")

    def initialize_log_display(self):
        """初始化日志显示 - 轻量级加载最近日志（已弃用，保留兼容性）"""
        try:
            if hasattr(self, 'logs_text'):
                self.log_message("正在加载最近日志...", "INFO", "GUI")

                # 只加载最近的少量日志，避免启动卡顿
                all_logs = self.load_recent_logs(limit=100)  # 只加载最近100条

                # 显示日志
                self.display_filtered_logs(all_logs)

                self.log_message(f"最近日志加载完成，共显示 {len(all_logs)} 条日志", "INFO", "GUI")
        except Exception as e:
            self.log_message(f"初始化日志显示失败: {e}", "ERROR", "GUI")

    def initialize_limited_log_display(self):
        """初始化限制数量的日志显示 - 只加载最新20条日志"""
        def load_logs_async():
            try:
                self.log_message("正在加载最近20条日志...", "INFO", "GUI")

                # 只加载最近的20条日志，避免卡顿
                recent_logs = self.load_recent_logs_limited(limit=20)

                # 在主线程中更新日志显示
                self.root.after(0, lambda: self.display_recent_logs(recent_logs))

            except Exception as e:
                self.log_message(f"异步加载日志失败: {e}", "ERROR", "GUI")

        # 使用线程异步加载，避免阻塞GUI
        threading.Thread(target=load_logs_async, daemon=True).start()

    def load_recent_logs_limited(self, limit=20):
        """加载最近的限制数量日志"""
        try:
            # 只读取当前GUI日志文件的最后几行
            if not os.path.exists(self.log_file):
                return []

            recent_logs = []
            with open(self.log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # 只取最后的几行
                recent_lines = lines[-limit:] if len(lines) > limit else lines

                for line in recent_lines:
                    line = line.strip()
                    if line:
                        log_entry = self.parse_log_line(line, "gui.log")
                        if log_entry:
                            recent_logs.append(log_entry)

            return recent_logs

        except Exception as e:
            self.log_message(f"加载最近日志失败: {e}", "ERROR", "GUI")
            return []

    def display_recent_logs(self, logs):
        """显示最近的日志到平台状态页面"""
        try:
            if not logs:
                self.log_message("没有最近日志可显示", "INFO", "GUI")
                return

            # 显示日志到平台状态页面的日志区域
            for log_entry in logs:
                timestamp = log_entry.get('timestamp', '')
                level = log_entry.get('level', 'info').upper()
                source = log_entry.get('source', 'unknown')
                message = log_entry.get('message', '')

                # 格式化时间显示
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    time_str = dt.strftime('%H:%M:%S')
                except:
                    time_str = timestamp

                # 格式化日志条目
                log_line = f"[{time_str}] [{level}] [{source}] {message}\n"

                # 检查是否为重复日志
                if self.is_duplicate_log(log_line.strip()):
                    continue  # 跳过重复日志

                # 获取颜色标签
                color_tag = self.get_log_color_tag(message, level)

                # 添加到平台页面的日志
                if hasattr(self, 'platform_log_text'):
                    self.platform_log_text.insert(tk.END, log_line, color_tag)
                    self.platform_log_text.see(tk.END)

            self.log_message(f"最近日志加载完成，共显示 {len(logs)} 条日志", "INFO", "GUI")

        except Exception as e:
            self.log_message(f"显示最近日志失败: {e}", "ERROR", "GUI")

    def setup_window(self):
        """设置主窗口"""
        self.root.title("对冲投注监控系统")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)
        
        # 设置图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
            
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 设置样式
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
    def setup_variables(self):
        """设置变量"""
        # 平台状态变量
        self.pinbet_status_var = tk.StringVar(value="未连接")
        self.crown_status_var = tk.StringVar(value="未连接")
        self.monitoring_status_var = tk.StringVar(value="未启动")

        # 对冲相关
        self.hedge_monitoring = False
        
        # 配置变量
        self.base_platform_var = tk.StringVar(value="auto")
        self.base_amount_var = tk.StringVar(value="50")
        self.max_hedge_amount_var = tk.StringVar(value="500")
        self.retry_attempts_var = tk.StringVar(value="3")
        self.retry_delay_var = tk.StringVar(value="2")
        self.timeout_seconds_var = tk.StringVar(value="30")
        self.base_first_delay_var = tk.StringVar(value="1.0")
        self.max_concurrent_hedges_var = tk.StringVar(value="1")
        self.duplicate_check_interval_var = tk.StringVar(value="1")
        self.enable_cross_platform_check_var = tk.BooleanVar(value=True)
        self.odds_change_threshold_var = tk.StringVar(value="1.0")
        self.ioratio_change_threshold_var = tk.StringVar(value="0.1")
        self.max_bets_per_match_var = tk.StringVar(value="3")
        self.enable_notifications_var = tk.BooleanVar(value=True)
        self.pinbet_ratio_var = tk.StringVar(value="1.0")
        self.crown_ratio_var = tk.StringVar(value="1.0")

        # 新增配置变量
        self.monitor_check_interval_var = tk.StringVar(value="10")
        self.crown_discount_rate_var = tk.StringVar(value="0.8")
        self.enable_sound_var = tk.BooleanVar(value=True)

        # 记录管理相关变量
        self.auto_refresh_var = tk.BooleanVar(value=False)
        self.auto_refresh_enabled = False
        
    def create_notebook(self):
        """创建标签页容器"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="5")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题栏
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        title_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(title_frame, text="📊 对冲投注监控系统", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, sticky=tk.W)
        
        # 状态指示器
        status_frame = ttk.Frame(title_frame)
        status_frame.grid(row=0, column=1, sticky=tk.E)
        
        self.connection_label = ttk.Label(status_frame, text="● 系统状态: 未连接", 
                                         foreground="red")
        self.connection_label.grid(row=0, column=0, padx=(0, 10))
        
        # 创建标签页
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
    def setup_pages(self):
        """设置所有页面"""
        # 1. 平台状态页面
        self.platforms_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.platforms_frame, text="🖥️ 平台状态")
        self.setup_platforms_page()
        
        # 2. 记录管理页面
        self.records_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.records_frame, text="📋 记录管理")
        self.setup_records_page()
        
        # 3. 配置管理页面
        self.config_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.config_frame, text="⚙️ 配置管理")
        self.setup_config_page()
        
    def setup_platforms_page(self):
        """设置平台状态页面 - 复刻web版本"""
        # 配置网格权重
        self.platforms_frame.columnconfigure(0, weight=1)
        self.platforms_frame.rowconfigure(2, weight=1)
        
        # 对冲控制区域
        hedge_control_frame = ttk.LabelFrame(self.platforms_frame, text="对冲控制", padding="10")
        hedge_control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        hedge_control_frame.columnconfigure(1, weight=1)
        
        # 对冲状态显示
        ttk.Label(hedge_control_frame, text="对冲状态:").grid(row=0, column=0, sticky=tk.W)
        self.hedge_status_label = ttk.Label(hedge_control_frame, text="未启动",
                                           foreground="gray")
        self.hedge_status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        # 控制按钮
        control_frame = ttk.Frame(hedge_control_frame)
        control_frame.grid(row=1, column=0, columnspan=2, pady=(10, 0))

        ttk.Button(control_frame, text="初始化系统",
                  command=self.initialize_system).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(control_frame, text="登录系统",
                  command=self.login_systems).grid(row=0, column=1, padx=5)
        self.hedge_btn = ttk.Button(control_frame, text="开启对冲",
                                   command=self.toggle_hedge_monitoring)
        self.hedge_btn.grid(row=0, column=2, padx=5)
        
        # 平台状态区域
        platforms_status_frame = ttk.LabelFrame(self.platforms_frame, text="平台状态", padding="10")
        platforms_status_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        platforms_status_frame.columnconfigure(1, weight=1)
        platforms_status_frame.columnconfigure(3, weight=1)
        
        # 平博状态
        ttk.Label(platforms_status_frame, text="平博状态:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W)
        self.pinbet_status_label = ttk.Label(platforms_status_frame, textvariable=self.pinbet_status_var,
                                            foreground="red")
        self.pinbet_status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 20))

        # 平博余额
        ttk.Label(platforms_status_frame, text="平博余额:", font=("Arial", 10, "bold")).grid(row=1, column=0, sticky=tk.W)
        self.pinbet_balance_label = ttk.Label(platforms_status_frame, text="--", foreground="gray")
        self.pinbet_balance_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 20))

        # 皇冠状态
        ttk.Label(platforms_status_frame, text="皇冠状态:", font=("Arial", 10, "bold")).grid(row=0, column=2, sticky=tk.W)
        self.crown_status_label = ttk.Label(platforms_status_frame, textvariable=self.crown_status_var,
                                           foreground="red")
        self.crown_status_label.grid(row=0, column=3, sticky=tk.W, padx=(10, 0))

        # 皇冠余额
        ttk.Label(platforms_status_frame, text="皇冠余额:", font=("Arial", 10, "bold")).grid(row=1, column=2, sticky=tk.W)
        self.crown_balance_label = ttk.Label(platforms_status_frame, text="--", foreground="gray")
        self.crown_balance_label.grid(row=1, column=3, sticky=tk.W, padx=(10, 0))
        
        # 平台操作按钮
        platform_actions_frame = ttk.Frame(platforms_status_frame)
        platform_actions_frame.grid(row=2, column=0, columnspan=4, pady=(10, 0))
        
        ttk.Button(platform_actions_frame, text="平博登录",
                  command=self.login_pinbet_only).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(platform_actions_frame, text="皇冠登录",
                  command=self.login_crown_only).grid(row=0, column=1, padx=5)
        ttk.Button(platform_actions_frame, text="测试平博连接",
                  command=self.test_pinbet_connection).grid(row=0, column=2, padx=5)
        ttk.Button(platform_actions_frame, text="测试皇冠连接",
                  command=self.test_crown_connection).grid(row=0, column=3, padx=5)
        ttk.Button(platform_actions_frame, text="查看账户余额",
                  command=self.show_balances).grid(row=0, column=4, padx=5)
        ttk.Button(platform_actions_frame, text="刷新状态",
                  command=self.refresh_platform_status).grid(row=0, column=5, padx=5)
        
        # 系统监控区域
        monitoring_frame = ttk.LabelFrame(self.platforms_frame, text="系统监控", padding="10")
        monitoring_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        monitoring_frame.columnconfigure(0, weight=1)
        monitoring_frame.rowconfigure(1, weight=1)
        
        # 监控状态
        monitor_status_frame = ttk.Frame(monitoring_frame)
        monitor_status_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        monitor_status_frame.columnconfigure(1, weight=1)
        
        ttk.Label(monitor_status_frame, text="监控状态:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W)
        self.monitoring_status_label = ttk.Label(monitor_status_frame, textvariable=self.monitoring_status_var, 
                                                foreground="gray")
        self.monitoring_status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 实时日志显示
        self.platform_log_text = scrolledtext.ScrolledText(
            monitoring_frame,
            height=15,
            width=80,
            bg='black',  # 设置背景色为黑色
            fg='white',  # 设置默认文字颜色为白色
            insertbackground='white'  # 设置光标颜色为白色
        )
        self.platform_log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置日志颜色标签
        self.setup_log_color_tags()

    def setup_log_color_tags(self):
        """设置日志颜色标签"""
        # 为系统监控日志文本框配置颜色标签
        self.platform_log_text.tag_configure("hedge_success", foreground="#00FF00", font=("Microsoft YaHei", 10, "bold"))  # 绿色，对冲成功
        self.platform_log_text.tag_configure("single_risk", foreground="#FF6600", font=("Microsoft YaHei", 10, "bold"))   # 橙色，单边风险
        self.platform_log_text.tag_configure("error", foreground="#FF0000", font=("Microsoft YaHei", 10, "bold"))         # 红色，错误
        self.platform_log_text.tag_configure("warning", foreground="#FFFF00", font=("Microsoft YaHei", 10, "bold"))       # 黄色，警告
        self.platform_log_text.tag_configure("info", foreground="#FFFFFF", font=("Microsoft YaHei", 10))                  # 白色，普通信息
        self.platform_log_text.tag_configure("debug", foreground="#CCCCCC", font=("Microsoft YaHei", 10))                 # 灰色，调试信息

        # 如果日志页面的文本框存在，也为其配置颜色标签
        if hasattr(self, 'logs_text'):
            self.logs_text.tag_configure("hedge_success", foreground="#00FF00", font=("Microsoft YaHei", 10, "bold"))
            self.logs_text.tag_configure("single_risk", foreground="#FF6600", font=("Microsoft YaHei", 10, "bold"))
            self.logs_text.tag_configure("error", foreground="#FF0000", font=("Microsoft YaHei", 10, "bold"))
            self.logs_text.tag_configure("warning", foreground="#FFFF00", font=("Microsoft YaHei", 10, "bold"))
            self.logs_text.tag_configure("info", foreground="#FFFFFF", font=("Microsoft YaHei", 10))
            self.logs_text.tag_configure("debug", foreground="#CCCCCC", font=("Microsoft YaHei", 10))

    def setup_records_page(self):
        """设置记录管理页面 - 使用标签页切换"""
        # 配置网格权重
        self.records_frame.columnconfigure(0, weight=1)
        self.records_frame.rowconfigure(1, weight=1)

        # 记录标题区域
        records_header_frame = ttk.Frame(self.records_frame, padding="10")
        records_header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))

        ttk.Label(records_header_frame, text="📋 投注记录管理",
                 font=("Arial", 14, "bold")).grid(row=0, column=0, sticky=tk.W)

        # 创建记录类型标签页容器
        self.records_notebook = ttk.Notebook(self.records_frame)
        self.records_notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # 创建各个记录类型的标签页
        self.setup_record_tabs()

        # 初始化自动刷新变量
        self.auto_refresh_var = tk.BooleanVar(value=False)

    def setup_record_tabs(self):
        """设置记录类型标签页"""
        # 对冲记录标签页
        self.hedge_records_frame = ttk.Frame(self.records_notebook)
        self.records_notebook.add(self.hedge_records_frame, text="📊 对冲记录")
        self.setup_single_record_tab(self.hedge_records_frame, "hedge")

        # 单边待补单标签页
        self.partial_records_frame = ttk.Frame(self.records_notebook)
        self.records_notebook.add(self.partial_records_frame, text="⚠️ 单边待补单")
        self.setup_single_record_tab(self.partial_records_frame, "partial")

        # 平博记录标签页
        self.pinbet_records_frame = ttk.Frame(self.records_notebook)
        self.records_notebook.add(self.pinbet_records_frame, text="🎯 平博记录")
        self.setup_single_record_tab(self.pinbet_records_frame, "pinbet")

        # 皇冠记录标签页
        self.crown_records_frame = ttk.Frame(self.records_notebook)
        self.records_notebook.add(self.crown_records_frame, text="👑 皇冠记录")
        self.setup_single_record_tab(self.crown_records_frame, "crown")

    def setup_single_record_tab(self, parent_frame, record_type):
        """设置单个记录标签页"""
        # 配置网格权重
        parent_frame.columnconfigure(0, weight=1)
        parent_frame.rowconfigure(1, weight=1)

        # 操作按钮区域
        control_frame = ttk.Frame(parent_frame, padding="10")
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # 操作按钮
        ttk.Button(control_frame, text="🔍 查询记录",
                  command=lambda: self.query_records_by_type(record_type)).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(control_frame, text="📥 导出记录",
                  command=lambda: self.export_records_by_type(record_type)).grid(row=0, column=1, padx=5)
        ttk.Button(control_frame, text="🗑️ 清空显示",
                  command=lambda: self.clear_records_display_by_type(record_type)).grid(row=0, column=2, padx=5)

        # 自动刷新控制
        auto_refresh_check = ttk.Checkbutton(control_frame, text="🔄 自动刷新",
                                           variable=self.auto_refresh_var,
                                           command=self.toggle_auto_refresh)
        auto_refresh_check.grid(row=0, column=3, padx=5)

        # 记录显示区域
        display_frame = ttk.LabelFrame(parent_frame, text=f"{self.get_record_type_display_name(record_type)}记录", padding="10")
        display_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        display_frame.columnconfigure(0, weight=1)
        display_frame.rowconfigure(0, weight=1)

        # 创建该类型的记录表格
        self.setup_records_table_by_type(display_frame, record_type)

    def get_record_type_display_name(self, record_type):
        """获取记录类型显示名称"""
        type_names = {
            "hedge": "对冲",
            "partial": "单边待补单",
            "pinbet": "平博",
            "crown": "皇冠"
        }
        return type_names.get(record_type, "未知")

    def setup_records_table_by_type(self, parent_frame, record_type):
        """根据记录类型设置记录表格"""
        # 根据记录类型设置不同的列
        if record_type == "hedge":
            # 对冲记录表格
            columns = ("create_time", "match", "pinbet_bet", "crown_bet", "total_amount", "status")
            headings = ["创建时间", "比赛", "平博投注", "皇冠投注", "总投注额", "状态"]
            widths = [150, 250, 180, 180, 100, 80]
        elif record_type == "partial":
            # 单边待补单记录表格
            columns = ("create_time", "match", "risk_type", "success_platform", "failed_platform", "total_amount", "actions")
            headings = ["创建时间", "比赛", "风险类型", "成功平台", "失败平台", "总投注额", "操作"]
            widths = [150, 200, 120, 100, 100, 100, 100]
        elif record_type == "pinbet":
            # 平博记录表格
            columns = ("start_time", "match", "bet_info", "amount", "odds", "status")
            headings = ["开赛时间", "比赛", "投注信息", "金额", "赔率", "状态"]
            widths = [150, 250, 150, 100, 80, 80]
        elif record_type == "crown":
            # 皇冠记录表格
            columns = ("start_time", "match", "bet_info", "amount", "odds", "status")
            headings = ["开赛时间", "比赛", "投注信息", "金额", "赔率", "状态"]
            widths = [150, 250, 150, 100, 80, 80]
        else:
            # 默认对冲记录格式
            columns = ("create_time", "match", "pinbet_bet", "crown_bet", "total_amount", "status")
            headings = ["创建时间", "比赛", "平博投注", "皇冠投注", "总投注额", "状态"]
            widths = [150, 250, 180, 180, 100, 80]

        # 创建表格
        tree_attr_name = f"{record_type}_records_tree"
        scrollbar_attr_name = f"{record_type}_records_scrollbar"

        # 如果已存在表格，先销毁
        if hasattr(self, tree_attr_name):
            getattr(self, tree_attr_name).destroy()
        if hasattr(self, scrollbar_attr_name):
            getattr(self, scrollbar_attr_name).destroy()

        # 创建新表格
        records_tree = ttk.Treeview(parent_frame, columns=columns, show="headings")
        setattr(self, tree_attr_name, records_tree)

        # 设置列标题和宽度
        for i, (col, heading, width) in enumerate(zip(columns, headings, widths)):
            records_tree.heading(col, text=heading)
            records_tree.column(col, width=width)

        records_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 添加滚动条
        records_scrollbar = ttk.Scrollbar(parent_frame, orient="vertical", command=records_tree.yview)
        setattr(self, scrollbar_attr_name, records_scrollbar)
        records_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        records_tree.configure(yscrollcommand=records_scrollbar.set)


        
    def log_message(self, message, level="INFO", source="system"):
        """添加日志消息并保存到文件"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] [{source}] {message}\n"

        # 保存到日志文件
        try:
            if hasattr(self, 'logger'):
                # 根据级别写入不同的日志级别
                if level == "ERROR":
                    self.logger.error(f"[{source}] {message}")
                elif level == "WARNING":
                    self.logger.warning(f"[{source}] {message}")
                elif level == "DEBUG":
                    self.logger.debug(f"[{source}] {message}")
                else:
                    self.logger.info(f"[{source}] {message}")
        except Exception as e:
            print(f"日志保存失败: {e}")

        # 通过消息队列发送到GUI显示（线程安全）
        self.message_queue.put(("log", log_entry))
        
    def update_status(self, status_type, value, color=None):
        """更新状态显示"""
        self.message_queue.put(("status", (status_type, value, color)))
        
    def process_messages(self):
        """处理消息队列（在主线程中运行）"""
        try:
            while True:
                msg_type, data = self.message_queue.get_nowait()
                
                if msg_type == "log":
                    # 检查是否为重复日志
                    if self.is_duplicate_log(data.strip()):
                        continue  # 跳过重复日志

                    # 解析日志条目以获取级别和消息
                    log_entry = self.parse_log_entry_from_text(data)
                    level = log_entry.get('level', 'INFO') if log_entry else 'INFO'
                    message = log_entry.get('message', data) if log_entry else data

                    # 获取颜色标签
                    color_tag = self.get_log_color_tag(message, level)

                    # 添加到平台页面的日志（带颜色）- 使用insert with tags方法
                    self.platform_log_text.insert(tk.END, data, color_tag)
                    self.platform_log_text.see(tk.END)

                elif msg_type == "status":
                    status_type, value, color = data
                    if status_type == "pinbet":
                        self.pinbet_status_var.set(value)
                        if color:
                            self.pinbet_status_label.config(foreground=color)
                    elif status_type == "crown":
                        self.crown_status_var.set(value)
                        if color:
                            self.crown_status_label.config(foreground=color)
                    elif status_type == "monitoring":
                        self.monitoring_status_var.set(value)
                        if color:
                            self.monitoring_status_label.config(foreground=color)
                    elif status_type == "connection":
                        self.connection_label.config(text=f"● 系统状态: {value}", 
                                                    foreground=color or "black")
                        
        except queue.Empty:
            pass
        
        # 降低消息处理频率，减少CPU占用
        self.root.after(200, self.process_messages)

    def get_log_color_tag(self, message, level):
        """根据日志内容和级别返回颜色标签"""
        message_lower = message.lower()
        level_lower = level.lower()

        # 检查对冲成功相关关键词
        hedge_success_keywords = [
            "对冲成功", "hedge success", "对冲投注成功", "完全成功对冲", "对冲执行成功",
            "对冲投注完全成功", "完全成功对冲记录保存成功", "对冲投注成功", "对冲投注执行成功"
        ]
        if any(keyword in message_lower for keyword in hedge_success_keywords):
            return "hedge_success"

        # 检查单边风险相关关键词
        single_risk_keywords = [
            "单边风险", "single risk", "单边待补单", "部分成功", "partial success", "单边记录",
            "检测到单边风险", "保存到单边记录", "单边风险记录保存成功", "添加新单边记录"
        ]
        if any(keyword in message_lower for keyword in single_risk_keywords):
            return "single_risk"

        # 根据日志级别返回颜色标签
        if level_lower == "error":
            return "error"
        elif level_lower == "warning":
            return "warning"
        elif level_lower == "debug":
            return "debug"
        else:
            return "info"

    def parse_log_entry_from_text(self, log_text):
        """从日志文本解析日志条目"""
        import re
        from datetime import datetime

        try:
            # 移除换行符
            line = log_text.strip()
            if not line:
                return None

            # 尝试解析GUI格式的日志
            # 格式: [HH:MM:SS] [LEVEL] [source] message
            gui_pattern = r'\[(\d{2}:\d{2}:\d{2})\] \[(\w+)\] \[([^\]]+)\] (.+)'
            match = re.match(gui_pattern, line)

            if match:
                time_str = match.group(1)
                level = match.group(2).lower()
                source = match.group(3)
                message = match.group(4)

                # 构造完整的时间戳
                today = datetime.now().strftime('%Y-%m-%d')
                timestamp = f"{today} {time_str}"

                return {
                    "timestamp": timestamp,
                    "level": level,
                    "source": source,
                    "message": message
                }

            # 尝试解析标准格式的日志
            # 格式: 2025-06-12 18:10:11,298 - module_name - LEVEL - message
            std_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),(\d{3}) - ([^-]+) - (\w+) - (.+)'
            match = re.match(std_pattern, line)

            if match:
                timestamp_str = f"{match.group(1)}.{match.group(2)}"
                source = match.group(3).strip()
                level = match.group(4).strip().lower()
                message = match.group(5).strip()

                return {
                    "timestamp": timestamp_str,
                    "level": level,
                    "source": source,
                    "message": message
                }

            # 如果都不匹配，作为简单日志处理
            return {
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "level": "info",
                "source": "unknown",
                "message": line
            }

        except Exception as e:
            self.log_message(f"解析日志条目失败: {e}", "ERROR")
            return None

    def parse_log_line(self, line, filename):
        """解析日志行"""
        import re
        from datetime import datetime

        try:
            line = line.strip()
            if not line:
                return None

            # 尝试解析标准格式的日志
            # 格式: 2025-06-12 18:10:11,298 - module_name - LEVEL - message
            log_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),(\d{3}) - ([^-]+) - (\w+) - (.+)'
            match = re.match(log_pattern, line)

            if match:
                timestamp_str = f"{match.group(1)}.{match.group(2)}"
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f').isoformat()
                source = match.group(3).strip()
                level = match.group(4).strip().lower()
                message = match.group(5).strip()

                return {
                    "timestamp": timestamp,
                    "level": level,
                    "source": source,
                    "message": message
                }

            # 尝试解析其他格式
            # 简单格式：直接作为info级别日志
            if len(line) > 10:
                return {
                    "timestamp": datetime.now().isoformat(),
                    "level": "info",
                    "source": filename.replace('.log', ''),
                    "message": line
                }

        except Exception as e:
            self.log_message(f"解析日志行失败: {e}", "ERROR")

        return None

    def setup_config_page(self):
        """设置配置管理页面 - 完全复刻web版本"""
        # 配置网格权重
        self.config_frame.columnconfigure(0, weight=1)
        self.config_frame.rowconfigure(1, weight=1)

        # 配置标题和保存按钮
        config_header_frame = ttk.Frame(self.config_frame, padding="10")
        config_header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        config_header_frame.columnconfigure(0, weight=1)

        title_frame = ttk.Frame(config_header_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        title_frame.columnconfigure(0, weight=1)

        ttk.Label(title_frame, text="⚙️ 系统配置管理",
                 font=("Arial", 14, "bold")).grid(row=0, column=0, sticky=tk.W)

        # 保存按钮
        save_frame = ttk.Frame(title_frame)
        save_frame.grid(row=0, column=1, sticky=tk.E)

        ttk.Button(save_frame, text="💾 保存配置",
                  command=self.save_config).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(save_frame, text="🔄 重置配置",
                  command=self.reset_config).grid(row=0, column=1)

        # 保存状态指示
        self.save_status_label = ttk.Label(config_header_frame, text="", foreground="green")
        self.save_status_label.grid(row=1, column=0, pady=(5, 0))

        # 配置内容区域（使用滚动框架）
        config_canvas = tk.Canvas(self.config_frame)
        config_scrollbar = ttk.Scrollbar(self.config_frame, orient="vertical", command=config_canvas.yview)
        self.config_content_frame = ttk.Frame(config_canvas)

        self.config_content_frame.bind(
            "<Configure>",
            lambda e: config_canvas.configure(scrollregion=config_canvas.bbox("all"))
        )

        config_canvas.create_window((0, 0), window=self.config_content_frame, anchor="nw")
        config_canvas.configure(yscrollcommand=config_scrollbar.set)

        config_canvas.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        config_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))

        # 配置内容
        self.setup_config_sections()

    def setup_config_sections(self):
        """设置配置各个部分 - 横向两列布局"""
        # 配置主容器的网格权重，支持两列布局
        self.config_content_frame.columnconfigure(0, weight=1)
        self.config_content_frame.columnconfigure(1, weight=1)

        # 左列配置项
        left_column_frame = ttk.Frame(self.config_content_frame)
        left_column_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 5), pady=10)
        left_column_frame.columnconfigure(0, weight=1)

        # 右列配置项
        right_column_frame = ttk.Frame(self.config_content_frame)
        right_column_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 10), pady=10)
        right_column_frame.columnconfigure(0, weight=1)

        # 左列：基础配置
        basic_frame = ttk.LabelFrame(left_column_frame, text="基础配置", padding="10")
        basic_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        basic_frame.columnconfigure(1, weight=1)

        ttk.Label(basic_frame, text="基准平台:").grid(row=0, column=0, sticky=tk.W, pady=2)
        base_platform_combo = ttk.Combobox(basic_frame, textvariable=self.base_platform_var,
                                          values=["auto", "pinbet", "crown"], state="readonly")
        base_platform_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        ttk.Label(basic_frame, text="基础投注金额:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(basic_frame, textvariable=self.base_amount_var).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        ttk.Label(basic_frame, text="最大对冲金额:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(basic_frame, textvariable=self.max_hedge_amount_var).grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        ttk.Label(basic_frame, text="对冲监控检查间隔(秒):").grid(row=3, column=0, sticky=tk.W, pady=2)
        ttk.Entry(basic_frame, textvariable=self.monitor_check_interval_var).grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        ttk.Label(basic_frame, text="皇冠折扣率:").grid(row=4, column=0, sticky=tk.W, pady=2)
        crown_discount_frame = ttk.Frame(basic_frame)
        crown_discount_frame.grid(row=4, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        crown_discount_frame.columnconfigure(0, weight=1)

        ttk.Entry(crown_discount_frame, textvariable=self.crown_discount_rate_var, width=10).grid(row=0, column=0, sticky=tk.W)
        ttk.Label(crown_discount_frame, text="(0.8=8折盘, 1.0=原盘, 0.5=5折盘)",
                 font=("Arial", 8), foreground="gray").grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        # 左列：执行配置
        exec_frame = ttk.LabelFrame(left_column_frame, text="执行配置", padding="10")
        exec_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        exec_frame.columnconfigure(1, weight=1)

        ttk.Label(exec_frame, text="重试次数:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(exec_frame, textvariable=self.retry_attempts_var).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        ttk.Label(exec_frame, text="重试延迟(秒):").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(exec_frame, textvariable=self.retry_delay_var).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        ttk.Label(exec_frame, text="超时时间(秒):").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(exec_frame, textvariable=self.timeout_seconds_var).grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        ttk.Label(exec_frame, text="基准优先延迟(秒):").grid(row=3, column=0, sticky=tk.W, pady=2)
        ttk.Entry(exec_frame, textvariable=self.base_first_delay_var).grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        # 左列：通知配置
        notification_frame = ttk.LabelFrame(left_column_frame, text="通知配置", padding="10")
        notification_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        notification_frame.columnconfigure(1, weight=1)

        ttk.Checkbutton(notification_frame, text="启用声音提示",
                       variable=self.enable_sound_var).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=2)

        # 右列：风险控制
        risk_frame = ttk.LabelFrame(right_column_frame, text="风险控制", padding="10")
        risk_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        risk_frame.columnconfigure(1, weight=1)

        ttk.Label(risk_frame, text="最大并发对冲数:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(risk_frame, textvariable=self.max_concurrent_hedges_var).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        ttk.Label(risk_frame, text="重复检测间隔(分钟):").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(risk_frame, textvariable=self.duplicate_check_interval_var).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        ttk.Checkbutton(risk_frame, text="启用跨平台重复检测",
                       variable=self.enable_cross_platform_check_var).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=2)

        # 右列：投注验证配置
        bet_validation_frame = ttk.LabelFrame(right_column_frame, text="投注验证配置", padding="10")
        bet_validation_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        bet_validation_frame.columnconfigure(1, weight=1)

        ttk.Label(bet_validation_frame, text="赔率变化阈值:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(bet_validation_frame, textvariable=self.odds_change_threshold_var).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        ttk.Label(bet_validation_frame, text="盘口变化阈值:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(bet_validation_frame, textvariable=self.ioratio_change_threshold_var).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        ttk.Label(bet_validation_frame, text="每场比赛最大投注数:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(bet_validation_frame, textvariable=self.max_bets_per_match_var).grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        # 右列：平台投注比例配置
        ratio_frame = ttk.LabelFrame(right_column_frame, text="平台投注比例配置", padding="10")
        ratio_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        ratio_frame.columnconfigure(1, weight=1)
        ratio_frame.columnconfigure(3, weight=1)

        # 说明文字
        ttk.Label(ratio_frame, text="设置各平台的投注金额比例（1.0为正常比例）",
                 font=("Arial", 8), foreground="gray").grid(row=0, column=0, columnspan=4, sticky=tk.W, pady=(0, 5))

        ttk.Label(ratio_frame, text="平博比例:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(ratio_frame, textvariable=self.pinbet_ratio_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 20), pady=2)

        ttk.Label(ratio_frame, text="皇冠比例:").grid(row=1, column=2, sticky=tk.W, pady=2)
        crown_ratio_entry = ttk.Entry(ratio_frame, textvariable=self.crown_ratio_var, width=10)
        crown_ratio_entry.grid(row=1, column=3, sticky=tk.W, padx=(10, 0), pady=2)

        # 投注金额预览
        preview_frame = ttk.LabelFrame(ratio_frame, text="投注金额预览", padding="5")
        preview_frame.grid(row=2, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(10, 0))
        preview_frame.columnconfigure(1, weight=1)
        preview_frame.columnconfigure(3, weight=1)

        ttk.Label(preview_frame, text="基础金额:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.base_amount_preview = ttk.Label(preview_frame, text="200", foreground="blue")
        self.base_amount_preview.grid(row=0, column=1, sticky=tk.W, padx=(10, 20), pady=2)

        ttk.Label(preview_frame, text="平博投注:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.pinbet_amount_preview = ttk.Label(preview_frame, text="200", foreground="green")
        self.pinbet_amount_preview.grid(row=1, column=1, sticky=tk.W, padx=(10, 20), pady=2)

        ttk.Label(preview_frame, text="皇冠投注:").grid(row=1, column=2, sticky=tk.W, pady=2)
        self.crown_amount_preview = ttk.Label(preview_frame, text="250", foreground="orange")
        self.crown_amount_preview.grid(row=1, column=3, sticky=tk.W, padx=(10, 0), pady=2)

        # 绑定事件以实时更新预览
        self.base_amount_var.trace('w', self.update_bet_preview)
        self.pinbet_ratio_var.trace('w', self.update_bet_preview)
        self.crown_ratio_var.trace('w', self.update_bet_preview)
        self.crown_discount_rate_var.trace('w', self.update_bet_preview)

        # 初始化预览
        self.update_bet_preview()

    def update_bet_preview(self, *args):
        """更新投注金额预览"""
        try:
            base_amount = float(self.base_amount_var.get() or 200)
            pinbet_ratio = float(self.pinbet_ratio_var.get() or 1.0)
            crown_ratio = float(self.crown_ratio_var.get() or 1.0)
            crown_discount = float(self.crown_discount_rate_var.get() or 0.8)

            # 计算投注金额
            pinbet_amount = base_amount * pinbet_ratio
            crown_amount = base_amount * crown_ratio / crown_discount  # 考虑折扣率

            # 更新显示
            self.base_amount_preview.config(text=f"{base_amount:.0f}")
            self.pinbet_amount_preview.config(text=f"{pinbet_amount:.0f}")
            self.crown_amount_preview.config(text=f"{crown_amount:.0f}")

        except (ValueError, ZeroDivisionError):
            # 输入无效时显示默认值
            self.base_amount_preview.config(text="200")
            self.pinbet_amount_preview.config(text="200")
            self.crown_amount_preview.config(text="250")



    # ==================== 系统控制方法 ====================

    def initialize_system(self):
        """初始化系统"""
        def init_worker():
            try:
                self.log_message("正在初始化系统...")
                self.update_status("connection", "初始化中...", "orange")

                # 检查系统是否可用
                if not SYSTEM_AVAILABLE or HedgeBettingSystem is None:
                    self.log_message("系统模块不可用，请检查导入", "ERROR")
                    self.update_status("connection", "模块不可用", "red")
                    return

                self.hedge_system = HedgeBettingSystem()
                success = self.hedge_system.initialize_systems()

                if success:
                    self.log_message("系统初始化成功")
                    self.update_status("connection", "已初始化", "blue")

                    # 设置对冲系统日志转发到GUI
                    self.setup_hedge_log_forwarding()

                    # 初始化时启动日志监控和加载
                    if not self.log_monitoring_active:
                        self.log_message("启动日志监控...")
                        self.start_log_monitoring()
                        self.log_monitoring_active = True

                    # 初始化日志显示（限制为20条）
                    if not self.logs_initialized:
                        self.log_message("加载最近日志...")
                        self.initialize_limited_log_display()
                        self.logs_initialized = True
                else:
                    self.log_message("系统初始化失败", "ERROR")
                    self.update_status("connection", "初始化失败", "red")

            except Exception as e:
                self.log_message(f"初始化异常: {e}", "ERROR")
                self.update_status("connection", "初始化异常", "red")

        threading.Thread(target=init_worker, daemon=True).start()

    def login_systems(self):
        """登录系统"""
        if not self.hedge_system:
            messagebox.showwarning("警告", "请先初始化系统")
            return

        def login_worker():
            try:
                self.log_message("正在登录系统...")
                self.update_status("pinbet", "登录中...", "orange")
                self.update_status("crown", "登录中...", "orange")

                success = self.hedge_system.login_systems()

                if success:
                    self.log_message("系统登录成功")
                    self.update_status("pinbet", "已登录", "green")
                    self.update_status("crown", "已登录", "green")
                    self.update_status("connection", "已连接", "green")

                    # 登录成功后更新余额显示
                    self.update_platform_balances()
                else:
                    self.log_message("系统登录失败", "ERROR")
                    self.update_status("pinbet", "登录失败", "red")
                    self.update_status("crown", "登录失败", "red")

            except Exception as e:
                self.log_message(f"登录异常: {e}", "ERROR")

        threading.Thread(target=login_worker, daemon=True).start()

    def toggle_hedge_monitoring(self):
        """切换对冲监控状态"""
        if not self.hedge_monitoring:
            self.start_hedge_monitoring()
        else:
            self.stop_hedge_monitoring()

    def start_hedge_monitoring(self):
        """启动对冲监控"""
        # 检查系统是否已初始化和登录
        if not self.hedge_system:
            messagebox.showwarning("警告", "请先初始化系统")
            return

        if not self.hedge_system.systems_logged_in:
            messagebox.showwarning("警告", "请先登录系统")
            return

        # 检查对冲功能是否可用
        if not hasattr(self.hedge_system, 'hedge_integration') or not self.hedge_system.hedge_integration:
            messagebox.showerror("错误", "对冲集成器未初始化，请检查系统配置")
            return

        if not self.hedge_system.hedge_integration.is_hedge_available():
            messagebox.showerror("错误", "对冲功能不可用，请检查配置和登录状态")
            return

        # 检查双平台登录状态和余额
        if not self.check_platforms_ready():
            return

        self.hedge_monitoring = True
        self.hedge_btn.config(text="停止对冲")
        self.hedge_status_label.config(text="监控中", foreground="green")
        self.log_message("开启对冲监控")

        def hedge_monitoring_worker():
            try:
                # 调用hedge_main中的自动监控方法
                interval = int(self.config_data.get("hedge", {}).get("monitor_check_interval", 10))
                self.hedge_system.start_auto_hedge_monitoring(interval)
            except KeyboardInterrupt:
                self.log_message("对冲监控已停止")
            except Exception as e:
                self.log_message(f"对冲监控异常: {e}", "ERROR")
            finally:
                # 重置状态
                self.hedge_monitoring = False
                self.hedge_btn.config(text="开启对冲")
                self.hedge_status_label.config(text="已停止", foreground="gray")

        self.monitoring_thread = threading.Thread(target=hedge_monitoring_worker, daemon=True)
        self.monitoring_thread.start()

    def stop_hedge_monitoring(self):
        """停止对冲监控"""
        self.hedge_monitoring = False
        self.hedge_btn.config(text="开启对冲")
        self.hedge_status_label.config(text="正在停止...", foreground="orange")
        self.log_message("正在停止对冲监控...")

        # 停止监控线程
        if hasattr(self, 'hedge_system') and self.hedge_system and hasattr(self.hedge_system, 'hedge_integration'):
            # 发送停止信号给监控循环
            self.hedge_system.hedge_integration.stop_monitoring()

            # 等待线程结束
            if hasattr(self, 'monitoring_thread') and self.monitoring_thread.is_alive():
                def wait_for_stop():
                    try:
                        # 等待最多10秒让监控线程自然结束
                        self.monitoring_thread.join(timeout=10)
                        if self.monitoring_thread.is_alive():
                            self.log_message("监控线程未能在10秒内停止", "WARNING")
                        else:
                            self.log_message("对冲监控已成功停止")

                        # 更新UI状态
                        self.hedge_status_label.config(text="已停止", foreground="gray")
                    except Exception as e:
                        self.log_message(f"停止监控时发生异常: {e}", "ERROR")
                        self.hedge_status_label.config(text="停止异常", foreground="red")

                # 在后台线程中等待停止，避免阻塞GUI
                threading.Thread(target=wait_for_stop, daemon=True).start()
            else:
                self.hedge_status_label.config(text="已停止", foreground="gray")
                self.log_message("对冲监控已停止")
        else:
            self.hedge_status_label.config(text="已停止", foreground="gray")
            self.log_message("对冲监控已停止")

    def check_platforms_ready(self):
        """检查双平台是否已登录且有余额"""
        try:
            # 检查平博状态和余额
            pinbet_ready = False
            crown_ready = False

            if hasattr(self.hedge_system, 'pinbet_system') and self.hedge_system.pinbet_system:
                if hasattr(self.hedge_system.pinbet_system, 'is_logged_in') and self.hedge_system.pinbet_system.is_logged_in:
                    # 获取平博余额
                    success, balance_data = self.hedge_system.pinbet_system.api.get_balance()
                    if success and isinstance(balance_data, dict):
                        balance = balance_data.get('betCredit', 0)
                        if balance > 0:
                            pinbet_ready = True
                            self.update_pinbet_balance(balance, balance_data.get('currency', 'CNY'))
                        else:
                            self.log_message("平博余额为0，无法进行对冲", "WARNING")
                    else:
                        self.log_message("无法获取平博余额", "WARNING")
                else:
                    self.log_message("平博未登录", "WARNING")
            else:
                self.log_message("平博系统未初始化", "WARNING")

            # 检查皇冠状态和余额
            if hasattr(self.hedge_system, 'crown_system') and self.hedge_system.crown_system:
                if hasattr(self.hedge_system.crown_system, 'is_logged_in') and self.hedge_system.crown_system.is_logged_in:
                    # 获取皇冠余额
                    account_data = self.hedge_system.crown_system.get_account_balance()
                    if account_data and hasattr(self.hedge_system.crown_system, 'credit'):
                        balance = float(self.hedge_system.crown_system.credit)
                        if balance > 0:
                            crown_ready = True
                            self.update_crown_balance(balance)
                        else:
                            self.log_message("皇冠余额为0，无法进行对冲", "WARNING")
                    else:
                        self.log_message("无法获取皇冠余额", "WARNING")
                else:
                    self.log_message("皇冠未登录", "WARNING")
            else:
                self.log_message("皇冠系统未初始化", "WARNING")

            if not pinbet_ready or not crown_ready:
                messagebox.showwarning("警告", "双平台必须都已登录且有余额才能开启对冲")
                return False

            return True

        except Exception as e:
            self.log_message(f"检查平台状态异常: {e}", "ERROR")
            messagebox.showerror("错误", f"检查平台状态失败: {e}")
            return False

    def update_pinbet_balance(self, balance, currency="CNY"):
        """更新平博余额显示"""
        self.pinbet_balance_label.config(text=f"{balance} {currency}", foreground="green")

    def update_crown_balance(self, balance, currency="CNY"):
        """更新皇冠余额显示"""
        self.crown_balance_label.config(text=f"{balance} {currency}", foreground="green")

    def update_platform_balances(self):
        """更新平台余额显示"""
        def balance_worker():
            try:
                # 更新平博余额
                if hasattr(self.hedge_system, 'pinbet_system') and self.hedge_system.pinbet_system:
                    if hasattr(self.hedge_system.pinbet_system, 'is_logged_in') and self.hedge_system.pinbet_system.is_logged_in:
                        success, balance_data = self.hedge_system.pinbet_system.api.get_balance()
                        if success and isinstance(balance_data, dict):
                            balance = balance_data.get('betCredit', 0)
                            currency = balance_data.get('currency', 'CNY')
                            self.update_pinbet_balance(balance, currency)
                        else:
                            self.pinbet_balance_label.config(text="获取失败", foreground="red")
                    else:
                        self.pinbet_balance_label.config(text="未登录", foreground="gray")

                # 更新皇冠余额
                if hasattr(self.hedge_system, 'crown_system') and self.hedge_system.crown_system:
                    if hasattr(self.hedge_system.crown_system, 'is_logged_in') and self.hedge_system.crown_system.is_logged_in:
                        account_data = self.hedge_system.crown_system.get_account_balance()
                        if account_data and hasattr(self.hedge_system.crown_system, 'credit'):
                            balance = float(self.hedge_system.crown_system.credit)
                            self.update_crown_balance(balance)
                        else:
                            self.crown_balance_label.config(text="获取失败", foreground="red")
                    else:
                        self.crown_balance_label.config(text="未登录", foreground="gray")

            except Exception as e:
                self.log_message(f"更新余额异常: {e}", "ERROR")

        threading.Thread(target=balance_worker, daemon=True).start()

    def login_pinbet_only(self):
        """单独登录平博平台"""
        if not self.hedge_system:
            messagebox.showwarning("警告", "请先初始化系统")
            return

        def login_worker():
            try:
                self.log_message("正在登录平博平台...")
                self.update_status("pinbet", "登录中...", "orange")

                # 单独登录平博 - 直接使用hedge_main中的登录方法
                if hasattr(self.hedge_system, 'pinbet_system') and self.hedge_system.pinbet_system:
                    # 从配置文件获取登录信息
                    pinbet_config = self.hedge_system.config.get("pinbo_api", {}).get("account", {})
                    pinbet_username = pinbet_config.get("username")
                    pinbet_password = pinbet_config.get("password")

                    if pinbet_username and pinbet_password:
                        self.log_message(f"正在登录平博账号: {pinbet_username}")
                        success = self.hedge_system.pinbet_system.login(pinbet_username, pinbet_password)
                    else:
                        self.log_message("平博账号信息未配置，尝试使用默认登录方法")
                        success = self.hedge_system.pinbet_system.login()

                    if success:
                        self.log_message("平博平台登录成功")
                        self.update_status("pinbet", "已登录", "green")

                        # 更新平博余额
                        try:
                            success, balance_data = self.hedge_system.pinbet_system.api.get_balance()
                            if success and isinstance(balance_data, dict):
                                balance = balance_data.get('betCredit', 0)
                                currency = balance_data.get('currency', 'CNY')
                                self.update_pinbet_balance(balance, currency)
                        except Exception as e:
                            self.log_message(f"获取平博余额失败: {e}", "WARNING")
                    else:
                        self.log_message("平博平台登录失败", "ERROR")
                        self.update_status("pinbet", "登录失败", "red")
                else:
                    self.log_message("平博系统未初始化", "ERROR")
                    self.update_status("pinbet", "系统未初始化", "red")

            except Exception as e:
                self.log_message(f"平博登录异常: {e}", "ERROR")
                self.update_status("pinbet", "登录异常", "red")

        threading.Thread(target=login_worker, daemon=True).start()

    def login_crown_only(self):
        """单独登录皇冠平台"""
        if not self.hedge_system:
            messagebox.showwarning("警告", "请先初始化系统")
            return

        def login_worker():
            try:
                self.log_message("正在登录皇冠平台...")
                self.update_status("crown", "登录中...", "orange")

                # 单独登录皇冠
                if hasattr(self.hedge_system, 'crown_system') and self.hedge_system.crown_system:
                    success = self.hedge_system.crown_system.login()
                    if success:
                        self.log_message("皇冠平台登录成功")
                        self.update_status("crown", "已登录", "green")

                        # 更新皇冠余额
                        try:
                            account_data = self.hedge_system.crown_system.get_account_balance()
                            if account_data and hasattr(self.hedge_system.crown_system, 'credit'):
                                balance = float(self.hedge_system.crown_system.credit)
                                self.update_crown_balance(balance)
                        except Exception as e:
                            self.log_message(f"获取皇冠余额失败: {e}", "WARNING")
                    else:
                        self.log_message("皇冠平台可能处于维护状态，登录失败", "WARNING")
                        self.update_status("crown", "平台维护中", "orange")
                else:
                    self.log_message("皇冠系统未初始化", "ERROR")
                    self.update_status("crown", "系统未初始化", "red")

            except Exception as e:
                self.log_message(f"皇冠登录异常: {e}", "ERROR")
                self.update_status("crown", "登录异常", "red")

        threading.Thread(target=login_worker, daemon=True).start()

    # ==================== 平台测试方法 ====================

    def test_pinbet_connection(self):
        """测试平博连接 - 使用真实API"""
        def test_worker():
            try:
                self.log_message("正在测试平博连接...")
                self.update_status("pinbet", "测试中...", "orange")

                if not self.hedge_system or not hasattr(self.hedge_system, 'pinbet_system'):
                    self.log_message("平博系统未初始化", "ERROR")
                    self.update_status("pinbet", "未初始化", "red")
                    return

                pinbet_system = self.hedge_system.pinbet_system
                if not pinbet_system:
                    self.log_message("平博系统实例为空", "ERROR")
                    self.update_status("pinbet", "系统错误", "red")
                    return

                # 测试登录状态
                if hasattr(pinbet_system, 'is_logged_in') and pinbet_system.is_logged_in:
                    # 测试余额查询来验证连接
                    success, balance_data = pinbet_system.api.get_balance()
                    if success and isinstance(balance_data, dict):
                        balance = balance_data.get('betCredit', 0)
                        self.log_message(f"平博连接测试成功，余额: {balance}")
                        self.update_status("pinbet", "连接正常", "green")
                    else:
                        self.log_message("平博连接测试失败：无法获取余额", "ERROR")
                        self.update_status("pinbet", "连接失败", "red")
                else:
                    self.log_message("平博未登录", "WARNING")
                    self.update_status("pinbet", "未登录", "orange")

            except Exception as e:
                self.log_message(f"平博连接测试异常: {e}", "ERROR")
                self.update_status("pinbet", "测试异常", "red")

        threading.Thread(target=test_worker, daemon=True).start()

    def test_crown_connection(self):
        """测试皇冠连接 - 使用真实API"""
        def test_worker():
            try:
                self.log_message("正在测试皇冠连接...")
                self.update_status("crown", "测试中...", "orange")

                if not self.hedge_system or not hasattr(self.hedge_system, 'crown_system'):
                    self.log_message("皇冠系统未初始化", "ERROR")
                    self.update_status("crown", "未初始化", "red")
                    return

                crown_system = self.hedge_system.crown_system
                if not crown_system:
                    self.log_message("皇冠系统实例为空", "ERROR")
                    self.update_status("crown", "系统错误", "red")
                    return

                # 测试登录状态
                if hasattr(crown_system, 'is_logged_in') and crown_system.is_logged_in:
                    # 测试账户余额查询来验证连接
                    account_data = crown_system.get_account_balance()
                    if account_data:
                        balance = getattr(crown_system, 'credit', 0)
                        self.log_message(f"皇冠连接测试成功，余额: {balance}")
                        self.update_status("crown", "连接正常", "green")
                    else:
                        self.log_message("皇冠连接测试失败：无法获取余额", "ERROR")
                        self.update_status("crown", "连接失败", "red")
                else:
                    self.log_message("皇冠未登录", "WARNING")
                    self.update_status("crown", "未登录", "orange")

            except Exception as e:
                self.log_message(f"皇冠连接测试异常: {e}", "ERROR")
                self.update_status("crown", "测试异常", "red")

        threading.Thread(target=test_worker, daemon=True).start()

    def show_balances(self):
        """显示账户余额 - 使用真实API"""
        if not self.hedge_system:
            messagebox.showwarning("警告", "请先初始化系统")
            return

        self.log_message("正在获取账户余额...")
        self.update_platform_balances()

    def refresh_platform_status(self):
        """刷新平台状态"""
        self.log_message("正在刷新平台状态...")
        self.update_platform_balances()
        self.log_message("平台状态已刷新")

    # ==================== 配置管理方法 ====================

    def load_config(self):
        """加载配置"""
        try:
            config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config", "config.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                self.populate_config_form()
                self.log_message("配置加载成功")
            else:
                self.log_message("配置文件不存在，使用默认配置", "WARNING")
        except Exception as e:
            self.log_message(f"配置加载失败: {e}", "ERROR")

    def populate_config_form(self):
        """填充配置表单"""
        hedge_config = self.config_data.get("hedge", {})
        auto_bet_config = self.config_data.get("auto_bet", {})

        # 基础配置
        self.base_platform_var.set(hedge_config.get("base_platform", "auto"))
        self.base_amount_var.set(str(hedge_config.get("base_amount", 50)))
        self.max_hedge_amount_var.set(str(hedge_config.get("max_hedge_amount", 500)))

        # 执行配置
        self.retry_attempts_var.set(str(hedge_config.get("retry_attempts", 3)))
        self.retry_delay_var.set(str(hedge_config.get("retry_delay", 2)))
        self.timeout_seconds_var.set(str(hedge_config.get("timeout_seconds", 30)))
        self.base_first_delay_var.set(str(hedge_config.get("base_first_delay", 1.0)))

        # 风险控制
        self.max_concurrent_hedges_var.set(str(hedge_config.get("max_concurrent_hedges", 1)))
        self.duplicate_check_interval_var.set(str(hedge_config.get("duplicate_check_interval", 1)))
        self.enable_cross_platform_check_var.set(hedge_config.get("enable_cross_platform_duplicate_check", True))

        # 投注验证配置
        self.odds_change_threshold_var.set(str(auto_bet_config.get("odds_change_threshold",
                                                                  hedge_config.get("odds_change_threshold", 1.0))))
        self.ioratio_change_threshold_var.set(str(auto_bet_config.get("ioratio_change_threshold",
                                                                     hedge_config.get("ioratio_change_threshold", 0.1))))
        self.max_bets_per_match_var.set(str(auto_bet_config.get("max_bets_per_match",
                                                               hedge_config.get("max_bets_per_match", 3))))

        # 新增配置选项
        self.monitor_check_interval_var.set(str(hedge_config.get("monitor_check_interval", 10)))
        self.crown_discount_rate_var.set(str(hedge_config.get("crown_discount_rate", 0.8)))

        # 通知配置
        notification_config = self.config_data.get("notification", {})
        self.enable_sound_var.set(notification_config.get("enable_sound", True))

        # 平台投注比例配置
        platform_ratios = hedge_config.get("platform_bet_ratios", {"pinbet": 1.0, "crown": 1.0})
        self.pinbet_ratio_var.set(str(platform_ratios.get("pinbet", 1.0)))
        self.crown_ratio_var.set(str(platform_ratios.get("crown", 1.0)))

    def save_config(self):
        """保存配置"""
        try:
            # 构建新的配置数据
            new_hedge_config = {
                "enable": True,  # 移除hedge_enabled_var，始终启用
                "base_platform": self.base_platform_var.get(),
                "base_amount": float(self.base_amount_var.get()),
                "max_hedge_amount": float(self.max_hedge_amount_var.get()),
                "retry_attempts": int(self.retry_attempts_var.get()),
                "retry_delay": float(self.retry_delay_var.get()),
                "timeout_seconds": int(self.timeout_seconds_var.get()),
                "base_first_delay": float(self.base_first_delay_var.get()),
                "max_concurrent_hedges": int(self.max_concurrent_hedges_var.get()),
                "duplicate_check_interval": float(self.duplicate_check_interval_var.get()),
                "enable_cross_platform_duplicate_check": self.enable_cross_platform_check_var.get(),
                "odds_change_threshold": float(self.odds_change_threshold_var.get()),
                "ioratio_change_threshold": float(self.ioratio_change_threshold_var.get()),
                "max_bets_per_match": int(self.max_bets_per_match_var.get()),
                "monitor_check_interval": int(self.monitor_check_interval_var.get()),
                "crown_discount_rate": float(self.crown_discount_rate_var.get()),
                "platform_bet_ratios": {
                    "pinbet": float(self.pinbet_ratio_var.get()),
                    "crown": float(self.crown_ratio_var.get())
                }
            }

            # 构建通知配置
            new_notification_config = {
                "enable_sound": self.enable_sound_var.get(),
                "sound_file": "alert.mp3"  # 默认声音文件
            }

            # 更新配置数据
            self.config_data["hedge"] = new_hedge_config
            self.config_data["notification"] = new_notification_config

            # 保存到文件
            config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config", "config.json")
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)

            # 通知对冲系统重新加载配置
            if hasattr(self, 'hedge_system') and self.hedge_system:
                try:
                    # 首先清除所有HedgeConfig的类级别缓存
                    from config.hedge_config import HedgeConfig
                    HedgeConfig.clear_cache()
                    self.log_message("已清除HedgeConfig类级别缓存")

                    # 清除hedge_integration的配置缓存
                    if hasattr(self.hedge_system, 'hedge_integration') and self.hedge_system.hedge_integration:
                        self.hedge_system.hedge_integration._cached_config = None
                        self.hedge_system.hedge_integration._config_cache_time = 0
                        self.log_message("已清除对冲集成器配置缓存")

                    # 重新加载对冲配置
                    if hasattr(self.hedge_system, 'hedge_config'):
                        try:
                            success = self.hedge_system.hedge_config.force_reload_config()
                            if success:
                                self.log_message("对冲配置已强制重新加载")
                            else:
                                self.log_message("对冲配置重新加载失败", "WARNING")
                        except Exception as e:
                            self.log_message(f"重新加载hedge_config时出错: {e}", "WARNING")

                    # 重新加载对冲管理器配置
                    if (hasattr(self.hedge_system, 'hedge_integration') and self.hedge_system.hedge_integration and
                        hasattr(self.hedge_system.hedge_integration, 'hedge_manager') and self.hedge_system.hedge_integration.hedge_manager and
                        hasattr(self.hedge_system.hedge_integration.hedge_manager, 'config')):
                        try:
                            success = self.hedge_system.hedge_integration.hedge_manager.config.force_reload_config()
                            if success:
                                self.log_message("对冲管理器配置已强制重新加载")
                            else:
                                self.log_message("对冲管理器配置重新加载失败", "WARNING")
                        except Exception as e:
                            self.log_message(f"重新加载对冲管理器配置时出错: {e}", "WARNING")

                    # 重新加载hedge_integration的配置
                    if (hasattr(self.hedge_system, 'hedge_integration') and self.hedge_system.hedge_integration and
                        hasattr(self.hedge_system.hedge_integration, 'config')):
                        try:
                            success = self.hedge_system.hedge_integration.config.force_reload_config()
                            if success:
                                self.log_message("对冲集成器配置已强制重新加载")
                            else:
                                self.log_message("对冲集成器配置重新加载失败", "WARNING")
                        except Exception as e:
                            self.log_message(f"重新加载对冲集成器配置时出错: {e}", "WARNING")

                except Exception as e:
                    self.log_message(f"重新加载对冲配置时出错: {e}", "WARNING")

            self.save_status_label.config(text="✅ 配置保存成功", foreground="green")
            self.log_message("配置保存成功，对冲系统配置已更新")

            # 3秒后清除状态
            self.root.after(3000, lambda: self.save_status_label.config(text=""))

        except Exception as e:
            self.save_status_label.config(text="❌ 配置保存失败", foreground="red")
            self.log_message(f"配置保存失败: {e}", "ERROR")

    def reset_config(self):
        """重置配置"""
        if messagebox.askyesno("确认", "确定要重置所有配置到默认值吗？"):
            # 重置所有变量到默认值
            self.base_platform_var.set("auto")
            self.base_amount_var.set("50")
            self.max_hedge_amount_var.set("500")
            self.retry_attempts_var.set("3")
            self.retry_delay_var.set("2")
            self.timeout_seconds_var.set("30")
            self.base_first_delay_var.set("1.0")
            self.max_concurrent_hedges_var.set("1")
            self.duplicate_check_interval_var.set("1")
            self.enable_cross_platform_check_var.set(True)
            self.odds_change_threshold_var.set("1.0")
            self.ioratio_change_threshold_var.set("0.1")
            self.max_bets_per_match_var.set("3")
            self.enable_notifications_var.set(True)
            self.pinbet_ratio_var.set("1.0")
            self.crown_ratio_var.set("1.0")

            self.save_status_label.config(text="🔄 配置已重置", foreground="blue")
            self.log_message("配置已重置到默认值")

            # 3秒后清除状态
            self.root.after(3000, lambda: self.save_status_label.config(text=""))

    # ==================== 记录管理方法 ====================

    def query_records_by_type(self, record_type):
        """根据记录类型查询记录"""
        # 清空该类型的现有记录
        self.clear_records_display_by_type(record_type)

        if record_type == "hedge":
            self.query_hedge_records_by_type(record_type)
        elif record_type == "partial":
            self.query_partial_records_by_type(record_type)
        elif record_type == "pinbet":
            self.query_pinbet_records_by_type(record_type)
        elif record_type == "crown":
            self.query_crown_records_by_type(record_type)
        else:
            self.log_message("未知的记录类型", "ERROR")

    def export_records_by_type(self, record_type):
        """根据记录类型导出记录"""
        # 获取对应的表格
        tree_attr_name = f"{record_type}_records_tree"
        if hasattr(self, tree_attr_name):
            records_tree = getattr(self, tree_attr_name)
            self.export_records_from_tree(records_tree, record_type)
        else:
            self.log_message(f"未找到{record_type}记录表格", "ERROR")

    def clear_records_display_by_type(self, record_type):
        """根据记录类型清空记录显示"""
        tree_attr_name = f"{record_type}_records_tree"
        if hasattr(self, tree_attr_name):
            records_tree = getattr(self, tree_attr_name)
            for item in records_tree.get_children():
                records_tree.delete(item)
            self.log_message(f"{self.get_record_type_display_name(record_type)}记录显示已清空")
        else:
            self.log_message(f"未找到{record_type}记录表格", "ERROR")

    def get_record_type_code(self):
        """获取记录类型代码（保持兼容性）"""
        if hasattr(self, 'record_type_var'):
            record_type_name = self.record_type_var.get()
            type_codes = {"对冲记录": "hedge", "单边待补单": "partial", "平博记录": "pinbet", "皇冠记录": "crown"}
            return type_codes.get(record_type_name, "hedge")
        return "hedge"

    def get_record_type_name(self):
        """获取记录类型名称（保持兼容性）"""
        if hasattr(self, 'record_type_var'):
            return self.record_type_var.get()
        return "对冲记录"

    def query_records(self):
        """查询记录 - 点击查询按钮后执行（保持兼容性）"""
        record_type = self.get_record_type_code()

        # 清空现有记录
        self.clear_records_display()

        if record_type == "hedge":
            self.query_hedge_records()
        elif record_type == "partial":
            self.query_partial_records()
        elif record_type == "pinbet":
            self.query_pinbet_records()
        elif record_type == "crown":
            self.query_crown_records()
        else:
            self.log_message("未知的记录类型", "ERROR")

    def query_hedge_records_by_type(self, record_type):
        """查询对冲记录（按类型）"""
        self.log_message("正在查询对冲记录...")

        def records_worker():
            try:
                self.load_hedge_records_by_type(record_type)
                self.log_message("对冲记录查询完成")
            except Exception as e:
                self.log_message(f"对冲记录查询异常: {e}", "ERROR")

        threading.Thread(target=records_worker, daemon=True).start()

    def query_partial_records_by_type(self, record_type):
        """查询单边待补单记录（按类型）"""
        self.log_message("正在查询单边待补单记录...")

        def records_worker():
            try:
                self.load_partial_records_by_type(record_type)
                self.log_message("单边待补单记录查询完成")
            except Exception as e:
                self.log_message(f"单边待补单记录查询异常: {e}", "ERROR")

        threading.Thread(target=records_worker, daemon=True).start()

    def query_pinbet_records_by_type(self, record_type):
        """查询平博记录（按类型）"""
        self.log_message("正在查询平博投注记录...")

        def records_worker():
            try:
                self.load_pinbet_records_by_type(record_type)
                self.log_message("平博投注记录查询完成")
            except Exception as e:
                self.log_message(f"平博记录查询异常: {e}", "ERROR")

        threading.Thread(target=records_worker, daemon=True).start()

    def query_crown_records_by_type(self, record_type):
        """查询皇冠记录（按类型）"""
        self.log_message("正在查询皇冠投注记录...")

        def records_worker():
            try:
                self.load_crown_records_by_type(record_type)
                self.log_message("皇冠投注记录查询完成")
            except Exception as e:
                self.log_message(f"皇冠记录查询异常: {e}", "ERROR")

        threading.Thread(target=records_worker, daemon=True).start()

    def query_hedge_records(self):
        """查询对冲记录"""
        self.log_message("正在查询对冲记录...")

        def records_worker():
            try:
                self.load_hedge_records()
                self.log_message("对冲记录查询完成")
            except Exception as e:
                self.log_message(f"对冲记录查询异常: {e}", "ERROR")

        threading.Thread(target=records_worker, daemon=True).start()

    def query_partial_records(self):
        """查询单边待补单记录"""
        self.log_message("正在查询单边待补单记录...")

        def records_worker():
            try:
                self.load_partial_records()
                self.log_message("单边待补单记录查询完成")
            except Exception as e:
                self.log_message(f"单边待补单记录查询异常: {e}", "ERROR")

        threading.Thread(target=records_worker, daemon=True).start()

    def query_pinbet_records(self):
        """查询平博记录"""
        self.log_message("正在查询平博投注记录...")

        def records_worker():
            try:
                self.load_pinbet_records()
                self.log_message("平博投注记录查询完成")
            except Exception as e:
                self.log_message(f"平博记录查询异常: {e}", "ERROR")

        threading.Thread(target=records_worker, daemon=True).start()

    def query_crown_records(self):
        """查询皇冠记录"""
        self.log_message("正在查询皇冠投注记录...")

        def records_worker():
            try:
                self.load_crown_records()
                self.log_message("皇冠投注记录查询完成")
            except Exception as e:
                self.log_message(f"皇冠记录查询异常: {e}", "ERROR")

        threading.Thread(target=records_worker, daemon=True).start()

    def clear_records_display(self):
        """清空记录显示"""
        if hasattr(self, 'records_tree'):
            for item in self.records_tree.get_children():
                self.records_tree.delete(item)

    def load_hedge_records(self):
        """加载对冲记录 - 优先显示当天记录"""
        try:
            # 查找对冲记录文件
            hedge_records = []

            # 查找data目录下的对冲记录文件
            data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
            web_data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "web", "data")

            # 按优先级构建文件路径列表
            today = datetime.now().strftime("%Y-%m-%d")
            yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

            # 优先级文件列表：当天记录 > 昨天记录 > 备份记录
            priority_files = []
            backup_files = []

            for base_dir in [data_dir, web_data_dir]:
                if os.path.exists(base_dir):
                    # 最高优先级：今天的记录
                    today_file = os.path.join(base_dir, f"hedge_records_{today}.json")
                    if os.path.exists(today_file):
                        priority_files.insert(0, today_file)  # 插入到最前面

                    # 次优先级：昨天的记录
                    yesterday_file = os.path.join(base_dir, f"hedge_records_{yesterday}.json")
                    if os.path.exists(yesterday_file):
                        priority_files.append(yesterday_file)

                    # 最低优先级：备份文件
                    backups_dir = os.path.join(base_dir, "backups")
                    if os.path.exists(backups_dir):
                        for file in os.listdir(backups_dir):
                            if file.startswith("hedge_records_") and file.endswith(".json"):
                                backup_files.append(os.path.join(backups_dir, file))

            # 合并文件列表，优先级文件在前
            all_files = priority_files + backup_files

            # 优先加载当天记录
            today_records = []
            other_records = []
            loaded_count = 0

            for file_path in all_files:
                if os.path.exists(file_path):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            records_data = json.load(f)
                            if isinstance(records_data, list):
                                # 区分当天记录和其他记录
                                if f"hedge_records_{today}.json" in file_path:
                                    today_records.extend(records_data)
                                    self.log_message(f"优先加载当天对冲记录: {os.path.basename(file_path)}")
                                else:
                                    other_records.extend(records_data)
                                    self.log_message(f"加载历史对冲记录: {os.path.basename(file_path)}")
                                loaded_count += 1
                    except Exception as e:
                        self.log_message(f"加载文件 {file_path} 失败: {e}", "WARNING")

            # 优先显示当天记录，如果当天无记录则显示提示
            if today_records:
                hedge_records = today_records
                self.log_message(f"显示当天对冲记录 {len(today_records)} 条")
            else:
                # 当天无记录时，检查是否有历史记录可供参考
                if other_records:
                    self.log_message(f"当天无对冲记录，发现历史记录 {len(other_records)} 条")
                    # 询问用户是否要显示历史记录
                    hedge_records = []  # 默认不显示历史记录，只显示当天
                else:
                    self.log_message("未找到任何对冲记录")
                    hedge_records = []

            # 按时间倒序排列，只显示最近50条
            hedge_records.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            hedge_records = hedge_records[:50]

            # 格式化并显示记录
            for record in hedge_records:
                try:
                    created_at = record.get('created_at', '')
                    if created_at:
                        # 格式化时间显示
                        try:
                            dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                            created_at = dt.strftime("%Y-%m-%d %H:%M:%S")
                        except:
                            pass

                    match_info = record.get('match_info', '未知比赛')

                    # 格式化平博投注信息
                    pinbet_bet = record.get('pinbet_bet', {})
                    pinbet_info = "未投注"
                    if pinbet_bet and pinbet_bet.get('success'):
                        bet_type = pinbet_bet.get('bet_type', '')
                        team = pinbet_bet.get('team', '')
                        handicap = pinbet_bet.get('handicap_value', '')
                        amount = pinbet_bet.get('amount', 0)
                        odds = pinbet_bet.get('odds', 0)
                        pinbet_info = f"{bet_type} {team} {handicap} {amount}@{odds}"

                    # 格式化皇冠投注信息
                    crown_bet = record.get('crown_bet', {})
                    crown_info = "未投注"
                    if crown_bet and crown_bet.get('success'):
                        bet_type = crown_bet.get('bet_type', '')
                        team = crown_bet.get('team', '')
                        handicap = crown_bet.get('handicap_value', '')
                        amount = crown_bet.get('amount', 0)
                        odds = crown_bet.get('odds', 0)
                        crown_info = f"{bet_type} {team} {handicap} {amount}@{odds}"

                    # 计算总投注额
                    total_amount = record.get('total_amount', 0)
                    if not total_amount:
                        pinbet_amount = pinbet_bet.get('amount', 0) if pinbet_bet else 0
                        crown_amount = crown_bet.get('amount', 0) if crown_bet else 0
                        total_amount = pinbet_amount + crown_amount

                    status = record.get('status', 'unknown')

                    # 状态中文化
                    status_map = {
                        'completed': '已完成',
                        'pending': '进行中',
                        'failed': '失败',
                        'cancelled': '已取消',
                        'partial_success': '部分成功'
                    }
                    status_cn = status_map.get(status, status)

                    # 插入记录
                    record_values = (
                        created_at,
                        match_info[:40],  # 增加比赛名称显示长度
                        pinbet_info[:35],  # 增加投注信息显示长度
                        crown_info[:35],   # 增加投注信息显示长度
                        f"{total_amount:.0f}",
                        status_cn
                    )

                    self.records_tree.insert("", "end", values=record_values)

                except Exception as e:
                    self.log_message(f"格式化对冲记录失败: {e}", "WARNING")
                    continue

            if hedge_records:
                self.log_message(f"加载对冲记录 {len(hedge_records)} 条 (来自 {loaded_count} 个文件)")
            else:
                # 当天无记录时显示简单提示
                today = datetime.now().strftime("%Y-%m-%d")
                self.log_message(f"当天({today})暂无对冲记录", "INFO")
                # 显示无记录的提示
                self.records_tree.insert("", "end", values=(
                    today,
                    "当天暂无对冲记录",
                    "系统尚未执行对冲",
                    "请等待对冲机会",
                    "",
                    "无记录"
                ))

        except Exception as e:
            self.log_message(f"加载对冲记录异常: {e}", "ERROR")

    def load_hedge_records_by_type(self, record_type):
        """按类型加载对冲记录"""
        try:
            # 获取对应的表格
            tree_attr_name = f"{record_type}_records_tree"
            if not hasattr(self, tree_attr_name):
                self.log_message(f"未找到{record_type}记录表格", "ERROR")
                return

            records_tree = getattr(self, tree_attr_name)

            # 查找对冲记录文件
            hedge_records = []

            # 查找data目录下的对冲记录文件
            data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
            web_data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "web", "data")

            # 按优先级构建文件路径列表
            today = datetime.now().strftime("%Y-%m-%d")
            yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

            # 优先级文件列表：当天记录 > 昨天记录 > 备份记录
            priority_files = []
            backup_files = []

            for base_dir in [data_dir, web_data_dir]:
                if os.path.exists(base_dir):
                    # 最高优先级：今天的记录
                    today_file = os.path.join(base_dir, f"hedge_records_{today}.json")
                    if os.path.exists(today_file):
                        priority_files.insert(0, today_file)  # 插入到最前面

                    # 次优先级：昨天的记录
                    yesterday_file = os.path.join(base_dir, f"hedge_records_{yesterday}.json")
                    if os.path.exists(yesterday_file):
                        priority_files.append(yesterday_file)

                    # 最低优先级：备份文件
                    backups_dir = os.path.join(base_dir, "backups")
                    if os.path.exists(backups_dir):
                        for file in os.listdir(backups_dir):
                            if file.startswith("hedge_records_") and file.endswith(".json"):
                                backup_files.append(os.path.join(backups_dir, file))

            # 合并文件列表，优先级文件在前
            all_files = priority_files + backup_files

            # 优先加载当天记录
            today_records = []
            other_records = []
            loaded_count = 0

            for file_path in all_files:
                if os.path.exists(file_path):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            records_data = json.load(f)
                            if isinstance(records_data, list):
                                # 区分当天记录和其他记录
                                if f"hedge_records_{today}.json" in file_path:
                                    today_records.extend(records_data)
                                    self.log_message(f"优先加载当天对冲记录: {os.path.basename(file_path)}")
                                else:
                                    other_records.extend(records_data)
                                    self.log_message(f"加载历史对冲记录: {os.path.basename(file_path)}")
                                loaded_count += 1
                    except Exception as e:
                        self.log_message(f"加载文件 {file_path} 失败: {e}", "WARNING")

            # 优先显示当天记录，如果当天无记录则显示提示
            if today_records:
                hedge_records = today_records
                self.log_message(f"显示当天对冲记录 {len(today_records)} 条")
            else:
                # 当天无记录时，检查是否有历史记录可供参考
                if other_records:
                    self.log_message(f"当天无对冲记录，发现历史记录 {len(other_records)} 条")
                    # 询问用户是否要显示历史记录
                    hedge_records = []  # 默认不显示历史记录，只显示当天
                else:
                    self.log_message("未找到任何对冲记录")
                    hedge_records = []

            # 按时间倒序排列，只显示最近50条
            hedge_records.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            hedge_records = hedge_records[:50]

            # 格式化并显示记录
            for record in hedge_records:
                try:
                    created_at = record.get('created_at', '')
                    if created_at:
                        # 格式化时间显示
                        try:
                            dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                            created_at = dt.strftime("%Y-%m-%d %H:%M:%S")
                        except:
                            pass

                    match_info = record.get('match_info', '未知比赛')

                    # 格式化平博投注信息
                    pinbet_bet = record.get('pinbet_bet', {})
                    pinbet_info = "未投注"
                    if pinbet_bet and pinbet_bet.get('success'):
                        bet_type = pinbet_bet.get('bet_type', '')
                        team = pinbet_bet.get('team', '')
                        handicap = pinbet_bet.get('handicap_value', '')
                        amount = pinbet_bet.get('amount', 0)
                        odds = pinbet_bet.get('odds', 0)
                        pinbet_info = f"{bet_type} {team} {handicap} {amount}@{odds}"

                    # 格式化皇冠投注信息
                    crown_bet = record.get('crown_bet', {})
                    crown_info = "未投注"
                    if crown_bet and crown_bet.get('success'):
                        bet_type = crown_bet.get('bet_type', '')
                        team = crown_bet.get('team', '')
                        handicap = crown_bet.get('handicap_value', '')
                        amount = crown_bet.get('amount', 0)
                        odds = crown_bet.get('odds', 0)
                        crown_info = f"{bet_type} {team} {handicap} {amount}@{odds}"

                    # 计算总投注额
                    total_amount = record.get('total_amount', 0)
                    if not total_amount:
                        pinbet_amount = pinbet_bet.get('amount', 0) if pinbet_bet else 0
                        crown_amount = crown_bet.get('amount', 0) if crown_bet else 0
                        total_amount = pinbet_amount + crown_amount

                    status = record.get('status', 'unknown')

                    # 状态中文化
                    status_map = {
                        'completed': '已完成',
                        'pending': '进行中',
                        'failed': '失败',
                        'cancelled': '已取消',
                        'partial_success': '部分成功'
                    }
                    status_cn = status_map.get(status, status)

                    # 插入记录
                    record_values = (
                        created_at,
                        match_info[:40],  # 增加比赛名称显示长度
                        pinbet_info[:35],  # 增加投注信息显示长度
                        crown_info[:35],   # 增加投注信息显示长度
                        f"{total_amount:.0f}",
                        status_cn
                    )

                    records_tree.insert("", "end", values=record_values)

                except Exception as e:
                    self.log_message(f"格式化对冲记录失败: {e}", "WARNING")
                    continue

            if hedge_records:
                self.log_message(f"加载对冲记录 {len(hedge_records)} 条 (来自 {loaded_count} 个文件)")
            else:
                # 当天无记录时显示简单提示
                today = datetime.now().strftime("%Y-%m-%d")
                self.log_message(f"当天({today})暂无对冲记录", "INFO")
                # 显示无记录的提示
                records_tree.insert("", "end", values=(
                    today,
                    "当天暂无对冲记录",
                    "系统尚未执行对冲",
                    "请等待对冲机会",
                    "",
                    "无记录"
                ))

        except Exception as e:
            self.log_message(f"加载对冲记录异常: {e}", "ERROR")

    def load_partial_records_by_type(self, record_type):
        """按类型加载单边待补单记录"""
        try:
            # 获取对应的表格
            tree_attr_name = f"{record_type}_records_tree"
            if not hasattr(self, tree_attr_name):
                self.log_message(f"未找到{record_type}记录表格", "ERROR")
                return

            records_tree = getattr(self, tree_attr_name)

            # 导入单边记录管理器
            from hedge.partial_record_manager import PartialRecordManager

            partial_manager = PartialRecordManager()
            partial_records = partial_manager.get_partial_records(days=7)

            if partial_records:
                for record in partial_records:
                    try:
                        created_at = record.get('created_at', '')
                        if created_at:
                            # 格式化时间显示
                            try:
                                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                                created_at = dt.strftime('%m-%d %H:%M')
                            except:
                                pass

                        match_info = record.get('match_info', '未知比赛')
                        risk_type = record.get('risk_type', '未知风险')
                        total_amount = record.get('total_amount', 0)

                        # 分析成功和失败的平台
                        pinbet_bet = record.get('pinbet_bet', {})
                        crown_bet = record.get('crown_bet', {})

                        pinbet_success = pinbet_bet.get('success', False)
                        crown_success = crown_bet.get('success', False)

                        if pinbet_success and not crown_success:
                            success_platform = "平博"
                            failed_platform = "皇冠"
                        elif crown_success and not pinbet_success:
                            success_platform = "皇冠"
                            failed_platform = "平博"
                        else:
                            success_platform = "未知"
                            failed_platform = "未知"

                        # 插入记录
                        record_values = (
                            created_at,
                            match_info[:30],
                            risk_type,
                            success_platform,
                            failed_platform,
                            f"{total_amount:.0f}",
                            "待处理"
                        )

                        records_tree.insert("", "end", values=record_values)

                    except Exception as e:
                        self.log_message(f"格式化单边记录失败: {e}", "WARNING")
                        continue

                self.log_message(f"加载单边待补单记录 {len(partial_records)} 条")
            else:
                self.log_message("未找到单边待补单记录")
                # 显示无记录的提示
                records_tree.insert("", "end", values=(
                    datetime.now().strftime("%m-%d %H:%M"),
                    "暂无单边待补单记录",
                    "系统运行正常",
                    "",
                    "",
                    "",
                    "无记录"
                ))

        except Exception as e:
            self.log_message(f"加载单边待补单记录异常: {e}", "ERROR")

    def load_pinbet_records_by_type(self, record_type):
        """按类型加载平博记录"""
        try:
            # 获取对应的表格
            tree_attr_name = f"{record_type}_records_tree"
            if not hasattr(self, tree_attr_name):
                self.log_message(f"未找到{record_type}记录表格", "ERROR")
                return

            records_tree = getattr(self, tree_attr_name)

            if not self.hedge_system or not hasattr(self.hedge_system, 'pinbet_system'):
                self.log_message("平博系统未初始化", "WARNING")
                return

            pinbet_system = self.hedge_system.pinbet_system
            if not pinbet_system or not hasattr(pinbet_system, 'api'):
                self.log_message("平博API未初始化", "WARNING")
                return

            # 获取未结算注单
            success, wagers_data = pinbet_system.api.get_pending_wagers()
            if success and isinstance(wagers_data, dict):
                page_info = wagers_data.get('page', {})
                records = page_info.get('records', [])

                for record in records:
                    try:
                        # 平博API返回的是数组格式，需要按索引解析
                        if not record or len(record) < 15:
                            continue

                        # 解析记录数据 - 使用与load_pinbet_records相同的逻辑
                        # 投注时间 - 索引14
                        start_time = str(record[14]) if len(record) > 14 and record[14] is not None else ""

                        # 比赛信息 - 索引9
                        match_info = str(record[9]) if len(record) > 9 and record[9] is not None else "未知比赛"

                        # 检测比赛阶段（上半场/全场）
                        period = "全场"  # 默认为全场
                        if len(record) > 42 and record[42] is not None:
                            try:
                                event_phase = int(record[42])
                                if event_phase == 1:
                                    period = "上半场"  # 半场
                                elif event_phase == 0:
                                    period = "全场"    # 全场
                            except (ValueError, TypeError):
                                period = "全场"

                        # 投注信息 - 组合索引22和24，并加上阶段信息
                        bet_info = ""
                        if len(record) > 22 and record[22] is not None:
                            bet_info = str(record[22])
                        if len(record) > 24 and record[24] is not None and record[24] != record[22]:
                            additional_info = str(record[24])
                            if bet_info:
                                bet_info = f"{bet_info} {additional_info}"
                            else:
                                bet_info = additional_info

                        # 将比赛阶段信息添加到投注类型中显示
                        if period != "全场":
                            bet_info = f"[{period}] {bet_info}"

                        # 提取投注金额和可赢金额 - 使用与平博系统相同的逻辑
                        amount = 0.0
                        to_win = 0.0

                        # 查找RISK或WIN字段的位置
                        risk_type_index = -1
                        for i, value in enumerate(record):
                            if value == "RISK" or value == "WIN":
                                risk_type_index = i
                                break

                        # 如果找到了RISK字段，获取其后的投注金额和潜在赢取金额
                        if risk_type_index > 0 and risk_type_index + 2 < len(record):
                            # 潜在赢取金额通常在RISK后的第一个位置
                            if record[risk_type_index + 1] is not None:
                                try:
                                    to_win = float(record[risk_type_index + 1])
                                except (ValueError, TypeError):
                                    to_win = 0.0

                            # 投注金额通常在RISK后的第二个位置
                            if record[risk_type_index + 2] is not None:
                                try:
                                    amount = float(record[risk_type_index + 2])
                                except (ValueError, TypeError):
                                    amount = 0.0

                        # 如果上面的方法没找到，尝试直接使用索引24（如果存在）
                        if amount == 0.0 and len(record) > 24 and record[24] is not None:
                            try:
                                amount = float(record[24])
                            except (ValueError, TypeError):
                                amount = 0.0

                        # 提取赔率 - 根据抓包发现，赔率字段在索引25
                        odds = ""
                        if len(record) > 25 and record[25] is not None:
                            try:
                                odds_value = float(record[25])
                                odds = f"{odds_value:.2f}"
                            except (ValueError, TypeError):
                                # 如果索引25解析失败，尝试通过投注金额和可赢金额计算
                                if amount > 0 and to_win > 0:
                                    try:
                                        odds_value = (to_win + amount) / amount
                                        odds = f"{odds_value:.2f}"
                                    except ZeroDivisionError:
                                        odds = "1.00"
                                else:
                                    odds = "1.00"
                        else:
                            # 如果没有索引25，使用计算方式
                            if amount > 0 and to_win > 0:
                                try:
                                    odds_value = (to_win + amount) / amount
                                    odds = f"{odds_value:.2f}"
                                except ZeroDivisionError:
                                    odds = "1.00"
                            else:
                                odds = "1.00"

                        # 状态 - 检查索引18
                        status = "未结算"
                        if len(record) > 18 and record[18] is not None:
                            status_18 = str(record[18])
                            if status_18 in ["OPEN", "PENDING", "CLOSED"]:
                                status = status_18

                        # 添加到表格 - 新格式：开赛时间、比赛、投注信息、金额、赔率、状态
                        records_tree.insert("", "end", values=(
                            start_time, match_info, bet_info, amount, odds, status
                        ))
                    except Exception as e:
                        self.log_message(f"解析平博记录失败: {e}", "ERROR")
                        # 打印调试信息
                        self.log_message(f"记录数据: {record}", "DEBUG")

                self.log_message(f"加载平博记录 {len(records)} 条")
            else:
                self.log_message("获取平博记录失败", "WARNING")

        except Exception as e:
            self.log_message(f"加载平博记录异常: {e}", "ERROR")

    def load_crown_records_by_type(self, record_type):
        """按类型加载皇冠记录"""
        try:
            # 获取对应的表格
            tree_attr_name = f"{record_type}_records_tree"
            if not hasattr(self, tree_attr_name):
                self.log_message(f"未找到{record_type}记录表格", "ERROR")
                return

            records_tree = getattr(self, tree_attr_name)

            if not self.hedge_system or not hasattr(self.hedge_system, 'crown_system'):
                self.log_message("皇冠系统未初始化", "WARNING")
                return

            crown_system = self.hedge_system.crown_system
            if not crown_system:
                self.log_message("皇冠系统未初始化", "WARNING")
                return

            # 获取今日注单
            wagers = crown_system.get_today_wagers_direct(gtype="BK")  # 篮球注单
            if wagers:
                for wager in wagers:
                    try:
                        # 解析记录数据 - 使用与load_crown_records相同的逻辑

                        # 开赛时间 - 优先使用datetime字段（比赛开赛时间），如果没有则使用addtime（投注时间）
                        start_time = wager.get('datetime', wager.get('DATETIME', ''))
                        if not start_time:
                            start_time = wager.get('addtime', '')

                        # 比赛信息 - 使用team_h_show和team_c_show（参考web程序）
                        team_h = wager.get('team_h_show', wager.get('team_h', ''))
                        team_c = wager.get('team_c_show', wager.get('team_c', ''))
                        match_info = f"{team_h} vs {team_c}"

                        # 投注信息 - 组合投注类型和选择
                        bet_type_desc = wager.get('bet_type_desc', wager.get('bet_wtype', ''))
                        bet_choice = wager.get('bet_choice', wager.get('chose_team', ''))
                        period = wager.get('period', '')

                        # 组合投注信息
                        if period and period != "全场":
                            bet_info = f"{period} {bet_type_desc} {bet_choice}".strip()
                        else:
                            bet_info = f"{bet_type_desc} {bet_choice}".strip()

                        # 金额
                        amount = wager.get('gold', 0)

                        # 赔率 - 使用ioratio字段
                        odds = wager.get('ioratio', 0)

                        # 状态 - 使用web程序的状态转换逻辑
                        wager_status = wager.get('wager_status', '')
                        status = self.convert_crown_status(wager_status)

                        # 添加到表格 - 新格式：开赛时间、比赛、投注信息、金额、赔率、状态
                        records_tree.insert("", "end", values=(
                            start_time, match_info, bet_info, amount, odds, status
                        ))
                    except Exception as e:
                        self.log_message(f"解析皇冠记录失败: {e}", "ERROR")
                        # 打印调试信息
                        self.log_message(f"记录数据: {wager}", "DEBUG")

                self.log_message(f"加载皇冠记录 {len(wagers)} 条")
            else:
                self.log_message("获取皇冠记录失败", "WARNING")

        except Exception as e:
            self.log_message(f"加载皇冠记录异常: {e}", "ERROR")

    def export_records_from_tree(self, records_tree, record_type):
        """从指定的表格导出记录"""
        try:
            # 获取所有记录
            items = records_tree.get_children()
            if not items:
                self.log_message("没有记录可导出", "WARNING")
                return

            # 选择保存文件
            filename = filedialog.asksaveasfilename(
                title=f"导出{self.get_record_type_display_name(record_type)}记录",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if filename:
                import csv
                with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # 写入表头
                    columns = records_tree['columns']
                    headings = [records_tree.heading(col)['text'] for col in columns]
                    writer.writerow(headings)

                    # 写入数据
                    for item in items:
                        values = records_tree.item(item)['values']
                        writer.writerow(values)

                self.log_message(f"{self.get_record_type_display_name(record_type)}记录已导出到: {filename}")
            else:
                self.log_message("导出已取消")

        except Exception as e:
            self.log_message(f"导出记录失败: {e}", "ERROR")

    def load_partial_records(self):
        """加载单边待补单记录"""
        try:
            # 导入单边记录管理器
            from hedge.partial_record_manager import PartialRecordManager

            partial_manager = PartialRecordManager()
            partial_records = partial_manager.get_partial_records(days=7)

            if partial_records:
                for record in partial_records:
                    try:
                        created_at = record.get('created_at', '')
                        if created_at:
                            # 格式化时间显示
                            try:
                                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                                created_at = dt.strftime('%m-%d %H:%M')
                            except:
                                pass

                        match_info = record.get('match_info', '未知比赛')
                        risk_type = record.get('risk_type', '未知风险')
                        total_amount = record.get('total_amount', 0)

                        # 分析成功和失败的平台
                        pinbet_bet = record.get('pinbet_bet', {})
                        crown_bet = record.get('crown_bet', {})

                        pinbet_success = pinbet_bet.get('success', False)
                        crown_success = crown_bet.get('success', False)

                        if pinbet_success and not crown_success:
                            success_platform = "平博"
                            failed_platform = "皇冠"
                        elif crown_success and not pinbet_success:
                            success_platform = "皇冠"
                            failed_platform = "平博"
                        else:
                            success_platform = "未知"
                            failed_platform = "未知"

                        # 操作按钮（这里显示为文本，实际可以添加按钮功能）
                        actions = "待补单"

                        # 插入记录
                        record_values = (
                            created_at,
                            match_info[:30],
                            risk_type,
                            success_platform,
                            failed_platform,
                            f"{total_amount:.2f}",
                            actions
                        )

                        self.records_tree.insert("", "end", values=record_values)

                    except Exception as e:
                        self.log_message(f"格式化单边待补单记录失败: {e}", "WARNING")
                        continue

                self.log_message(f"加载单边待补单记录 {len(partial_records)} 条")
            else:
                self.log_message("未找到单边待补单记录")
                # 显示提示信息
                self.records_tree.insert("", "end", values=("", "暂无单边待补单记录", "", "", "", "", ""))

        except Exception as e:
            self.log_message(f"加载单边待补单记录异常: {e}", "ERROR")

    def load_pinbet_records(self):
        """加载平博投注记录 - 使用真实API"""
        try:
            if not self.hedge_system or not hasattr(self.hedge_system, 'pinbet_system'):
                self.log_message("平博系统未初始化", "WARNING")
                return

            pinbet_system = self.hedge_system.pinbet_system
            if not pinbet_system or not hasattr(pinbet_system, 'api'):
                self.log_message("平博API未初始化", "WARNING")
                return

            # 获取未结算注单
            success, wagers_data = pinbet_system.api.get_pending_wagers()
            if success and isinstance(wagers_data, dict):
                page_info = wagers_data.get('page', {})
                records = page_info.get('records', [])

                for record in records:
                    try:
                        # 平博API返回的是数组格式，需要按索引解析
                        if not record or len(record) < 15:
                            continue

                        # 解析记录数据 - 使用正确的索引
                        # 投注时间 - 索引14
                        start_time = str(record[14]) if len(record) > 14 and record[14] is not None else ""

                        # 比赛信息 - 索引9
                        match_info = str(record[9]) if len(record) > 9 and record[9] is not None else "未知比赛"

                        # 检测比赛阶段（上半场/全场）
                        period = "全场"  # 默认为全场
                        if len(record) > 42 and record[42] is not None:
                            try:
                                event_phase = int(record[42])
                                if event_phase == 1:
                                    period = "上半场"  # 半场
                                elif event_phase == 0:
                                    period = "全场"    # 全场
                            except (ValueError, TypeError):
                                period = "全场"

                        # 投注信息 - 组合索引22和24，并加上阶段信息
                        bet_info = ""
                        if len(record) > 22 and record[22] is not None:
                            bet_info = str(record[22])
                        if len(record) > 24 and record[24] is not None and record[24] != record[22]:
                            additional_info = str(record[24])
                            if bet_info:
                                bet_info = f"{bet_info} {additional_info}"
                            else:
                                bet_info = additional_info

                        # 将比赛阶段信息添加到投注类型中显示
                        if period != "全场":
                            bet_info = f"[{period}] {bet_info}"

                        # 提取投注金额和可赢金额 - 使用与平博系统相同的逻辑
                        amount = 0.0
                        to_win = 0.0

                        # 查找RISK或WIN字段的位置
                        risk_type_index = -1
                        for i, value in enumerate(record):
                            if value == "RISK" or value == "WIN":
                                risk_type_index = i
                                break

                        # 如果找到了RISK字段，获取其后的投注金额和潜在赢取金额
                        if risk_type_index > 0 and risk_type_index + 2 < len(record):
                            # 潜在赢取金额通常在RISK后的第一个位置
                            if record[risk_type_index + 1] is not None:
                                try:
                                    to_win = float(record[risk_type_index + 1])
                                except (ValueError, TypeError):
                                    to_win = 0.0

                            # 投注金额通常在RISK后的第二个位置
                            if record[risk_type_index + 2] is not None:
                                try:
                                    amount = float(record[risk_type_index + 2])
                                except (ValueError, TypeError):
                                    amount = 0.0

                        # 如果上面的方法没找到，尝试直接使用索引24（如果存在）
                        if amount == 0.0 and len(record) > 24 and record[24] is not None:
                            try:
                                amount = float(record[24])
                            except (ValueError, TypeError):
                                amount = 0.0

                        # 提取赔率 - 根据抓包发现，赔率字段在索引25
                        odds = ""
                        if len(record) > 25 and record[25] is not None:
                            try:
                                odds_value = float(record[25])
                                odds = f"{odds_value:.2f}"
                            except (ValueError, TypeError):
                                # 如果索引25解析失败，尝试通过投注金额和可赢金额计算
                                if amount > 0 and to_win > 0:
                                    try:
                                        odds_value = (to_win + amount) / amount
                                        odds = f"{odds_value:.2f}"
                                    except ZeroDivisionError:
                                        odds = "1.00"
                                else:
                                    odds = "1.00"
                        else:
                            # 如果没有索引25，使用计算方式
                            if amount > 0 and to_win > 0:
                                try:
                                    odds_value = (to_win + amount) / amount
                                    odds = f"{odds_value:.2f}"
                                except ZeroDivisionError:
                                    odds = "1.00"
                            else:
                                odds = "1.00"

                        # 状态 - 检查索引18
                        status = "未结算"
                        if len(record) > 18 and record[18] is not None:
                            status_18 = str(record[18])
                            if status_18 in ["OPEN", "PENDING", "CLOSED"]:
                                status = status_18

                        # 添加到表格 - 新格式：开赛时间、比赛、投注信息、金额、赔率、状态
                        self.records_tree.insert("", "end", values=(
                            start_time, match_info, bet_info, amount, odds, status
                        ))
                    except Exception as e:
                        self.log_message(f"解析平博记录失败: {e}", "ERROR")
                        # 打印调试信息
                        self.log_message(f"记录数据: {record}", "DEBUG")

                self.log_message(f"加载平博记录 {len(records)} 条")
            else:
                self.log_message("获取平博记录失败", "WARNING")

        except Exception as e:
            self.log_message(f"加载平博记录异常: {e}", "ERROR")

    def load_crown_records(self):
        """加载皇冠投注记录 - 使用真实API"""
        try:
            if not self.hedge_system or not hasattr(self.hedge_system, 'crown_system'):
                self.log_message("皇冠系统未初始化", "WARNING")
                return

            crown_system = self.hedge_system.crown_system
            if not crown_system:
                self.log_message("皇冠系统未初始化", "WARNING")
                return

            # 获取今日注单
            wagers = crown_system.get_today_wagers_direct(gtype="BK")  # 篮球注单
            if wagers:
                for wager in wagers:
                    try:
                        # 解析记录数据 - 参考web程序的处理逻辑

                        # 开赛时间 - 优先使用datetime字段（比赛开赛时间），如果没有则使用addtime（投注时间）
                        start_time = wager.get('datetime', wager.get('DATETIME', ''))
                        if not start_time:
                            start_time = wager.get('addtime', '')

                        # 比赛信息 - 使用team_h_show和team_c_show（参考web程序）
                        team_h = wager.get('team_h_show', wager.get('team_h', ''))
                        team_c = wager.get('team_c_show', wager.get('team_c', ''))
                        match_info = f"{team_h} vs {team_c}"

                        # 投注信息 - 组合投注类型和选择
                        bet_type_desc = wager.get('bet_type_desc', wager.get('bet_wtype', ''))
                        bet_choice = wager.get('bet_choice', wager.get('chose_team', ''))
                        period = wager.get('period', '')

                        # 组合投注信息
                        if period and period != "全场":
                            bet_info = f"{period} {bet_type_desc} {bet_choice}".strip()
                        else:
                            bet_info = f"{bet_type_desc} {bet_choice}".strip()

                        # 金额
                        amount = wager.get('gold', 0)

                        # 赔率 - 使用ioratio字段
                        odds = wager.get('ioratio', 0)

                        # 状态 - 使用web程序的状态转换逻辑
                        wager_status = wager.get('wager_status', '')
                        status = self.convert_crown_status(wager_status)

                        # 添加到表格 - 新格式：开赛时间、比赛、投注信息、金额、赔率、状态
                        self.records_tree.insert("", "end", values=(
                            start_time, match_info, bet_info, amount, odds, status
                        ))
                    except Exception as e:
                        self.log_message(f"解析皇冠记录失败: {e}", "ERROR")
                        # 打印调试信息
                        self.log_message(f"记录数据: {wager}", "DEBUG")

                self.log_message(f"加载皇冠记录 {len(wagers)} 条")
            else:
                self.log_message("获取皇冠记录失败", "WARNING")

        except Exception as e:
            self.log_message(f"加载皇冠记录异常: {e}", "ERROR")

    def parse_bet_type(self, bet_type, selection):
        """解析投注类型"""
        bet_type_map = {
            "1": "独赢",
            "2": "让分",
            "3": "大小",
            "4": "单队大小"
        }

        base_type = bet_type_map.get(str(bet_type), "未知")
        if selection:
            return f"{base_type}({selection})"
        return base_type

    def convert_crown_status(self, wager_status):
        """转换皇冠状态 - 只有明确显示"投注失败"时才标记为失败"""
        # 只有当明确出现"投注失败"时才标记为失败状态，其余都为成功状态
        if wager_status and "投注失败" in str(wager_status):
            return '失败'  # 失败
        else:
            return '成功'  # 成功（包括空值、"已成功"、"未知"等所有其他情况）

    def export_records(self):
        """导出记录"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8-sig', newline='') as f:
                    import csv
                    writer = csv.writer(f)

                    # 写入标题
                    writer.writerow(["时间", "平台", "比赛", "投注类型", "金额", "赔率", "状态"])

                    # 写入数据
                    for item in self.records_tree.get_children():
                        values = self.records_tree.item(item)['values']
                        writer.writerow(values)

                self.log_message(f"记录已导出到: {filename}")
                messagebox.showinfo("成功", f"记录已导出到:\n{filename}")

            except Exception as e:
                self.log_message(f"记录导出失败: {e}", "ERROR")
                messagebox.showerror("错误", f"记录导出失败:\n{e}")

    def clear_records(self):
        """清空记录（保留原方法名以兼容导出功能）"""
        if messagebox.askyesno("确认", "确定要清空所有投注记录吗？"):
            self.clear_records_display()
            self.log_message("投注记录显示已清空")





    # ==================== 自动刷新功能 ====================

    def toggle_auto_refresh(self):
        """切换自动刷新状态"""
        self.auto_refresh_enabled = self.auto_refresh_var.get()

        if self.auto_refresh_enabled:
            self.log_message("开启对冲记录自动刷新")
            self.start_auto_refresh()
        else:
            self.log_message("关闭对冲记录自动刷新")
            self.stop_auto_refresh()

    def start_auto_refresh(self):
        """开始自动刷新"""
        if self.auto_refresh_enabled:
            self.check_hedge_records_update()
            # 每5秒检查一次
            self.root.after(5000, self.start_auto_refresh)

    def stop_auto_refresh(self):
        """停止自动刷新"""
        self.auto_refresh_enabled = False

    def check_hedge_records_update(self):
        """检查对冲记录文件是否有更新"""
        try:
            # 只在对冲记录页面时才检查
            if not hasattr(self, 'record_type_var') or self.get_record_type_code() != "hedge":
                return

            # 获取当天对冲记录文件路径（与数据管理器保持一致）
            today = datetime.now().strftime("%Y-%m-%d")
            data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")

            # 使用与HedgeRecordManager一致的文件命名规则
            hedge_file_path = os.path.join(data_dir, f"hedge_records_{today}.json")

            # 检查文件是否存在
            if not os.path.exists(hedge_file_path):
                # 如果当天文件不存在，检查是否有其他日期的文件
                self._check_alternative_hedge_files(data_dir)
                return

            # 获取文件大小和修改时间
            current_size = os.path.getsize(hedge_file_path)
            current_mtime = os.path.getmtime(hedge_file_path)

            # 如果文件路径、大小或修改时间发生变化，说明有新记录
            if (hedge_file_path != self.last_hedge_file_path or
                current_size != self.last_hedge_file_size or
                not hasattr(self, 'last_hedge_file_mtime') or
                current_mtime != getattr(self, 'last_hedge_file_mtime', 0)):

                self.last_hedge_file_path = hedge_file_path
                self.last_hedge_file_size = current_size
                self.last_hedge_file_mtime = current_mtime

                # 自动刷新对冲记录
                self.log_message("检测到新的对冲记录，自动刷新...")
                self.refresh_hedge_records()

        except Exception as e:
            self.log_message(f"检查对冲记录更新失败: {e}", "WARNING")

    def _check_alternative_hedge_files(self, data_dir: str):
        """检查其他可能的对冲记录文件"""
        try:
            # 检查昨天的文件
            yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
            yesterday_file = os.path.join(data_dir, f"hedge_records_{yesterday}.json")

            if os.path.exists(yesterday_file):
                current_size = os.path.getsize(yesterday_file)
                if yesterday_file != self.last_hedge_file_path or current_size != self.last_hedge_file_size:
                    self.last_hedge_file_path = yesterday_file
                    self.last_hedge_file_size = current_size
                    self.log_message("检测到昨日对冲记录更新，自动刷新...")
                    self.refresh_hedge_records()

        except Exception as e:
            self.log_message(f"检查备选对冲记录文件失败: {e}", "WARNING")

    def refresh_hedge_records(self):
        """刷新对冲记录显示"""
        try:
            # 清空现有记录
            if hasattr(self, 'records_tree'):
                for item in self.records_tree.get_children():
                    self.records_tree.delete(item)

            # 重新加载记录
            self.load_hedge_records()

        except Exception as e:
            self.log_message(f"刷新对冲记录失败: {e}", "ERROR")

    # ==================== 窗口事件处理 ====================

    def on_closing(self):
        """窗口关闭事件"""
        try:
            # 停止自动刷新
            self.stop_auto_refresh()

            # 停止日志监控
            self.stop_log_monitoring()

            if self.hedge_monitoring:
                if messagebox.askokcancel("确认", "对冲监控正在运行，确定要退出吗？"):
                    self.hedge_monitoring = False
                    # 清理优化管理器
                    if self.optimization_enabled:
                        try:
                            shutdown_async_manager()
                            cleanup_memory_manager()
                            self.log_message("优化管理器已清理", "INFO")
                        except Exception as e:
                            print(f"清理优化管理器失败: {e}")
                    self.root.destroy()
            else:
                # 清理优化管理器
                if self.optimization_enabled:
                    try:
                        shutdown_async_manager()
                        cleanup_memory_manager()
                        self.log_message("优化管理器已清理", "INFO")
                    except Exception as e:
                        print(f"清理优化管理器失败: {e}")
                self.root.destroy()
        except Exception as e:
            self.log_message(f"关闭程序时出错: {e}", "ERROR")
            self.root.destroy()

    def run(self):
        """运行GUI"""
        self.log_message("BetBurger对冲投注系统GUI已启动")
        self.log_message("请先点击'初始化系统'，然后'登录系统'")
        self.root.mainloop()

def main():
    """主函数"""
    print("启动BetBurger对冲投注系统GUI...")

    try:
        app = BetBurgerGUI()
        app.run()
    except Exception as e:
        print(f"GUI启动失败: {e}")
        messagebox.showerror("错误", f"GUI启动失败:\n{e}")

if __name__ == "__main__":
    main()
