#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
比赛匹配学习系统
记录匹配失败的数据，分析原因，自动学习改进
"""

import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter

logger = logging.getLogger(__name__)


@dataclass
class MatchFailureRecord:
    """匹配失败记录"""
    timestamp: str
    source_platform: str  # 套利数据来源
    target_platform: str  # 目标平台（皇冠/平博）
    
    # 套利数据
    arbitrage_match: str
    arbitrage_league: str
    arbitrage_teams: List[str]
    arbitrage_time: str
    
    # 候选匹配数据
    candidate_matches: List[Dict]
    best_match_score: float
    best_match_data: Dict
    
    # 失败原因
    failure_reason: str
    failure_category: str  # team_mismatch, time_mismatch, league_mismatch, no_candidates
    
    # 分析数据
    analysis_notes: str = ""
    suggested_alias: Dict = None
    manual_review: bool = False


@dataclass
class LearningStats:
    """学习统计数据"""
    total_failures: int = 0
    by_category: Dict[str, int] = None
    by_platform: Dict[str, int] = None
    by_league: Dict[str, int] = None
    common_patterns: List[str] = None
    
    def __post_init__(self):
        if self.by_category is None:
            self.by_category = {}
        if self.by_platform is None:
            self.by_platform = {}
        if self.by_league is None:
            self.by_league = {}
        if self.common_patterns is None:
            self.common_patterns = []


class MatchLearningSystem:
    """比赛匹配学习系统"""
    
    def __init__(self, data_dir: str = "data/match_learning"):
        self.data_dir = data_dir
        self.failure_log_file = os.path.join(data_dir, "match_failures.jsonl")
        self.stats_file = os.path.join(data_dir, "learning_stats.json")
        self.suggestions_file = os.path.join(data_dir, "alias_suggestions.json")
        
        # 确保目录存在
        os.makedirs(data_dir, exist_ok=True)
        
        # 加载现有数据
        self.stats = self._load_stats()
        self.alias_suggestions = self._load_alias_suggestions()
        
        logger.info(f"匹配学习系统初始化完成，数据目录: {data_dir}")

    def record_failure(self, 
                      source_platform: str,
                      target_platform: str,
                      arbitrage_data: Dict,
                      candidate_matches: List[Dict],
                      best_match_score: float,
                      best_match_data: Dict,
                      failure_reason: str) -> str:
        """
        记录匹配失败
        
        返回:
            记录ID
        """
        try:
            # 解析套利数据
            arbitrage_match = arbitrage_data.get("match", "")
            arbitrage_teams = arbitrage_match.split(" - ") if " - " in arbitrage_match else arbitrage_match.split(" vs ")
            
            # 分析失败类别
            failure_category = self._analyze_failure_category(
                arbitrage_data, candidate_matches, best_match_score, failure_reason
            )
            
            # 创建失败记录
            record = MatchFailureRecord(
                timestamp=datetime.now().isoformat(),
                source_platform=source_platform,
                target_platform=target_platform,
                arbitrage_match=arbitrage_match,
                arbitrage_league=arbitrage_data.get("league", ""),
                arbitrage_teams=arbitrage_teams,
                arbitrage_time=arbitrage_data.get("time", ""),
                candidate_matches=candidate_matches[:5],  # 只保存前5个候选
                best_match_score=best_match_score,
                best_match_data=best_match_data,
                failure_reason=failure_reason,
                failure_category=failure_category
            )
            
            # 自动分析并生成建议
            self._analyze_and_suggest(record)
            
            # 保存记录
            record_id = self._save_failure_record(record)
            
            # 更新统计
            self._update_stats(record)
            
            logger.info(f"记录匹配失败: {record_id}, 类别: {failure_category}")
            return record_id
            
        except Exception as e:
            logger.error(f"记录匹配失败时出错: {e}")
            return ""

    def _analyze_failure_category(self, arbitrage_data: Dict, 
                                 candidate_matches: List[Dict],
                                 best_score: float,
                                 reason: str) -> str:
        """分析失败类别"""
        if not candidate_matches:
            return "no_candidates"
        
        if best_score < 0.3:
            return "team_mismatch"
        
        if "时间" in reason or "time" in reason.lower():
            return "time_mismatch"
        
        if "联赛" in reason or "league" in reason.lower():
            return "league_mismatch"
        
        if best_score < 0.6:
            return "low_confidence"
        
        return "unknown"

    def _analyze_and_suggest(self, record: MatchFailureRecord):
        """分析失败记录并生成改进建议"""
        try:
            suggestions = []
            
            # 1. 队伍名称别名建议
            if record.failure_category == "team_mismatch" and record.best_match_data:
                alias_suggestion = self._suggest_team_alias(record)
                if alias_suggestion:
                    record.suggested_alias = alias_suggestion
                    suggestions.append("建议添加队伍别名")
            
            # 2. 时间容差建议
            if record.failure_category == "time_mismatch":
                suggestions.append("建议调整时间匹配容差")
            
            # 3. 联赛映射建议
            if record.failure_category == "league_mismatch":
                suggestions.append("建议添加联赛映射")
            
            # 4. 阈值调整建议
            if record.failure_category == "low_confidence" and record.best_match_score > 0.5:
                suggestions.append(f"建议降低匹配阈值到{record.best_match_score:.2f}")
            
            record.analysis_notes = "; ".join(suggestions)
            
            # 标记需要人工审核的情况
            if record.best_match_score > 0.4 and record.failure_category in ["team_mismatch", "low_confidence"]:
                record.manual_review = True
                
        except Exception as e:
            logger.error(f"分析失败记录时出错: {e}")

    def _suggest_team_alias(self, record: MatchFailureRecord) -> Optional[Dict]:
        """建议队伍别名"""
        try:
            if not record.best_match_data or len(record.arbitrage_teams) != 2:
                return None
            
            # 获取最佳匹配的队伍名称
            best_match_teams = []
            if "TEAM_H" in record.best_match_data and "TEAM_C" in record.best_match_data:
                best_match_teams = [record.best_match_data["TEAM_H"], record.best_match_data["TEAM_C"]]
            elif "home_team" in record.best_match_data and "away_team" in record.best_match_data:
                best_match_teams = [record.best_match_data["home_team"], record.best_match_data["away_team"]]
            
            if len(best_match_teams) != 2:
                return None
            
            # 生成别名建议
            suggestion = {
                "league": record.arbitrage_league,
                "mappings": []
            }
            
            # 尝试正向和交叉匹配
            for i, arb_team in enumerate(record.arbitrage_teams):
                for j, match_team in enumerate(best_match_teams):
                    if self._is_potential_alias(arb_team, match_team):
                        suggestion["mappings"].append({
                            "standard_name": arb_team.strip(),
                            "alias": match_team.strip(),
                            "confidence": record.best_match_score
                        })
            
            return suggestion if suggestion["mappings"] else None
            
        except Exception as e:
            logger.error(f"生成队伍别名建议时出错: {e}")
            return None

    def _is_potential_alias(self, team1: str, team2: str) -> bool:
        """判断两个队伍名称是否可能是别名关系"""
        if not team1 or not team2:
            return False
        
        team1_lower = team1.lower().strip()
        team2_lower = team2.lower().strip()
        
        # 长度差异过大，不太可能是别名
        if abs(len(team1_lower) - len(team2_lower)) > max(len(team1_lower), len(team2_lower)) * 0.8:
            return False
        
        # 检查是否有公共词汇
        words1 = set(team1_lower.split())
        words2 = set(team2_lower.split())
        common_words = words1 & words2
        
        if common_words:
            return True
        
        # 检查是否有公共字符序列
        for i in range(len(team1_lower) - 2):
            substr = team1_lower[i:i+3]
            if substr in team2_lower:
                return True
        
        return False

    def _save_failure_record(self, record: MatchFailureRecord) -> str:
        """保存失败记录"""
        try:
            record_dict = asdict(record)
            record_id = f"{record.timestamp}_{record.source_platform}_{record.target_platform}"
            record_dict["record_id"] = record_id
            
            with open(self.failure_log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(record_dict, ensure_ascii=False) + '\n')
            
            return record_id
            
        except Exception as e:
            logger.error(f"保存失败记录时出错: {e}")
            return ""

    def _update_stats(self, record: MatchFailureRecord):
        """更新统计数据"""
        try:
            self.stats.total_failures += 1
            self.stats.by_category[record.failure_category] = self.stats.by_category.get(record.failure_category, 0) + 1
            self.stats.by_platform[record.target_platform] = self.stats.by_platform.get(record.target_platform, 0) + 1
            self.stats.by_league[record.arbitrage_league] = self.stats.by_league.get(record.arbitrage_league, 0) + 1
            
            # 保存统计数据
            self._save_stats()
            
        except Exception as e:
            logger.error(f"更新统计数据时出错: {e}")

    def get_learning_insights(self) -> Dict:
        """获取学习洞察"""
        try:
            insights = {
                "total_failures": self.stats.total_failures,
                "top_failure_categories": sorted(self.stats.by_category.items(), key=lambda x: x[1], reverse=True)[:5],
                "top_problem_platforms": sorted(self.stats.by_platform.items(), key=lambda x: x[1], reverse=True)[:3],
                "top_problem_leagues": sorted(self.stats.by_league.items(), key=lambda x: x[1], reverse=True)[:5],
                "pending_manual_reviews": self._count_pending_reviews(),
                "alias_suggestions_count": len(self.alias_suggestions),
                "recent_failures": self._get_recent_failures(hours=24)
            }
            
            return insights
            
        except Exception as e:
            logger.error(f"获取学习洞察时出错: {e}")
            return {}

    def _count_pending_reviews(self) -> int:
        """统计待人工审核的记录数量"""
        try:
            count = 0
            if os.path.exists(self.failure_log_file):
                with open(self.failure_log_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        record = json.loads(line.strip())
                        if record.get("manual_review", False):
                            count += 1
            return count
        except Exception as e:
            logger.error(f"统计待审核记录时出错: {e}")
            return 0

    def _get_recent_failures(self, hours: int = 24) -> List[Dict]:
        """获取最近的失败记录"""
        try:
            recent_failures = []
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            if os.path.exists(self.failure_log_file):
                with open(self.failure_log_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        record = json.loads(line.strip())
                        record_time = datetime.fromisoformat(record["timestamp"])
                        if record_time > cutoff_time:
                            recent_failures.append({
                                "timestamp": record["timestamp"],
                                "match": record["arbitrage_match"],
                                "category": record["failure_category"],
                                "score": record["best_match_score"]
                            })
            
            return sorted(recent_failures, key=lambda x: x["timestamp"], reverse=True)[:10]
            
        except Exception as e:
            logger.error(f"获取最近失败记录时出错: {e}")
            return []

    def _load_stats(self) -> LearningStats:
        """加载统计数据"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return LearningStats(**data)
            return LearningStats()
        except Exception as e:
            logger.error(f"加载统计数据时出错: {e}")
            return LearningStats()

    def _save_stats(self):
        """保存统计数据"""
        try:
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(self.stats), f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存统计数据时出错: {e}")

    def _load_alias_suggestions(self) -> List[Dict]:
        """加载别名建议"""
        try:
            if os.path.exists(self.suggestions_file):
                with open(self.suggestions_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logger.error(f"加载别名建议时出错: {e}")
            return []


# 全局实例
match_learning_system = MatchLearningSystem()
