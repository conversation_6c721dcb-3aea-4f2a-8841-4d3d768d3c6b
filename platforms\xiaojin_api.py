#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小金平台API实现
基于抓包文件实现登录、余额查询和未结算注单功能
"""

import requests
import json
import time
import logging
from typing import Dict, Any, Optional, Tuple, List
from urllib.parse import urlencode
import uuid

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class XiaojinAPI:
    """小金平台API客户端"""
    
    def __init__(self, jwt_token: Optional[str] = None):
        self.session = requests.Session()
        self.is_logged_in = False
        self.auth_token = None
        self.balance = 0.0
        
        # 基础URL
        self.login_base_url = "https://www.grandwanjiajin88.com"
        self.api_base_url = "https://sports-api.sbk-188-sports.com"
        
        # 设置基础headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'sec-ch-ua': '"Not=A?Brand";v="99", "Chromium";v="118"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        })
        
        # 生成设备标识
        self.client_id = str(uuid.uuid4())
        self.session_id = str(uuid.uuid4())
        self.tab_id = str(int(time.time() * 1000))[-6:]

        # 如果提供了JWT token，直接设置
        if jwt_token:
            self.set_jwt_token(jwt_token)
        
    def login(self, username: str, password: str) -> Tuple[bool, str]:
        """
        登录小金平台
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            (success, message): 登录结果和消息
        """
        try:
            logger.info(f"开始登录小金平台，用户名: {username}")
            
            # 1. 首先访问主页获取必要的cookies和tokens
            main_page_result = self._prepare_login_session()
            if not main_page_result:
                return False, "准备登录会话失败"
            
            # 2. 构建登录请求
            login_url = f"{self.login_base_url}/service/userapi/login"
            
            # 构建登录数据（基于抓包数据）
            login_data = {
                "ud": username,
                "pd": password,
                "blackbox": self._generate_blackbox(),
                "authtoken": self._generate_authtoken(),
                "fingerPrintId": self._generate_fingerprint(),
                "screenResolution": "960x1707",
                "deviceLanguage": "zh"
            }
            
            # 设置登录请求headers
            login_headers = {
                'Content-Type': 'application/json',
                'Origin': self.login_base_url,
                'Referer': f'{self.login_base_url}/zh-cn',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
            }
            
            # 发送登录请求
            response = self.session.post(
                login_url,
                json=login_data,
                headers=login_headers,
                timeout=30
            )
            
            logger.info(f"登录请求状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 检查响应内容
                try:
                    resp_data = response.json() if response.text.strip() else {}
                    logger.info(f"登录响应: {resp_data}")
                    
                    # 3. 登录成功后，需要获取体育平台的认证token
                    sports_token = self._get_sports_auth_token()
                    if sports_token:
                        self.auth_token = sports_token
                        self.is_logged_in = True
                        logger.info("小金平台登录成功")
                        return True, "登录成功"
                    else:
                        return False, "获取体育平台认证失败"
                        
                except json.JSONDecodeError:
                    # 某些情况下响应可能不是JSON格式
                    if "成功" in response.text or response.status_code == 200:
                        sports_token = self._get_sports_auth_token()
                        if sports_token:
                            self.auth_token = sports_token
                            self.is_logged_in = True
                            logger.info("小金平台登录成功")
                            return True, "登录成功"
                    
                    return False, f"登录响应解析失败: {response.text[:200]}"
            else:
                return False, f"登录失败，状态码: {response.status_code}"
                
        except requests.exceptions.RequestException as e:
            logger.error(f"登录请求异常: {e}")
            return False, f"网络请求失败: {str(e)}"
        except Exception as e:
            logger.error(f"登录过程异常: {e}")
            return False, f"登录异常: {str(e)}"
    
    def _prepare_login_session(self) -> bool:
        """准备登录会话，获取必要的cookies"""
        try:
            # 访问主页
            main_url = f"{self.login_base_url}/zh-cn"
            response = self.session.get(main_url, timeout=30)
            
            if response.status_code == 200:
                logger.info("成功访问主页，获取基础cookies")
                return True
            else:
                logger.error(f"访问主页失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"准备登录会话失败: {e}")
            return False
    
    def _generate_blackbox(self) -> str:
        """生成blackbox参数（简化版本）"""
        # 这是一个复杂的设备指纹，实际应用中需要更精确的实现
        # 这里使用抓包中的示例数据作为模板
        return "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"
    
    def _generate_authtoken(self) -> str:
        """生成authtoken参数"""
        # 基于抓包数据的示例token
        return "1Etp-I-IZ9Ss5lRZJ6gGKeDiv-C28V-N9IqoNlpBwF4."
    
    def _generate_fingerprint(self) -> str:
        """生成设备指纹"""
        # 基于抓包数据的示例指纹
        return "3d538a47eacde1ad2778207dac5166b4"
    
    def _get_sports_auth_token(self) -> Optional[str]:
        """获取体育平台的JWT认证token"""
        try:
            # 这里需要模拟获取体育平台token的过程
            # 实际实现中可能需要额外的请求来获取JWT token
            # 暂时使用抓包中的示例token
            sample_token = "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.wyUHVCoc3owiPkYziCeM8ytgANulUJjd0RWNeMjC5yI"
            
            logger.info("获取体育平台认证token成功")
            return sample_token
            
        except Exception as e:
            logger.error(f"获取体育平台token失败: {e}")
            return None

    def get_balance(self) -> Tuple[bool, Dict[str, Any]]:
        """
        获取账户余额

        Returns:
            (success, data): 成功标志和余额数据
        """
        if not self.is_logged_in or not self.auth_token:
            return False, {"error": "未登录或缺少认证token"}

        try:
            balance_url = f"{self.api_base_url}/api/v1/member/getbalance"

            headers = {
                'Authorization': f'Bearer {self.auth_token}',
                'Client': self.client_id,
                'SessionID': self.session_id,
                'TabId': self.tab_id,
                'DeviceType': 'Web',
                'Cache-Control': 'max-age=0',
                'Origin': 'https://sports.sbk-188-sports.com',
                'Referer': 'https://sports.sbk-188-sports.com/',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
            }

            response = self.session.get(balance_url, headers=headers, timeout=30)

            if response.status_code == 200:
                data = response.json()
                logger.info(f"余额查询成功: {data}")

                if data.get('r') == 0:  # r=0 表示成功
                    balance_amount = data.get('d', 0)
                    self.balance = balance_amount
                    return True, {
                        'balance': balance_amount,
                        'currency': 'CNY',
                        'raw_data': data
                    }
                else:
                    return False, {"error": f"余额查询失败: {data}"}
            else:
                logger.error(f"余额查询失败，状态码: {response.status_code}")
                return False, {"error": f"HTTP错误: {response.status_code}"}

        except requests.exceptions.RequestException as e:
            logger.error(f"余额查询网络异常: {e}")
            return False, {"error": f"网络请求失败: {str(e)}"}
        except Exception as e:
            logger.error(f"余额查询异常: {e}")
            return False, {"error": f"查询异常: {str(e)}"}

    def get_pending_bets(self) -> Tuple[bool, Dict[str, Any]]:
        """
        获取未结算注单

        Returns:
            (success, data): 成功标志和注单数据
        """
        if not self.is_logged_in or not self.auth_token:
            return False, {"error": "未登录或缺少认证token"}

        try:
            bets_url = f"{self.api_base_url}/api/v1/zh-cn/getmybet"

            headers = {
                'Authorization': f'Bearer {self.auth_token}',
                'Client': self.client_id,
                'SessionID': self.session_id,
                'TabId': self.tab_id,
                'DeviceType': 'Web',
                'Cache-Control': 'max-age=0',
                'Origin': 'https://sports.sbk-188-sports.com',
                'Referer': 'https://sports.sbk-188-sports.com/',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
            }

            response = self.session.get(bets_url, headers=headers, timeout=30)

            if response.status_code == 200:
                data = response.json()
                logger.info(f"注单查询成功，获取到 {len(data.get('d', {}).get('data', []))} 条记录")

                if data.get('r') == 0:  # r=0 表示成功
                    bet_data = data.get('d', {})
                    bets = bet_data.get('data', [])

                    # 解析注单数据
                    parsed_bets = []
                    for bet in bets:
                        parsed_bet = self._parse_bet_record(bet)
                        parsed_bets.append(parsed_bet)

                    return True, {
                        'total_count': len(parsed_bets),
                        'bets': parsed_bets,
                        'raw_data': data
                    }
                else:
                    return False, {"error": f"注单查询失败: {data}"}
            else:
                logger.error(f"注单查询失败，状态码: {response.status_code}")
                return False, {"error": f"HTTP错误: {response.status_code}"}

        except requests.exceptions.RequestException as e:
            logger.error(f"注单查询网络异常: {e}")
            return False, {"error": f"网络请求失败: {str(e)}"}
        except Exception as e:
            logger.error(f"注单查询异常: {e}")
            return False, {"error": f"查询异常: {str(e)}"}

    def _safe_float_parse(self, value: Any) -> float:
        """
        安全解析浮点数，处理包含逗号分隔符的数字字符串

        Args:
            value: 要解析的值

        Returns:
            解析后的浮点数，失败时返回0.0
        """
        try:
            if value is None:
                return 0.0

            # 如果已经是数字类型，直接返回
            if isinstance(value, (int, float)):
                return float(value)

            # 如果是字符串，移除逗号分隔符后解析
            if isinstance(value, str):
                # 移除逗号和空格
                cleaned_value = value.replace(',', '').replace(' ', '')
                if cleaned_value == '' or cleaned_value == 'null':
                    return 0.0
                return float(cleaned_value)

            # 其他类型尝试直接转换
            return float(value)

        except (ValueError, TypeError) as e:
            logger.error(f"数字解析失败: {value} -> {e}")
            return 0.0

    def _parse_bet_record(self, bet_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析注单记录

        Args:
            bet_data: 原始注单数据

        Returns:
            解析后的注单数据
        """
        try:
            # 获取基本信息
            bet_id = bet_data.get('id', '')
            bet_type = bet_data.get('t', '')
            competition = bet_data.get('cn', '')

            # 安全解析数字字段，移除逗号分隔符
            stake = self._safe_float_parse(bet_data.get('s', '0'))
            expected_payout = self._safe_float_parse(bet_data.get('ep', '0'))
            total_return = self._safe_float_parse(bet_data.get('rtep', '0'))

            status = bet_data.get('st', '')
            place_time = bet_data.get('pdt', '')

            # 获取投注详情
            legs = bet_data.get('l', [])
            bet_details = []

            for leg in legs:
                detail = {
                    'home_team': leg.get('h', ''),
                    'away_team': leg.get('a', ''),
                    'selection': leg.get('sn', ''),
                    'odds': self._safe_float_parse(leg.get('od', '0')),
                    'handicap': leg.get('hd', ''),
                    'match_time': leg.get('edt', ''),
                    'event_id': leg.get('eid', ''),
                    'market_type': leg.get('mty', ''),
                }
                bet_details.append(detail)

            return {
                'bet_id': bet_id,
                'bet_type': bet_type,
                'competition': competition,
                'stake': stake,
                'expected_payout': expected_payout,
                'total_return': total_return,
                'status': status,
                'place_time': place_time,
                'bet_details': bet_details,
                'is_settled': status not in [None, '', '确认'],
                'raw_data': bet_data
            }

        except Exception as e:
            logger.error(f"解析注单记录失败: {e}")
            return {
                'bet_id': bet_data.get('id', ''),
                'bet_type': bet_data.get('t', '未知'),
                'competition': bet_data.get('cn', '未知'),
                'stake': 0.0,
                'expected_payout': 0.0,
                'total_return': 0.0,
                'status': bet_data.get('st', '未知'),
                'place_time': bet_data.get('pdt', ''),
                'bet_details': [],
                'is_settled': False,
                'error': f"解析失败: {str(e)}",
                'raw_data': bet_data
            }

    def get_account_info(self) -> Dict[str, Any]:
        """
        获取账户综合信息

        Returns:
            账户信息字典
        """
        info = {
            'platform': '小金',
            'is_logged_in': self.is_logged_in,
            'balance': self.balance,
            'auth_token': bool(self.auth_token),
            'client_id': self.client_id,
            'session_id': self.session_id
        }

        if self.is_logged_in:
            # 获取最新余额
            balance_success, balance_data = self.get_balance()
            if balance_success:
                info['balance'] = balance_data.get('balance', 0)

            # 获取未结算注单数量
            bets_success, bets_data = self.get_pending_bets()
            if bets_success:
                info['pending_bets_count'] = bets_data.get('total_count', 0)

        return info

    def logout(self):
        """登出"""
        self.is_logged_in = False
        self.auth_token = None
        self.balance = 0.0
        self.session.cookies.clear()
        logger.info("已登出小金平台")

    def set_jwt_token(self, jwt_token: str) -> bool:
        """
        设置JWT Token，跳过登录直接使用token进行API调用

        Args:
            jwt_token: JWT认证token

        Returns:
            设置是否成功
        """
        try:
            if not jwt_token or not isinstance(jwt_token, str):
                logger.error("JWT Token无效")
                return False

            # 验证JWT token格式（应该有3个部分，用.分隔）
            parts = jwt_token.split('.')
            if len(parts) != 3:
                logger.error("JWT Token格式无效，应该包含3个部分")
                return False

            # 设置token和登录状态
            self.auth_token = jwt_token
            self.is_logged_in = True

            logger.info("JWT Token设置成功")
            return True

        except Exception as e:
            logger.error(f"设置JWT Token失败: {e}")
            return False

    def get_jwt_token_info(self) -> Dict[str, Any]:
        """
        获取JWT Token信息

        Returns:
            Token信息字典
        """
        try:
            if not self.auth_token:
                return {"error": "未设置JWT Token"}

            import base64

            # 解析JWT token的payload部分
            parts = self.auth_token.split('.')
            if len(parts) != 3:
                return {"error": "JWT Token格式无效"}

            # 解码payload（第二部分）
            payload = parts[1]
            # 添加必要的padding
            payload += '=' * (4 - len(payload) % 4)

            try:
                decoded_payload = base64.b64decode(payload)
                payload_data = json.loads(decoded_payload.decode('utf-8'))

                # 检查token是否过期
                current_time = int(time.time())
                exp_time = payload_data.get('exp', 0)
                is_expired = current_time > exp_time if exp_time else False

                return {
                    "user": payload_data.get('sub', 'Unknown'),
                    "issued_at": payload_data.get('iat', 0),
                    "expires_at": exp_time,
                    "is_expired": is_expired,
                    "payload": payload_data
                }

            except Exception as decode_error:
                return {"error": f"解码JWT Token失败: {decode_error}"}

        except Exception as e:
            return {"error": f"获取JWT Token信息失败: {e}"}

    def clear_jwt_token(self):
        """清除JWT Token"""
        self.auth_token = None
        self.is_logged_in = False
        logger.info("JWT Token已清除")


def main():
    """测试函数"""
    # 创建API实例
    api = XiaojinAPI()

    # 测试登录（需要提供真实的用户名和密码）
    username = input("请输入用户名: ")
    password = input("请输入密码: ")

    print("正在登录...")
    success, message = api.login(username, password)
    print(f"登录结果: {message}")

    if success:
        print("\n=== 账户信息 ===")
        account_info = api.get_account_info()
        for key, value in account_info.items():
            print(f"{key}: {value}")

        print("\n=== 余额查询 ===")
        balance_success, balance_data = api.get_balance()
        if balance_success:
            print(f"余额: {balance_data['balance']} {balance_data['currency']}")
        else:
            print(f"余额查询失败: {balance_data}")

        print("\n=== 未结算注单 ===")
        bets_success, bets_data = api.get_pending_bets()
        if bets_success:
            print(f"未结算注单数量: {bets_data['total_count']}")
            for i, bet in enumerate(bets_data['bets'][:3], 1):  # 只显示前3条
                print(f"\n注单 {i}:")
                print(f"  ID: {bet.get('bet_id', '未知')}")
                print(f"  类型: {bet.get('bet_type', '未知')}")
                print(f"  投注额: {bet.get('stake', 0)}")
                print(f"  状态: {bet.get('status', '未知')}")

                # 检查是否有解析错误
                if 'error' in bet:
                    print(f"  解析错误: {bet['error']}")

                # 安全访问投注详情
                bet_details = bet.get('bet_details', [])
                if bet_details:
                    detail = bet_details[0]
                    print(f"  比赛: {detail.get('home_team', '')} vs {detail.get('away_team', '')}")
                    print(f"  选择: {detail.get('selection', '')}")
                    print(f"  赔率: {detail.get('odds', 0)}")
                else:
                    print("  投注详情: 无")
        else:
            print(f"注单查询失败: {bets_data}")


if __name__ == "__main__":
    main()
