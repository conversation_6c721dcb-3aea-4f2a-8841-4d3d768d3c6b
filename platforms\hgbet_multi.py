#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import time
import argparse
import logging
import json
from datetime import datetime

# 导入HGBetBK类
from hgbet_bk import HGBetBK, load_config, setup_logging, fetch_latest_arbs

# 设置日志
logger = logging.getLogger(__name__)

class HGBetManager:
    """
    皇冠多账号投注管理器
    用于管理多个皇冠账号，实现多账号投注
    """
    
    def __init__(self):
        """初始化多账号管理器"""
        logger.info("初始化皇冠多账号投注管理器")
        self.config = load_config()
        self.accounts = []  # 存储所有账号实例和状态
        self.load_accounts()
    
    def load_accounts(self):
        """
        从配置文件加载所有账号
        """
        hgbet_config = self.config.get("hgbet", {})
        accounts_loaded = 0
        
        # 检查是否使用新版多账号配置
        if "accounts" in hgbet_config and isinstance(hgbet_config["accounts"], list):
            accounts = hgbet_config.get("accounts", [])
            logger.info(f"检测到{len(accounts)}个账号配置")
            
            for i, account_config in enumerate(accounts):
                if account_config.get("enabled", True):
                    try:
                        # 设置账号名称，如果没有则使用索引
                        if "account_name" not in account_config:
                            account_config["account_name"] = f"Account-{i+1}"
                            
                        account = HGBetBK(account_config=account_config)
                        self.accounts.append({
                            "instance": account,
                            "config": account_config,
                            "balance": 0.0,
                            "status": "init",  # 状态：init, logged_in, failed, disabled
                            "index": i
                        })
                        accounts_loaded += 1
                        logger.info(f"加载账号 [{account_config.get('account_name')}]: {account_config.get('username')}")
                    except Exception as e:
                        logger.error(f"加载账号 {account_config.get('username')} 失败: {e}")
                else:
                    logger.info(f"账号 {account_config.get('username')} 已禁用，跳过")
        else:
            # 尝试加载旧版单账号配置
            try:
                account = HGBetBK()
                username = account.username
                self.accounts.append({
                    "instance": account,
                    "config": {
                        "username": username,
                        "account_name": "Default",
                        "priority": 1,
                        "enabled": True
                    },
                    "balance": 0.0,
                    "status": "init",
                    "index": 0
                })
                accounts_loaded += 1
                logger.info(f"加载默认账号: {username}")
            except Exception as e:
                logger.error(f"加载默认账号失败: {e}")
        
        logger.info(f"共加载了 {accounts_loaded} 个账号")
    
    def login_all(self):
        """
        登录所有账号
        
        返回:
            登录成功的账号数量
        """
        success_count = 0
        
        for account in self.accounts:
            instance = account["instance"]
            config = account["config"]
            username = config.get("username")
            account_name = config.get("account_name", username)
            
            try:
                logger.info(f"尝试登录账号 [{account_name}]: {username}")
                if instance.login():
                    account["status"] = "logged_in"
                    account["balance"] = instance.credit
                    logger.info(f"账号 [{account_name}] 登录成功，余额: {instance.credit}")
                    success_count += 1
                else:
                    account["status"] = "failed"
                    logger.warning(f"账号 [{account_name}] 登录失败")
            except Exception as e:
                account["status"] = "failed"
                logger.error(f"账号 [{account_name}] 登录出错: {e}")
        
        logger.info(f"共 {success_count}/{len(self.accounts)} 个账号登录成功")
        return success_count
    
    def get_account_balances(self):
        """
        获取所有账号余额
        
        返回:
            账号余额信息的列表
        """
        balances = []
        
        for account in self.accounts:
            instance = account["instance"]
            config = account["config"]
            username = config.get("username")
            account_name = config.get("account_name", username)
            
            if account["status"] == "logged_in":
                try:
                    # 获取最新余额
                    instance.get_account_balance()
                    account["balance"] = instance.credit
                    
                    balances.append({
                        "username": username,
                        "account_name": account_name,
                        "balance": instance.credit,
                        "status": "active"
                    })
                    
                    logger.info(f"账号 [{account_name}] 余额: {instance.credit}")
                except Exception as e:
                    logger.error(f"获取账号 [{account_name}] 余额失败: {e}")
                    balances.append({
                        "username": username,
                        "account_name": account_name,
                        "balance": account["balance"],
                        "status": "error"
                    })
            else:
                balances.append({
                    "username": username,
                    "account_name": account_name,
                    "balance": account["balance"],
                    "status": account["status"]
                })
        
        return balances
    
    def process_bet_data_multi(self):
        """
        使用多账号处理套利数据并投注
        
        返回:
            bool: 是否找到有效投注选项
        """
        # 确保所有账号已登录
        for account in self.accounts:
            if account["status"] != "logged_in":
                instance = account["instance"]
                config = account["config"]
                username = config.get("username")
                account_name = config.get("account_name", username)
                
                try:
                    logger.info(f"尝试登录账号 [{account_name}]: {username}")
                    if instance.login():
                        account["status"] = "logged_in"
                        account["balance"] = instance.credit
                        logger.info(f"账号 [{account_name}] 登录成功，余额: {instance.credit}")
                    else:
                        logger.warning(f"账号 [{account_name}] 登录失败")
                except Exception as e:
                    logger.error(f"账号 [{account_name}] 登录出错: {e}")
        
        # 只使用已登录的账号
        active_accounts = [acc for acc in self.accounts if acc["status"] == "logged_in"]
        if not active_accounts:
            logger.error("没有可用的已登录账号，无法执行投注")
            return False
        
        # 按优先级排序账号（优先级数字小的优先）
        active_accounts.sort(key=lambda x: x["config"].get("priority", 999))
        
        # 获取套利数据
        arb_data = fetch_latest_arbs()
        if not arb_data:
            logger.debug("未找到套利数据")
            return False
        
        logger.info(f"获取到{len(arb_data)}条套利数据")
        
        processed_count = 0
        found_valid_bet = False
        
        for arb_item in arb_data:
            try:
                # 尝试每个账号处理这个套利项
                for account in active_accounts:
                    instance = account["instance"]
                    config = account["config"]
                    username = config.get("username")
                    account_name = config.get("account_name", username)
                    
                    # 检查账号余额限制
                    min_balance = config.get("min_balance", 0)
                    max_balance = config.get("max_balance", float('inf'))
                    
                    if instance.credit < min_balance:
                        logger.warning(f"账号 [{account_name}] 余额 {instance.credit} 低于最小余额限制 {min_balance}，跳过")
                        continue
                        
                    if instance.credit > max_balance:
                        logger.warning(f"账号 [{account_name}] 余额 {instance.credit} 超过最大余额限制 {max_balance}，跳过")
                        continue
                    
                    # 解析套利数据
                    bet_info = instance._parse_bet_info_from_data(arb_item)
                    
                    if not bet_info:
                        logger.debug(f"账号 [{account_name}] 无法解析套利数据，跳过")
                        continue
                        
                    # 检查是否已经下过注
                    if instance.is_bet_duplicated(bet_info):
                        logger.info(f"账号 [{account_name}] 检测到重复投注，跳过")
                        continue
                    
                    # 检查投注条件
                    if not instance._should_bet_on_option(arb_item):
                        logger.debug(f"账号 [{account_name}] 投注条件不满足，跳过")
                        continue
                    
                    # 执行投注
                    bet_result = instance.place_bet_simple(bet_info)
                    if bet_result:
                        found_valid_bet = True
                        account["balance"] = instance.credit  # 更新余额
                        logger.info(f"账号 [{account_name}] 成功执行投注: {bet_info.get('home_team')} vs {bet_info.get('away_team')}, "
                                  f"{bet_info.get('bet_type')}={bet_info.get('bet_team')}, 余额: {instance.credit}")
                        break  # 一个套利项只用一个账号投注
                    else:
                        logger.warning(f"账号 [{account_name}] 投注失败")
                    
                    processed_count += 1
                    
            except Exception as e:
                logger.error(f"处理套利项时出错: {e}")
                continue
                
        # 统计处理结果
        if processed_count > 0 or found_valid_bet:
            logger.info(f"本轮共处理了 {processed_count} 个套利项，{'找到' if found_valid_bet else '未找到'}有效的投注选项")
        
        return found_valid_bet

def main():
    """主程序入口"""
    # 初始化日志
    setup_logging()
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='皇冠篮球多账号投注系统')
    parser.add_argument('--interval', type=int, default=60, 
                       help='自动投注检查间隔（秒）')
    args = parser.parse_args()
    
    # 多账号模式
    print("\n===== 皇冠篮球多账号投注系统 =====")
    manager = HGBetManager()
    
    if not manager.accounts:
        logger.error("未找到有效账号配置，请检查config.json文件")
        return
        
    # 登录所有账号
    success_count = manager.login_all()
    if success_count == 0:
        logger.error("所有账号登录失败，程序退出")
        return
        
    # 获取所有账号余额
    balances = manager.get_account_balances()
    print("\n账号余额信息：")
    for i, balance in enumerate(balances):
        print(f"{i+1}. [{balance['account_name']}]: {balance['username']} - 余额: {balance['balance']} - 状态: {balance['status']}")
        
    # 进入菜单循环
    while True:
        print("\n===== 皇冠篮球多账号投注系统 =====")
        print("1. 获取所有账号余额")
        print("2. 手动处理套利数据并投注（单次）")
        print("3. 自动处理套利数据并投注（循环）")
        print("0. 退出")
        
        choice = input("请选择操作: ")
        
        if choice == "1":
            # 获取所有账号余额
            balances = manager.get_account_balances()
            print("\n账号余额信息：")
            for i, balance in enumerate(balances):
                print(f"{i+1}. [{balance['account_name']}]: {balance['username']} - 余额: {balance['balance']} - 状态: {balance['status']}")
                
        elif choice == "2":
            # 手动处理套利数据并投注（单次）
            print("开始处理套利数据并投注...")
            result = manager.process_bet_data_multi()
            print(f"处理完成，{'找到' if result else '未找到'}有效投注")
            
        elif choice == "3":
            # 自动处理套利数据并投注（循环）
            try:
                print(f"开始自动处理套利数据并投注，检查间隔 {args.interval} 秒...")
                print("按 Ctrl+C 停止自动投注")
                
                while True:
                    try:
                        manager.process_bet_data_multi()
                        time.sleep(args.interval)
                        
                        # 定期检查账号状态，确保都处于登录状态
                        for account in manager.accounts:
                            if account["status"] != "logged_in" and account["config"].get("enabled", True):
                                instance = account["instance"]
                                account_name = account["config"].get("account_name")
                                logger.info(f"账号 [{account_name}] 未登录，尝试重新登录")
                                instance.login()
                                
                    except KeyboardInterrupt:
                        print("\n自动投注已停止")
                        break
                    except Exception as e:
                        logger.error(f"自动投注过程中出错: {e}")
                        time.sleep(5)  # 出错后等待一段时间再继续
                        
            except KeyboardInterrupt:
                print("\n自动投注已停止")
                
        elif choice == "0":
            print("谢谢使用，再见!")
            break
            
        else:
            print("无效的选择，请重新输入")

if __name__ == "__main__":
    main() 