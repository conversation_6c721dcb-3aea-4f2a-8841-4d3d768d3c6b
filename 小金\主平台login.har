{"_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "1440550", "pageref": "page_1", "request": {"method": "POST", "url": "https://www.grandwanjiajin88.com/service/userapi/login", "httpVersion": "http/2.0", "headers": [{"name": ":authority", "value": "www.grandwanjiajin88.com"}, {"name": ":method", "value": "POST"}, {"name": ":path", "value": "/service/userapi/login"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "application/json, text/plain, */*"}, {"name": "accept-encoding", "value": "gzip, deflate, br"}, {"name": "accept-language", "value": "zh-CN,zh;q=0.9"}, {"name": "content-length", "value": "2262"}, {"name": "content-type", "value": "application/json"}, {"name": "cookie", "value": "fp_token_7c6a6574-f011-4c9a-abdd-9894a102ccef=9HTRcTFQ5co3pJyAJ4x32nD7TZRQWJSXWxQwXOOabeY=; check=%7B%22cookieNotify%22%3Atrue%2C%22notifySmartBanner%22%3Atrue%2C%22alreadyPop%22%3Afalse%2C%22notifyEmailVerify%22%3Afalse%7D; a-188mem=438832906.20480.0000; recentlyGames=%5B%22mahjongways2%22%2C%22luckyneko%22%2C%22vs20olympgate%22%2C%22hlcs%22%2C%22swcsy%22%2C%22swkxcs%22%2C%22ptxpope77bbaafqsp%22%2C%22tsartreasures%22%2C%22graffitirush%22%2C%22mahjongways%22%5D; ASP.NET_SessionId=lyqrac5mewd0qsmzl24s2ule; prefer=%7B%22sb%22%3Atrue%2C%22c%22%3A%22CN%22%2C%22cid%22%3A44%2C%22r%22%3A%22China%22%2C%22l%22%3A%22zh-cn%2Cen-gb%22%2C%22did%22%3A%22837082747276964345%22%2C%22ratelist%22%3A%7B%7D%7D"}, {"name": "origin", "value": "https://www.grandwanjiajin88.com"}, {"name": "referer", "value": "https://www.grandwanjiajin88.com/zh-cn"}, {"name": "sec-ch-ua", "value": "\"Not=A?Brand\";v=\"99\", \"Chromium\";v=\"118\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36"}], "queryString": [], "cookies": [{"name": "fp_token_7c6a6574-f011-4c9a-abdd-9894a102ccef", "value": "9HTRcTFQ5co3pJyAJ4x32nD7TZRQWJSXWxQwXOOabeY=", "path": "/", "domain": "www.grandwanjiajin88.com", "expires": "2026-07-01T02:01:06.698Z", "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "check", "value": "%7B%22cookieNotify%22%3Atrue%2C%22notifySmartBanner%22%3Atrue%2C%22alreadyPop%22%3Afalse%2C%22notifyEmailVerify%22%3Afalse%7D", "path": "/", "domain": "www.grandwanjiajin88.com", "expires": "2025-07-29T14:49:36.385Z", "httpOnly": false, "secure": true, "sameSite": "Lax"}, {"name": "a-188mem", "value": "438832906.20480.0000", "path": "/", "domain": "www.grandwanjiajin88.com", "expires": "1969-12-31T23:59:59.000Z", "httpOnly": true, "secure": true}, {"name": "recentlyGames", "value": "%5B%22mahjongways2%22%2C%22luckyneko%22%2C%22vs20olympgate%22%2C%22hlcs%22%2C%22swcsy%22%2C%22swkxcs%22%2C%22ptxpope77bbaafqsp%22%2C%22tsartreasures%22%2C%22graffitirush%22%2C%22mahjongways%22%5D", "path": "/", "domain": "www.grandwanjiajin88.com", "expires": "2025-07-30T14:57:12.529Z", "httpOnly": false, "secure": true, "sameSite": "Lax"}, {"name": "ASP.NET_SessionId", "value": "lyqrac5mewd0qsmzl24s2ule", "path": "/", "domain": "www.grandwanjiajin88.com", "expires": "1969-12-31T23:59:59.000Z", "httpOnly": true, "secure": false, "sameSite": "Lax"}, {"name": "prefer", "value": "%7B%22sb%22%3Atrue%2C%22c%22%3A%22CN%22%2C%22cid%22%3A44%2C%22r%22%3A%22China%22%2C%22l%22%3A%22zh-cn%2Cen-gb%22%2C%22did%22%3A%22837082747276964345%22%2C%22ratelist%22%3A%7B%7D%7D", "path": "/", "domain": "www.grandwanjiajin88.com", "expires": "2025-07-31T02:01:04.297Z", "httpOnly": false, "secure": true, "sameSite": "Lax"}], "headersSize": -1, "bodySize": 2262, "postData": {"mimeType": "application/json", "text": "{\"ud\":\"<EMAIL>\",\"pd\":\"123456\",\"blackbox\":\"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\",\"authtoken\":\"1Etp-I-IZ9Ss5lRZJ6gGKeDiv-C28V-N9IqoNlpBwF4.\",\"fingerPrintId\":\"3d538a47eacde1ad2778207dac5166b4\",\"screenResolution\":\"960x1707\",\"deviceLanguage\":\"zh\"}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "http/2.0", "headers": [{"name": "access-control-allow-origin", "value": "*"}, {"name": "cache-control", "value": "no-cache"}, {"name": "content-encoding", "value": "gzip"}, {"name": "content-type", "value": "application/json; charset=utf-8"}, {"name": "date", "value": "Tue, 01 Jul 2025 02:01:19 GMT"}, {"name": "expires", "value": "-1"}, {"name": "pragma", "value": "no-cache"}, {"name": "server", "value": "Microsoft-IIS/10.0"}, {"name": "set-cookie", "value": "ASP.NET_SessionId=; expires=Mon, 30-Jun-2025 02:01:20 GMT; path=/"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "x-aspnet-version", "value": "4.0.30319"}], "cookies": [{"name": "ASP.NET_SessionId", "value": "", "path": "/", "domain": "www.grandwanjiajin88.com", "expires": "2025-06-30T02:01:20.000Z", "httpOnly": false, "secure": false}], "content": {"size": 115, "mimeType": "application/json"}, "redirectURL": "", "headersSize": -1, "bodySize": -1, "_transferSize": 243, "_error": null}}