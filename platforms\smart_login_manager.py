#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能登录管理器
解决频繁登录触发验证码的问题
"""

import time
import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Tuple, Optional

logger = logging.getLogger(__name__)

class SmartLoginManager:
    """智能登录管理器"""
    
    def __init__(self, platform_name: str = "pinbet"):
        self.platform_name = platform_name
        self.session_file = f"data/{platform_name}_session.json"
        self.login_history_file = f"data/{platform_name}_login_history.json"
        
        # 登录控制参数
        self.min_login_interval = 30  # 最小登录间隔（秒）- 30秒（进一步降低限制）
        self.max_login_attempts_per_hour = 15  # 每小时最大登录尝试次数（进一步增加限制）
        self.session_validity_hours = 12  # 会话有效期（小时）
        # 移除验证码冷却机制，允许随时获取验证码
        
        # 确保数据目录存在
        os.makedirs("data", exist_ok=True)
        
        # 加载历史数据
        self.login_history = self._load_login_history()
        self.session_data = self._load_session_data()
    
    def _load_login_history(self) -> Dict:
        """加载登录历史"""
        try:
            if os.path.exists(self.login_history_file):
                with open(self.login_history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载登录历史失败: {e}")
        
        return {
            "attempts": [],
            "last_captcha_trigger": None,
            "consecutive_failures": 0
        }
    
    def _save_login_history(self):
        """保存登录历史"""
        try:
            with open(self.login_history_file, 'w', encoding='utf-8') as f:
                json.dump(self.login_history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存登录历史失败: {e}")
    
    def _load_session_data(self) -> Dict:
        """加载会话数据"""
        try:
            if os.path.exists(self.session_file):
                with open(self.session_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载会话数据失败: {e}")
        
        return {}
    
    def _save_session_data(self):
        """保存会话数据"""
        try:
            with open(self.session_file, 'w', encoding='utf-8') as f:
                json.dump(self.session_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存会话数据失败: {e}")
    
    def is_session_valid(self) -> bool:
        """检查当前会话是否有效"""
        if not self.session_data:
            return False
        
        try:
            login_time_str = self.session_data.get("login_time")
            if not login_time_str:
                return False
            
            login_time = datetime.fromisoformat(login_time_str)
            expiry_time = login_time + timedelta(hours=self.session_validity_hours)
            
            is_valid = datetime.now() < expiry_time
            
            if is_valid:
                logger.info(f"会话仍然有效，剩余时间: {expiry_time - datetime.now()}")
            else:
                logger.info("会话已过期")
            
            return is_valid
            
        except Exception as e:
            logger.error(f"检查会话有效性失败: {e}")
            return False
    
    def can_attempt_login(self) -> Tuple[bool, str]:
        """检查是否可以尝试登录"""
        now = datetime.now()
        
        # 1. 检查最小登录间隔（移除验证码冷却检查，允许随时获取验证码）
        attempts = self.login_history.get("attempts", [])
        if attempts:
            try:
                last_attempt = datetime.fromisoformat(attempts[-1]["timestamp"])
                min_next_time = last_attempt + timedelta(seconds=self.min_login_interval)
                
                if now < min_next_time:
                    remaining = min_next_time - now
                    return False, f"登录间隔不足，剩余时间: {remaining}"
            except Exception as e:
                logger.warning(f"解析最后登录时间失败: {e}")
        
        # 3. 检查每小时登录次数限制
        one_hour_ago = now - timedelta(hours=1)
        recent_attempts = [
            attempt for attempt in attempts
            if datetime.fromisoformat(attempt["timestamp"]) > one_hour_ago
        ]
        
        if len(recent_attempts) >= self.max_login_attempts_per_hour:
            return False, f"每小时登录次数已达上限 ({self.max_login_attempts_per_hour}次)"
        
        return True, "可以尝试登录"
    
    def record_login_attempt(self, success: bool, error_code: Optional[int] = None, message: str = ""):
        """记录登录尝试"""
        attempt = {
            "timestamp": datetime.now().isoformat(),
            "success": success,
            "error_code": error_code,
            "message": message
        }
        
        self.login_history["attempts"].append(attempt)
        
        # 只保留最近100次尝试
        if len(self.login_history["attempts"]) > 100:
            self.login_history["attempts"] = self.login_history["attempts"][-100:]
        
        # 更新连续失败次数
        if success:
            self.login_history["consecutive_failures"] = 0
        else:
            self.login_history["consecutive_failures"] += 1
        
        # 记录验证码触发（仅用于统计，不再设置冷却）
        if error_code == 16:  # 需要验证码
            self.login_history["last_captcha_trigger"] = datetime.now().isoformat()
            logger.info("触发验证码机制")
        
        self._save_login_history()
        logger.info(f"记录登录尝试: 成功={success}, 错误码={error_code}, 消息={message}")
    
    def record_successful_login(self, cookies: Dict, tokens: Dict = None):
        """记录成功登录的会话信息"""
        self.session_data = {
            "login_time": datetime.now().isoformat(),
            "cookies": cookies,
            "tokens": tokens or {},
            "platform": self.platform_name
        }
        
        self._save_session_data()
        logger.info("保存登录会话信息")
    
    def get_session_data(self) -> Dict:
        """获取会话数据"""
        return self.session_data
    
    def clear_session(self):
        """清除会话数据"""
        self.session_data = {}
        if os.path.exists(self.session_file):
            os.remove(self.session_file)
        logger.info("清除会话数据")
    
    def get_login_statistics(self) -> Dict:
        """获取登录统计信息"""
        attempts = self.login_history.get("attempts", [])
        
        if not attempts:
            return {
                "total_attempts": 0,
                "success_rate": 0,
                "last_attempt": None,
                "consecutive_failures": 0
            }
        
        total_attempts = len(attempts)
        successful_attempts = sum(1 for attempt in attempts if attempt["success"])
        success_rate = successful_attempts / total_attempts * 100
        
        return {
            "total_attempts": total_attempts,
            "successful_attempts": successful_attempts,
            "success_rate": round(success_rate, 2),
            "last_attempt": attempts[-1]["timestamp"],
            "consecutive_failures": self.login_history.get("consecutive_failures", 0),
            "last_captcha_trigger": self.login_history.get("last_captcha_trigger")
        }
    
    def should_use_manual_login(self) -> Tuple[bool, str]:
        """判断是否应该使用手动登录"""
        consecutive_failures = self.login_history.get("consecutive_failures", 0)

        # 只有在连续失败次数过多时才建议手动登录
        # 移除验证码触发的手动登录建议，因为我们有自动验证码处理
        if consecutive_failures >= 5:  # 提高阈值，从3次改为5次
            return True, f"连续失败{consecutive_failures}次，建议手动登录"

        return False, "可以尝试自动登录"
    
    def get_recommended_action(self) -> str:
        """获取推荐的操作"""
        if self.is_session_valid():
            return "使用现有会话"
        
        can_login, reason = self.can_attempt_login()
        if not can_login:
            return f"等待: {reason}"
        
        should_manual, manual_reason = self.should_use_manual_login()
        if should_manual:
            return f"手动登录: {manual_reason}"
        
        return "可以尝试自动登录"
