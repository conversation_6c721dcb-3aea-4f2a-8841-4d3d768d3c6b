#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from collections import deque

logger = logging.getLogger(__name__)

@dataclass
class ExecutionRecord:
    """执行记录数据类"""
    timestamp: datetime
    success: bool
    duration: float
    platform: str
    match_key: str
    error_type: Optional[str] = None
    is_duplicate: bool = False
    retry_count: int = 0

@dataclass
class MetricsSummary:
    """指标摘要数据类"""
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    duplicate_executions: int = 0
    avg_duration: float = 0.0
    success_rate: float = 0.0
    error_distribution: Dict[str, int] = field(default_factory=dict)
    platform_stats: Dict[str, Dict] = field(default_factory=dict)

class HedgeMetrics:
    """对冲投注监控指标管理器"""
    
    def __init__(self, max_records: int = 1000):
        """
        初始化指标管理器
        
        参数:
            max_records: 最大记录数量
        """
        self.max_records = max_records
        self.records: deque = deque(maxlen=max_records)
        self._lock = threading.RLock()
        
        # 实时统计
        self.total_count = 0
        self.success_count = 0
        self.duplicate_count = 0
        self.error_count = 0
        self.total_duration = 0.0
        
        # 错误统计
        self.error_types = {}
        self.platform_stats = {}
        
        logger.info("对冲投注监控指标管理器初始化完成")
    
    def record_execution(self, success: bool, duration: float, platform: str, 
                        match_key: str, error_type: Optional[str] = None, 
                        is_duplicate: bool = False, retry_count: int = 0):
        """
        记录执行指标
        
        参数:
            success: 是否成功
            duration: 执行时长（秒）
            platform: 平台名称
            match_key: 比赛标识
            error_type: 错误类型
            is_duplicate: 是否重复投注
            retry_count: 重试次数
        """
        with self._lock:
            # 创建记录
            record = ExecutionRecord(
                timestamp=datetime.now(),
                success=success,
                duration=duration,
                platform=platform,
                match_key=match_key,
                error_type=error_type,
                is_duplicate=is_duplicate,
                retry_count=retry_count
            )
            
            # 添加到记录队列
            self.records.append(record)
            
            # 更新实时统计
            self.total_count += 1
            self.total_duration += duration
            
            if success:
                self.success_count += 1
            else:
                self.error_count += 1
                if error_type:
                    self.error_types[error_type] = self.error_types.get(error_type, 0) + 1
            
            if is_duplicate:
                self.duplicate_count += 1
            
            # 更新平台统计
            if platform not in self.platform_stats:
                self.platform_stats[platform] = {
                    "total": 0, "success": 0, "failed": 0, "duplicates": 0, "total_duration": 0.0
                }
            
            stats = self.platform_stats[platform]
            stats["total"] += 1
            stats["total_duration"] += duration
            
            if success:
                stats["success"] += 1
            else:
                stats["failed"] += 1
            
            if is_duplicate:
                stats["duplicates"] += 1
            
            logger.debug(f"记录执行指标: {platform}, 成功={success}, 时长={duration:.2f}s")
    
    def get_metrics_summary(self, time_window_hours: Optional[int] = None) -> MetricsSummary:
        """
        获取指标摘要
        
        参数:
            time_window_hours: 时间窗口（小时），None表示全部时间
        
        返回:
            指标摘要
        """
        with self._lock:
            # 过滤时间窗口内的记录
            if time_window_hours:
                cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
                filtered_records = [r for r in self.records if r.timestamp >= cutoff_time]
            else:
                filtered_records = list(self.records)
            
            if not filtered_records:
                return MetricsSummary()
            
            # 计算统计指标
            total = len(filtered_records)
            successful = sum(1 for r in filtered_records if r.success)
            failed = total - successful
            duplicates = sum(1 for r in filtered_records if r.is_duplicate)
            
            total_duration = sum(r.duration for r in filtered_records)
            avg_duration = total_duration / total if total > 0 else 0.0
            success_rate = (successful / total * 100) if total > 0 else 0.0
            
            # 错误分布
            error_dist = {}
            for record in filtered_records:
                if record.error_type:
                    error_dist[record.error_type] = error_dist.get(record.error_type, 0) + 1
            
            # 平台统计
            platform_stats = {}
            for record in filtered_records:
                platform = record.platform
                if platform not in platform_stats:
                    platform_stats[platform] = {
                        "total": 0, "success": 0, "failed": 0, "duplicates": 0, 
                        "avg_duration": 0.0, "success_rate": 0.0
                    }
                
                stats = platform_stats[platform]
                stats["total"] += 1
                if record.success:
                    stats["success"] += 1
                else:
                    stats["failed"] += 1
                if record.is_duplicate:
                    stats["duplicates"] += 1
            
            # 计算平台平均值
            for platform, stats in platform_stats.items():
                platform_records = [r for r in filtered_records if r.platform == platform]
                if platform_records:
                    stats["avg_duration"] = sum(r.duration for r in platform_records) / len(platform_records)
                    stats["success_rate"] = (stats["success"] / stats["total"] * 100) if stats["total"] > 0 else 0.0
            
            return MetricsSummary(
                total_executions=total,
                successful_executions=successful,
                failed_executions=failed,
                duplicate_executions=duplicates,
                avg_duration=avg_duration,
                success_rate=success_rate,
                error_distribution=error_dist,
                platform_stats=platform_stats
            )
    
    def get_health_status(self) -> Dict:
        """获取系统健康状态"""
        summary = self.get_metrics_summary(time_window_hours=1)  # 最近1小时
        
        # 健康状态判断
        if summary.total_executions == 0:
            status = "unknown"
            level = "info"
        elif summary.success_rate >= 90:
            status = "healthy"
            level = "info"
        elif summary.success_rate >= 70:
            status = "warning"
            level = "warning"
        else:
            status = "critical"
            level = "error"
        
        # 检查重复投注率
        duplicate_rate = (summary.duplicate_executions / summary.total_executions * 100) if summary.total_executions > 0 else 0
        if duplicate_rate > 20:  # 重复投注率超过20%
            status = "warning" if status == "healthy" else status
            level = "warning"
        
        return {
            "status": status,
            "level": level,
            "metrics": {
                "success_rate": round(summary.success_rate, 2),
                "avg_duration": round(summary.avg_duration, 2),
                "total_executions": summary.total_executions,
                "duplicate_rate": round(duplicate_rate, 2),
                "error_count": summary.failed_executions
            },
            "alerts": self._generate_alerts(summary)
        }
    
    def _generate_alerts(self, summary: MetricsSummary) -> List[Dict]:
        """生成告警信息"""
        alerts = []
        
        # 成功率告警
        if summary.total_executions > 0:
            if summary.success_rate < 50:
                alerts.append({
                    "type": "critical",
                    "message": f"成功率过低: {summary.success_rate:.1f}%",
                    "suggestion": "检查网络连接和平台状态"
                })
            elif summary.success_rate < 80:
                alerts.append({
                    "type": "warning", 
                    "message": f"成功率偏低: {summary.success_rate:.1f}%",
                    "suggestion": "关注错误日志，可能需要调整重试策略"
                })
        
        # 重复投注告警
        duplicate_rate = (summary.duplicate_executions / summary.total_executions * 100) if summary.total_executions > 0 else 0
        if duplicate_rate > 30:
            alerts.append({
                "type": "warning",
                "message": f"重复投注率过高: {duplicate_rate:.1f}%",
                "suggestion": "检查重复投注检测逻辑"
            })
        
        # 执行时间告警
        if summary.avg_duration > 10:
            alerts.append({
                "type": "warning",
                "message": f"平均执行时间过长: {summary.avg_duration:.1f}秒",
                "suggestion": "检查网络延迟和API响应时间"
            })
        
        # 错误类型告警
        for error_type, count in summary.error_distribution.items():
            if count > summary.total_executions * 0.3:  # 某类错误超过30%
                alerts.append({
                    "type": "warning",
                    "message": f"频繁出现错误: {error_type} ({count}次)",
                    "suggestion": f"重点关注 {error_type} 类型的错误"
                })
        
        return alerts
    
    def get_recent_errors(self, limit: int = 10) -> List[Dict]:
        """获取最近的错误记录"""
        with self._lock:
            error_records = [
                {
                    "timestamp": r.timestamp.isoformat(),
                    "platform": r.platform,
                    "match_key": r.match_key,
                    "error_type": r.error_type,
                    "duration": r.duration,
                    "retry_count": r.retry_count
                }
                for r in reversed(self.records) 
                if not r.success and r.error_type
            ]
            return error_records[:limit]
    
    def reset_metrics(self):
        """重置所有指标"""
        with self._lock:
            self.records.clear()
            self.total_count = 0
            self.success_count = 0
            self.duplicate_count = 0
            self.error_count = 0
            self.total_duration = 0.0
            self.error_types.clear()
            self.platform_stats.clear()
            logger.info("指标数据已重置")

# 全局指标管理器实例
_metrics_manager = None

def get_metrics_manager() -> HedgeMetrics:
    """获取全局指标管理器实例"""
    global _metrics_manager
    if _metrics_manager is None:
        _metrics_manager = HedgeMetrics()
    return _metrics_manager
