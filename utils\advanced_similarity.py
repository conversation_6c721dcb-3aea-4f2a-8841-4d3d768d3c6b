"""
高级字符串相似度计算模块
提供多种字符串相似度算法，用于改进比赛匹配的准确性
"""

import re
import difflib
import logging
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class SimilarityResult:
    """相似度计算结果"""
    score: float  # 总体相似度分数 (0-1)
    method: str   # 使用的主要方法
    details: Dict  # 详细信息
    confidence: float  # 置信度 (0-1)


class AdvancedSimilarity:
    """高级字符串相似度计算器"""
    
    def __init__(self):
        # 无意义词汇列表（扩展版）
        self.meaningless_words = {
            'de', 'del', 'la', 'las', 'los', 'el', 'vs', 'v', 'fc', 'club', 'team',
            'basketball', 'football', 'soccer', '篮球', '足球', '队', '俱乐部',
            'u19', 'u20', 'u21', 'u22', 'u23', 'junior', 'senior', 'women', 'men',
            '女子', '男子', '青年', '成年', 'reserve', 'reserves', 'ii', 'iii', 'b'
        }
        
        # 性别标识符
        self.gender_markers = {
            'female': ['(女)', '(女子)', '女子', '(w)', 'women', 'female', 'ladies'],
            'male': ['(男)', '(男子)', '男子', '(m)', 'men', 'male', 'gentlemen']
        }
        
        # 年龄组标识符
        self.age_markers = [
            'u19', 'u20', 'u21', 'u22', 'u23', 'u18', 'u17', 'u16',
            '(u19)', '(u20)', '(u21)', '(u22)', '(u23)', '(u18)', '(u17)', '(u16)'
        ]

    def calculate_similarity(self, str1: str, str2: str, 
                           use_alias_check: bool = True,
                           team_aliases: Dict = None) -> SimilarityResult:
        """
        计算两个字符串的相似度
        
        参数:
            str1: 字符串1
            str2: 字符串2
            use_alias_check: 是否使用别名检查
            team_aliases: 队伍别名字典
            
        返回:
            SimilarityResult对象
        """
        if not str1 or not str2:
            return SimilarityResult(0.0, "empty_check", {}, 0.0)
        
        # 预处理字符串
        norm_str1 = self._normalize_string(str1)
        norm_str2 = self._normalize_string(str2)
        
        # 1. 精确匹配检查
        if norm_str1 == norm_str2:
            return SimilarityResult(1.0, "exact_match", 
                                  {"original1": str1, "original2": str2}, 1.0)
        
        # 2. 别名匹配检查
        if use_alias_check and team_aliases:
            alias_score = self._check_alias_match(str1, str2, team_aliases)
            if alias_score > 0.9:
                return SimilarityResult(alias_score, "alias_match", 
                                      {"alias_score": alias_score}, 0.95)
        
        # 3. 多算法综合评分
        scores = {}
        
        # Levenshtein距离相似度
        scores['levenshtein'] = self._levenshtein_similarity(norm_str1, norm_str2)
        
        # Jaccard相似度
        scores['jaccard'] = self._jaccard_similarity(norm_str1, norm_str2)
        
        # 词汇级别相似度
        scores['word_level'] = self._word_level_similarity(norm_str1, norm_str2)
        
        # 最长公共子序列相似度
        scores['lcs'] = self._lcs_similarity(norm_str1, norm_str2)
        
        # 语音相似度（简化版）
        scores['phonetic'] = self._phonetic_similarity(norm_str1, norm_str2)
        
        # 计算加权平均分数
        weights = {
            'levenshtein': 0.25,
            'jaccard': 0.20,
            'word_level': 0.30,
            'lcs': 0.15,
            'phonetic': 0.10
        }
        
        final_score = sum(scores[method] * weight for method, weight in weights.items())
        
        # 计算置信度
        confidence = self._calculate_confidence(scores, final_score)
        
        # 确定主要方法
        main_method = max(scores.items(), key=lambda x: x[1])[0]
        
        return SimilarityResult(
            score=final_score,
            method=main_method,
            details=scores,
            confidence=confidence
        )

    def _normalize_string(self, text: str) -> str:
        """标准化字符串"""
        if not text:
            return ""
        
        # 转小写
        normalized = text.lower().strip()
        
        # 移除性别标识符
        for gender_list in self.gender_markers.values():
            for marker in gender_list:
                normalized = normalized.replace(marker.lower(), '').strip()
        
        # 移除年龄组标识符
        for marker in self.age_markers:
            normalized = normalized.replace(marker.lower(), '').strip()
        
        # 移除特殊字符，保留字母、数字和空格
        normalized = re.sub(r'[^\w\s]', ' ', normalized)
        
        # 移除多余空格
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        
        return normalized

    def detect_gender_from_text(self, text: str) -> str:
        """
        从文本中检测性别标识符

        参数:
            text: 输入文本

        返回:
            'male': 男子比赛
            'female': 女子比赛
            'unknown': 无法确定
        """
        if not text:
            return 'unknown'

        text_lower = text.lower().strip()

        # 检查女子标识符
        for marker in self.gender_markers['female']:
            if marker.lower() in text_lower:
                return 'female'

        # 检查男子标识符
        for marker in self.gender_markers['male']:
            if marker.lower() in text_lower:
                return 'male'

        return 'unknown'

    def _levenshtein_similarity(self, str1: str, str2: str) -> float:
        """计算Levenshtein距离相似度"""
        if not str1 or not str2:
            return 0.0
        
        # 计算编辑距离
        distance = self._levenshtein_distance(str1, str2)
        max_len = max(len(str1), len(str2))
        
        if max_len == 0:
            return 1.0
        
        return 1.0 - (distance / max_len)

    def _levenshtein_distance(self, str1: str, str2: str) -> int:
        """计算Levenshtein编辑距离"""
        if len(str1) < len(str2):
            return self._levenshtein_distance(str2, str1)
        
        if len(str2) == 0:
            return len(str1)
        
        previous_row = list(range(len(str2) + 1))
        for i, c1 in enumerate(str1):
            current_row = [i + 1]
            for j, c2 in enumerate(str2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]

    def _jaccard_similarity(self, str1: str, str2: str) -> float:
        """计算Jaccard相似度"""
        if not str1 or not str2:
            return 0.0
        
        # 使用字符级别的n-gram
        ngrams1 = set(self._get_ngrams(str1, 2))
        ngrams2 = set(self._get_ngrams(str2, 2))
        
        if not ngrams1 and not ngrams2:
            return 1.0
        
        intersection = len(ngrams1 & ngrams2)
        union = len(ngrams1 | ngrams2)
        
        return intersection / union if union > 0 else 0.0

    def _get_ngrams(self, text: str, n: int) -> List[str]:
        """获取n-gram"""
        if len(text) < n:
            return [text]
        return [text[i:i+n] for i in range(len(text) - n + 1)]

    def _word_level_similarity(self, str1: str, str2: str) -> float:
        """计算词汇级别相似度"""
        words1 = set(word for word in str1.split() if word not in self.meaningless_words)
        words2 = set(word for word in str2.split() if word not in self.meaningless_words)
        
        if not words1 and not words2:
            return 1.0
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1 & words2)
        union = len(words1 | words2)
        
        return intersection / union if union > 0 else 0.0

    def _lcs_similarity(self, str1: str, str2: str) -> float:
        """计算最长公共子序列相似度"""
        if not str1 or not str2:
            return 0.0
        
        lcs_length = self._longest_common_subsequence(str1, str2)
        max_len = max(len(str1), len(str2))
        
        return lcs_length / max_len if max_len > 0 else 0.0

    def _longest_common_subsequence(self, str1: str, str2: str) -> int:
        """计算最长公共子序列长度"""
        m, n = len(str1), len(str2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if str1[i-1] == str2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])
        
        return dp[m][n]

    def _phonetic_similarity(self, str1: str, str2: str) -> float:
        """计算语音相似度（简化版）"""
        # 简化的语音相似度，基于首字母和元音
        def get_phonetic_key(text):
            if not text:
                return ""
            
            # 提取首字母
            first_char = text[0] if text else ""
            
            # 提取元音
            vowels = ''.join(c for c in text if c in 'aeiou')
            
            return first_char + vowels
        
        key1 = get_phonetic_key(str1)
        key2 = get_phonetic_key(str2)
        
        if not key1 or not key2:
            return 0.0
        
        return difflib.SequenceMatcher(None, key1, key2).ratio()

    def _check_alias_match(self, str1: str, str2: str, team_aliases: Dict) -> float:
        """检查别名匹配"""
        if not team_aliases:
            return 0.0
        
        str1_norm = str1.lower().strip()
        str2_norm = str2.lower().strip()
        
        # 遍历所有联赛的队伍别名
        for league_name, league_teams in team_aliases.items():
            for standard_name, aliases in league_teams.items():
                all_names = [standard_name.lower()] + [alias.lower() for alias in aliases]
                
                if str1_norm in all_names and str2_norm in all_names:
                    return 0.95  # 高分数表示别名匹配
        
        return 0.0

    def _calculate_confidence(self, scores: Dict[str, float], final_score: float) -> float:
        """计算置信度"""
        # 计算分数的标准差
        score_values = list(scores.values())
        mean_score = sum(score_values) / len(score_values)
        variance = sum((score - mean_score) ** 2 for score in score_values) / len(score_values)
        std_dev = variance ** 0.5
        
        # 置信度与标准差成反比，与最终分数成正比
        confidence = (1.0 - std_dev) * final_score
        
        return max(0.0, min(1.0, confidence))


# 全局实例
similarity_calculator = AdvancedSimilarity()


def calculate_team_similarity(team1: str, team2: str,
                            match_team1: str, match_team2: str,
                            team_aliases: Dict = None,
                            strict_mode: bool = True) -> Tuple[float, Dict]:
    """
    计算队伍匹配相似度

    参数:
        team1, team2: 目标队伍名称
        match_team1, match_team2: 候选队伍名称
        team_aliases: 队伍别名字典
        strict_mode: 严格模式，要求两个队伍都有合理匹配度

    返回:
        (相似度分数, 详细信息)
    """
    # 性别检测 - 先检查是否存在性别不匹配
    target_gender1 = similarity_calculator.detect_gender_from_text(team1)
    target_gender2 = similarity_calculator.detect_gender_from_text(team2)
    match_gender1 = similarity_calculator.detect_gender_from_text(match_team1)
    match_gender2 = similarity_calculator.detect_gender_from_text(match_team2)

    # 确定整体性别
    target_genders = [g for g in [target_gender1, target_gender2] if g != 'unknown']
    match_genders = [g for g in [match_gender1, match_gender2] if g != 'unknown']

    # 如果有明确的性别标识符，检查是否匹配
    if target_genders and match_genders:
        target_overall = target_genders[0]
        match_overall = match_genders[0]
        if target_overall != match_overall:
            return 0.0, {
                "match_type": "gender_mismatch",
                "target_gender": target_overall,
                "match_gender": match_overall,
                "reason": "性别不匹配"
            }

    # 计算正向匹配
    home_result = similarity_calculator.calculate_similarity(
        team1, match_team1, team_aliases=team_aliases)
    away_result = similarity_calculator.calculate_similarity(
        team2, match_team2, team_aliases=team_aliases)
    forward_score = (home_result.score + away_result.score) / 2

    # 计算交叉匹配（考虑主客队颠倒）
    home_cross_result = similarity_calculator.calculate_similarity(
        team1, match_team2, team_aliases=team_aliases)
    away_cross_result = similarity_calculator.calculate_similarity(
        team2, match_team1, team_aliases=team_aliases)
    cross_score = (home_cross_result.score + away_cross_result.score) / 2

    # 严格模式：要求两个队伍都有合理的匹配度
    if strict_mode:
        min_individual_score = 0.3  # 单个队伍的最低匹配分数

        # 检查正向匹配
        if forward_score > 0:
            if home_result.score < min_individual_score or away_result.score < min_individual_score:
                forward_score = 0.0  # 如果任一队伍匹配度过低，整体分数为0

        # 检查交叉匹配
        if cross_score > 0:
            if home_cross_result.score < min_individual_score or away_cross_result.score < min_individual_score:
                cross_score = 0.0  # 如果任一队伍匹配度过低，整体分数为0

    # 取最高分数
    if forward_score >= cross_score:
        final_score = forward_score
        match_type = "forward"
        details = {
            "home_match": home_result,
            "away_match": away_result,
            "match_type": match_type
        }
    else:
        final_score = cross_score
        match_type = "cross"
        details = {
            "home_match": home_cross_result,
            "away_match": away_cross_result,
            "match_type": match_type
        }

    return final_score, details
