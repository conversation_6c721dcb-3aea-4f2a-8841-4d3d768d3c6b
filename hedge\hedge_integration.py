#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import time
from typing import Dict, List, Tuple
from datetime import datetime

from hedge.hedge_manager import HedgeManager
from config.hedge_config import HedgeConfig
from platforms.betburger_fetcher import fetch_and_save_data, process_betburger_data
from validation.match_validator import MatchValidator
from validation.match_safety import MatchSafety
import json

logger = logging.getLogger(__name__)

class HedgeIntegration:
    """对冲功能集成器"""
    
    def __init__(self, pinbet_system=None, crown_system=None, learning_controller=None):
        """
        初始化集成器

        参数:
            pinbet_system: 平博投注系统实例
            crown_system: 皇冠投注系统实例
            learning_controller: 学习控制器实例
        """
        self.pinbet_system = pinbet_system
        self.crown_system = crown_system
        self.learning_controller = learning_controller
        self.hedge_manager = None
        self.config = HedgeConfig()
        self.match_validator = MatchValidator()
        self.match_safety = MatchSafety()

        # 缓存配置以减少重复加载
        self._cached_config = None
        self._config_cache_time = 0

        # 监控控制标志位
        self._monitoring_active = False

        # 初始化对冲管理器
        if pinbet_system and crown_system:
            self.hedge_manager = HedgeManager(pinbet_system, crown_system)
            logger.info("对冲集成器初始化完成（实时匹配模式）")
        else:
            logger.warning("缺少投注系统实例，对冲功能不可用")

    def _get_cached_config(self):
        """获取缓存的配置，避免频繁加载"""
        current_time = time.time()
        # 配置缓存30秒
        if self._cached_config is None or (current_time - self._config_cache_time) > 30:
            from utils.utils import load_config
            self._cached_config = load_config()
            self._config_cache_time = current_time
        return self._cached_config

    def stop_monitoring(self):
        """停止监控"""
        self._monitoring_active = False
        logger.info("收到停止监控信号")

    def is_monitoring_active(self) -> bool:
        """检查监控是否处于活跃状态"""
        return self._monitoring_active

    def is_hedge_available(self) -> bool:
        """检查对冲功能是否可用"""
        try:
            # 基本组件检查
            if not self.hedge_manager:
                logger.debug("对冲管理器不可用")
                return False

            if not self.config.is_enabled():
                logger.debug("对冲功能在配置中未启用")
                return False

            if not self.pinbet_system:
                logger.debug("平博系统实例不可用")
                return False

            if not self.crown_system:
                logger.debug("皇冠系统实例不可用")
                return False

            # 检查平博登录状态
            pinbet_logged_in = self._check_pinbet_login_status()
            if not pinbet_logged_in:
                logger.debug("平博未登录")
                return False

            # 检查皇冠登录状态
            crown_logged_in = self._check_crown_login_status()
            if not crown_logged_in:
                logger.debug("皇冠未登录")
                return False

            logger.debug("对冲功能可用")
            return True

        except Exception as e:
            logger.error(f"检查对冲可用性时出错: {e}")
            return False

    def _check_pinbet_login_status(self) -> bool:
        """检查平博登录状态"""
        try:
            # 平博可能是PinBetSystem类型，需要检查api属性
            if hasattr(self.pinbet_system, 'api'):
                api = self.pinbet_system.api
                if hasattr(api, 'is_logged_in'):
                    return bool(api.is_logged_in)

            # 直接检查is_logged_in属性
            if hasattr(self.pinbet_system, 'is_logged_in'):
                return bool(self.pinbet_system.is_logged_in)

            logger.warning("平博系统缺少登录状态属性")
            return False

        except Exception as e:
            logger.error(f"检查平博登录状态失败: {e}")
            return False

    def _check_crown_login_status(self) -> bool:
        """检查皇冠登录状态"""
        try:
            # 皇冠直接检查is_logged_in属性
            if hasattr(self.crown_system, 'is_logged_in'):
                return bool(self.crown_system.is_logged_in)

            logger.warning("皇冠系统缺少登录状态属性")
            return False

        except Exception as e:
            logger.error(f"检查皇冠登录状态失败: {e}")
            return False
    
    def fetch_arbitrage_opportunities(self) -> List[Dict]:
        """
        获取套利机会
        
        返回:
            套利机会列表，每个元素包含平博和皇冠的投注选项
        """
        try:
            # 获取最新数据，使用缓存的配置
            cached_config = self._get_cached_config()
            success, file_path = fetch_and_save_data(cached_config)
            if not success:
                logger.debug("未获取到新的套利数据")
                return []
            
            # 加载JSON文件
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, dict) or "bets" not in data:
                logger.warning("套利数据格式不正确")
                return []
            
            bets = data.get("bets", [])
            if not bets:
                return []
            
            # 处理套利数据
            matches = process_betburger_data(bets)
            if not matches:
                return []
            
            # 查找包含平博和皇冠的套利机会
            arbitrage_opportunities = []
            
            for match_id, bet_options in matches.items():
                pinbet_options = []
                crown_options = []
                
                for bet_option in bet_options:
                    bookmaker = bet_option.get("bookmaker", "").lower()

                    # 为对冲验证添加必要的字段
                    enhanced_option = bet_option.copy()
                    enhanced_option["odds"] = bet_option.get("koef", "0")  # 添加odds字段

                    if "平博" in bookmaker or "pinbet" in bookmaker:
                        enhanced_option["platform"] = "pinbet"  # 添加platform字段
                        pinbet_options.append(enhanced_option)
                    elif "皇冠" in bookmaker or "crown" in bookmaker:
                        enhanced_option["platform"] = "crown"  # 添加platform字段
                        crown_options.append(enhanced_option)
                
                # 如果同时有平博和皇冠的选项，则构成套利机会
                if pinbet_options and crown_options:
                    for pinbet_opt in pinbet_options:
                        for crown_opt in crown_options:
                            # 简化验证：套利数据已预匹配，只做基本检查
                            logger.info(f"检查对冲对: {pinbet_opt.get('platform')} vs {crown_opt.get('platform')}")

                            # 基本验证：检查必要字段
                            if self._basic_validation(pinbet_opt, crown_opt):
                                # 直接构建套利机会，跳过复杂匹配验证
                                opportunity = [pinbet_opt, crown_opt]
                                opportunity.append({
                                    "validation": {"basic_check": True},
                                    "reason": "基本验证通过，套利数据已预匹配",
                                    "pre_validated": True,
                                    "confidence": "high"  # 假设套利数据质量高
                                })
                                arbitrage_opportunities.append(opportunity)
                                logger.info(f"✓ 发现有效对冲机会: {pinbet_opt.get('match')} vs {crown_opt.get('match')}")
                            else:
                                logger.debug(f"✗ 基本验证失败")
            
            logger.info(f"找到 {len(arbitrage_opportunities)} 个对冲机会")

            # 如果没有找到对冲机会，提供详细的分析
            if len(arbitrage_opportunities) == 0:
                logger.info("=== 对冲机会分析 ===")
                for match_id, bet_options in matches.items():
                    pinbet_count = sum(1 for opt in bet_options if opt.get("bookmaker", "").lower().find("平博") != -1 or opt.get("bookmaker", "").lower().find("pinbet") != -1)
                    crown_count = sum(1 for opt in bet_options if opt.get("bookmaker", "").lower().find("皇冠") != -1 or opt.get("bookmaker", "").lower().find("crown") != -1)

                    logger.info(f"比赛 {match_id}: 平博选项={pinbet_count}, 皇冠选项={crown_count}")

                    if pinbet_count > 0 and crown_count > 0:
                        # 获取具体选项进行分析
                        pinbet_options = []
                        crown_options = []

                        for bet_option in bet_options:
                            bookmaker = bet_option.get("bookmaker", "").lower()
                            enhanced_option = bet_option.copy()
                            enhanced_option["odds"] = bet_option.get("koef", "0")

                            if "平博" in bookmaker or "pinbet" in bookmaker:
                                enhanced_option["platform"] = "pinbet"
                                pinbet_options.append(enhanced_option)
                            elif "皇冠" in bookmaker or "crown" in bookmaker:
                                enhanced_option["platform"] = "crown"
                                crown_options.append(enhanced_option)

                        if pinbet_options and crown_options:
                            option1, option2 = pinbet_options[0], crown_options[0]
                            logger.info(f"测试选项1: {option1.get('platform')} - {option1.get('match')}")
                            logger.info(f"测试选项2: {option2.get('platform')} - {option2.get('match')}")

                            # 执行快速检查并记录详细结果
                            quick_result = self._quick_match_check(option1, option2)
                            logger.info(f"快速检查结果: {'通过' if quick_result['valid'] else '失败'}")
                            if not quick_result['valid']:
                                logger.info(f"失败原因: {quick_result['reason']}")
                                logger.info(f"检查类型: {quick_result['check_type']}")

                                # 如果是队伍相似性问题，提供更多细节
                                if quick_result['check_type'] == 'team_similarity':
                                    match1 = option1.get('match', '').lower()
                                    match2 = option2.get('match', '').lower()
                                    words1 = set(match1.split())
                                    words2 = set(match2.split())
                                    common_words = words1 & words2
                                    logger.info(f"队伍1词汇: {words1}")
                                    logger.info(f"队伍2词汇: {words2}")
                                    logger.info(f"共同词汇: {common_words} (数量: {len(common_words)})")
                logger.info("=== 分析结束 ===")
            return arbitrage_opportunities
            
        except Exception as e:
            logger.error(f"获取套利机会时出错: {e}")
            return []





    def _quick_match_check(self, option1: Dict, option2: Dict) -> Dict:
        """
        快速预检查 - 基本信息验证（优化实时匹配性能）

        参数:
            option1: 投注选项1
            option2: 投注选项2

        返回:
            检查结果字典
        """
        try:
            # 1. 检查基本字段是否存在
            required_fields = ['match', 'platform', 'odds']
            for field in required_fields:
                if not option1.get(field) or not option2.get(field):
                    return {
                        "valid": False,
                        "reason": f"缺少必要字段: {field}",
                        "check_type": "field_check"
                    }

            # 2. 检查平台是否不同
            if option1.get('platform') == option2.get('platform'):
                return {
                    "valid": False,
                    "reason": "相同平台，无法对冲",
                    "check_type": "platform_check"
                }

            # 3. 检查是否是平博和皇冠的组合
            platforms = {option1.get('platform'), option2.get('platform')}
            expected_platforms = {'pinbet', 'crown'}
            if platforms != expected_platforms:
                return {
                    "valid": False,
                    "reason": f"平台组合不正确: {platforms}，期望: {expected_platforms}",
                    "check_type": "platform_combination"
                }

            # 4. 快速队伍名称检查（使用连续字符匹配）
            match1 = option1.get('match', '').lower()
            match2 = option2.get('match', '').lower()

            # 简单的队伍名称相似性检查
            if not match1 or not match2:
                return {
                    "valid": False,
                    "reason": "比赛信息为空",
                    "check_type": "match_info"
                }

            # 使用连续字符匹配算法
            max_common_length = self._find_longest_common_substring(match1, match2)

            if max_common_length < 3:  # 至少要有3个连续字符相同
                return {
                    "valid": False,
                    "reason": f"队伍名称差异过大，最长连续匹配仅{max_common_length}个字符",
                    "check_type": "team_similarity",
                    "common_length": max_common_length
                }

            # 5. 基本赔率格式检查
            try:
                odds1 = float(option1.get('odds', 0))
                odds2 = float(option2.get('odds', 0))

                if odds1 <= 1.0 or odds2 <= 1.0:
                    return {
                        "valid": False,
                        "reason": "赔率不合理",
                        "check_type": "odds_check"
                    }

            except (ValueError, TypeError):
                return {
                    "valid": False,
                    "reason": "赔率格式错误",
                    "check_type": "odds_format"
                }

            # 所有快速检查通过
            return {
                "valid": True,
                "reason": "快速检查通过",
                "check_type": "all_passed",
                "common_length": max_common_length
            }

        except Exception as e:
            logger.error(f"快速检查异常: {e}")
            return {
                "valid": False,
                "reason": f"快速检查异常: {str(e)}",
                "check_type": "exception"
            }

    def _basic_validation(self, option1: Dict, option2: Dict) -> bool:
        """
        基本验证：检查必要字段、平台组合和黑名单

        参数:
            option1: 投注选项1
            option2: 投注选项2

        返回:
            是否通过基本验证
        """
        try:
            # 1. 检查必要字段
            required_fields = ['match', 'platform', 'odds']
            for field in required_fields:
                if not option1.get(field) or not option2.get(field):
                    logger.debug(f"缺少必要字段: {field}")
                    return False

            # 2. 检查平台组合
            platforms = {option1.get('platform'), option2.get('platform')}
            expected_platforms = {'pinbet', 'crown'}
            if platforms != expected_platforms:
                logger.debug(f"平台组合不正确: {platforms}")
                return False

            # 3. 检查赔率格式
            try:
                odds1 = float(option1.get('odds', 0))
                odds2 = float(option2.get('odds', 0))
                if odds1 <= 1.0 or odds2 <= 1.0:
                    logger.debug("赔率不合理")
                    return False
            except (ValueError, TypeError):
                logger.debug("赔率格式错误")
                return False

            # 4. 黑名单检查
            try:
                blacklist_check = self.match_safety._check_blacklist(option1, option2)
                if not blacklist_check.passed:
                    logger.warning(f"黑名单检查失败: {blacklist_check.message}")
                    return False
            except Exception as e:
                logger.error(f"黑名单检查异常: {e}")
                return False

            logger.debug("基本验证通过（包含黑名单检查）")
            return True

        except Exception as e:
            logger.error(f"基本验证异常: {e}")
            return False

    def _find_longest_common_substring(self, str1: str, str2: str) -> int:
        """
        查找两个字符串的最长公共子串长度

        参数:
            str1: 字符串1
            str2: 字符串2

        返回:
            最长公共子串的长度
        """
        try:
            if not str1 or not str2:
                return 0

            # 动态规划算法查找最长公共子串
            m, n = len(str1), len(str2)
            dp = [[0] * (n + 1) for _ in range(m + 1)]
            max_length = 0

            for i in range(1, m + 1):
                for j in range(1, n + 1):
                    if str1[i-1] == str2[j-1]:
                        dp[i][j] = dp[i-1][j-1] + 1
                        max_length = max(max_length, dp[i][j])
                    else:
                        dp[i][j] = 0

            return max_length

        except Exception as e:
            logger.error(f"查找最长公共子串异常: {e}")
            return 0

    def _perform_safety_checks_only(self, option1: Dict, option2: Dict) -> Tuple[bool, str]:
        """
        仅执行安全检查（跳过匹配验证）

        返回:
            (是否安全, 原因说明)
        """
        try:
            safety_checks = self.match_safety.perform_safety_checks(option1, option2)
            is_safe, reason = self.match_safety.is_safe_to_proceed(safety_checks)
            return is_safe, reason

        except Exception as e:
            logger.error(f"安全检查失败: {e}")
            return False, f"安全检查异常: {str(e)}"

    def _is_hedge_pair(self, option1: Dict, option2: Dict) -> Tuple[bool, str, Dict]:
        """
        检查两个投注选项是否构成安全的对冲对

        参数:
            option1: 投注选项1
            option2: 投注选项2

        返回:
            (是否构成对冲对, 详细说明, 验证结果)
        """
        try:
            logger.info("开始进行实时比赛匹配验证")

            # 1. 快速预检查 - 优化性能
            quick_check = self._quick_match_check(option1, option2)
            if not quick_check["valid"]:
                logger.debug(f"快速检查失败: {quick_check['reason']}")
                return False, f"✗ 快速检查失败: {quick_check['reason']}", {
                    "quick_check": quick_check,
                    "match_validation": {"valid": False, "reason": "快速检查失败"},
                    "safety_checks": {"safe": False, "reason": "基本信息不匹配"}
                }

            logger.debug(f"快速检查通过: {quick_check['reason']}")

            # 2. 执行详细比赛匹配验证
            match_result = self.match_validator.validate_match_pair(option1, option2)

            # 3. 执行安全检查
            safety_checks = self.match_safety.perform_safety_checks(option1, option2)

            # 4. 判断是否安全进行对冲
            is_match_valid, match_reason = self.match_validator.is_safe_to_hedge(match_result)
            is_safety_ok, safety_reason = self.match_safety.is_safe_to_proceed(safety_checks)

            # 5. 综合判断
            is_safe_hedge = is_match_valid and is_safety_ok

            # 6. 生成详细说明
            if is_safe_hedge:
                confidence = match_result.score.confidence.value if match_result.score else "unknown"
                detail_msg = f"✓ 实时验证通过 (置信度: {confidence})"
            else:
                reasons = []
                if not is_match_valid:
                    reasons.append(f"比赛匹配: {match_reason}")
                if not is_safety_ok:
                    reasons.append(f"安全检查: {safety_reason}")
                detail_msg = f"✗ 实时验证失败: {'; '.join(reasons)}"

            # 7. 构建验证结果
            validation_result = {
                "quick_check": quick_check,
                "match_validation": {
                    "valid": is_match_valid,
                    "reason": match_reason,
                    "score": match_result.score.__dict__ if match_result.score else None,
                    "warnings": match_result.warnings,
                    "errors": match_result.errors
                },
                "safety_checks": {
                    "safe": is_safety_ok,
                    "reason": safety_reason,
                    "checks": [
                        {
                            "rule": check.rule_name,
                            "passed": check.passed,
                            "risk_level": check.risk_level.value,
                            "message": check.message
                        }
                        for check in safety_checks
                    ]
                },
                "summary": self.match_safety.get_safety_summary(safety_checks)
            }

            logger.info(f"比赛匹配验证完成: {detail_msg}")

            return is_safe_hedge, detail_msg, validation_result

        except Exception as e:
            logger.error(f"比赛匹配验证异常: {e}")
            error_msg = f"验证系统异常: {str(e)}"
            error_result = {
                "error": error_msg,
                "match_validation": {"valid": False, "reason": error_msg},
                "safety_checks": {"safe": False, "reason": error_msg}
            }
            return False, error_msg, error_result
    
    def execute_hedge_opportunity(self, arbitrage_data: List[Dict]) -> Dict:
        """
        执行对冲机会（包含实时验证）

        参数:
            arbitrage_data: 套利数据（包含平博和皇冠的投注选项）

        返回:
            执行结果
        """
        if not self.is_hedge_available():
            return {"success": False, "message": "对冲功能不可用"}

        try:
            logger.info("开始执行对冲投注")

            # 简化验证：只做基本检查
            if len(arbitrage_data) >= 2:
                option1, option2 = arbitrage_data[0], arbitrage_data[1]

                logger.info("执行基本验证...")
                if not self._basic_validation(option1, option2):
                    logger.error("基本验证失败")

                    # 记录匹配失败到学习系统
                    self._record_match_failure(arbitrage_data, "基本验证失败")

                    return {
                        "success": False,
                        "message": "基本验证失败：缺少必要字段或平台组合错误"
                    }

                logger.info("基本验证通过，直接执行对冲投注")

            # 执行对冲投注
            result = self.hedge_manager.execute_hedge_bet(arbitrage_data)

            if result.get("success"):
                logger.info("对冲投注执行成功")
                # 播放成功提示音
                self._play_success_notification()
            else:
                # 只记录高级别的失败信息，避免与对冲管理器的日志重复
                failure_message = result.get('message', '')

                # 检查是否是基准平台投注失败
                if "基准平台" in failure_message and "投注失败" in failure_message:
                    # 基准平台投注失败，记录为对冲取消
                    logger.warning(f"对冲投注失败: {failure_message}，对冲取消")
                else:
                    # 其他类型的失败
                    logger.warning(f"对冲投注失败: {failure_message}")

                # 如果是匹配相关的失败，记录到学习系统
                if any(keyword in failure_message for keyword in ['匹配', '找不到', '无法找到', '比赛不存在']):
                    self._record_match_failure(arbitrage_data, failure_message)

            return result

        except Exception as e:
            logger.error(f"执行对冲机会时出错: {e}")
            return {"success": False, "message": f"执行失败: {str(e)}"}

    def _record_match_failure(self, arbitrage_data: List[Dict], failure_reason: str):
        """
        记录匹配失败到学习系统

        参数:
            arbitrage_data: 套利数据
            failure_reason: 失败原因
        """
        try:
            if not self.learning_controller:
                return

            # 确定目标平台
            target_platform = "unknown"

            # 从套利数据中提取平台信息
            for option in arbitrage_data:
                bookmaker = option.get("bookmaker", "").lower()
                if "crown" in bookmaker or "皇冠" in bookmaker:
                    target_platform = "crown"
                    break
                elif "pinbet" in bookmaker or "平博" in bookmaker:
                    target_platform = "pinbet"
                    break

            # 构建套利数据字典
            arbitrage_dict = {}
            if len(arbitrage_data) >= 2:
                option1, option2 = arbitrage_data[0], arbitrage_data[1]

                # 提取比赛信息
                arbitrage_dict = {
                    "match": option1.get("match", ""),
                    "home_team": option1.get("home_team", ""),
                    "away_team": option1.get("away_team", ""),
                    "league": option1.get("league", ""),
                    "source": "betburger",
                    "options": arbitrage_data
                }

            # 记录到学习系统
            self.learning_controller.handle_match_failure(
                arbitrage_dict, target_platform, failure_reason
            )

            logger.debug(f"已记录匹配失败到学习系统: {target_platform} - {failure_reason}")

        except Exception as e:
            logger.error(f"记录匹配失败时出错: {e}")

    def execute_hedge_opportunity_force(self, arbitrage_data: List[Dict]) -> Dict:
        """
        强制执行对冲机会（跳过实时验证，用于用户确认后）

        参数:
            arbitrage_data: 套利数据

        返回:
            执行结果
        """
        if not self.is_hedge_available():
            return {"success": False, "message": "对冲功能不可用"}

        try:
            logger.info("强制执行对冲投注（跳过实时验证）")

            # 直接执行对冲投注，跳过实时验证
            result = self.hedge_manager.execute_hedge_bet(arbitrage_data)

            if result.get("success"):
                logger.info("强制对冲投注执行成功")
                self._play_success_notification()
            else:
                logger.warning(f"强制对冲投注执行失败: {result.get('message')}")

            return result

        except Exception as e:
            logger.error(f"强制执行对冲机会时出错: {e}")
            return {"success": False, "message": f"强制执行失败: {str(e)}"}

    def monitor_and_execute_hedges(self, check_interval: int = 5) -> bool:
        """
        监控并执行对冲机会
        
        参数:
            check_interval: 检查间隔（秒）
        
        返回:
            是否成功启动监控
        """
        if not self.is_hedge_available():
            logger.warning("对冲功能不可用，无法启动监控")
            return False
        
        logger.info(f"开始监控对冲机会，检查间隔: {check_interval}秒")

        # 设置监控状态为活跃
        self._monitoring_active = True

        # 添加计数器以减少日志频率
        check_count = 0
        last_log_time = time.time()

        try:
            while self._monitoring_active:
                check_count += 1
                current_time = time.time()

                # 获取套利机会
                opportunities = self.fetch_arbitrage_opportunities()

                if opportunities:
                    logger.info(f"发现 {len(opportunities)} 个对冲机会")

                    # 执行第一个机会（可以根据需要调整策略）
                    for opportunity in opportunities:
                        result = self.execute_hedge_opportunity(opportunity)

                        if result.get("success"):
                            logger.info("对冲投注成功，暂停监控以避免过度投注")
                            time.sleep(30)  # 成功后暂停30秒
                            break
                        else:
                            logger.warning(f"对冲投注失败: {result.get('message')}")
                else:
                    # 每60秒或每10次检查记录一次无机会的日志
                    if (current_time - last_log_time) >= 60 or check_count % 10 == 0:
                        logger.debug(f"第{check_count}次检查，暂无对冲机会")
                        last_log_time = current_time

                # 等待下次检查，但要检查停止信号
                for _ in range(check_interval):
                    if not self._monitoring_active:
                        break
                    time.sleep(1)

        except KeyboardInterrupt:
            logger.info("对冲监控已手动停止")
        except Exception as e:
            logger.error(f"对冲监控异常: {e}")
        finally:
            self._monitoring_active = False
            logger.info("对冲监控已停止")

        return True
    
    def get_hedge_statistics(self) -> Dict:
        """获取对冲统计信息"""
        if not self.hedge_manager:
            return {"error": "对冲管理器不可用"}

        return self.hedge_manager.get_hedge_status()


    
    def _play_success_notification(self):
        """播放成功提示音"""
        try:
            if self.config.is_notifications_enabled():
                import pygame.mixer
                pygame.mixer.init()
                pygame.mixer.music.load("alert.mp3")
                pygame.mixer.music.play()
                logger.info("播放对冲成功提示音")
        except Exception as e:
            logger.debug(f"播放提示音失败: {e}")
    
    def create_hedge_report(self) -> str:
        """创建对冲报告"""
        try:
            if not self.hedge_manager:
                return "对冲管理器不可用"
            
            status = self.hedge_manager.get_hedge_status()
            config_summary = self.config.get_config_summary()
            
            report = f"""
=== 对冲系统报告 ===
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{config_summary}

运行状态:
- 活跃对冲: {status.get('active_hedges', 0)}
- 历史记录: {status.get('total_history', 0)}
- 系统状态: {'正常' if self.is_hedge_available() else '不可用'}

平台状态:
- 平博登录: {'是' if getattr(self.pinbet_system, 'is_logged_in', False) else '否'}
- 皇冠登录: {'是' if getattr(self.crown_system, 'is_logged_in', False) else '否'}

=== 报告结束 ===
"""
            return report
            
        except Exception as e:
            logger.error(f"创建对冲报告时出错: {e}")
            return f"报告生成失败: {str(e)}"

def integrate_hedge_to_existing_systems():
    """
    将对冲功能集成到现有系统中的示例函数
    """
    try:
        # 导入现有系统
        from platforms.pinbet_bk import PinBetSystem  
        from platforms.hgbet_bk import HGBetBK
        
        # 创建系统实例
        pinbet_system = PinBetSystem()
        crown_system = HGBetBK()
        
        # 创建对冲集成器
        hedge_integration = HedgeIntegration(pinbet_system, crown_system)
        
        # 检查对冲功能是否可用
        if hedge_integration.is_hedge_available():
            logger.info("对冲功能已成功集成")
            return hedge_integration
        else:
            logger.warning("对冲功能集成失败，请检查配置和登录状态")
            return None
            
    except ImportError as e:
        logger.error(f"导入现有系统失败: {e}")
        return None
    except Exception as e:
        logger.error(f"集成对冲功能时出错: {e}")
        return None

if __name__ == "__main__":
    # 测试集成功能
    integration = integrate_hedge_to_existing_systems()
    
    if integration:
        print("对冲功能集成成功！")
        print(integration.create_hedge_report())
    else:
        print("对冲功能集成失败。")
