#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HAR文件比赛数据分析工具
专门用于分析比赛数据响应路径 n-0-2 等
"""

import json
import os
import sys
from typing import Dict, List, Any, Optional

class HARMatchAnalyzer:
    """HAR比赛数据分析器"""
    
    def __init__(self, har_file: str):
        """初始化分析器"""
        self.har_file = har_file
        self.har_data = None
        self.load_har_file()
    
    def load_har_file(self):
        """加载HAR文件"""
        try:
            with open(self.har_file, 'r', encoding='utf-8') as f:
                self.har_data = json.load(f)
            print(f"✅ 成功加载HAR文件: {os.path.basename(self.har_file)}")
        except Exception as e:
            print(f"❌ 加载HAR文件失败: {e}")
            sys.exit(1)
    
    def find_match_responses(self) -> List[Dict]:
        """查找包含比赛数据的响应"""
        match_responses = []
        
        entries = self.har_data.get('log', {}).get('entries', [])
        
        for i, entry in enumerate(entries):
            request = entry.get('request', {})
            response = entry.get('response', {})
            content = response.get('content', {})
            
            url = request.get('url', '')
            text = content.get('text', '')
            
            # 检查是否是比赛数据响应
            if text and self._is_match_response(text, url, entry):
                match_responses.append({
                    'index': i,
                    'url': url,
                    'method': request.get('method', ''),
                    'status': response.get('status', ''),
                    'size': content.get('size', 0),
                    'data': text
                })
        
        return match_responses
    
    def _is_match_response(self, text: str, url: str, entry: Dict) -> bool:
        """判断是否是比赛数据响应"""
        try:
            # 首先检查是否是XHR请求
            resource_type = entry.get('_resourceType', '').lower()
            if resource_type != 'xhr':
                return False

            data = json.loads(text)

            # 检查数据结构
            has_match_structure = (
                ('n' in data and isinstance(data['n'], list)) or
                ('l' in data and isinstance(data['l'], list)) or
                ('events' in data and isinstance(data['events'], list)) or
                ('matches' in data and isinstance(data['matches'], list))
            )

            return has_match_structure

        except json.JSONDecodeError:
            return False
    
    def analyze_match_structure(self, response_data: str) -> Dict:
        """分析比赛数据结构"""
        try:
            data = json.loads(response_data)
            analysis = {
                'structure_type': 'unknown',
                'total_leagues': 0,
                'total_matches': 0,
                'leagues': [],
                'sample_paths': []
            }
            
            # 分析 'n' 结构
            if 'n' in data and isinstance(data['n'], list):
                analysis['structure_type'] = 'n_structure'
                analysis['total_leagues'] = len(data['n'])
                
                for league_idx, league_data in enumerate(data['n']):
                    if isinstance(league_data, list) and len(league_data) > 2:
                        league_name = league_data[1] if len(league_data) > 1 else f"联赛{league_idx}"
                        matches = league_data[2] if len(league_data) > 2 else []
                        match_count = len(matches) if isinstance(matches, list) else 0
                        
                        analysis['total_matches'] += match_count
                        analysis['leagues'].append({
                            'index': league_idx,
                            'name': league_name,
                            'match_count': match_count,
                            'matches': self._extract_match_info(matches)
                        })
                        
                        # 生成示例路径
                        for match_idx in range(min(3, match_count)):  # 最多3个示例
                            analysis['sample_paths'].append(f"n-{league_idx}-{match_idx}")
            
            # 分析 'l' 结构
            elif 'l' in data and isinstance(data['l'], list):
                analysis['structure_type'] = 'l_structure'
                
                for sport_idx, sport_data in enumerate(data['l']):
                    if isinstance(sport_data, list) and len(sport_data) > 2:
                        sport_name = sport_data[1] if len(sport_data) > 1 else f"体育{sport_idx}"
                        leagues = sport_data[2] if len(sport_data) > 2 else []
                        
                        if isinstance(leagues, list):
                            analysis['total_leagues'] += len(leagues)
                            
                            for league_idx, league_info in enumerate(leagues):
                                if isinstance(league_info, list) and len(league_info) > 2:
                                    league_name = league_info[1] if len(league_info) > 1 else f"联赛{league_idx}"
                                    matches = league_info[2] if len(league_info) > 2 else []
                                    match_count = len(matches) if isinstance(matches, list) else 0
                                    
                                    analysis['total_matches'] += match_count
                                    analysis['leagues'].append({
                                        'sport_index': sport_idx,
                                        'sport_name': sport_name,
                                        'league_index': league_idx,
                                        'league_name': league_name,
                                        'match_count': match_count,
                                        'matches': self._extract_match_info(matches)
                                    })
                                    
                                    # 生成示例路径
                                    for match_idx in range(min(3, match_count)):
                                        analysis['sample_paths'].append(f"l-{sport_idx}-{league_idx}-{match_idx}")
            
            return analysis
            
        except Exception as e:
            return {'error': str(e)}
    
    def _extract_match_info(self, matches: List) -> List[Dict]:
        """提取比赛信息"""
        match_info = []
        
        if not isinstance(matches, list):
            return match_info
        
        for i, match in enumerate(matches[:5]):  # 最多提取5场比赛
            if isinstance(match, list) and len(match) >= 3:
                info = {
                    'index': i,
                    'home_team': match[1] if len(match) > 1 else "未知主队",
                    'away_team': match[2] if len(match) > 2 else "未知客队",
                    'match_id': match[0] if len(match) > 0 else "未知ID"
                }
                
                # 提取其他可能的信息
                if len(match) > 3:
                    info['extra_data'] = match[3:]
                
                match_info.append(info)
        
        return match_info
    
    def extract_path_data(self, path: str) -> Optional[Any]:
        """根据路径提取数据"""
        match_responses = self.find_match_responses()
        
        for response in match_responses:
            try:
                data = json.loads(response['data'])
                result = self._extract_by_path(data, path)
                if result is not None:
                    return {
                        'path': path,
                        'source_url': response['url'],
                        'data': result
                    }
            except:
                continue
        
        return None
    
    def _extract_by_path(self, data: Dict, path: str) -> Optional[Any]:
        """根据路径从数据中提取"""
        try:
            if path.startswith('n-'):
                parts = path.split('-')
                if len(parts) >= 3:
                    league_idx = int(parts[1])
                    match_idx = int(parts[2])
                    
                    if 'n' in data and isinstance(data['n'], list):
                        if 0 <= league_idx < len(data['n']):
                            league_data = data['n'][league_idx]
                            if isinstance(league_data, list) and len(league_data) > 2:
                                matches = league_data[2]
                                if isinstance(matches, list) and 0 <= match_idx < len(matches):
                                    return matches[match_idx]
            
            elif path.startswith('l-'):
                parts = path.split('-')
                if len(parts) >= 4:
                    sport_idx = int(parts[1])
                    league_idx = int(parts[2])
                    match_idx = int(parts[3])
                    
                    if 'l' in data and isinstance(data['l'], list):
                        if 0 <= sport_idx < len(data['l']):
                            sport_data = data['l'][sport_idx]
                            if isinstance(sport_data, list) and len(sport_data) > 2:
                                leagues = sport_data[2]
                                if isinstance(leagues, list) and 0 <= league_idx < len(leagues):
                                    league_info = leagues[league_idx]
                                    if isinstance(league_info, list) and len(league_info) > 2:
                                        matches = league_info[2]
                                        if isinstance(matches, list) and 0 <= match_idx < len(matches):
                                            return matches[match_idx]
            
            return None
            
        except (ValueError, IndexError):
            return None
    
    def run_analysis(self):
        """运行完整分析"""
        print("\n=== HAR比赛数据分析 ===")
        
        # 查找比赛响应
        match_responses = self.find_match_responses()
        print(f"\n找到 {len(match_responses)} 个比赛数据响应:")
        
        for i, response in enumerate(match_responses):
            print(f"\n响应 {i+1}:")
            print(f"  URL: {response['url']}")
            print(f"  方法: {response['method']}")
            print(f"  状态: {response['status']}")
            print(f"  大小: {response['size']} 字节")
            
            # 分析结构
            analysis = self.analyze_match_structure(response['data'])
            
            if 'error' not in analysis:
                print(f"  结构类型: {analysis['structure_type']}")
                print(f"  总联赛数: {analysis['total_leagues']}")
                print(f"  总比赛数: {analysis['total_matches']}")
                
                # 显示联赛信息
                print(f"  联赛详情:")
                for league in analysis['leagues'][:3]:  # 只显示前3个联赛
                    if 'sport_name' in league:
                        print(f"    {league['sport_name']} - {league['league_name']}: {league['match_count']} 场比赛")
                    else:
                        print(f"    {league['name']}: {league['match_count']} 场比赛")
                    
                    # 显示比赛示例
                    for match in league['matches'][:2]:  # 只显示前2场比赛
                        print(f"      {match['home_team']} vs {match['away_team']}")
                
                # 显示路径示例
                if analysis['sample_paths']:
                    print(f"  示例路径: {', '.join(analysis['sample_paths'][:5])}")
            else:
                print(f"  分析错误: {analysis['error']}")
        
        # 测试特定路径
        print(f"\n=== 测试路径 n-0-2 ===")
        result = self.extract_path_data("n-0-2")
        if result:
            print(f"路径: {result['path']}")
            print(f"来源: {result['source_url']}")
            print(f"数据: {json.dumps(result['data'], ensure_ascii=False, indent=2)}")
        else:
            print("路径 n-0-2 没有找到数据")

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python har_match_analyzer.py <har_file>")
        print("示例: python har_match_analyzer.py data.har")
        return 1
    
    har_file = sys.argv[1]
    
    if not os.path.exists(har_file):
        print(f"❌ 文件不存在: {har_file}")
        return 1
    
    analyzer = HARMatchAnalyzer(har_file)
    analyzer.run_analysis()
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
