# 小金平台API使用说明

## 概述

本项目基于抓包文件实现了小金平台的登录、余额查询和未结算注单查询功能。

## 文件结构

```
├── platforms/
│   └── xiaojin_api.py          # 小金平台API实现
├── test_xiaojin_api.py         # API测试脚本
├── xiaojin_config.py           # 配置管理
└── 小金平台API使用说明.md      # 本文档
```

## 功能特性

### 1. 登录功能
- 支持用户名密码登录
- 自动处理设备指纹和认证token
- 获取体育平台JWT认证
- 会话状态管理

### 2. 余额查询
- 实时获取账户余额
- 支持多币种显示
- 错误处理和重试机制

### 3. 未结算注单查询
- 获取所有未结算投注
- 详细的注单信息解析
- 支持多种投注类型

## 快速开始

### 1. 安装依赖

```bash
pip install requests
```

### 2. 基本使用

```python
from platforms.xiaojin_api import XiaojinAPI

# 创建API实例
api = XiaojinAPI()

# 登录
success, message = api.login("your_username", "your_password")
if success:
    print("登录成功")
    
    # 查询余额
    balance_success, balance_data = api.get_balance()
    if balance_success:
        print(f"余额: {balance_data['balance']}")
    
    # 查询未结算注单
    bets_success, bets_data = api.get_pending_bets()
    if bets_success:
        print(f"未结算注单数量: {bets_data['total_count']}")
        
    # 登出
    api.logout()
```

### 3. 运行测试

```bash
python test_xiaojin_api.py
```

## API详细说明

### XiaojinAPI类

#### 初始化
```python
api = XiaojinAPI()
```

#### 登录方法
```python
success, message = api.login(username, password)
```
- **参数**:
  - `username`: 用户名
  - `password`: 密码
- **返回**: `(bool, str)` - 成功标志和消息

#### 余额查询
```python
success, data = api.get_balance()
```
- **返回**: `(bool, dict)` - 成功标志和余额数据
- **数据格式**:
  ```python
  {
      'balance': 5934.92,
      'currency': 'CNY',
      'raw_data': {...}
  }
  ```

#### 未结算注单查询
```python
success, data = api.get_pending_bets()
```
- **返回**: `(bool, dict)` - 成功标志和注单数据
- **数据格式**:
  ```python
  {
      'total_count': 2,
      'bets': [
          {
              'bet_id': '72105113250298',
              'bet_type': '角球:滚球 大 / 小',
              'competition': '世界球会冠军杯2025(在美国)',
              'stake': 66.0,
              'expected_payout': 54.78,
              'total_return': 120.78,
              'status': '确认',
              'place_time': '2025-07-01T09:49:37.223151',
              'bet_details': [
                  {
                      'home_team': '曼城',
                      'away_team': '希拉尔',
                      'selection': '小',
                      'odds': 1.83,
                      'handicap': '10.5',
                      'match_time': '2025-07-01T09:00:00'
                  }
              ],
              'is_settled': False
          }
      ]
  }
  ```

#### 账户信息
```python
info = api.get_account_info()
```
- **返回**: `dict` - 账户综合信息

#### 登出
```python
api.logout()
```

## 配置管理

### 使用配置文件

1. 创建示例配置:
```bash
python xiaojin_config.py
```

2. 复制并修改配置:
```bash
cp xiaojin_config_example.json xiaojin_config.json
```

3. 编辑配置文件:
```json
{
  "platform_name": "小金",
  "account": {
    "username": "your_username",
    "password": "your_password"
  },
  "timeouts": {
    "login": 30,
    "balance": 15,
    "bets": 20
  }
}
```

## 抓包数据分析

### 登录流程
1. **URL**: `https://www.grandwanjiajin88.com/service/userapi/login`
2. **方法**: POST
3. **关键参数**:
   - `ud`: 用户名
   - `pd`: 密码
   - `blackbox`: 设备指纹
   - `authtoken`: 认证token
   - `fingerPrintId`: 指纹ID

### 余额查询
1. **URL**: `https://sports-api.sbk-188-sports.com/api/v1/member/getbalance`
2. **方法**: GET
3. **认证**: Bearer Token
4. **响应**: `{"r":0,"d":5934.92103}`

### 注单查询
1. **URL**: `https://sports-api.sbk-188-sports.com/api/v1/zh-cn/getmybet`
2. **方法**: GET
3. **认证**: Bearer Token
4. **响应**: 包含详细注单信息的JSON

## 错误处理

### 常见错误
1. **登录失败**: 检查用户名密码
2. **认证失败**: Token过期，需要重新登录
3. **网络错误**: 检查网络连接
4. **数据解析错误**: 响应格式变化

### 错误码说明
- `r=0`: 成功
- `r!=0`: 失败，具体错误信息在响应中

## 注意事项

1. **安全性**: 
   - 不要在代码中硬编码用户名密码
   - 使用配置文件管理敏感信息
   - 定期更换密码

2. **频率限制**:
   - 避免频繁请求
   - 实现适当的延时机制
   - 监控API调用频率

3. **会话管理**:
   - Token有时效性，需要定期刷新
   - 实现自动重新登录机制
   - 正确处理会话过期

4. **数据准确性**:
   - 验证返回数据的完整性
   - 处理异常情况
   - 记录详细日志

## 扩展功能

可以基于现有实现扩展以下功能：
1. 投注功能
2. 历史记录查询
3. 账户交易记录
4. 实时赔率获取
5. 自动化交易

## 技术支持

如有问题，请检查：
1. 网络连接是否正常
2. 账号信息是否正确
3. 平台是否有更新
4. 日志文件中的错误信息

## 更新日志

### v1.0.0 (2025-07-01)
- 初始版本发布
- 实现基础登录、余额查询、注单查询功能
- 添加配置管理和测试脚本
