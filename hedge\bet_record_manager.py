#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
投注记录管理模块
负责管理对冲投注记录、单平台投注记录、失败补单等功能
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class BetType(Enum):
    """投注类型枚举"""
    SINGLE = "single"  # 单平台投注
    HEDGE = "hedge"    # 对冲投注

class BetStatus(Enum):
    """投注状态枚举"""
    PENDING = "pending"      # 待处理
    SUCCESS = "success"      # 成功
    FAILED = "failed"        # 失败
    TIMEOUT = "timeout"      # 超时
    CANCELLED = "cancelled"  # 取消
    RETRY_PENDING = "retry_pending"  # 待补单

@dataclass
class BetRecord:
    """投注记录数据类"""
    record_id: str           # 记录ID
    bet_type: BetType        # 投注类型
    platform: str            # 平台名称
    match_info: Dict         # 比赛信息
    bet_info: Dict           # 投注信息
    amount: float            # 投注金额
    odds: float              # 赔率
    status: BetStatus        # 状态
    timestamp: datetime      # 时间戳
    order_no: Optional[str] = None      # 订单号
    error_message: Optional[str] = None # 错误信息
    retry_count: int = 0                # 重试次数
    max_retries: int = 3               # 最大重试次数
    hedge_pair_id: Optional[str] = None # 对冲配对ID
    profit: Optional[float] = None      # 盈利

@dataclass
class HedgePair:
    """对冲配对记录"""
    pair_id: str             # 配对ID
    base_record: BetRecord   # 基准投注记录
    hedge_record: Optional[BetRecord] = None  # 对冲投注记录
    total_amount: float = 0.0      # 总投注金额
    expected_profit: float = 0.0   # 预期盈利
    actual_profit: Optional[float] = None  # 实际盈利
    status: str = "incomplete"     # 配对状态: incomplete, complete, failed
    created_at: datetime = None
    completed_at: Optional[datetime] = None

class BetRecordManager:
    """投注记录管理器"""
    
    def __init__(self, data_dir: str = "data"):
        """
        初始化投注记录管理器
        
        参数:
            data_dir: 数据存储目录
        """
        self.data_dir = data_dir
        self.records_file = os.path.join(data_dir, "bet_records.json")
        self.hedge_pairs_file = os.path.join(data_dir, "hedge_pairs.json")
        self.failed_bets_file = os.path.join(data_dir, "failed_bets.json")
        
        # 确保数据目录存在
        os.makedirs(data_dir, exist_ok=True)
        
        # 内存缓存
        self.records: Dict[str, BetRecord] = {}
        self.hedge_pairs: Dict[str, HedgePair] = {}
        self.failed_bets: Dict[str, BetRecord] = {}
        
        # 加载数据
        self._load_data()
    
    def _load_data(self):
        """加载数据"""
        try:
            # 加载投注记录
            if os.path.exists(self.records_file):
                with open(self.records_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for record_id, record_data in data.items():
                        record_data['timestamp'] = datetime.fromisoformat(record_data['timestamp'])
                        record_data['bet_type'] = BetType(record_data['bet_type'])
                        record_data['status'] = BetStatus(record_data['status'])
                        self.records[record_id] = BetRecord(**record_data)
            
            # 加载对冲配对
            if os.path.exists(self.hedge_pairs_file):
                with open(self.hedge_pairs_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for pair_id, pair_data in data.items():
                        if pair_data['created_at']:
                            pair_data['created_at'] = datetime.fromisoformat(pair_data['created_at'])
                        if pair_data['completed_at']:
                            pair_data['completed_at'] = datetime.fromisoformat(pair_data['completed_at'])
                        
                        # 重建BetRecord对象
                        base_data = pair_data['base_record']
                        base_data['timestamp'] = datetime.fromisoformat(base_data['timestamp'])
                        base_data['bet_type'] = BetType(base_data['bet_type'])
                        base_data['status'] = BetStatus(base_data['status'])
                        pair_data['base_record'] = BetRecord(**base_data)
                        
                        if pair_data['hedge_record']:
                            hedge_data = pair_data['hedge_record']
                            hedge_data['timestamp'] = datetime.fromisoformat(hedge_data['timestamp'])
                            hedge_data['bet_type'] = BetType(hedge_data['bet_type'])
                            hedge_data['status'] = BetStatus(hedge_data['status'])
                            pair_data['hedge_record'] = BetRecord(**hedge_data)
                        
                        self.hedge_pairs[pair_id] = HedgePair(**pair_data)
            
            # 加载失败投注
            if os.path.exists(self.failed_bets_file):
                with open(self.failed_bets_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for record_id, record_data in data.items():
                        record_data['timestamp'] = datetime.fromisoformat(record_data['timestamp'])
                        record_data['bet_type'] = BetType(record_data['bet_type'])
                        record_data['status'] = BetStatus(record_data['status'])
                        self.failed_bets[record_id] = BetRecord(**record_data)
            
            logger.info(f"加载投注记录: {len(self.records)}条, 对冲配对: {len(self.hedge_pairs)}条, 失败投注: {len(self.failed_bets)}条")
            
        except Exception as e:
            logger.error(f"加载投注记录数据失败: {e}")
    
    def _save_data(self):
        """保存数据"""
        try:
            # 保存投注记录
            records_data = {}
            for record_id, record in self.records.items():
                record_dict = asdict(record)
                record_dict['timestamp'] = record.timestamp.isoformat()
                record_dict['bet_type'] = record.bet_type.value
                record_dict['status'] = record.status.value
                records_data[record_id] = record_dict
            
            with open(self.records_file, 'w', encoding='utf-8') as f:
                json.dump(records_data, f, ensure_ascii=False, indent=2)
            
            # 保存对冲配对
            pairs_data = {}
            for pair_id, pair in self.hedge_pairs.items():
                pair_dict = asdict(pair)
                if pair_dict['created_at']:
                    pair_dict['created_at'] = pair.created_at.isoformat()
                if pair_dict['completed_at']:
                    pair_dict['completed_at'] = pair.completed_at.isoformat()
                
                # 处理BetRecord对象
                base_dict = asdict(pair.base_record)
                base_dict['timestamp'] = pair.base_record.timestamp.isoformat()
                base_dict['bet_type'] = pair.base_record.bet_type.value
                base_dict['status'] = pair.base_record.status.value
                pair_dict['base_record'] = base_dict
                
                if pair.hedge_record:
                    hedge_dict = asdict(pair.hedge_record)
                    hedge_dict['timestamp'] = pair.hedge_record.timestamp.isoformat()
                    hedge_dict['bet_type'] = pair.hedge_record.bet_type.value
                    hedge_dict['status'] = pair.hedge_record.status.value
                    pair_dict['hedge_record'] = hedge_dict
                
                pairs_data[pair_id] = pair_dict
            
            with open(self.hedge_pairs_file, 'w', encoding='utf-8') as f:
                json.dump(pairs_data, f, ensure_ascii=False, indent=2)
            
            # 保存失败投注
            failed_data = {}
            for record_id, record in self.failed_bets.items():
                record_dict = asdict(record)
                record_dict['timestamp'] = record.timestamp.isoformat()
                record_dict['bet_type'] = record.bet_type.value
                record_dict['status'] = record.status.value
                failed_data[record_id] = record_dict
            
            with open(self.failed_bets_file, 'w', encoding='utf-8') as f:
                json.dump(failed_data, f, ensure_ascii=False, indent=2)
            
            logger.debug("投注记录数据保存成功")
            
        except Exception as e:
            logger.error(f"保存投注记录数据失败: {e}")
    
    def add_bet_record(self, record: BetRecord) -> bool:
        """
        添加投注记录
        
        参数:
            record: 投注记录
            
        返回:
            是否添加成功
        """
        try:
            self.records[record.record_id] = record
            
            # 如果是失败的投注，同时添加到失败列表
            if record.status == BetStatus.FAILED:
                record.status = BetStatus.RETRY_PENDING
                self.failed_bets[record.record_id] = record
            
            self._save_data()
            logger.info(f"添加投注记录: {record.record_id} - {record.platform} - {record.amount}")
            return True
            
        except Exception as e:
            logger.error(f"添加投注记录失败: {e}")
            return False
    
    def update_bet_status(self, record_id: str, status: BetStatus, 
                         order_no: Optional[str] = None, 
                         error_message: Optional[str] = None) -> bool:
        """
        更新投注状态
        
        参数:
            record_id: 记录ID
            status: 新状态
            order_no: 订单号
            error_message: 错误信息
            
        返回:
            是否更新成功
        """
        try:
            if record_id not in self.records:
                logger.warning(f"投注记录不存在: {record_id}")
                return False
            
            record = self.records[record_id]
            record.status = status
            
            if order_no:
                record.order_no = order_no
            if error_message:
                record.error_message = error_message
            
            # 处理失败投注：只有需要补单的才保存，完全失败的不保存
            if status == BetStatus.FAILED:
                # 检查是否需要补单（根据错误类型判断）
                error_msg = error_message or ''
                if any(keyword in error_msg.lower() for keyword in ['网络', 'timeout', '超时', '连接', '临时']):
                    # 网络等临时错误，标记为需要补单
                    record.status = BetStatus.RETRY_PENDING
                    self.failed_bets[record_id] = record
                    logger.info(f"投注失败但可补单: {record_id}, 原因: {error_msg}")
                else:
                    # 其他错误（如重复投注、参数错误等），不保存记录
                    logger.info(f"投注失败不保存: {record_id}, 原因: {error_msg}")
                    if record_id in self.records:
                        del self.records[record_id]
                    return True  # 返回成功，但不保存记录
            elif status == BetStatus.SUCCESS and record_id in self.failed_bets:
                # 从失败列表中移除
                del self.failed_bets[record_id]
            
            self._save_data()
            logger.info(f"更新投注状态: {record_id} -> {status.value}")
            return True
            
        except Exception as e:
            logger.error(f"更新投注状态失败: {e}")
            return False

    def create_hedge_pair(self, base_record: BetRecord, expected_profit: float) -> str:
        """
        创建对冲配对

        参数:
            base_record: 基准投注记录
            expected_profit: 预期盈利

        返回:
            配对ID
        """
        try:
            pair_id = f"hedge_{int(datetime.now().timestamp() * 1000)}"

            hedge_pair = HedgePair(
                pair_id=pair_id,
                base_record=base_record,
                total_amount=base_record.amount,
                expected_profit=expected_profit,
                created_at=datetime.now()
            )

            self.hedge_pairs[pair_id] = hedge_pair

            # 更新基准记录的配对ID
            base_record.hedge_pair_id = pair_id
            self.records[base_record.record_id] = base_record

            self._save_data()
            logger.info(f"创建对冲配对: {pair_id}")
            return pair_id

        except Exception as e:
            logger.error(f"创建对冲配对失败: {e}")
            return ""

    def complete_hedge_pair(self, pair_id: str, hedge_record: BetRecord) -> bool:
        """
        完成对冲配对

        参数:
            pair_id: 配对ID
            hedge_record: 对冲投注记录

        返回:
            是否完成成功
        """
        try:
            if pair_id not in self.hedge_pairs:
                logger.warning(f"对冲配对不存在: {pair_id}")
                return False

            hedge_pair = self.hedge_pairs[pair_id]
            hedge_pair.hedge_record = hedge_record
            hedge_pair.total_amount += hedge_record.amount
            hedge_pair.completed_at = datetime.now()

            # 更新对冲记录的配对ID
            hedge_record.hedge_pair_id = pair_id
            self.records[hedge_record.record_id] = hedge_record

            # 计算实际盈利
            if (hedge_pair.base_record.status == BetStatus.SUCCESS and
                hedge_record.status == BetStatus.SUCCESS):
                hedge_pair.status = "complete"
                # 这里可以根据实际结果计算盈利
                hedge_pair.actual_profit = hedge_pair.expected_profit
            else:
                hedge_pair.status = "failed"
                hedge_pair.actual_profit = -hedge_pair.total_amount

            self._save_data()
            logger.info(f"完成对冲配对: {pair_id}")
            return True

        except Exception as e:
            logger.error(f"完成对冲配对失败: {e}")
            return False

    def get_bet_records(self, platform: Optional[str] = None,
                       status: Optional[BetStatus] = None,
                       bet_type: Optional[BetType] = None,
                       hours: int = 24) -> List[BetRecord]:
        """
        获取投注记录

        参数:
            platform: 平台名称过滤
            status: 状态过滤
            bet_type: 投注类型过滤
            hours: 时间范围（小时）

        返回:
            投注记录列表
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            records = []

            for record in self.records.values():
                # 时间过滤
                if record.timestamp < cutoff_time:
                    continue

                # 平台过滤
                if platform and record.platform != platform:
                    continue

                # 状态过滤
                if status and record.status != status:
                    continue

                # 投注类型过滤
                if bet_type and record.bet_type != bet_type:
                    continue

                records.append(record)

            # 按时间倒序排列
            records.sort(key=lambda x: x.timestamp, reverse=True)
            return records

        except Exception as e:
            logger.error(f"获取投注记录失败: {e}")
            return []

    def get_hedge_pairs(self, status: Optional[str] = None, hours: int = 24) -> List[HedgePair]:
        """
        获取对冲配对记录

        参数:
            status: 状态过滤
            hours: 时间范围（小时）

        返回:
            对冲配对列表
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            pairs = []

            for pair in self.hedge_pairs.values():
                # 时间过滤
                if pair.created_at and pair.created_at < cutoff_time:
                    continue

                # 状态过滤
                if status and pair.status != status:
                    continue

                pairs.append(pair)

            # 按创建时间倒序排列
            pairs.sort(key=lambda x: x.created_at or datetime.min, reverse=True)
            return pairs

        except Exception as e:
            logger.error(f"获取对冲配对失败: {e}")
            return []

    def get_failed_bets(self) -> List[BetRecord]:
        """
        获取失败待补单的投注

        返回:
            失败投注列表
        """
        try:
            failed_list = list(self.failed_bets.values())
            failed_list.sort(key=lambda x: x.timestamp, reverse=True)
            return failed_list

        except Exception as e:
            logger.error(f"获取失败投注失败: {e}")
            return []

    def retry_failed_bet(self, record_id: str) -> bool:
        """
        重试失败的投注

        参数:
            record_id: 记录ID

        返回:
            是否可以重试
        """
        try:
            if record_id not in self.failed_bets:
                logger.warning(f"失败投注记录不存在: {record_id}")
                return False

            record = self.failed_bets[record_id]

            # 检查重试次数
            if record.retry_count >= record.max_retries:
                logger.warning(f"投注重试次数已达上限: {record_id}")
                return False

            # 增加重试次数
            record.retry_count += 1
            record.status = BetStatus.PENDING

            # 更新记录
            self.records[record_id] = record
            self._save_data()

            logger.info(f"标记投注重试: {record_id} (第{record.retry_count}次)")
            return True

        except Exception as e:
            logger.error(f"重试失败投注失败: {e}")
            return False

    def remove_failed_bet(self, record_id: str) -> bool:
        """
        移除失败投注（放弃补单）

        参数:
            record_id: 记录ID

        返回:
            是否移除成功
        """
        try:
            if record_id in self.failed_bets:
                record = self.failed_bets[record_id]
                record.status = BetStatus.CANCELLED

                # 从失败列表中移除
                del self.failed_bets[record_id]

                # 更新主记录
                self.records[record_id] = record
                self._save_data()

                logger.info(f"移除失败投注: {record_id}")
                return True
            else:
                logger.warning(f"失败投注记录不存在: {record_id}")
                return False

        except Exception as e:
            logger.error(f"移除失败投注失败: {e}")
            return False

    def get_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """
        获取投注统计信息

        参数:
            hours: 统计时间范围（小时）

        返回:
            统计信息字典
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            # 基础统计
            total_records = 0
            success_records = 0
            failed_records = 0
            pending_records = 0
            total_amount = 0.0
            total_profit = 0.0

            # 平台统计
            platform_stats = {}

            # 对冲统计
            hedge_stats = {
                "total_pairs": 0,
                "complete_pairs": 0,
                "failed_pairs": 0,
                "incomplete_pairs": 0,
                "total_hedge_profit": 0.0
            }

            # 统计投注记录
            for record in self.records.values():
                if record.timestamp < cutoff_time:
                    continue

                total_records += 1
                total_amount += record.amount

                if record.status == BetStatus.SUCCESS:
                    success_records += 1
                    if record.profit:
                        total_profit += record.profit
                elif record.status == BetStatus.FAILED:
                    failed_records += 1
                elif record.status in [BetStatus.PENDING, BetStatus.RETRY_PENDING]:
                    pending_records += 1

                # 平台统计
                if record.platform not in platform_stats:
                    platform_stats[record.platform] = {
                        "total": 0,
                        "success": 0,
                        "failed": 0,
                        "amount": 0.0,
                        "profit": 0.0
                    }

                platform_stats[record.platform]["total"] += 1
                platform_stats[record.platform]["amount"] += record.amount

                if record.status == BetStatus.SUCCESS:
                    platform_stats[record.platform]["success"] += 1
                    if record.profit:
                        platform_stats[record.platform]["profit"] += record.profit
                elif record.status == BetStatus.FAILED:
                    platform_stats[record.platform]["failed"] += 1

            # 统计对冲配对
            for pair in self.hedge_pairs.values():
                if pair.created_at and pair.created_at < cutoff_time:
                    continue

                hedge_stats["total_pairs"] += 1

                if pair.status == "complete":
                    hedge_stats["complete_pairs"] += 1
                    if pair.actual_profit:
                        hedge_stats["total_hedge_profit"] += pair.actual_profit
                elif pair.status == "failed":
                    hedge_stats["failed_pairs"] += 1
                else:
                    hedge_stats["incomplete_pairs"] += 1

            # 计算成功率
            success_rate = (success_records / total_records * 100) if total_records > 0 else 0

            # 计算平台成功率
            for platform, stats in platform_stats.items():
                stats["success_rate"] = (stats["success"] / stats["total"] * 100) if stats["total"] > 0 else 0

            return {
                "summary": {
                    "total_records": total_records,
                    "success_records": success_records,
                    "failed_records": failed_records,
                    "pending_records": pending_records,
                    "success_rate": round(success_rate, 2),
                    "total_amount": round(total_amount, 2),
                    "total_profit": round(total_profit, 2),
                    "failed_bets_count": len(self.failed_bets)
                },
                "platform_stats": platform_stats,
                "hedge_stats": hedge_stats,
                "time_range_hours": hours
            }

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

    def export_records_to_dict(self, hours: int = 24) -> Dict[str, Any]:
        """
        导出投注记录为字典格式

        参数:
            hours: 导出时间范围（小时）

        返回:
            包含所有记录的字典
        """
        try:
            records = self.get_bet_records(hours=hours)
            hedge_pairs = self.get_hedge_pairs(hours=hours)
            failed_bets = self.get_failed_bets()

            # 转换为字典格式
            records_data = []
            for record in records:
                record_dict = {
                    "record_id": record.record_id,
                    "bet_type": record.bet_type.value,
                    "platform": record.platform,
                    "match_info": record.match_info,
                    "bet_info": record.bet_info,
                    "amount": record.amount,
                    "odds": record.odds,
                    "status": record.status.value,
                    "timestamp": record.timestamp.isoformat(),
                    "order_no": record.order_no,
                    "error_message": record.error_message,
                    "retry_count": record.retry_count,
                    "hedge_pair_id": record.hedge_pair_id,
                    "profit": record.profit
                }
                records_data.append(record_dict)

            # 转换对冲配对
            pairs_data = []
            for pair in hedge_pairs:
                pair_dict = {
                    "pair_id": pair.pair_id,
                    "base_record_id": pair.base_record.record_id,
                    "hedge_record_id": pair.hedge_record.record_id if pair.hedge_record else None,
                    "total_amount": pair.total_amount,
                    "expected_profit": pair.expected_profit,
                    "actual_profit": pair.actual_profit,
                    "status": pair.status,
                    "created_at": pair.created_at.isoformat() if pair.created_at else None,
                    "completed_at": pair.completed_at.isoformat() if pair.completed_at else None
                }
                pairs_data.append(pair_dict)

            # 转换失败投注
            failed_data = []
            for record in failed_bets:
                record_dict = {
                    "record_id": record.record_id,
                    "platform": record.platform,
                    "match_info": record.match_info,
                    "bet_info": record.bet_info,
                    "amount": record.amount,
                    "odds": record.odds,
                    "timestamp": record.timestamp.isoformat(),
                    "error_message": record.error_message,
                    "retry_count": record.retry_count,
                    "max_retries": record.max_retries
                }
                failed_data.append(record_dict)

            return {
                "bet_records": records_data,
                "hedge_pairs": pairs_data,
                "failed_bets": failed_data,
                "statistics": self.get_statistics(hours),
                "export_time": datetime.now().isoformat(),
                "time_range_hours": hours
            }

        except Exception as e:
            logger.error(f"导出记录失败: {e}")
            return {}

    def cleanup_old_records(self, days: int = 30):
        """
        清理旧记录

        参数:
            days: 保留天数
        """
        try:
            cutoff_time = datetime.now() - timedelta(days=days)

            # 清理投注记录
            old_records = [
                record_id for record_id, record in self.records.items()
                if record.timestamp < cutoff_time
            ]

            for record_id in old_records:
                del self.records[record_id]

            # 清理对冲配对
            old_pairs = [
                pair_id for pair_id, pair in self.hedge_pairs.items()
                if pair.created_at and pair.created_at < cutoff_time
            ]

            for pair_id in old_pairs:
                del self.hedge_pairs[pair_id]

            # 清理失败投注（只保留最近7天的）
            failed_cutoff = datetime.now() - timedelta(days=7)
            old_failed = [
                record_id for record_id, record in self.failed_bets.items()
                if record.timestamp < failed_cutoff
            ]

            for record_id in old_failed:
                del self.failed_bets[record_id]

            if old_records or old_pairs or old_failed:
                self._save_data()
                logger.info(f"清理旧记录: 投注记录{len(old_records)}条, 对冲配对{len(old_pairs)}条, 失败投注{len(old_failed)}条")

        except Exception as e:
            logger.error(f"清理旧记录失败: {e}")




# 全局实例
_bet_record_manager = None

def get_bet_record_manager() -> BetRecordManager:
    """获取全局投注记录管理器实例"""
    global _bet_record_manager
    if _bet_record_manager is None:
        _bet_record_manager = BetRecordManager()
    return _bet_record_manager
