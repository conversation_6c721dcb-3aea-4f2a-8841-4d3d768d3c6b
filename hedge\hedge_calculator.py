#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import math
from typing import Dict, Tuple, Optional

logger = logging.getLogger(__name__)

class BetAmountCalculator:
    """投注金额分配计算器"""
    
    def __init__(self):
        """初始化计算器"""
        logger.info("投注金额计算器初始化完成")
    
    def calculate_hedge_amounts(self, base_amount: float, base_odds: float,
                              hedge_odds: float, base_ratio: float = 1.0,
                              hedge_ratio: float = 1.0, crown_discount_rate: float = 1.0,
                              base_platform: str = "pinbet") -> Dict[str, float]:
        """
        计算对冲投注金额分配（套利公式 + 比例调整 + 金额折扣率）

        参数:
            base_amount: 基准投注金额 (如200)
            base_odds: 基准平台赔率 (如1.88)
            hedge_odds: 对冲平台赔率 (如2.06)
            base_ratio: 基准平台投注比例 (如1.0)
            hedge_ratio: 对冲平台投注比例 (如1.25)
            crown_discount_rate: 皇冠金额折扣率 (如0.8表示8折盘，想要有效投注200需实际投注250)
            base_platform: 基准平台 ("pinbet" 或 "crown")

        返回:
            {
                'base_amount': 250,     # 基准平台实际投注金额
                'hedge_amount': 182,    # 对冲平台实际投注金额
                'total_investment': 432,
                'guaranteed_return': 470,
                'net_profit': 38,
                'profit_rate': 8.8
            }
        """
        try:
            if base_amount <= 0 or base_odds <= 1.0 or hedge_odds <= 1.0:
                raise ValueError("投注金额必须大于0，赔率必须大于1.0")

            if base_ratio <= 0 or hedge_ratio <= 0:
                raise ValueError("投注比例必须大于0")

            # 第一步：使用基准金额进行套利计算（不先应用比例）
            calculated_hedge_amount = (base_amount * base_odds) / hedge_odds

            # 第二步：应用平台投注比例和折扣率
            if base_platform == "crown":
                # 皇冠作为基准平台
                effective_base_amount = base_amount  # 有效投注金额
                actual_base_amount = (base_amount * base_ratio) / crown_discount_rate  # 实际投注金额
                actual_hedge_amount = calculated_hedge_amount * hedge_ratio  # 平博投注金额
                logger.info(f"皇冠基准: 有效投注{effective_base_amount} -> 实际投注{actual_base_amount:.2f} (比例{base_ratio}, 折扣{crown_discount_rate})")
            else:
                # 平博作为基准平台
                actual_base_amount = base_amount * base_ratio  # 平博投注金额
                effective_hedge_amount = calculated_hedge_amount  # 皇冠有效投注
                actual_hedge_amount = (calculated_hedge_amount * hedge_ratio) / crown_discount_rate  # 皇冠实际投注
                logger.info(f"平博基准: 皇冠有效投注{effective_hedge_amount:.2f} -> 实际投注{actual_hedge_amount:.2f} (比例{hedge_ratio}, 折扣{crown_discount_rate})")

            # 计算总投资（使用实际投注金额）
            total_investment = actual_base_amount + actual_hedge_amount

            # 计算保证收益（使用有效投注金额和原始赔率）
            if base_platform == "crown":
                guaranteed_return = effective_base_amount * base_odds  # 皇冠有效投注 × 皇冠赔率
                effective_hedge_amount = calculated_hedge_amount  # 平博有效投注
            else:
                guaranteed_return = base_amount * base_odds  # 平博有效投注 × 平博赔率
                effective_base_amount = base_amount  # 平博有效投注

            # 计算净利润
            net_profit = guaranteed_return - total_investment

            # 计算收益率
            profit_rate = (net_profit / total_investment) * 100 if total_investment > 0 else 0

            result = {
                'base_amount': round(actual_base_amount, 2),      # 实际投注金额
                'hedge_amount': round(actual_hedge_amount, 2),   # 实际投注金额
                'total_investment': round(total_investment, 2),
                'guaranteed_return': round(guaranteed_return, 2),
                'net_profit': round(net_profit, 2),
                'profit_rate': round(profit_rate, 2),
                'base_ratio': base_ratio,
                'hedge_ratio': hedge_ratio,
                'crown_discount_rate': crown_discount_rate,
                'base_platform': base_platform,
                'effective_base_amount': round(effective_base_amount, 2),    # 有效投注金额
                'effective_hedge_amount': round(effective_hedge_amount, 2),  # 有效投注金额
                'original_base_odds': base_odds,
                'original_hedge_odds': hedge_odds
            }

            logger.info(f"套利计算结果: 基准={result['base_amount']}, 对冲={result['hedge_amount']}, 净利润={result['net_profit']}")
            return result

        except Exception as e:
            logger.error(f"计算对冲金额时出错: {e}")
            raise
    
    def calculate_kelly_amounts(self, base_amount: float, base_odds: float, 
                              hedge_odds: float, win_probability: float = 0.5) -> Dict[str, float]:
        """
        使用凯利公式计算最优投注金额分配
        
        参数:
            base_amount: 总可用资金
            base_odds: 基准平台赔率
            hedge_odds: 对冲平台赔率
            win_probability: 基准平台获胜概率（默认0.5）
        
        返回:
            优化后的投注金额分配
        """
        try:
            # 凯利公式: f = (bp - q) / b
            # 其中 b = 赔率-1, p = 获胜概率, q = 失败概率
            
            base_b = base_odds - 1
            hedge_b = hedge_odds - 1
            p = win_probability
            q = 1 - win_probability
            
            # 计算凯利比例
            base_kelly = (base_b * p - q) / base_b if base_b > 0 else 0
            hedge_kelly = (hedge_b * q - p) / hedge_b if hedge_b > 0 else 0
            
            # 确保比例为正且不超过1
            base_kelly = max(0, min(1, base_kelly))
            hedge_kelly = max(0, min(1, hedge_kelly))
            
            # 标准化比例
            total_kelly = base_kelly + hedge_kelly
            if total_kelly > 0:
                base_kelly = base_kelly / total_kelly
                hedge_kelly = hedge_kelly / total_kelly
            else:
                base_kelly = hedge_kelly = 0.5
            
            # 计算投注金额
            base_bet = base_amount * base_kelly
            hedge_bet = base_amount * hedge_kelly
            
            result = {
                'base_amount': round(base_bet, 2),
                'hedge_amount': round(hedge_bet, 2),
                'total_investment': round(base_bet + hedge_bet, 2),
                'base_kelly_ratio': round(base_kelly, 4),
                'hedge_kelly_ratio': round(hedge_kelly, 4)
            }
            
            logger.debug(f"凯利公式计算结果: {result}")
            return result
            
        except Exception as e:
            logger.error(f"凯利公式计算时出错: {e}")
            # 返回等额分配作为备选
            return {
                'base_amount': round(base_amount / 2, 2),
                'hedge_amount': round(base_amount / 2, 2),
                'total_investment': round(base_amount, 2),
                'base_kelly_ratio': 0.5,
                'hedge_kelly_ratio': 0.5
            }
    
    def validate_balance_requirements(self, base_amount: float, hedge_amount: float,
                                    base_balance: float, hedge_balance: float) -> Tuple[bool, str]:
        """
        验证两个平台是否有足够余额
        
        参数:
            base_amount: 基准平台投注金额
            hedge_amount: 对冲平台投注金额
            base_balance: 基准平台余额
            hedge_balance: 对冲平台余额
        
        返回:
            (是否有足够余额, 说明信息)
        """
        try:
            # 检查基准平台余额
            if base_balance < base_amount:
                return False, f"基准平台余额不足: 需要{base_amount}, 可用{base_balance}"
            
            # 检查对冲平台余额
            if hedge_balance < hedge_amount:
                return False, f"对冲平台余额不足: 需要{hedge_amount}, 可用{hedge_balance}"
            
            # 检查余额安全边际（保留10%作为缓冲）
            base_safety_margin = base_balance * 0.1
            hedge_safety_margin = hedge_balance * 0.1
            
            if base_balance - base_amount < base_safety_margin:
                logger.warning(f"基准平台余额接近最低限制: 剩余{base_balance - base_amount}")
            
            if hedge_balance - hedge_amount < hedge_safety_margin:
                logger.warning(f"对冲平台余额接近最低限制: 剩余{hedge_balance - hedge_amount}")
            
            return True, "余额验证通过"
            
        except Exception as e:
            logger.error(f"余额验证时出错: {e}")
            return False, f"余额验证失败: {str(e)}"
    
    def calculate_expected_return(self, base_amount: float, base_odds: float,
                                hedge_amount: float, hedge_odds: float,
                                base_win_prob: float = 0.5) -> Dict[str, float]:
        """
        计算预期收益
        
        参数:
            base_amount: 基准平台投注金额
            base_odds: 基准平台赔率
            hedge_amount: 对冲平台投注金额
            hedge_odds: 对冲平台赔率
            base_win_prob: 基准平台获胜概率
        
        返回:
            预期收益分析
        """
        try:
            total_investment = base_amount + hedge_amount
            hedge_win_prob = 1 - base_win_prob
            
            # 基准平台获胜的情况
            base_win_return = base_amount * base_odds - hedge_amount
            
            # 对冲平台获胜的情况
            hedge_win_return = hedge_amount * hedge_odds - base_amount
            
            # 计算期望收益
            expected_return = (base_win_return * base_win_prob + 
                             hedge_win_return * hedge_win_prob)
            
            # 计算期望收益率
            expected_return_rate = (expected_return / total_investment) * 100 if total_investment > 0 else 0
            
            result = {
                'total_investment': round(total_investment, 2),
                'base_win_return': round(base_win_return, 2),
                'hedge_win_return': round(hedge_win_return, 2),
                'expected_return': round(expected_return, 2),
                'expected_return_rate': round(expected_return_rate, 2),
                'base_win_prob': base_win_prob,
                'hedge_win_prob': hedge_win_prob
            }
            
            logger.debug(f"预期收益计算结果: {result}")
            return result
            
        except Exception as e:
            logger.error(f"计算预期收益时出错: {e}")
            return {
                'total_investment': 0,
                'base_win_return': 0,
                'hedge_win_return': 0,
                'expected_return': 0,
                'expected_return_rate': 0,
                'base_win_prob': 0.5,
                'hedge_win_prob': 0.5
            }
    
    def optimize_amounts(self, target_amount: float, base_odds: float, hedge_odds: float,
                        max_base: float, max_hedge: float) -> Dict[str, float]:
        """
        根据余额限制优化投注金额
        
        参数:
            target_amount: 目标基准投注金额
            base_odds: 基准平台赔率
            hedge_odds: 对冲平台赔率
            max_base: 基准平台最大可投注金额
            max_hedge: 对冲平台最大可投注金额
        
        返回:
            优化后的投注金额分配
        """
        try:
            # 计算理想的对冲金额
            ideal_hedge = (target_amount * base_odds) / hedge_odds
            
            # 检查是否超出限制
            if target_amount <= max_base and ideal_hedge <= max_hedge:
                # 理想情况，无需调整
                return self.calculate_hedge_amounts(target_amount, base_odds, hedge_odds)
            
            # 需要调整，找到最大可行的基准金额
            max_possible_base = min(max_base, (max_hedge * hedge_odds) / base_odds)
            
            if max_possible_base <= 0:
                raise ValueError("无法找到可行的投注金额分配")
            
            # 使用调整后的基准金额重新计算
            result = self.calculate_hedge_amounts(max_possible_base, base_odds, hedge_odds)
            result['optimized'] = True
            result['original_target'] = target_amount
            
            logger.info(f"投注金额已优化: 原目标{target_amount} -> 实际{max_possible_base}")
            return result
            
        except Exception as e:
            logger.error(f"优化投注金额时出错: {e}")
            raise
    
    def calculate_arbitrage_profit_rate(self, base_odds: float, hedge_odds: float) -> float:
        """
        计算套利收益率
        
        参数:
            base_odds: 基准平台赔率
            hedge_odds: 对冲平台赔率
        
        返回:
            套利收益率（百分比）
        """
        try:
            # 计算隐含概率之和
            implied_prob_sum = (1 / base_odds) + (1 / hedge_odds)
            
            # 如果概率之和小于1，存在套利机会
            if implied_prob_sum < 1:
                arbitrage_rate = ((1 - implied_prob_sum) / implied_prob_sum) * 100
                return round(arbitrage_rate, 2)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"计算套利收益率时出错: {e}")
            return 0.0
