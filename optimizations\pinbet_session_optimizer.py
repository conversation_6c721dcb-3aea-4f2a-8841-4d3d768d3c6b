#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
平博会话优化器 - 解决会话激活延迟问题

主要优化：
1. 修复时间戳计算错误
2. 优化会话保活策略
3. 实现快速重连机制
4. 添加连接池管理
"""

import time
import logging
import threading
from datetime import datetime
from typing import Optional, Dict, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

logger = logging.getLogger(__name__)


class PinbetSessionOptimizer:
    """平博会话优化器 - 遵循SOLID原则"""
    
    def __init__(self, pinbet_instance):
        self.pinbet = pinbet_instance
        self.session_pool = {}  # 会话池
        self.last_activity_time = time.time()  # 修复时间戳初始化
        self.session_lock = threading.RLock()
        
        # 优化配置
        self.max_session_idle_time = 180  # 3分钟（减少到更合理的值）
        self.session_refresh_interval = 300  # 5分钟强制刷新
        self.quick_reconnect_enabled = True
        self.connection_pool_size = 5
        
        # 设置优化的会话配置
        self.setup_optimized_session()
        
        logger.info("平博会话优化器已初始化")
    
    def setup_optimized_session(self):
        """设置优化的会话配置"""
        if not hasattr(self.pinbet, 'session') or self.pinbet.session is None:
            self.pinbet.session = requests.Session()
        
        # 配置连接池和重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=0.5,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]
        )
        
        adapter = HTTPAdapter(
            pool_connections=self.connection_pool_size,
            pool_maxsize=self.connection_pool_size * 2,
            max_retries=retry_strategy,
            pool_block=False
        )
        
        self.pinbet.session.mount("http://", adapter)
        self.pinbet.session.mount("https://", adapter)
        
        # 设置合理的超时
        self.pinbet.session.timeout = (10, 30)  # 连接超时10秒，读取超时30秒
        
        # 设置Keep-Alive
        self.pinbet.session.headers.update({
            'Connection': 'keep-alive',
            'Keep-Alive': 'timeout=60, max=100'
        })
        
        logger.info("会话配置优化完成")
    
    def update_activity_time(self):
        """更新活动时间 - 修复时间戳问题"""
        with self.session_lock:
            self.last_activity_time = time.time()  # 使用正确的时间戳
            logger.debug(f"更新活动时间: {datetime.fromtimestamp(self.last_activity_time)}")
    
    def get_session_idle_time(self) -> float:
        """获取会话空闲时间 - 修复计算逻辑"""
        current_time = time.time()
        idle_time = current_time - self.last_activity_time
        
        # 防止异常的负数或过大值
        if idle_time < 0 or idle_time > 86400:  # 超过24小时认为异常
            logger.warning(f"检测到异常的空闲时间: {idle_time}秒，重置为0")
            self.last_activity_time = current_time
            idle_time = 0
        
        return idle_time
    
    def check_session_health(self) -> tuple[bool, str]:
        """检查会话健康状态 - 优化版本"""
        try:
            idle_time = self.get_session_idle_time()
            
            # 如果空闲时间超过阈值，需要刷新
            if idle_time > self.max_session_idle_time:
                logger.info(f"会话空闲{idle_time:.0f}秒，需要刷新")
                return self.quick_refresh_session()
            
            # 检查会话是否仍然有效
            if hasattr(self.pinbet, 'is_logged_in') and not self.pinbet.is_logged_in:
                return False, "会话已失效"
            
            return True, "会话正常"
            
        except Exception as e:
            logger.error(f"检查会话健康状态失败: {e}")
            return False, f"健康检查异常: {e}"
    
    def quick_refresh_session(self) -> tuple[bool, str]:
        """快速刷新会话 - 优化版本"""
        try:
            with self.session_lock:
                # 方法1: 尝试轻量级刷新（发送心跳请求）
                if self.quick_reconnect_enabled:
                    success, message = self.send_heartbeat()
                    if success:
                        self.update_activity_time()
                        return True, "快速刷新成功"
                
                # 方法2: 尝试获取余额（中等开销）
                success, message = self.refresh_with_balance_check()
                if success:
                    self.update_activity_time()
                    return True, "余额刷新成功"
                
                # 方法3: 重新登录（最后手段）
                logger.warning("快速刷新失败，尝试重新登录")
                return self.force_reconnect()
                
        except Exception as e:
            logger.error(f"快速刷新会话失败: {e}")
            return False, f"刷新异常: {e}"
    
    def send_heartbeat(self) -> tuple[bool, str]:
        """发送心跳请求保持会话活跃"""
        try:
            # 发送一个轻量级的API请求
            if hasattr(self.pinbet, 'session') and self.pinbet.session:
                # 尝试访问用户信息或其他轻量级端点
                response = self.pinbet.session.get(
                    f"{self.pinbet.base_url}/api/user/info",
                    timeout=5
                )
                
                if response.status_code == 200:
                    logger.debug("心跳请求成功")
                    return True, "心跳成功"
                else:
                    logger.debug(f"心跳请求失败: {response.status_code}")
                    return False, f"心跳失败: {response.status_code}"
            
            return False, "会话不存在"
            
        except Exception as e:
            logger.debug(f"心跳请求异常: {e}")
            return False, f"心跳异常: {e}"
    
    def refresh_with_balance_check(self) -> tuple[bool, str]:
        """通过余额检查刷新会话"""
        try:
            if hasattr(self.pinbet, 'get_balance'):
                balance_result = self.pinbet.get_balance()
                if balance_result and isinstance(balance_result, (int, float)):
                    logger.debug(f"余额检查成功: {balance_result}")
                    return True, "余额检查成功"
                else:
                    return False, "余额检查失败"
            
            return False, "余额方法不可用"
            
        except Exception as e:
            logger.debug(f"余额检查异常: {e}")
            return False, f"余额检查异常: {e}"
    
    def force_reconnect(self) -> tuple[bool, str]:
        """强制重新连接"""
        try:
            # 清理旧会话
            if hasattr(self.pinbet, 'session') and self.pinbet.session:
                self.pinbet.session.close()
            
            # 重新设置会话
            self.setup_optimized_session()
            
            # 重新登录
            if hasattr(self.pinbet, 'login') and hasattr(self.pinbet, 'username') and hasattr(self.pinbet, 'password'):
                login_result = self.pinbet.login(self.pinbet.username, self.pinbet.password)
                if login_result:
                    self.update_activity_time()
                    logger.info("强制重连成功")
                    return True, "重连成功"
                else:
                    return False, "重新登录失败"
            
            return False, "无法重新登录"
            
        except Exception as e:
            logger.error(f"强制重连失败: {e}")
            return False, f"重连异常: {e}"
    
    def optimize_api_call(self, api_func, *args, **kwargs):
        """优化API调用 - 装饰器模式"""
        try:
            # 检查会话健康状态
            healthy, message = self.check_session_health()
            if not healthy:
                logger.warning(f"会话不健康: {message}")
                # 尝试修复会话
                fixed, fix_message = self.quick_refresh_session()
                if not fixed:
                    raise Exception(f"会话修复失败: {fix_message}")
            
            # 执行API调用
            result = api_func(*args, **kwargs)
            
            # 更新活动时间
            self.update_activity_time()
            
            return result
            
        except Exception as e:
            logger.error(f"优化API调用失败: {e}")
            raise
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        idle_time = self.get_session_idle_time()
        
        return {
            'session_idle_time': idle_time,
            'last_activity': datetime.fromtimestamp(self.last_activity_time).isoformat(),
            'session_healthy': idle_time < self.max_session_idle_time,
            'quick_reconnect_enabled': self.quick_reconnect_enabled,
            'max_idle_time': self.max_session_idle_time,
            'connection_pool_size': self.connection_pool_size
        }


def apply_pinbet_session_optimization(pinbet_instance):
    """应用平博会话优化 - 工厂函数"""
    optimizer = PinbetSessionOptimizer(pinbet_instance)
    
    # 替换原有的会话检查方法
    original_check_session = getattr(pinbet_instance, 'check_session_validity', None)
    if original_check_session:
        pinbet_instance.check_session_validity = optimizer.check_session_health
    
    # 替换原有的刷新方法
    original_refresh = getattr(pinbet_instance, 'refresh_session', None)
    if original_refresh:
        pinbet_instance.refresh_session = optimizer.quick_refresh_session
    
    # 添加优化器引用
    pinbet_instance.session_optimizer = optimizer
    
    logger.info("平博会话优化已应用")
    return optimizer


# 使用示例
def optimize_pinbet_session_usage():
    """使用示例"""
    # 假设有一个pinbet实例
    # pinbet = PinbetAPI()
    
    # 应用优化
    # optimizer = apply_pinbet_session_optimization(pinbet)
    
    # 使用优化后的API调用
    # result = optimizer.optimize_api_call(pinbet.get_balance)
    
    # 获取统计信息
    # stats = optimizer.get_statistics()
    # print(f"会话统计: {stats}")
    
    pass