#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import re
import difflib
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

from utils.advanced_similarity import similarity_calculator, calculate_team_similarity

logger = logging.getLogger(__name__)

class MatchConfidence(Enum):
    """比赛匹配置信度枚举"""
    HIGH = "high"           # 高置信度 >= 0.85
    MEDIUM = "medium"       # 中置信度 0.70-0.84
    LOW = "low"            # 低置信度 0.50-0.69
    REJECTED = "rejected"   # 拒绝匹配 < 0.50

@dataclass
class MatchScore:
    """比赛匹配分数详情"""
    team_score: float = 0.0      # 队伍名称匹配分数
    time_score: float = 0.0      # 时间匹配分数
    league_score: float = 0.0    # 联赛匹配分数
    market_score: float = 0.0    # 盘口匹配分数
    total_score: float = 0.0     # 总分
    confidence: MatchConfidence = MatchConfidence.REJECTED
    
    def calculate_total(self):
        """计算总分"""
        # 权重分配：队伍40%，时间30%，联赛20%，盘口10%
        self.total_score = (
            self.team_score * 0.4 +
            self.time_score * 0.3 +
            self.league_score * 0.2 +
            self.market_score * 0.1
        )
        
        # 确定置信度
        if self.total_score >= 0.85:
            self.confidence = MatchConfidence.HIGH
        elif self.total_score >= 0.70:
            self.confidence = MatchConfidence.MEDIUM
        elif self.total_score >= 0.50:
            self.confidence = MatchConfidence.LOW
        else:
            self.confidence = MatchConfidence.REJECTED

@dataclass
class MatchResult:
    """比赛匹配结果"""
    is_valid: bool = False
    score: MatchScore = None
    platform1_info: Dict = None
    platform2_info: Dict = None
    warnings: List[str] = None
    errors: List[str] = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []
        if self.errors is None:
            self.errors = []

class MatchValidator:
    """比赛匹配验证器"""
    
    def __init__(self):
        """初始化验证器"""
        # 队伍名称标准化规则
        self.team_name_replacements = {
            # 常见的队伍名称变体
            "洛杉矶湖人": ["湖人", "Lakers", "LA Lakers"],
            "北港八塘码头": ["全球港口"],
            "波士顿凯尔特人": ["凯尔特人", "Celtics", "Boston"],
            "泰拉菲玛吉普": ["Terrafirma Dyip", "起亚嘉华"],

            # 波多黎各篮球联赛队伍别名
            "桑图尔塞捕蟹者": ["圣特鲁斯加拉加斯", "Santurce Crabbers", "Cangrejeros de Santurce"],
            "卡瓜斯克里奥尔人": ["卡瓜斯的克里奥尔人", "Criollos de Caguas", "Caguas Creoles"],

            # 可以根据需要添加更多
        }
        
        # 联赛名称标准化规则
        self.league_name_replacements = {
            "NBA": ["美国职业篮球联赛", "National Basketball Association"],
            "CBA": ["中国男子篮球职业联赛", "Chinese Basketball Association"],
            # 可以根据需要添加更多
        }
        
        # 盘口类型标准化
        self.market_type_mapping = {
            "让球": ["handicap", "spread", "让分"],
            "大小球": ["total", "over_under", "总分"],
            "独赢": ["moneyline", "1x2", "胜负"]
        }
        
        logger.info("比赛匹配验证器初始化完成")
    
    def validate_match_pair(self, option1: Dict, option2: Dict) -> MatchResult:
        """
        验证两个投注选项是否为同一场比赛
        
        参数:
            option1: 平台1的投注选项
            option2: 平台2的投注选项
        
        返回:
            匹配结果
        """
        try:
            logger.info("开始验证比赛匹配")
            
            # 提取比赛信息
            match1_info = self._extract_match_info(option1, "平台1")
            match2_info = self._extract_match_info(option2, "平台2")
            
            # 创建匹配结果对象
            result = MatchResult(
                platform1_info=match1_info,
                platform2_info=match2_info
            )
            
            # 计算各维度匹配分数
            score = MatchScore()
            
            # 1. 队伍名称匹配
            score.team_score = self._calculate_team_match_score(
                match1_info, match2_info, result
            )
            
            # 2. 时间匹配
            score.time_score = self._calculate_time_match_score(
                match1_info, match2_info, result
            )
            
            # 3. 联赛匹配
            score.league_score = self._calculate_league_match_score(
                match1_info, match2_info, result
            )
            
            # 4. 盘口匹配
            score.market_score = self._calculate_market_match_score(
                match1_info, match2_info, result
            )
            
            # 计算总分和置信度
            score.calculate_total()
            result.score = score
            
            # 判断是否有效
            result.is_valid = score.confidence in [MatchConfidence.HIGH, MatchConfidence.MEDIUM]
            
            # 记录结果
            logger.info(f"比赛匹配验证完成: 总分={score.total_score:.3f}, 置信度={score.confidence.value}")
            
            return result
            
        except Exception as e:
            logger.error(f"比赛匹配验证异常: {e}")
            result = MatchResult()
            result.errors.append(f"验证异常: {str(e)}")
            return result
    
    def _extract_match_info(self, option: Dict, platform_name: str) -> Dict:
        """提取比赛信息"""
        try:
            # 解析比赛名称
            match_name = option.get("match", "")
            teams = match_name.split(" - ") if " - " in match_name else match_name.split(" vs ")
            
            home_team = teams[0].strip() if len(teams) > 0 else ""
            away_team = teams[1].strip() if len(teams) > 1 else ""
            
            # 提取其他信息
            league = option.get("league", "")
            description = option.get("description", "")
            started_at = option.get("started_at", 0)
            
            # 解析盘口信息
            market_info = self._parse_market_info(description)
            
            # 转换时间戳
            match_time = None
            if started_at:
                try:
                    if isinstance(started_at, (int, float)):
                        match_time = datetime.fromtimestamp(started_at)
                    else:
                        # 尝试解析字符串时间
                        match_time = datetime.fromisoformat(str(started_at))
                except:
                    logger.warning(f"无法解析时间: {started_at}")
            
            return {
                "platform": platform_name,
                "home_team": home_team,
                "away_team": away_team,
                "league": league,
                "match_time": match_time,
                "market_type": market_info.get("type", ""),
                "market_param": market_info.get("param", ""),
                "description": description,
                "original_data": option
            }

        except Exception as e:
            logger.error(f"提取比赛信息失败: {e}")
            return {
                "platform": platform_name,
                "home_team": "",
                "away_team": "",
                "league": "",
                "match_time": None,
                "market_type": "",
                "market_param": "",
                "description": "",
                "original_data": option
            }

    def _parse_market_info(self, description: str) -> Dict:
        """解析盘口信息"""
        try:
            market_info = {"type": "", "param": ""}

            if "让球" in description or "让分" in description:
                market_info["type"] = "让球"
                # 提取让球数值
                match = re.search(r'[+-]?\d+\.?\d*', description)
                if match:
                    market_info["param"] = match.group()
            elif "大小球" in description or "总分" in description:
                market_info["type"] = "大小球"
                # 提取大小球数值
                match = re.search(r'\d+\.?\d*', description)
                if match:
                    market_info["param"] = match.group()
            elif "独赢" in description or "胜负" in description:
                market_info["type"] = "独赢"

            return market_info

        except Exception as e:
            logger.error(f"解析盘口信息失败: {e}")
            return {"type": "", "param": ""}

    def _calculate_team_match_score(self, match1: Dict, match2: Dict, result: MatchResult) -> float:
        """计算队伍名称匹配分数 - 使用高级团队相似度算法"""
        try:
            home1, away1 = match1["home_team"], match1["away_team"]
            home2, away2 = match2["home_team"], match2["away_team"]

            if not all([home1, away1, home2, away2]):
                result.warnings.append("队伍名称信息不完整")
                return 0.0

            # 加载队伍别名配置
            team_aliases = self._load_team_aliases()

            # 使用高级团队相似度计算
            final_score, details = calculate_team_similarity(
                home1, away1, home2, away2, team_aliases
            )

            # 记录详细匹配信息
            match_type = details.get("match_type", "unknown")
            home_match = details.get("home_match")
            away_match = details.get("away_match")

            if home_match and away_match:
                logger.debug(f"队伍匹配详情 ({match_type}): "
                           f"主队 {home_match.score:.3f} ({home_match.method}), "
                           f"客队 {away_match.score:.3f} ({away_match.method})")

            # 记录结果信息
            if final_score >= 0.8:
                logger.info(f"队伍匹配良好: {home1} vs {away1} <-> {home2} vs {away2}, "
                          f"分数: {final_score:.3f} (匹配类型: {match_type})")
            elif final_score >= 0.5:
                result.warnings.append(f"队伍匹配一般: 分数 {final_score:.3f} (匹配类型: {match_type})")
            else:
                result.warnings.append(f"队伍匹配较差: 分数 {final_score:.3f} (匹配类型: {match_type})")

            return final_score

        except Exception as e:
            logger.error(f"计算队伍匹配分数失败: {e}")
            result.errors.append(f"队伍匹配计算异常: {str(e)}")
            return 0.0

    def _calculate_time_match_score(self, match1: Dict, match2: Dict, result: MatchResult) -> float:
        """计算时间匹配分数"""
        try:
            time1 = match1["match_time"]
            time2 = match2["match_time"]

            # 如果任一时间为空，给予中等分数（避免因时间信息缺失而完全拒绝）
            if not time1 or not time2:
                result.warnings.append("比赛时间信息缺失")
                return 0.6  # 给予中等分数

            # 计算时间差
            time_diff = abs((time1 - time2).total_seconds()) / 60  # 转换为分钟

            # 根据时间差计算分数
            if time_diff <= 5:
                score = 1.0  # 5分钟内，完全匹配
            elif time_diff <= 30:
                score = 0.8  # 30分钟内，高度匹配
            elif time_diff <= 120:
                score = 0.3  # 2小时内，可疑匹配
                result.warnings.append(f"比赛时间差异较大: {time_diff:.1f}分钟")
            else:
                score = 0.0  # 超过2小时，拒绝匹配
                result.errors.append(f"比赛时间差异过大: {time_diff:.1f}分钟")

            logger.debug(f"时间匹配分数: {score:.3f}, 时间差: {time_diff:.1f}分钟")
            return score

        except Exception as e:
            logger.error(f"计算时间匹配分数失败: {e}")
            result.errors.append(f"时间匹配计算异常: {str(e)}")
            return 0.0

    def _calculate_league_match_score(self, match1: Dict, match2: Dict, result: MatchResult) -> float:
        """计算联赛匹配分数"""
        try:
            league1 = match1["league"]
            league2 = match2["league"]

            if not league1 or not league2:
                result.warnings.append("联赛信息缺失")
                return 0.5  # 给予中等分数

            # 标准化联赛名称
            league1_norm = self._normalize_league_name(league1)
            league2_norm = self._normalize_league_name(league2)

            # 计算相似度
            similarity = self._calculate_string_similarity(league1_norm, league2_norm)

            if similarity >= 0.8:
                logger.debug(f"联赛匹配良好: {league1} <-> {league2}")
            elif similarity >= 0.5:
                result.warnings.append(f"联赛匹配一般: {league1} <-> {league2}")
            else:
                result.warnings.append(f"联赛不匹配: {league1} <-> {league2}")

            return similarity

        except Exception as e:
            logger.error(f"计算联赛匹配分数失败: {e}")
            result.errors.append(f"联赛匹配计算异常: {str(e)}")
            return 0.0

    def _calculate_market_match_score(self, match1: Dict, match2: Dict, result: MatchResult) -> float:
        """计算盘口匹配分数"""
        try:
            type1 = match1["market_type"]
            type2 = match2["market_type"]
            param1 = match1["market_param"]
            param2 = match2["market_param"]

            if not type1 or not type2:
                result.warnings.append("盘口类型信息缺失")
                return 0.5

            # 标准化盘口类型
            type1_norm = self._normalize_market_type(type1)
            type2_norm = self._normalize_market_type(type2)

            # 检查盘口类型是否匹配
            if type1_norm != type2_norm:
                result.warnings.append(f"盘口类型不匹配: {type1} <-> {type2}")
                return 0.0

            # 如果是让球或大小球，检查参数是否匹配
            if type1_norm in ["让球", "大小球"] and param1 and param2:
                try:
                    param1_val = float(param1)
                    param2_val = float(param2)
                    param_diff = abs(param1_val - param2_val)

                    if param_diff <= 0.1:
                        return 1.0  # 参数完全匹配
                    elif param_diff <= 0.5:
                        return 0.8  # 参数接近匹配
                    else:
                        result.warnings.append(f"盘口参数差异较大: {param1} <-> {param2}")
                        return 0.3
                except:
                    result.warnings.append("盘口参数格式异常")
                    return 0.5

            # 独赢盘口或无参数的情况
            return 1.0

        except Exception as e:
            logger.error(f"计算盘口匹配分数失败: {e}")
            result.errors.append(f"盘口匹配计算异常: {str(e)}")
            return 0.0

    def _normalize_team_name(self, team_name: str) -> str:
        """标准化队伍名称"""
        if not team_name:
            return ""

        # 转换为小写并去除空格
        normalized = team_name.lower().strip()

        # 应用替换规则
        for standard_name, variants in self.team_name_replacements.items():
            if normalized in [v.lower() for v in variants]:
                return standard_name.lower()

        return normalized

    def _normalize_league_name(self, league_name: str) -> str:
        """标准化联赛名称"""
        if not league_name:
            return ""

        # 转换为小写并去除空格
        normalized = league_name.lower().strip()

        # 应用替换规则
        for standard_name, variants in self.league_name_replacements.items():
            if normalized in [v.lower() for v in variants]:
                return standard_name.lower()

        return normalized

    def _normalize_market_type(self, market_type: str) -> str:
        """标准化盘口类型"""
        if not market_type:
            return ""

        market_type_lower = market_type.lower()

        # 查找匹配的标准类型
        for standard_type, variants in self.market_type_mapping.items():
            if market_type_lower in [v.lower() for v in variants]:
                return standard_type

        return market_type

    def _calculate_string_similarity(self, str1: str, str2: str) -> float:
        """计算字符串相似度 - 使用高级相似度算法"""
        if not str1 or not str2:
            return 0.0

        # 使用高级相似度计算器
        result = similarity_calculator.calculate_similarity(str1, str2)

        # 记录详细信息用于调试
        logger.debug(f"字符串相似度计算: '{str1}' vs '{str2}' = {result.score:.3f} "
                    f"(方法: {result.method}, 置信度: {result.confidence:.3f})")

        return result.score

    def get_match_summary(self, result: MatchResult) -> str:
        """获取匹配结果摘要"""
        if not result.score:
            return "匹配验证失败"

        score = result.score
        platform1 = result.platform1_info
        platform2 = result.platform2_info

        summary = f"""
=== 比赛匹配验证结果 ===
总分: {score.total_score:.3f} | 置信度: {score.confidence.value.upper()}

比赛信息:
  平台1: {platform1.get('home_team', '')} vs {platform1.get('away_team', '')}
  平台2: {platform2.get('home_team', '')} vs {platform2.get('away_team', '')}

详细分数:
  队伍匹配: {score.team_score:.3f} (权重40%)
  时间匹配: {score.time_score:.3f} (权重30%)
  联赛匹配: {score.league_score:.3f} (权重20%)
  盘口匹配: {score.market_score:.3f} (权重10%)

匹配状态: {'✓ 通过' if result.is_valid else '✗ 拒绝'}
"""

        if result.warnings:
            summary += f"\n警告信息:\n"
            for warning in result.warnings:
                summary += f"  ⚠ {warning}\n"

        if result.errors:
            summary += f"\n错误信息:\n"
            for error in result.errors:
                summary += f"  ✗ {error}\n"

        return summary

    def is_safe_to_hedge(self, result: MatchResult) -> Tuple[bool, str]:
        """
        判断是否安全进行对冲投注

        返回:
            (是否安全, 原因说明)
        """
        if not result.is_valid:
            return False, "比赛匹配验证失败"

        if result.score.confidence == MatchConfidence.HIGH:
            return True, "高置信度匹配，安全对冲"
        elif result.score.confidence == MatchConfidence.MEDIUM:
            # 中置信度需要额外检查
            if result.errors:
                return False, f"存在错误: {'; '.join(result.errors)}"

            # 检查关键维度
            if result.score.team_score < 0.6:
                return False, "队伍匹配分数过低，存在风险"

            if result.score.time_score < 0.3:
                return False, "时间匹配分数过低，可能是不同比赛"

            return True, "中置信度匹配，建议谨慎对冲"
        else:
            return False, "匹配置信度过低，不建议对冲"

    def _load_team_aliases(self) -> Dict:
        """加载队伍别名配置"""
        try:
            import json
            import os
            config_path = "config/team_aliases.json"
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get("team_aliases", {})
            else:
                logger.warning(f"队伍别名配置文件不存在: {config_path}")
                return {}
        except Exception as e:
            logger.error(f"加载队伍别名配置失败: {e}")
            return {}
