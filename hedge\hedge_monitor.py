#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class BetStatus(Enum):
    """投注状态枚举"""
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"

@dataclass
class BetRecord:
    """投注记录数据类"""
    bet_id: str
    platform: str
    amount: float
    odds: float
    status: BetStatus
    timestamp: datetime
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3

class BetMonitor:
    """投注状态监控器"""
    
    def __init__(self):
        """初始化监控器"""
        self.active_bets = {}  # 活跃投注记录
        self.completed_bets = []  # 已完成投注记录
        self.monitoring = False
        self.monitor_thread = None
        self.callbacks = {
            'on_bet_success': [],
            'on_bet_failed': [],
            'on_bet_timeout': [],
            'on_hedge_complete': [],
            'on_hedge_partial': []
        }
        
        logger.info("投注监控器初始化完成")
    
    def add_bet_record(self, bet_id: str, platform: str, amount: float, 
                      odds: float, max_retries: int = 3) -> BetRecord:
        """
        添加投注记录
        
        参数:
            bet_id: 投注ID
            platform: 平台名称
            amount: 投注金额
            odds: 赔率
            max_retries: 最大重试次数
        
        返回:
            投注记录对象
        """
        record = BetRecord(
            bet_id=bet_id,
            platform=platform,
            amount=amount,
            odds=odds,
            status=BetStatus.PENDING,
            timestamp=datetime.now(),
            max_retries=max_retries
        )
        
        self.active_bets[bet_id] = record
        logger.info(f"添加投注记录: {bet_id} - {platform} - {amount}")
        
        return record
    
    def update_bet_status(self, bet_id: str, status: BetStatus, 
                         error_message: Optional[str] = None):
        """
        更新投注状态
        
        参数:
            bet_id: 投注ID
            status: 新状态
            error_message: 错误信息（如果有）
        """
        if bet_id not in self.active_bets:
            logger.warning(f"投注记录不存在: {bet_id}")
            return
        
        record = self.active_bets[bet_id]
        old_status = record.status
        record.status = status
        
        if error_message:
            record.error_message = error_message
        
        logger.info(f"投注状态更新: {bet_id} {old_status.value} -> {status.value}")
        
        # 触发回调
        self._trigger_callbacks(record, status)
        
        # 如果投注完成，移动到已完成列表
        if status in [BetStatus.SUCCESS, BetStatus.FAILED, BetStatus.CANCELLED]:
            self.completed_bets.append(record)
            del self.active_bets[bet_id]
    
    def increment_retry_count(self, bet_id: str) -> bool:
        """
        增加重试计数
        
        参数:
            bet_id: 投注ID
        
        返回:
            是否还可以重试
        """
        if bet_id not in self.active_bets:
            return False
        
        record = self.active_bets[bet_id]
        record.retry_count += 1
        
        logger.info(f"投注重试计数: {bet_id} - {record.retry_count}/{record.max_retries}")
        
        return record.retry_count < record.max_retries
    
    def get_bet_record(self, bet_id: str) -> Optional[BetRecord]:
        """获取投注记录"""
        return self.active_bets.get(bet_id)
    
    def get_active_bets(self) -> List[BetRecord]:
        """获取所有活跃投注"""
        return list(self.active_bets.values())
    
    def get_completed_bets(self, hours: int = 24) -> List[BetRecord]:
        """
        获取指定时间内的已完成投注
        
        参数:
            hours: 时间范围（小时）
        
        返回:
            已完成投注列表
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [bet for bet in self.completed_bets if bet.timestamp >= cutoff_time]
    
    def register_callback(self, event: str, callback: Callable):
        """
        注册事件回调
        
        参数:
            event: 事件名称
            callback: 回调函数
        """
        if event in self.callbacks:
            self.callbacks[event].append(callback)
            logger.info(f"注册回调: {event}")
        else:
            logger.warning(f"未知事件类型: {event}")
    
    def _trigger_callbacks(self, record: BetRecord, status: BetStatus):
        """触发相应的回调函数"""
        try:
            if status == BetStatus.SUCCESS:
                for callback in self.callbacks['on_bet_success']:
                    callback(record)
            elif status == BetStatus.FAILED:
                for callback in self.callbacks['on_bet_failed']:
                    callback(record)
            elif status == BetStatus.TIMEOUT:
                for callback in self.callbacks['on_bet_timeout']:
                    callback(record)
        except Exception as e:
            logger.error(f"回调函数执行失败: {e}")
    
    def start_monitoring(self, check_interval: int = 5):
        """
        开始监控投注状态
        
        参数:
            check_interval: 检查间隔（秒）
        """
        if self.monitoring:
            logger.warning("监控已在运行")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(check_interval,),
            daemon=True
        )
        self.monitor_thread.start()
        
        logger.info(f"开始投注监控，检查间隔: {check_interval}秒")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=10)
        
        logger.info("投注监控已停止")
    
    def _monitor_loop(self, check_interval: int):
        """监控循环"""
        while self.monitoring:
            try:
                self._check_timeouts()
                time.sleep(check_interval)
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                time.sleep(check_interval)
    
    def _check_timeouts(self):
        """检查超时的投注"""
        current_time = datetime.now()
        timeout_threshold = timedelta(minutes=5)  # 5分钟超时
        
        for bet_id, record in list(self.active_bets.items()):
            if record.status == BetStatus.PENDING:
                if current_time - record.timestamp > timeout_threshold:
                    logger.warning(f"投注超时: {bet_id}")
                    self.update_bet_status(bet_id, BetStatus.TIMEOUT, "投注超时")
    
    def check_hedge_completion(self, hedge_id: str, bet_ids: List[str]) -> Dict[str, str]:
        """
        检查对冲投注完成状态
        
        参数:
            hedge_id: 对冲ID
            bet_ids: 相关投注ID列表
        
        返回:
            对冲状态信息
        """
        try:
            bet_statuses = {}
            
            for bet_id in bet_ids:
                if bet_id in self.active_bets:
                    bet_statuses[bet_id] = self.active_bets[bet_id].status.value
                else:
                    # 在已完成投注中查找
                    for completed_bet in self.completed_bets:
                        if completed_bet.bet_id == bet_id:
                            bet_statuses[bet_id] = completed_bet.status.value
                            break
                    else:
                        bet_statuses[bet_id] = "unknown"
            
            # 分析对冲状态
            success_count = sum(1 for status in bet_statuses.values() if status == "success")
            failed_count = sum(1 for status in bet_statuses.values() if status == "failed")
            pending_count = sum(1 for status in bet_statuses.values() if status == "pending")
            
            if success_count == len(bet_ids):
                hedge_status = "complete"
                self._trigger_hedge_callbacks('on_hedge_complete', hedge_id, bet_statuses)
            elif success_count > 0 and failed_count > 0:
                hedge_status = "partial"
                self._trigger_hedge_callbacks('on_hedge_partial', hedge_id, bet_statuses)
            elif failed_count == len(bet_ids):
                hedge_status = "failed"
            elif pending_count > 0:
                hedge_status = "pending"
            else:
                hedge_status = "unknown"
            
            return {
                "hedge_id": hedge_id,
                "hedge_status": hedge_status,
                "bet_statuses": bet_statuses,
                "success_count": success_count,
                "failed_count": failed_count,
                "pending_count": pending_count
            }
            
        except Exception as e:
            logger.error(f"检查对冲完成状态时出错: {e}")
            return {
                "hedge_id": hedge_id,
                "hedge_status": "error",
                "error": str(e)
            }
    
    def _trigger_hedge_callbacks(self, event: str, hedge_id: str, bet_statuses: Dict[str, str]):
        """触发对冲相关回调"""
        try:
            for callback in self.callbacks.get(event, []):
                callback(hedge_id, bet_statuses)
        except Exception as e:
            logger.error(f"对冲回调函数执行失败: {e}")
    
    def get_statistics(self) -> Dict[str, int]:
        """获取监控统计信息"""
        active_count = len(self.active_bets)
        completed_count = len(self.completed_bets)
        
        # 统计已完成投注的状态
        success_count = sum(1 for bet in self.completed_bets if bet.status == BetStatus.SUCCESS)
        failed_count = sum(1 for bet in self.completed_bets if bet.status == BetStatus.FAILED)
        timeout_count = sum(1 for bet in self.completed_bets if bet.status == BetStatus.TIMEOUT)
        cancelled_count = sum(1 for bet in self.completed_bets if bet.status == BetStatus.CANCELLED)
        
        return {
            "active_bets": active_count,
            "completed_bets": completed_count,
            "success_bets": success_count,
            "failed_bets": failed_count,
            "timeout_bets": timeout_count,
            "cancelled_bets": cancelled_count,
            "success_rate": round((success_count / completed_count * 100) if completed_count > 0 else 0, 2)
        }
    
    def clear_old_records(self, hours: int = 72):
        """
        清理旧的投注记录
        
        参数:
            hours: 保留时间（小时）
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        before_count = len(self.completed_bets)
        self.completed_bets = [bet for bet in self.completed_bets if bet.timestamp >= cutoff_time]
        after_count = len(self.completed_bets)
        
        cleared_count = before_count - after_count
        if cleared_count > 0:
            logger.info(f"清理了 {cleared_count} 条旧投注记录")
    
    def export_records(self, hours: int = 24) -> List[Dict]:
        """
        导出投注记录
        
        参数:
            hours: 导出时间范围（小时）
        
        返回:
            投注记录列表
        """
        records = self.get_completed_bets(hours)
        
        return [
            {
                "bet_id": record.bet_id,
                "platform": record.platform,
                "amount": record.amount,
                "odds": record.odds,
                "status": record.status.value,
                "timestamp": record.timestamp.isoformat(),
                "error_message": record.error_message,
                "retry_count": record.retry_count
            }
            for record in records
        ]
