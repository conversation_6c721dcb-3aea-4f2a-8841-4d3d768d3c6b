{
        "_priority": "High",
        "_resourceType": "fetch",
        "cache": {},
        "connection": "1461181",
        "request": {
          "method": "GET",
          "url": "https://sports-api.sbk-188-sports.com/api/v1/zh-cn/getmybet",
          "httpVersion": "HTTP/1.1",
          "headers": [
            {
              "name": "Accept",
              "value": "*/*"
            },
            {
              "name": "Accept-Encoding",
              "value": "gzip, deflate, br"
            },
            {
              "name": "Accept-Language",
              "value": "zh-CN,zh;q=0.9"
            },
            {
              "name": "Authorization",
              "value": "Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.d3y7wMXLDHm36iWLdIUlyeeCA6T1gffCHi0PcZJfB-w"
            },
            {
              "name": "Cache-Control",
              "value": "max-age=0"
            },
            {
              "name": "Client",
              "value": "ba85d040-0e1e-4efa-ac78-77aed1214432"
            },
            {
              "name": "Connection",
              "value": "keep-alive"
            },
            {
              "name": "<PERSON><PERSON>",
              "value": "sb-188cshapi=875302666.20480.0000"
            },
            {
              "name": "DeviceType",
              "value": "Web"
            },
            {
              "name": "Host",
              "value": "sports-api.sbk-188-sports.com"
            },
            {
              "name": "Origin",
              "value": "https://sports.sbk-188-sports.com"
            },
            {
              "name": "Referer",
              "value": "https://sports.sbk-188-sports.com/"
            },
            {
              "name": "Sec-Fetch-Dest",
              "value": "empty"
            },
            {
              "name": "Sec-Fetch-Mode",
              "value": "cors"
            },
            {
              "name": "Sec-Fetch-Site",
              "value": "same-site"
            },
            {
              "name": "SessionID",
              "value": "2239481a-9a9e-476b-88d0-a8594a4398e0"
            },
            {
              "name": "TabId",
              "value": "871714"
            },
            {
              "name": "User-Agent",
              "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36"
            },
            {
              "name": "sec-ch-ua",
              "value": "\"Not=A?Brand\";v=\"99\", \"Chromium\";v=\"118\""
            },
            {
              "name": "sec-ch-ua-mobile",
              "value": "?0"
            },
            {
              "name": "sec-ch-ua-platform",
              "value": "\"Windows\""
            }
          ],
          "queryString": [],
          "cookies": [
            {
              "name": "sb-188cshapi",
              "value": "875302666.20480.0000",
              "path": "/",
              "domain": "sports-api.sbk-188-sports.com",
              "expires": "1969-12-31T23:59:59.000Z",
              "httpOnly": true,
              "secure": true,
              "sameSite": "None"
            }
          ],
          "headersSize": 1574,
          "bodySize": 0
        },
        "response": {
          "status": 200,
          "statusText": "OK",
          "httpVersion": "HTTP/1.1",
          "headers": [
            {
              "name": "Access-Control-Allow-Credentials",
              "value": "true"
            },
            {
              "name": "Access-Control-Allow-Methods",
              "value": "*"
            },
            {
              "name": "Access-Control-Allow-Origin",
              "value": "https://sports.sbk-188-sports.com"
            },
            {
              "name": "Access-Control-Expose-Headers",
              "value": "Authorization,Server,Client,Set-Cookie,SessionID,_ga"
            },
            {
              "name": "Authorization",
              "value": "Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.d3y7wMXLDHm36iWLdIUlyeeCA6T1gffCHi0PcZJfB-w"
            },
            {
              "name": "Cache-Control",
              "value": "no-cache"
            },
            {
              "name": "Content-Encoding",
              "value": "gzip"
            },
            {
              "name": "Content-Type",
              "value": "application/json; charset=utf-8"
            },
            {
              "name": "Date",
              "value": "Tue, 01 Jul 2025 02:21:17 GMT"
            },
            {
              "name": "Expires",
              "value": "-1"
            },
            {
              "name": "Pragma",
              "value": "no-cache"
            },
            {
              "name": "Server",
              "value": "AEEAFC"
            },
            {
              "name": "Transfer-Encoding",
              "value": "chunked"
            },
            {
              "name": "Vary",
              "value": "Accept-Encoding"
            }
          ],
          "cookies": [],
          "content": {
            "size": 2357,
            "mimeType": "application/json",
            "compression": 1170,
            "text": "{\"r\":0,\"d\":{\"data\":[{\"id\":\"72105113250298\",\"l\":[{\"h\":\"曼城\",\"a\":\"希拉尔\",\"sn\":\"小\",\"od\":\"1.83\",\"hd\":\"10.5\",\"en\":\"Manchester City vs Al Hilal Riyadh\",\"m\":null,\"e\":false,\"s\":null,\"t\":null,\"nm\":false,\"nu\":false,\"ip\":true,\"ors\":null,\"orss\":null,\"bi\":null,\"edt\":\"2025-07-01T09:00:00\",\"eid\":9643481,\"peid\":9643475,\"mid\":175143904,\"mty\":16,\"cid\":31554,\"ot\":1,\"ood\":1.83,\"soc\":null,\"avb\":false,\"sid\":9831036053,\"sch\":null,\"sca\":null,\"vt\":2,\"isi\":{\"eid\":9643481,\"ps\":[\"FT\"]},\"fsi\":null,\"spid\":1,\"wcr\":null,\"wsid\":9042060010}],\"t\":\"角球:滚球 大 / 小\",\"cn\":\"世界球会冠军杯2025(在美国)\",\"nr\":false,\"nc\":false,\"st\":null,\"s\":\"66.00\",\"ep\":\"54.78\",\"rtep\":\"120.78\",\"c\":false,\"pdt\":\"2025-07-01T09:49:37.223151\",\"ldt\":\"2025-07-01T09:49:49.423386\",\"wr\":\"确认\",\"tf\":null,\"tra\":null,\"d\":true,\"wt\":null,\"wty\":1,\"isEpm\":false,\"isEps\":false,\"ib\":false,\"dt\":0,\"iscc\":false,\"ril\":[],\"bbril\":null,\"dil\":[],\"ip\":true,\"idr\":false,\"col\":[],\"dcoubs\":0,\"stkre\":66.0,\"stk\":66.0,\"cost\":0.0,\"dst\":66.0,\"wid\":7178119016,\"sd\":null,\"isfb\":false,\"shl\":null,\"dsr\":\"\",\"cut\":\"0001-01-01T00:00:00\",\"ucut\":\"0001-01-01T00:00:00\",\"aco\":null,\"isa\":false},{\"id\":\"81259223539919\",\"l\":[{\"h\":\"金州女武神\",\"a\":\"西雅图暴风\",\"sn\":\"金州女武神\",\"od\":\"1.98\",\"hd\":\"-27.5\",\"en\":\"Golden State Valkyries vs Seattle Storm\",\"m\":null,\"e\":false,\"s\":null,\"t\":null,\"nm\":false,\"nu\":false,\"ip\":true,\"ors\":null,\"orss\":null,\"bi\":null,\"edt\":\"2025-06-30T08:30:00\",\"eid\":9642745,\"peid\":9642745,\"mid\":175119128,\"mty\":337,\"cid\":26087,\"ot\":1,\"ood\":1.98,\"soc\":5,\"avb\":false,\"sid\":9830934994,\"sch\":null,\"sca\":null,\"vt\":2,\"isi\":null,\"fsi\":[{\"sc\":\"84 - 57\"}],\"spid\":2,\"wcr\":null,\"wsid\":9041301573}],\"t\":\"滚球 让球\",\"cn\":\"WNBA美国女子职业篮球联赛\",\"nr\":true,\"nc\":false,\"st\":\"Wager_Lose\",\"s\":\"300.00\",\"ep\":\"294.00\",\"rtep\":\"594.00\",\"c\":false,\"pdt\":\"2025-06-30T10:18:57.009461\",\"ldt\":\"2025-06-30T10:29:33\",\"wr\":null,\"tf\":\"-300.00\",\"tra\":\"0.00\",\"d\":false,\"wt\":null,\"wty\":1,\"isEpm\":false,\"isEps\":false,\"ib\":false,\"dt\":0,\"iscc\":false,\"ril\":[],\"bbril\":null,\"dil\":[],\"ip\":true,\"idr\":false,\"col\":[],\"dcoubs\":0,\"stkre\":300.0,\"stk\":300.0,\"cost\":0.0,\"dst\":300.0,\"wid\":7177774069,\"sd\":\"2025-06-30T10:29:33\",\"isfb\":false,\"shl\":null,\"dsr\":\"\",\"cut\":\"0001-01-01T00:00:00\",\"ucut\":\"0001-01-01T00:00:00\",\"aco\":null,\"isa\":false}],\"v\":1751336402380.731,\"type\":true,\"flag\":true,\"acoflag\":true,\"ubc\":1,\"cbc\":0}}"
          }
}