#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = "low"           # 低风险
    MEDIUM = "medium"     # 中风险
    HIGH = "high"         # 高风险
    CRITICAL = "critical" # 严重风险

@dataclass
class SafetyRule:
    """安全规则定义"""
    name: str
    description: str
    risk_level: RiskLevel
    enabled: bool = True

@dataclass
class SafetyCheck:
    """安全检查结果"""
    rule_name: str
    passed: bool
    risk_level: RiskLevel
    message: str
    details: Dict = None

class MatchSafety:
    """比赛匹配安全检查器"""
    
    def __init__(self):
        """初始化安全检查器"""
        self.blacklist_file = "data/match_blacklist.json"
        self.safety_log_file = "data/safety_checks.log"

        # 确保数据目录存在
        os.makedirs("data", exist_ok=True)

        # 加载黑名单
        self.blacklist = self._load_blacklist()

        # 定义安全规则
        self.safety_rules = self._initialize_safety_rules()

        logger.info("比赛匹配安全检查器初始化完成")
    
    def _initialize_safety_rules(self) -> List[SafetyRule]:
        """初始化安全规则"""
        return [
            SafetyRule(
                name="team_name_similarity",
                description="队伍名称相似度检查",
                risk_level=RiskLevel.CRITICAL
            ),
            SafetyRule(
                name="time_difference",
                description="比赛时间差异检查",
                risk_level=RiskLevel.HIGH
            ),
            SafetyRule(
                name="league_consistency",
                description="联赛一致性检查",
                risk_level=RiskLevel.MEDIUM
            ),
            SafetyRule(
                name="market_type_match",
                description="盘口类型匹配检查",
                risk_level=RiskLevel.HIGH
            ),
            SafetyRule(
                name="blacklist_check",
                description="黑名单检查",
                risk_level=RiskLevel.CRITICAL
            ),
            SafetyRule(
                name="odds_reasonableness",
                description="赔率合理性检查",
                risk_level=RiskLevel.MEDIUM
            ),
            SafetyRule(
                name="duplicate_bet_check",
                description="重复投注检查",
                risk_level=RiskLevel.HIGH
            )
        ]
    
    def perform_safety_checks(self, option1: Dict, option2: Dict) -> List[SafetyCheck]:
        """
        执行安全检查
        
        参数:
            option1: 平台1投注选项
            option2: 平台2投注选项
        
        返回:
            安全检查结果列表
        """
        checks = []
        
        try:
            # 执行各项安全检查
            for rule in self.safety_rules:
                if not rule.enabled:
                    continue
                
                check_result = self._execute_safety_rule(rule, option1, option2)
                checks.append(check_result)
            
            # 记录检查结果
            self._log_safety_checks(option1, option2, checks)
            
            return checks
            
        except Exception as e:
            logger.error(f"安全检查执行失败: {e}")
            error_check = SafetyCheck(
                rule_name="system_error",
                passed=False,
                risk_level=RiskLevel.CRITICAL,
                message=f"安全检查系统异常: {str(e)}"
            )
            return [error_check]
    
    def _execute_safety_rule(self, rule: SafetyRule, option1: Dict, option2: Dict) -> SafetyCheck:
        """执行单个安全规则"""
        try:
            if rule.name == "team_name_similarity":
                return self._check_team_name_similarity(option1, option2)
            elif rule.name == "time_difference":
                return self._check_time_difference(option1, option2)
            elif rule.name == "league_consistency":
                return self._check_league_consistency(option1, option2)
            elif rule.name == "market_type_match":
                return self._check_market_type_match(option1, option2)
            elif rule.name == "blacklist_check":
                return self._check_blacklist(option1, option2)
            elif rule.name == "odds_reasonableness":
                return self._check_odds_reasonableness(option1, option2)
            elif rule.name == "duplicate_bet_check":
                return self._check_duplicate_bet(option1, option2)
            else:
                return SafetyCheck(
                    rule_name=rule.name,
                    passed=False,
                    risk_level=rule.risk_level,
                    message=f"未知的安全规则: {rule.name}"
                )
                
        except Exception as e:
            logger.error(f"执行安全规则 {rule.name} 失败: {e}")
            return SafetyCheck(
                rule_name=rule.name,
                passed=False,
                risk_level=rule.risk_level,
                message=f"规则执行异常: {str(e)}"
            )
    
    def _check_team_name_similarity(self, option1: Dict, option2: Dict) -> SafetyCheck:
        """检查队伍名称相似度"""
        try:
            match1 = option1.get("match", "")
            match2 = option2.get("match", "")
            
            if not match1 or not match2:
                return SafetyCheck(
                    rule_name="team_name_similarity",
                    passed=False,
                    risk_level=RiskLevel.CRITICAL,
                    message="比赛名称信息缺失"
                )
            
            # 解析队伍名称
            teams1 = match1.split(" - ") if " - " in match1 else match1.split(" vs ")
            teams2 = match2.split(" - ") if " - " in match2 else match2.split(" vs ")
            
            if len(teams1) != 2 or len(teams2) != 2:
                return SafetyCheck(
                    rule_name="team_name_similarity",
                    passed=False,
                    risk_level=RiskLevel.CRITICAL,
                    message="无法解析队伍名称"
                )
            
            # 计算相似度
            similarity = self._calculate_team_similarity(teams1, teams2)

            if similarity >= 0.8:
                return SafetyCheck(
                    rule_name="team_name_similarity",
                    passed=True,
                    risk_level=RiskLevel.LOW,
                    message=f"队伍名称匹配良好 (相似度: {similarity:.3f})",
                    details={"similarity": similarity}
                )
            elif similarity >= 0.6:
                return SafetyCheck(
                    rule_name="team_name_similarity",
                    passed=True,
                    risk_level=RiskLevel.MEDIUM,
                    message=f"队伍名称匹配一般 (相似度: {similarity:.3f})",
                    details={"similarity": similarity}
                )
            elif similarity >= 0.4:  # 降低阈值，适应连续字符匹配
                return SafetyCheck(
                    rule_name="team_name_similarity",
                    passed=True,
                    risk_level=RiskLevel.HIGH,
                    message=f"队伍名称匹配较低但可接受 (相似度: {similarity:.3f})",
                    details={"similarity": similarity}
                )
            else:
                return SafetyCheck(
                    rule_name="team_name_similarity",
                    passed=False,
                    risk_level=RiskLevel.CRITICAL,
                    message=f"队伍名称匹配度过低 (相似度: {similarity:.3f})",
                    details={"similarity": similarity}
                )
                
        except Exception as e:
            return SafetyCheck(
                rule_name="team_name_similarity",
                passed=False,
                risk_level=RiskLevel.CRITICAL,
                message=f"队伍名称检查异常: {str(e)}"
            )
    
    def _check_time_difference(self, option1: Dict, option2: Dict) -> SafetyCheck:
        """检查比赛时间差异"""
        try:
            time1 = option1.get("started_at")
            time2 = option2.get("started_at")
            
            if not time1 or not time2:
                return SafetyCheck(
                    rule_name="time_difference",
                    passed=True,
                    risk_level=RiskLevel.MEDIUM,
                    message="比赛时间信息缺失，跳过检查"
                )
            
            # 转换时间戳
            if isinstance(time1, (int, float)):
                dt1 = datetime.fromtimestamp(time1)
            else:
                dt1 = datetime.fromisoformat(str(time1))
            
            if isinstance(time2, (int, float)):
                dt2 = datetime.fromtimestamp(time2)
            else:
                dt2 = datetime.fromisoformat(str(time2))
            
            # 计算时间差（分钟）
            time_diff = abs((dt1 - dt2).total_seconds()) / 60
            
            if time_diff <= 5:
                return SafetyCheck(
                    rule_name="time_difference",
                    passed=True,
                    risk_level=RiskLevel.LOW,
                    message=f"比赛时间匹配良好 (差异: {time_diff:.1f}分钟)",
                    details={"time_diff_minutes": time_diff}
                )
            elif time_diff <= 30:
                return SafetyCheck(
                    rule_name="time_difference",
                    passed=True,
                    risk_level=RiskLevel.MEDIUM,
                    message=f"比赛时间差异可接受 (差异: {time_diff:.1f}分钟)",
                    details={"time_diff_minutes": time_diff}
                )
            elif time_diff <= 120:
                return SafetyCheck(
                    rule_name="time_difference",
                    passed=False,
                    risk_level=RiskLevel.HIGH,
                    message=f"比赛时间差异较大 (差异: {time_diff:.1f}分钟)",
                    details={"time_diff_minutes": time_diff}
                )
            else:
                return SafetyCheck(
                    rule_name="time_difference",
                    passed=False,
                    risk_level=RiskLevel.CRITICAL,
                    message=f"比赛时间差异过大 (差异: {time_diff:.1f}分钟)",
                    details={"time_diff_minutes": time_diff}
                )
                
        except Exception as e:
            return SafetyCheck(
                rule_name="time_difference",
                passed=False,
                risk_level=RiskLevel.HIGH,
                message=f"时间差异检查异常: {str(e)}"
            )
    
    def _check_league_consistency(self, option1: Dict, option2: Dict) -> SafetyCheck:
        """检查联赛一致性"""
        try:
            league1 = option1.get("league", "")
            league2 = option2.get("league", "")
            
            if not league1 or not league2:
                return SafetyCheck(
                    rule_name="league_consistency",
                    passed=True,
                    risk_level=RiskLevel.MEDIUM,
                    message="联赛信息缺失，跳过检查"
                )
            
            # 标准化联赛名称
            league1_norm = league1.lower().strip()
            league2_norm = league2.lower().strip()
            
            # 检查是否匹配
            if league1_norm == league2_norm:
                return SafetyCheck(
                    rule_name="league_consistency",
                    passed=True,
                    risk_level=RiskLevel.LOW,
                    message="联赛信息完全匹配"
                )
            elif league1_norm in league2_norm or league2_norm in league1_norm:
                return SafetyCheck(
                    rule_name="league_consistency",
                    passed=True,
                    risk_level=RiskLevel.MEDIUM,
                    message="联赛信息部分匹配"
                )
            else:
                return SafetyCheck(
                    rule_name="league_consistency",
                    passed=False,
                    risk_level=RiskLevel.HIGH,
                    message=f"联赛信息不匹配: {league1} vs {league2}"
                )
                
        except Exception as e:
            return SafetyCheck(
                rule_name="league_consistency",
                passed=False,
                risk_level=RiskLevel.MEDIUM,
                message=f"联赛一致性检查异常: {str(e)}"
            )
    
    def _check_market_type_match(self, option1: Dict, option2: Dict) -> SafetyCheck:
        """检查盘口类型匹配"""
        try:
            desc1 = option1.get("description", "")
            desc2 = option2.get("description", "")
            
            if not desc1 or not desc2:
                return SafetyCheck(
                    rule_name="market_type_match",
                    passed=True,
                    risk_level=RiskLevel.MEDIUM,
                    message="盘口描述信息缺失，跳过检查"
                )
            
            # 检查盘口类型
            type1 = self._extract_market_type(desc1)
            type2 = self._extract_market_type(desc2)
            
            if type1 == type2:
                return SafetyCheck(
                    rule_name="market_type_match",
                    passed=True,
                    risk_level=RiskLevel.LOW,
                    message=f"盘口类型匹配: {type1}"
                )
            else:
                return SafetyCheck(
                    rule_name="market_type_match",
                    passed=False,
                    risk_level=RiskLevel.HIGH,
                    message=f"盘口类型不匹配: {type1} vs {type2}"
                )
                
        except Exception as e:
            return SafetyCheck(
                rule_name="market_type_match",
                passed=False,
                risk_level=RiskLevel.HIGH,
                message=f"盘口类型检查异常: {str(e)}"
            )
    
    def _check_blacklist(self, option1: Dict, option2: Dict) -> SafetyCheck:
        """检查黑名单"""
        try:
            # 检查队伍是否在黑名单中
            match1 = option1.get("match", "")
            match2 = option2.get("match", "")
            
            for blacklisted_item in self.blacklist.get("teams", []):
                if blacklisted_item.lower() in match1.lower() or blacklisted_item.lower() in match2.lower():
                    return SafetyCheck(
                        rule_name="blacklist_check",
                        passed=False,
                        risk_level=RiskLevel.CRITICAL,
                        message=f"发现黑名单队伍: {blacklisted_item}"
                    )
            
            # 检查联赛是否在黑名单中
            league1 = option1.get("league", "")
            league2 = option2.get("league", "")
            
            for blacklisted_league in self.blacklist.get("leagues", []):
                if blacklisted_league.lower() in league1.lower() or blacklisted_league.lower() in league2.lower():
                    return SafetyCheck(
                        rule_name="blacklist_check",
                        passed=False,
                        risk_level=RiskLevel.CRITICAL,
                        message=f"发现黑名单联赛: {blacklisted_league}"
                    )
            
            return SafetyCheck(
                rule_name="blacklist_check",
                passed=True,
                risk_level=RiskLevel.LOW,
                message="黑名单检查通过"
            )
            
        except Exception as e:
            return SafetyCheck(
                rule_name="blacklist_check",
                passed=False,
                risk_level=RiskLevel.CRITICAL,
                message=f"黑名单检查异常: {str(e)}"
            )

    def _check_odds_reasonableness(self, option1: Dict, option2: Dict) -> SafetyCheck:
        """检查赔率合理性"""
        try:
            odds1 = float(option1.get("koef", "0"))
            odds2 = float(option2.get("koef", "0"))

            if odds1 <= 0 or odds2 <= 0:
                return SafetyCheck(
                    rule_name="odds_reasonableness",
                    passed=False,
                    risk_level=RiskLevel.HIGH,
                    message="发现无效赔率"
                )

            # 检查赔率是否在合理范围内
            if odds1 < 1.01 or odds1 > 50 or odds2 < 1.01 or odds2 > 50:
                return SafetyCheck(
                    rule_name="odds_reasonableness",
                    passed=False,
                    risk_level=RiskLevel.MEDIUM,
                    message=f"赔率超出合理范围: {odds1}, {odds2}"
                )

            # 检查赔率差异是否过大
            odds_ratio = max(odds1, odds2) / min(odds1, odds2)
            if odds_ratio > 3.0:
                return SafetyCheck(
                    rule_name="odds_reasonableness",
                    passed=False,
                    risk_level=RiskLevel.MEDIUM,
                    message=f"赔率差异过大: {odds1} vs {odds2} (比率: {odds_ratio:.2f})"
                )

            return SafetyCheck(
                rule_name="odds_reasonableness",
                passed=True,
                risk_level=RiskLevel.LOW,
                message="赔率合理性检查通过"
            )

        except Exception as e:
            return SafetyCheck(
                rule_name="odds_reasonableness",
                passed=False,
                risk_level=RiskLevel.MEDIUM,
                message=f"赔率合理性检查异常: {str(e)}"
            )

    def _check_duplicate_bet(self, option1: Dict, option2: Dict) -> SafetyCheck:
        """检查重复投注"""
        try:
            # 这里可以实现重复投注检查逻辑
            # 例如检查最近是否已经对同一比赛进行过投注

            return SafetyCheck(
                rule_name="duplicate_bet_check",
                passed=True,
                risk_level=RiskLevel.LOW,
                message="重复投注检查通过"
            )

        except Exception as e:
            return SafetyCheck(
                rule_name="duplicate_bet_check",
                passed=False,
                risk_level=RiskLevel.HIGH,
                message=f"重复投注检查异常: {str(e)}"
            )

    def _calculate_team_similarity(self, teams1: List[str], teams2: List[str]) -> float:
        """计算队伍相似度"""
        try:
            # 标准化队伍名称
            teams1_norm = [team.lower().strip() for team in teams1]
            teams2_norm = [team.lower().strip() for team in teams2]

            # 计算正向匹配
            forward_score = 0
            for i in range(2):
                if teams1_norm[i] == teams2_norm[i]:
                    forward_score += 1
                elif teams1_norm[i] in teams2_norm[i] or teams2_norm[i] in teams1_norm[i]:
                    forward_score += 0.8
                elif self._has_common_words(teams1_norm[i], teams2_norm[i]):
                    forward_score += 0.6

            # 计算交叉匹配（考虑主客队颠倒）
            cross_score = 0
            for i in range(2):
                j = 1 - i  # 交叉索引
                if teams1_norm[i] == teams2_norm[j]:
                    cross_score += 1
                elif teams1_norm[i] in teams2_norm[j] or teams2_norm[j] in teams1_norm[i]:
                    cross_score += 0.8
                elif self._has_common_words(teams1_norm[i], teams2_norm[j]):
                    cross_score += 0.6

            # 取最高分数并标准化
            return max(forward_score, cross_score) / 2

        except Exception as e:
            logger.error(f"计算队伍相似度失败: {e}")
            return 0.0

    def _has_common_words(self, team1: str, team2: str) -> bool:
        """检查两个队伍名称是否有共同单词"""
        words1 = set(team1.split())
        words2 = set(team2.split())
        common_words = words1 & words2
        return len(common_words) > 0

    def _extract_market_type(self, description: str) -> str:
        """提取盘口类型"""
        desc_lower = description.lower()

        if any(keyword in desc_lower for keyword in ["让球", "让分", "handicap"]):
            return "handicap"
        elif any(keyword in desc_lower for keyword in ["大小球", "总分", "total", "over", "under"]):
            return "total"
        elif any(keyword in desc_lower for keyword in ["独赢", "胜负", "moneyline"]):
            return "moneyline"
        else:
            return "unknown"

    def _load_blacklist(self) -> Dict:
        """加载黑名单"""
        try:
            if os.path.exists(self.blacklist_file):
                with open(self.blacklist_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 创建默认黑名单
                default_blacklist = {
                    "teams": [
                        "总三分球数"
                    ],
                     "leagues": [
                        "委内瑞拉",
                        "波多黎各",
                        "多米尼加",
                        "巴西",
                        "玻利维亚"
                        ]
                }
                self._save_blacklist(default_blacklist)
                return default_blacklist
        except Exception as e:
            logger.error(f"加载黑名单失败: {e}")
            return {"teams": [], "leagues": []}

    # 已移除白名单功能，简化为只检查黑名单

    def _save_blacklist(self, blacklist: Dict):
        """保存黑名单"""
        try:
            with open(self.blacklist_file, 'w', encoding='utf-8') as f:
                json.dump(blacklist, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存黑名单失败: {e}")

    # 已移除白名单保存功能

    def _log_safety_checks(self, option1: Dict, option2: Dict, checks: List[SafetyCheck]):
        """记录安全检查日志"""
        try:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "option1": {
                    "match": option1.get("match", ""),
                    "league": option1.get("league", ""),
                    "bookmaker": option1.get("bookmaker", "")
                },
                "option2": {
                    "match": option2.get("match", ""),
                    "league": option2.get("league", ""),
                    "bookmaker": option2.get("bookmaker", "")
                },
                "checks": [
                    {
                        "rule": check.rule_name,
                        "passed": check.passed,
                        "risk_level": check.risk_level.value,
                        "message": check.message
                    }
                    for check in checks
                ]
            }

            with open(self.safety_log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')

        except Exception as e:
            logger.error(f"记录安全检查日志失败: {e}")

    def get_safety_summary(self, checks: List[SafetyCheck]) -> Dict:
        """获取安全检查摘要"""
        total_checks = len(checks)
        passed_checks = sum(1 for check in checks if check.passed)
        failed_checks = total_checks - passed_checks

        risk_counts = {level.value: 0 for level in RiskLevel}
        for check in checks:
            if not check.passed:
                risk_counts[check.risk_level.value] += 1

        return {
            "total_checks": total_checks,
            "passed_checks": passed_checks,
            "failed_checks": failed_checks,
            "risk_distribution": risk_counts,
            "overall_safe": failed_checks == 0 or risk_counts["critical"] == 0
        }

    def add_to_blacklist(self, item_type: str, item_value: str):
        """添加到黑名单"""
        if item_type in ["teams", "leagues"]:
            if item_value not in self.blacklist[item_type]:
                self.blacklist[item_type].append(item_value)
                self._save_blacklist(self.blacklist)
                logger.info(f"已添加到黑名单 ({item_type}): {item_value}")

    def remove_from_blacklist(self, item_type: str, item_value: str):
        """从黑名单移除"""
        if item_type in ["teams", "leagues"]:
            if item_value in self.blacklist[item_type]:
                self.blacklist[item_type].remove(item_value)
                self._save_blacklist(self.blacklist)
                logger.info(f"已从黑名单移除 ({item_type}): {item_value}")

    def is_safe_to_proceed(self, checks: List[SafetyCheck]) -> Tuple[bool, str]:
        """
        判断是否安全继续执行对冲

        返回:
            (是否安全, 原因说明)
        """
        critical_failures = [check for check in checks if not check.passed and check.risk_level == RiskLevel.CRITICAL]

        if critical_failures:
            reasons = [check.message for check in critical_failures]
            return False, f"发现严重风险: {'; '.join(reasons)}"

        high_risk_failures = [check for check in checks if not check.passed and check.risk_level == RiskLevel.HIGH]

        if len(high_risk_failures) > 1:
            return False, f"发现多个高风险问题: {len(high_risk_failures)}个"

        return True, "安全检查通过"
