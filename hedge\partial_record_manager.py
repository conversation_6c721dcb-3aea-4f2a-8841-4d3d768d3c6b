#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from utils.json_data_manager import get_data_manager

# 设置日志
logger = logging.getLogger(__name__)

class PartialRecordManager:
    """单边待补单记录管理器"""
    
    def __init__(self, data_dir: str = "data"):
        """
        初始化单边记录管理器
        
        参数:
            data_dir: 数据目录
        """
        self.data_manager = get_data_manager(data_dir)
        self.records_file = "partial_records"  # 单独的文件名
        logger.info("单边记录管理器初始化完成")
    
    def save_partial_record(self, record_data: Dict) -> bool:
        """
        保存单边待补单记录
        
        参数:
            record_data: 单边记录数据
            
        返回:
            是否保存成功
        """
        try:
            # 验证必要字段
            if not record_data.get("hedge_id"):
                logger.error("单边记录缺少hedge_id字段")
                return False
            
            # 添加时间戳
            current_time = datetime.now().isoformat()
            record_data["created_at"] = current_time
            record_data["updated_at"] = current_time
            record_data["record_type"] = "partial_risk"  # 标记为单边风险记录
            
            # 获取今天的日期字符串用于分片存储
            date_str = datetime.now().strftime("%Y-%m-%d")
            
            # 加载现有记录
            existing_records = self.data_manager.load_data(self.records_file, date_str, [])
            
            # 检查是否已存在相同的hedge_id
            hedge_id = record_data["hedge_id"]
            for i, record in enumerate(existing_records):
                if record.get("hedge_id") == hedge_id:
                    # 更新现有记录
                    record_data["updated_at"] = current_time
                    existing_records[i] = record_data
                    logger.info(f"更新现有单边记录: {hedge_id}")
                    break
            else:
                # 添加新记录
                existing_records.append(record_data)
                logger.info(f"添加新单边记录: {hedge_id}")
            
            # 保存记录
            success = self.data_manager.save_data(self.records_file, existing_records, date_str)
            
            if success:
                logger.info(f"单边记录保存成功: {hedge_id}")
                return True
            else:
                logger.error(f"单边记录保存失败: {hedge_id}")
                return False
                
        except Exception as e:
            logger.error(f"保存单边记录时出错: {e}", exc_info=True)
            return False
    
    def get_partial_records(self, days: int = 7, status: Optional[str] = None) -> List[Dict]:
        """
        获取单边待补单记录
        
        参数:
            days: 查询天数
            status: 状态过滤 (pending, completed, failed)
            
        返回:
            单边记录列表
        """
        try:
            partial_records = []
            
            for i in range(days):
                date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                records = self.data_manager.load_data(self.records_file, date, [])
                
                for record in records:
                    # 状态过滤
                    if status and record.get("status") != status:
                        continue
                    
                    # 添加额外信息用于显示
                    record["date"] = date
                    record["risk_type"] = self._analyze_risk_type(record)
                    partial_records.append(record)
            
            # 按时间倒序排列
            partial_records.sort(key=lambda x: x.get("created_at", ""), reverse=True)
            return partial_records
            
        except Exception as e:
            logger.error(f"获取单边记录失败: {e}")
            return []
    
    def _analyze_risk_type(self, record: Dict) -> str:
        """
        分析单边风险类型
        
        参数:
            record: 单边记录
            
        返回:
            风险类型描述
        """
        try:
            pinbet_bet = record.get("pinbet_bet", {})
            crown_bet = record.get("crown_bet", {})
            
            pinbet_success = pinbet_bet.get("success", False)
            crown_success = crown_bet.get("success", False)
            
            if pinbet_success and not crown_success:
                return "平博成功，皇冠失败"
            elif crown_success and not pinbet_success:
                return "皇冠成功，平博失败"
            else:
                return "状态不明"
                
        except Exception as e:
            logger.error(f"分析风险类型失败: {e}")
            return "分析失败"
    
    def update_partial_status(self, hedge_id: str, new_status: str, 
                             补单_result: Optional[Dict] = None) -> bool:
        """
        更新单边记录状态
        
        参数:
            hedge_id: 对冲ID
            new_status: 新状态 (completed, failed, retry_pending)
            补单_result: 补单结果
            
        返回:
            是否更新成功
        """
        try:
            # 查找记录
            record = self.get_partial_record_by_id(hedge_id)
            if not record:
                logger.error(f"未找到单边记录: {hedge_id}")
                return False
            
            # 更新状态
            record["status"] = new_status
            record["updated_at"] = datetime.now().isoformat()
            
            # 如果有补单结果，更新相关信息
            if 补单_result:
                record["补单_result"] = 补单_result
                record["补单_time"] = datetime.now().isoformat()
            
            # 保存更新
            date_str = datetime.fromisoformat(record["created_at"]).strftime("%Y-%m-%d")
            records = self.data_manager.load_data(self.records_file, date_str, [])
            
            for i, existing_record in enumerate(records):
                if existing_record.get("hedge_id") == hedge_id:
                    records[i] = record
                    break
            
            success = self.data_manager.save_data(self.records_file, records, date_str)
            if success:
                logger.info(f"更新单边记录状态成功: {hedge_id} -> {new_status}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新单边记录状态失败: {e}")
            return False
    
    def get_partial_record_by_id(self, hedge_id: str, date_str: Optional[str] = None) -> Optional[Dict]:
        """
        根据ID获取单边记录
        
        参数:
            hedge_id: 对冲ID
            date_str: 日期字符串，None表示搜索最近几天
            
        返回:
            单边记录或None
        """
        try:
            # 如果没有指定日期，搜索最近7天
            if date_str is None:
                for i in range(7):
                    search_date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                    records = self.data_manager.load_data(self.records_file, search_date, [])
                    
                    for record in records:
                        if record.get("hedge_id") == hedge_id:
                            return record
                return None
            else:
                records = self.data_manager.load_data(self.records_file, date_str, [])
                for record in records:
                    if record.get("hedge_id") == hedge_id:
                        return record
                return None
                
        except Exception as e:
            logger.error(f"根据ID获取单边记录失败: {e}")
            return None
    
    def get_statistics(self, days: int = 7) -> Dict:
        """
        获取单边记录统计信息
        
        参数:
            days: 统计天数
            
        返回:
            统计信息字典
        """
        try:
            total_records = 0
            pending_records = 0
            completed_records = 0
            failed_records = 0
            total_loss = 0.0
            
            for i in range(days):
                date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                records = self.data_manager.load_data(self.records_file, date, [])
                
                for record in records:
                    total_records += 1
                    status = record.get("status", "pending")
                    
                    if status == "pending":
                        pending_records += 1
                    elif status == "completed":
                        completed_records += 1
                    elif status == "failed":
                        failed_records += 1
                    
                    # 计算损失（单边风险通常是负盈利）
                    actual_profit = record.get("actual_profit", 0)
                    if actual_profit < 0:
                        total_loss += abs(actual_profit)
            
            return {
                "total_records": total_records,
                "pending_records": pending_records,
                "completed_records": completed_records,
                "failed_records": failed_records,
                "completion_rate": (completed_records / total_records * 100) if total_records > 0 else 0,
                "total_loss": total_loss,
                "average_loss": (total_loss / total_records) if total_records > 0 else 0,
                "days": days
            }
            
        except Exception as e:
            logger.error(f"获取单边记录统计信息失败: {e}")
            return {}
