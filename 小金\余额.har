{"_priority": "High", "_resourceType": "fetch", "cache": {}, "connection": "1460827", "request": {"method": "GET", "url": "https://sports-api.sbk-188-sports.com/api/v1/member/getbalance", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.9"}, {"name": "Authorization", "value": "Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.d3y7wMXLDHm36iWLdIUlyeeCA6T1gffCHi0PcZJfB-w"}, {"name": "Cache-Control", "value": "max-age=0"}, {"name": "Client", "value": "ba85d040-0e1e-4efa-ac78-77aed1214432"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON>", "value": "sb-188cshapi=875302666.20480.0000"}, {"name": "DeviceType", "value": "Web"}, {"name": "Host", "value": "sports-api.sbk-188-sports.com"}, {"name": "Origin", "value": "https://sports.sbk-188-sports.com"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://sports.sbk-188-sports.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-site"}, {"name": "SessionID", "value": "2239481a-9a9e-476b-88d0-a8594a4398e0"}, {"name": "TabId", "value": "871714"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "sec-ch-ua", "value": "\"Not=A?Brand\";v=\"99\", \"Chromium\";v=\"118\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [], "cookies": [{"name": "sb-188cshapi", "value": "875302666.20480.0000", "path": "/", "domain": "sports-api.sbk-188-sports.com", "expires": "1969-12-31T23:59:59.000Z", "httpOnly": true, "secure": true, "sameSite": "None"}], "headersSize": 1577, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Access-Control-Allow-Credentials", "value": "true"}, {"name": "Access-Control-Allow-Methods", "value": "*"}, {"name": "Access-Control-Allow-Origin", "value": "https://sports.sbk-188-sports.com"}, {"name": "Access-Control-Expose-Headers", "value": "Authorization,Server,Client,Set-Cookie,SessionID,_ga"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Tue, 01 Jul 2025 02:20:55 GMT"}, {"name": "Expires", "value": "-1"}, {"name": "Pragma", "value": "no-cache"}, {"name": "Server", "value": "AEEAFC"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Vary", "value": "Accept-Encoding"}], "cookies": [], "content": {"size": 22, "mimeType": "application/json", "compression": -31, "text": "{\"r\":0,\"d\":5934.92103}"}, "redirectURL": "", "headersSize": 472, "bodySize": 53, "_transferSize": 525, "_error": null}}