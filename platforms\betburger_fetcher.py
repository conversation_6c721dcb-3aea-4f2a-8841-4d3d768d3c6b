import json
import logging
import requests
import time
from datetime import datetime
import os
import sys
import pygame.mixer

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from utils.utils import load_config, setup_logging, format_handicap, get_handicap_sign, save_json_data

# 获取模块级别的日志记录器
logger = logging.getLogger(__name__)

def make_api_request(url, headers, data, max_retries=3, retry_delay=5):
    """发送API请求并处理重试逻辑"""
    for attempt in range(max_retries):
        try:
            response = requests.post(url=url, headers=headers, data=data, timeout=30)
            response.raise_for_status()
            return response
        except (requests.ConnectionError, requests.Timeout) as e:
            if attempt == max_retries - 1:
                logger.error(f"连接错误或超时，请求失败: {e}")
            else:
                logger.debug(f"连接错误或超时 (尝试 {attempt+1}/{max_retries})")
                time.sleep(retry_delay)
        except requests.RequestException as e:
            logger.error(f"请求异常: {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
            else:
                raise

def format_handicap_with_sign(value):
    """格式化盘口值，确保正值前有+号，负值保持不变"""
    try:
        if value is None or value == "":
            return "0"
        
        if isinstance(value, str):
            value = value.strip()
            if not value:
                return "0"
            
            # 如果已经是有符号的字符串，检查开头
            if value.startswith('+') or value.startswith('-'):
                return value
            
            # 尝试转为浮点数
            num_value = float(value)
        else:
            num_value = float(value)
            
        # 格式化数值，确保正值前有+号
        if num_value > 0:
            return f"+{format_handicap(num_value)}"
        elif num_value < 0:
            return format_handicap(num_value)  # 负号已经包含
        else:
            return "0"
    except (ValueError, TypeError):
        return str(value)

def play_notification_sound():
    """播放提示音（使用 pygame）"""
    config = load_config()
    notification_config = config.get("notification", {})
    
    if not notification_config.get("enable_sound", True):
        return
        
    sound_file = notification_config.get("sound_file")
    if not sound_file:
        logger.warning("未配置提示音文件路径")
        return
        
    try:
        logger.info(f"播放提示音: {sound_file}")
        pygame.mixer.init()  # 初始化音频模块
        pygame.mixer.music.load(sound_file)
        pygame.mixer.music.play()
        while pygame.mixer.music.get_busy():  # 等待播放完成
            time.sleep(0.1)
    except Exception as e:
        logger.error(f"播放提示音失败: {e}")

def fetch_betburger_data(config=None):
    """
    从BetBurger获取套利数据

    参数:
        config: 可选的配置字典，如果不提供则自动加载

    返回:
        成功：包含套利数据的字典列表
        失败：空列表
    """
    if config is None:
        config = load_config()
    betburger_config = config.get("betburger", {})
    access_token = betburger_config.get("access_token")
    base_url = betburger_config.get("api_url")
    search_filter = betburger_config.get("search_filter")
    per_page = betburger_config.get("per_page")  
    
    if not access_token:
        logger.error("未配置BetBurger访问令牌，请在config/config.json中配置betburger.access_token")
        return []
    
    try:
        url = base_url
        headers = {
            'accept': 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        data = {
            'event_id': '',
            'show_event_arbs': '',
            'excluded_events[]': '',
            'per_page': per_page,  # 使用配置文件中的per_page参数
            'search_filter[]': search_filter,
            'sort_by': 'percent',
            'excluded_bets[]': '',
            'excluded_bk_events[]': '',
            'bookmakers2[]': '',
            'access_token': access_token,
            'grouped': 'true',
            'locale': 'cn',
            'koef_format': 'decimal'
        }
        
        response = make_api_request(url, headers, data)
        
        try:
            response_data = response.json()
        except json.JSONDecodeError as e:
            logger.error(f"解析JSON失败: {e}")
            logger.error(f"响应内容预览: {response.text[:200]}")
            return []
            
        bets = response_data.get("bets", [])
        if bets:
            logger.info(f"获取到 {len(bets)} 个投注选项")
        else:
            logger.debug("未获取到投注选项")
        
        return bets
        
    except Exception as e:
        logger.error(f"获取BetBurger数据时出错: {e}")
        return []

def process_betburger_data(bets):
    """
    处理BetBurger返回的原始数据
    
    参数:
        bets: BetBurger API返回的投注选项列表
        
    返回:
        处理后的投注数据字典
    """
    # 修改书商映射，添加更多可能的皇冠标识
    bookmaker_map = {
        1: "平博", 
        5: "皇冠"

    }
    matches = {}
    
    try:
        for bet in bets:
            try:
                bet_id = bet.get("id")
                home_team = bet.get('team1_name', '')
                away_team = bet.get('team2_name', '')
                if not home_team:
                    home_team = bet.get('home', '')
                if not away_team:
                    away_team = bet.get('away', '')
                match_name = f"{home_team} - {away_team}"
                league_name = bet.get('league')
                bookmaker_id = bet.get('bookmaker_id')
                
                # 获取书商名称，确保皇冠相关的ID都映射到"皇冠"
                bookmaker_name = bookmaker_map.get(bookmaker_id, '未知')
                if bookmaker_id in [5, 105, 205] or '皇冠' in str(bet.get('bookmaker_name', '')):
                    bookmaker_name = "皇冠"
                    
                started_at = bet.get('started_at')
                original_bet_name = bet.get('bet_name', '')
                event_id = bet.get('event_id')
                match_identifier = str(event_id) if event_id else f"{league_name}:{match_name}:{started_at}"
                period_id = bet.get('period_id')
                try:
                    period_id = int(period_id)
                except (ValueError, TypeError):
                    period_id = 0
                period_text = {
                    3: "",
                    5: "第1节",
                    6: "第2节",
                    7: "第3节",
                    8: "第4节",
                    10: "上半场",
                    13: "下半场"
                }.get(period_id, "")
                market_and_bet_type = 0
                market_type = bet.get('market_and_bet_type', '')
                market_param = bet.get('market_and_bet_type_param', '')
                
                # 先确定市场类型
                if market_type:
                    try:
                        market_and_bet_type = int(market_type)
                    except:
                        market_and_bet_type = 0
                
                # 处理market_param，根据市场类型决定是否添加符号
                if market_param is not None:
                    market_param = str(market_param).strip()
                    
                    # 只对让分盘(17,18)添加+号，大小球盘口(19,20)保持原样
                    if market_param and not market_param.startswith('-') and not market_param.startswith('+'):
                        try:
                            if (market_and_bet_type in [17, 18]) and float(market_param) > 0:
                                # 只对让分盘口添加+号
                                market_param = f"+{market_param}"
                        except:
                            pass
                else:
                    market_param = "0"
                
                formatted_market_param = format_handicap(market_param)
                bet_description = ""
                
                # 大小球盘口描述 - 不添加符号
                if market_and_bet_type == 19:  # 大
                    # 大球盘口，显示原始值，不加符号
                    bet_description = f"{period_text}大{market_param.replace('+', '')}"
                elif market_and_bet_type == 20:  # 小
                    # 小球盘口，显示原始值，不加符号
                    bet_description = f"{period_text}小{market_param.replace('+', '')}"

                elif market_and_bet_type == 1:  # 主队独赢
                    bet_description = f"{home_team}独赢"
                elif market_and_bet_type == 2:  # 客队独赢
                    bet_description = f"{away_team}独赢"

                elif market_and_bet_type == 21:  # 主队大
                    bet_description = f"{home_team}{period_text}主队大{market_param.replace('+', '')}"
                elif market_and_bet_type == 22:  # 主队小
                    bet_description = f"{home_team}{period_text}主队小{market_param.replace('+', '')}"
                    
                elif market_and_bet_type == 23:  # 客队大
                    bet_description = f"{away_team}{period_text}客队大{market_param.replace('+', '')}"
                elif market_and_bet_type == 24:  # 客队小
                    bet_description = f"{away_team}{period_text}客队小{market_param.replace('+', '')}"
                elif market_and_bet_type == 17 or market_and_bet_type == 18:  # 主/客队让球统一处理
                    # 让球盘口，保留符号
                    team_name = home_team if market_and_bet_type == 17 else away_team
                    bet_description = f"{team_name}{period_text}让球{market_param}"
                else:
                    bet_description = original_bet_name
                koef = bet.get('koef', '')
                if koef:
                    try:
                        koef_value = float(koef)
                        hong_kong_odds = koef_value - 1
                        if hong_kong_odds < 0.01:
                            hong_kong_odds = 0.01
                        logger.debug(f"赔率转换: 欧赔 {koef_value:.2f} -> 香港盘 {hong_kong_odds:.2f}")
                        formatted_koef = f"{koef_value:.2f}"
                        bet_description += f"，水位: {koef_value:.2f}"
                    except:
                        formatted_koef = str(koef)
                        logger.warning(f"无法转换赔率: {koef}")
                        bet_description += f"，水位: {koef}"
                debug_info = {
                    "original_bet_name": original_bet_name,
                    "market_and_bet_type": market_and_bet_type,
                    "period_id": period_id,
                    "event_id": event_id
                }
                if match_identifier not in matches:
                    matches[match_identifier] = []
                matches[match_identifier].append({
                    "id": bet_id,
                    "bookmaker": bookmaker_name,
                    "league": league_name,
                    "match": match_name,
                    "description": bet_description,
                    "period_id": period_id,
                    "market_and_bet_type": market_and_bet_type,
                    "market_param": market_param,
                    "koef": formatted_koef,
                    "started_at": started_at,
                    "debug_info": debug_info
                })
            except Exception as e:
                logger.error(f'处理套利数据时出错: {e}', exc_info=True)
                continue
                
        return matches
                
    except Exception as e:
        logger.error(f'处理套利数据时出错: {e}', exc_info=True)
        return {}

def save_arb_data(matches):
    """
    保存套利数据到JSON文件
    
    参数:
        matches: 处理后的比赛数据字典
        
    返回:
        包含保存结果的字典
    """
    if not matches:
        logger.info("没有套利数据需要保存")
        return {"success": False, "message": "没有套利数据需要保存"}
    
    # 创建每日唯一JSON文件
    current_date = datetime.now().strftime("%Y%m%d")
    filename = f"betdata-{current_date}.json"
    
    # 计算有效的套利组合
    valid_arbs = []
    notification_count = 0
    
    for match_identifier, bets_list in matches.items():
        if len(bets_list) >= 2:
            league = bets_list[0]["league"]
            match = bets_list[0]["match"]
            started_at = bets_list[0]["started_at"]
            
            # 准备保存数据
            arb_data = {
                "league": league,
                "match": match,
                "started_at": started_at,
                "bets": bets_list
            }
            
            valid_arbs.append(arb_data)
            notification_count += 1
            
            # 打印并发出通知
            event_id = bets_list[0].get("debug_info", {}).get("event_id", "未知")
            started_at_formatted = datetime.fromtimestamp(started_at).strftime('%H:%M %m月 %d日')
            
            print("\n================================ 套利组合 =================================")
            print(f"比赛: {match} ({started_at_formatted})")
            print(f"联赛: {league}")
            print(f"比赛ID: {event_id}")
            print("-------------------------------------------------------------------------")
            for bet_info in bets_list:
                print(f"{bet_info['bookmaker']}：{bet_info['match']}，{bet_info['description']}")
            print("==========================================================================\n")
    
    # 如果有套利组合，播放提示音
    if notification_count > 0:
        play_notification_sound()
    
    # 读取现有数据
    existing_arbs = []
    if os.path.exists(filename):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # 确保读取的数据是列表类型
                if isinstance(data, list):
                    existing_arbs = data
                elif isinstance(data, dict) and "bets" in data:
                    # 如果是字典且包含bets键，提取bets内容
                    existing_arbs = []
                    logger.info("文件中的数据格式是字典，正在转换为列表格式")
                else:
                    logger.warning(f"文件 {filename} 中的数据格式不符合预期，将重新创建")
                    existing_arbs = []
        except (json.JSONDecodeError, FileNotFoundError) as e:
            logger.warning(f"读取文件 {filename} 时出错: {e}, 将创建新文件")
            existing_arbs = []
    
    # 添加新数据到现有数据中
    all_arbs = existing_arbs + valid_arbs
    
    # 保存到文件
    success = save_json_data(filename, all_arbs)
    
    if success:
        return {
            "success": True, 
            "filename": filename, 
            "count": len(valid_arbs),
            "total": len(all_arbs)
        }
    else:
        return {"success": False, "message": "保存数据失败"}

def fetch_and_process_data():
    """
    抓取数据并处理，返回处理后的结果
    
    返回:
        包含处理结果的字典 
    """
    try:
        # 抓取数据
        bets = fetch_betburger_data()
        
        # if not bets:
        #     return {"success": False, "message": "没有获取到套利数据"}
            
        # 处理数据
        matches = process_betburger_data(bets)
        
        # 保存数据
        result = save_arb_data(matches)
        
        return result
    except Exception as e:
        logger.error(f"抓取和处理数据过程中出错: {e}", exc_info=True)
        return {"success": False, "message": f"抓取和处理数据失败: {str(e)}"}

def start_fetcher():
    """启动BetBurger数据抓取器"""
    logger.info("启动BetBurger数据抓取器")
    
    pygame.mixer.init()  # 提前初始化，避免首次播放延迟
    
    try:
        last_warning_time = time.time()
        while True:
            try:
                result = fetch_and_process_data()
                
                if result.get("success"):
                    logger.info(f"成功保存 {result.get('count')} 个新套利组合到 {result.get('filename')}，文件中共有 {result.get('total')} 个套利组合")
                else:
                    # 控制警告日志的频率，避免频繁输出相同警告
                    current_time = time.time()
                    if current_time - last_warning_time > 60:  # 至少间隔60秒
                        logger.debug(result.get("message"))
                        last_warning_time = current_time
                
            except Exception as e:
                logger.error(f"抓取数据过程中出错: {e}", exc_info=True)
            
            # 等待一段时间再次抓取
            time.sleep(5)
            
    except KeyboardInterrupt:
        logger.info("数据抓取器已手动停止")

def generate_update_id():
    """
    生成一个随机更新ID
    
    返回:
        随机字符串ID
    """
    import random
    import string
    
    # 生成一个随机字符串，长度为16
    letters = string.ascii_lowercase + string.digits
    return ''.join(random.choice(letters) for i in range(16))

def fetch_and_save_data(config=None):
    """
    获取并保存套利数据到文件中

    参数:
        config: 可选的配置字典，如果不提供则自动加载

    返回:
        (bool, str): 是否成功和保存的文件路径元组
    """
    # 获取数据
    bets = fetch_betburger_data(config)
    
    if not bets:
        # 调整为debug级别，减少无数据时的日志
        logger.debug("未获取到套利数据")
        return False, ""
    
    # 生成文件名
    now = datetime.now()
    date_str = now.strftime("%Y%m%d")
    update_id = generate_update_id()
    file_name = f"betdata-{date_str}.json"
    
    # 保存JSON文件
    try:
        # 确保数据目录存在
        data_dir = "data"
        os.makedirs(data_dir, exist_ok=True)
        
        full_path = os.path.join(data_dir, file_name)
        
        # 构造完整数据
        data = {
            "timestamp": now.strftime("%Y-%m-%d %H:%M:%S"),
            "update_id": update_id,
            "bets": bets
        }
        
        with open(full_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"成功获取并保存 {len(bets)} 条投注数据到 {file_name}")
        
        # 注释掉提示音播放，现在只在投注成功后才播放提示音
        # play_notification_sound()
        
        return True, full_path
        
    except Exception as e:
        logger.error(f"保存套利数据失败: {e}")
        return False, ""

if __name__ == "__main__":
    # 设置日志
    setup_logging()
    
    print("BetBurger数据抓取器已启动...")
    start_fetcher() 