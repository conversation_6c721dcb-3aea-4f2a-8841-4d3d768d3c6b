#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
匹配学习系统管理界面
用于查看、分析和管理匹配失败数据
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List
from utils.match_learning import match_learning_system, MatchFailureRecord


class LearningDashboard:
    """学习系统管理界面"""
    
    def __init__(self):
        self.learning_system = match_learning_system
    
    def show_dashboard(self):
        """显示学习系统仪表板"""
        print("=" * 80)
        print("🧠 匹配学习系统仪表板")
        print("=" * 80)
        
        # 获取学习洞察
        insights = self.learning_system.get_learning_insights()
        
        # 显示总体统计
        print(f"\n📊 总体统计:")
        print(f"  总失败次数: {insights.get('total_failures', 0)}")
        print(f"  待人工审核: {insights.get('pending_manual_reviews', 0)}")
        print(f"  别名建议数: {insights.get('alias_suggestions_count', 0)}")
        
        # 显示失败类别分布
        print(f"\n🔍 失败类别分布:")
        for category, count in insights.get('top_failure_categories', []):
            percentage = (count / insights.get('total_failures', 1)) * 100
            print(f"  {category}: {count} 次 ({percentage:.1f}%)")
        
        # 显示问题平台
        print(f"\n🏟️ 问题平台:")
        for platform, count in insights.get('top_problem_platforms', []):
            print(f"  {platform}: {count} 次")
        
        # 显示问题联赛
        print(f"\n🏆 问题联赛:")
        for league, count in insights.get('top_problem_leagues', [])[:5]:
            print(f"  {league}: {count} 次")
        
        # 显示最近失败
        print(f"\n⏰ 最近24小时失败:")
        recent_failures = insights.get('recent_failures', [])
        if recent_failures:
            for failure in recent_failures[:5]:
                time_str = failure['timestamp'][:19].replace('T', ' ')
                print(f"  {time_str} | {failure['match']} | {failure['category']} | 分数:{failure['score']:.2f}")
        else:
            print("  无最近失败记录")
    
    def show_failure_details(self, limit: int = 10):
        """显示详细的失败记录"""
        print("\n" + "=" * 80)
        print("📋 详细失败记录")
        print("=" * 80)
        
        try:
            failure_file = self.learning_system.failure_log_file
            if not os.path.exists(failure_file):
                print("暂无失败记录")
                return
            
            records = []
            with open(failure_file, 'r', encoding='utf-8') as f:
                for line in f:
                    records.append(json.loads(line.strip()))
            
            # 按时间倒序排列
            records.sort(key=lambda x: x['timestamp'], reverse=True)
            
            for i, record in enumerate(records[:limit]):
                print(f"\n📝 记录 {i+1}:")
                print(f"  时间: {record['timestamp'][:19].replace('T', ' ')}")
                print(f"  比赛: {record['arbitrage_match']}")
                print(f"  联赛: {record['arbitrage_league']}")
                print(f"  平台: {record['source_platform']} → {record['target_platform']}")
                print(f"  失败类别: {record['failure_category']}")
                print(f"  最佳分数: {record['best_match_score']:.3f}")
                print(f"  失败原因: {record['failure_reason']}")
                
                if record.get('analysis_notes'):
                    print(f"  分析建议: {record['analysis_notes']}")
                
                if record.get('manual_review'):
                    print(f"  🔍 需要人工审核")
                
                if record.get('suggested_alias'):
                    print(f"  💡 别名建议: {record['suggested_alias']}")
        
        except Exception as e:
            print(f"读取失败记录时出错: {e}")
    
    def show_alias_suggestions(self):
        """显示别名建议"""
        print("\n" + "=" * 80)
        print("💡 队伍别名建议")
        print("=" * 80)
        
        try:
            failure_file = self.learning_system.failure_log_file
            if not os.path.exists(failure_file):
                print("暂无别名建议")
                return
            
            suggestions = []
            with open(failure_file, 'r', encoding='utf-8') as f:
                for line in f:
                    record = json.loads(line.strip())
                    if record.get('suggested_alias'):
                        suggestions.append(record)
            
            if not suggestions:
                print("暂无别名建议")
                return
            
            # 按联赛分组
            by_league = {}
            for record in suggestions:
                league = record['arbitrage_league']
                if league not in by_league:
                    by_league[league] = []
                by_league[league].append(record)
            
            for league, records in by_league.items():
                print(f"\n🏆 {league}:")
                for record in records:
                    alias_data = record['suggested_alias']
                    print(f"  比赛: {record['arbitrage_match']}")
                    print(f"  时间: {record['timestamp'][:19].replace('T', ' ')}")
                    print(f"  置信度: {record['best_match_score']:.3f}")
                    
                    for mapping in alias_data.get('mappings', []):
                        print(f"    建议: '{mapping['standard_name']}' ↔ '{mapping['alias']}'")
                    print()
        
        except Exception as e:
            print(f"读取别名建议时出错: {e}")
    
    def export_alias_suggestions(self, output_file: str = "suggested_aliases.json"):
        """导出别名建议到文件"""
        try:
            failure_file = self.learning_system.failure_log_file
            if not os.path.exists(failure_file):
                print("暂无数据可导出")
                return
            
            # 收集所有别名建议
            alias_suggestions = {}
            
            with open(failure_file, 'r', encoding='utf-8') as f:
                for line in f:
                    record = json.loads(line.strip())
                    if record.get('suggested_alias'):
                        alias_data = record['suggested_alias']
                        league = alias_data.get('league', '未知联赛')
                        
                        if league not in alias_suggestions:
                            alias_suggestions[league] = {}
                        
                        for mapping in alias_data.get('mappings', []):
                            standard_name = mapping['standard_name']
                            alias = mapping['alias']
                            
                            if standard_name not in alias_suggestions[league]:
                                alias_suggestions[league][standard_name] = []
                            
                            if alias not in alias_suggestions[league][standard_name]:
                                alias_suggestions[league][standard_name].append(alias)
            
            # 导出到文件
            export_data = {
                "export_time": datetime.now().isoformat(),
                "description": "从匹配失败记录中提取的队伍别名建议",
                "team_aliases": alias_suggestions
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 别名建议已导出到: {output_file}")
            print(f"   包含 {len(alias_suggestions)} 个联赛的别名建议")
            
        except Exception as e:
            print(f"导出别名建议时出错: {e}")
    
    def analyze_patterns(self):
        """分析失败模式"""
        print("\n" + "=" * 80)
        print("🔍 失败模式分析")
        print("=" * 80)
        
        try:
            failure_file = self.learning_system.failure_log_file
            if not os.path.exists(failure_file):
                print("暂无数据可分析")
                return
            
            records = []
            with open(failure_file, 'r', encoding='utf-8') as f:
                for line in f:
                    records.append(json.loads(line.strip()))
            
            if not records:
                print("暂无失败记录")
                return
            
            # 分析分数分布
            scores = [r['best_match_score'] for r in records]
            print(f"\n📊 匹配分数分析:")
            print(f"  平均分数: {sum(scores)/len(scores):.3f}")
            print(f"  最高分数: {max(scores):.3f}")
            print(f"  最低分数: {min(scores):.3f}")
            
            # 分数区间分布
            score_ranges = {
                "0.0-0.2": 0,
                "0.2-0.4": 0,
                "0.4-0.6": 0,
                "0.6-0.8": 0,
                "0.8-1.0": 0
            }
            
            for score in scores:
                if score < 0.2:
                    score_ranges["0.0-0.2"] += 1
                elif score < 0.4:
                    score_ranges["0.2-0.4"] += 1
                elif score < 0.6:
                    score_ranges["0.4-0.6"] += 1
                elif score < 0.8:
                    score_ranges["0.6-0.8"] += 1
                else:
                    score_ranges["0.8-1.0"] += 1
            
            print(f"\n📈 分数区间分布:")
            for range_name, count in score_ranges.items():
                percentage = (count / len(scores)) * 100
                print(f"  {range_name}: {count} 次 ({percentage:.1f}%)")
            
            # 时间分布分析
            hours = [datetime.fromisoformat(r['timestamp']).hour for r in records]
            hour_counts = {}
            for hour in hours:
                hour_counts[hour] = hour_counts.get(hour, 0) + 1
            
            print(f"\n⏰ 失败时间分布 (前5个高峰时段):")
            sorted_hours = sorted(hour_counts.items(), key=lambda x: x[1], reverse=True)
            for hour, count in sorted_hours[:5]:
                print(f"  {hour:02d}:00-{hour:02d}:59: {count} 次")
            
        except Exception as e:
            print(f"分析失败模式时出错: {e}")
    
    def interactive_menu(self):
        """交互式菜单"""
        while True:
            print("\n" + "=" * 50)
            print("🧠 匹配学习系统管理")
            print("=" * 50)
            print("1. 显示仪表板")
            print("2. 查看详细失败记录")
            print("3. 查看别名建议")
            print("4. 导出别名建议")
            print("5. 分析失败模式")
            print("0. 退出")
            
            try:
                choice = input("\n请选择操作 (0-5): ").strip()
                
                if choice == "0":
                    print("👋 再见！")
                    break
                elif choice == "1":
                    self.show_dashboard()
                elif choice == "2":
                    limit = input("显示记录数量 (默认10): ").strip()
                    limit = int(limit) if limit.isdigit() else 10
                    self.show_failure_details(limit)
                elif choice == "3":
                    self.show_alias_suggestions()
                elif choice == "4":
                    filename = input("输出文件名 (默认suggested_aliases.json): ").strip()
                    filename = filename if filename else "suggested_aliases.json"
                    self.export_alias_suggestions(filename)
                elif choice == "5":
                    self.analyze_patterns()
                else:
                    print("❌ 无效选择，请重试")
                
                input("\n按回车键继续...")
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 操作出错: {e}")
                input("\n按回车键继续...")


def main():
    """主函数"""
    dashboard = LearningDashboard()
    dashboard.interactive_menu()


if __name__ == "__main__":
    main()
