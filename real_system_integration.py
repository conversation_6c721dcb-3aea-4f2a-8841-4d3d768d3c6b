#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
真实对冲系统集成模块
用于连接和获取真实的对冲投注系统数据
"""

import os
import sys
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class RealSystemIntegration:
    """真实系统集成类"""

    def __init__(self):
        """初始化集成器"""
        self.modules_available = False
        self.hedge_manager = None
        self.metrics_manager = None
        self.lock_manager = None
        self.config_manager = None

        self._initialize_modules()

    def _initialize_modules(self):
        """初始化真实系统模块"""
        try:
            logger.info("开始初始化真实系统模块")

            # 尝试导入真实的对冲系统模块
            from hedge.hedge_manager import HedgeManager
            from hedge.hedge_metrics import get_metrics_manager
            from hedge.execution_lock import get_lock_manager
            from config.hedge_config import HedgeConfig

            logger.info("对冲系统模块导入成功")

            # 创建平台实例（不自动登录）
            logger.info("创建平博实例...")
            pinbet_instance = self._create_pinbet_instance()

            logger.info("创建皇冠实例...")
            crown_instance = self._create_crown_instance()

            logger.info(f"平台实例创建结果: pinbet={pinbet_instance is not None}, crown={crown_instance is not None}")

            # 检查实例是否创建成功
            if not pinbet_instance:
                logger.error("平博实例创建失败")
            if not crown_instance:
                logger.error("皇冠实例创建失败")

            # 初始化管理器（即使某个平台实例为None也继续初始化）
            logger.info("初始化对冲管理器...")
            self.hedge_manager = HedgeManager(
                pinbet_instance=pinbet_instance,
                crown_instance=crown_instance
            )

            logger.info("初始化其他管理器...")
            self.metrics_manager = get_metrics_manager()
            self.lock_manager = get_lock_manager()
            self.config_manager = HedgeConfig()

            self.modules_available = True
            logger.info("真实对冲系统模块加载成功")

        except ImportError as e:
            logger.warning(f"无法加载真实系统模块: {e}")
            self.modules_available = False
        except Exception as e:
            logger.error(f"初始化真实系统模块失败: {e}", exc_info=True)
            self.modules_available = False

    def _create_pinbet_instance(self):
        """创建平博实例"""
        try:
            from platforms.pinbet_bk import PinBetSystem
            return PinBetSystem()
        except Exception as e:
            logger.error(f"创建平博实例失败: {e}")
            return None

    def _create_crown_instance(self):
        """创建皇冠实例"""
        try:
            from platforms.hgbet_bk import HGBetBK
            return HGBetBK()
        except Exception as e:
            logger.error(f"创建皇冠实例失败: {e}")
            return None

    def get_config(self) -> Dict[str, Any]:
        """获取配置"""
        if not self.modules_available:
            raise RuntimeError("真实系统模块不可用")

        try:
            # 获取对冲配置
            hedge_config = self.config_manager.get_all_config()

            # 同时获取auto_bet配置
            from utils.utils import load_config
            full_config = load_config()
            auto_bet_config = full_config.get('auto_bet', {})

            # 将auto_bet配置合并到返回结果中
            hedge_config['auto_bet'] = auto_bet_config

            return hedge_config
        except Exception as e:
            logger.error(f"获取配置失败: {e}")
            raise

    def update_config(self, new_config: Dict[str, Any]) -> Dict[str, Any]:
        """更新配置"""
        if not self.modules_available:
            raise RuntimeError("真实系统模块不可用")

        try:
            # 更新对冲配置
            if 'hedge' in new_config:
                self.config_manager.update_config(new_config['hedge'])

            # 更新auto_bet配置
            if 'auto_bet' in new_config:
                from utils.utils import load_config, save_config
                full_config = load_config()
                full_config['auto_bet'] = new_config['auto_bet']
                save_config(full_config)

            return {"success": True, "message": "配置更新成功"}
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return {"success": False, "message": f"配置更新失败: {str(e)}"}

    def get_platform_status(self) -> Dict[str, Any]:
        """获取平台状态"""
        if not self.modules_available:
            raise RuntimeError("真实系统模块不可用")

        try:
            # 检测真实平台状态
            status = {}

            # 检测皇冠平台
            if hasattr(self.hedge_manager, 'crown'):
                status['crown'] = self._check_platform_status('crown', self.hedge_manager.crown)

            # 检测平博平台
            if hasattr(self.hedge_manager, 'pinbet'):
                status['pinbet'] = self._check_platform_status('pinbet', self.hedge_manager.pinbet)

            return status
        except Exception as e:
            logger.error(f"获取平台状态失败: {e}")
            raise

    def _check_platform_status(self, platform_name: str, platform_instance) -> Dict[str, Any]:
        """检查单个平台状态"""
        try:
            if not platform_instance:
                return {
                    "status": "unavailable",
                    "message": f"{platform_name}平台实例不可用",
                    "logged_in": False,
                    "balance": 0
                }

            # 检查登录状态
            logged_in = False
            balance = 0
            message = "未知状态"

            if platform_name == 'crown':
                # 检查皇冠登录状态
                if hasattr(platform_instance, 'is_logged_in'):
                    logged_in = platform_instance.is_logged_in()
                if hasattr(platform_instance, 'get_balance'):
                    try:
                        balance = platform_instance.get_balance()
                    except:
                        balance = 0
                message = "已连接" if logged_in else "未登录"

            elif platform_name == 'pinbet':
                # 检查平博登录状态
                if hasattr(platform_instance, 'api') and hasattr(platform_instance.api, 'is_logged_in'):
                    logged_in = platform_instance.api.is_logged_in()
                if hasattr(platform_instance, 'api') and hasattr(platform_instance.api, 'get_balance'):
                    try:
                        balance = platform_instance.api.get_balance()
                    except:
                        balance = 0
                message = "已连接" if logged_in else "未登录"

            return {
                "status": "connected" if logged_in else "disconnected",
                "message": message,
                "logged_in": logged_in,
                "balance": balance
            }

        except Exception as e:
            logger.error(f"检查{platform_name}平台状态失败: {e}")
            return {
                "status": "error",
                "message": f"状态检查失败: {str(e)}",
                "logged_in": False,
                "balance": 0
            }

    def get_platform_balances(self) -> Dict[str, float]:
        """获取平台余额"""
        if not self.modules_available:
            raise RuntimeError("真实系统模块不可用")

        balances = {}
        try:
            # 获取皇冠余额
            if hasattr(self.hedge_manager, 'crown') and self.hedge_manager.crown:
                try:
                    crown_balance = self.hedge_manager.crown.get_balance()
                    balances['crown'] = crown_balance
                except Exception as e:
                    logger.warning(f"获取皇冠余额失败: {e}")
                    balances['crown'] = 0

            # 获取平博余额
            if hasattr(self.hedge_manager, 'pinbet') and self.hedge_manager.pinbet:
                try:
                    pinbet_balance = self.hedge_manager.pinbet.api.get_balance()
                    balances['pinbet'] = pinbet_balance
                except Exception as e:
                    logger.warning(f"获取平博余额失败: {e}")
                    balances['pinbet'] = 0

        except Exception as e:
            logger.error(f"获取平台余额失败: {e}")

        return balances


# 全局集成器实例
_integration = None

def get_real_system_integration() -> RealSystemIntegration:
    """获取全局真实系统集成器实例"""
    global _integration
    if _integration is None:
        _integration = RealSystemIntegration()
    return _integration
