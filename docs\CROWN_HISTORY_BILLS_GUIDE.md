# 皇冠历史账单查询功能使用指南

## 🎯 功能概述

基于用户提供的HAR抓包文件，成功开发了皇冠历史投注账单查询功能。该功能可以查询和分析用户的历史投注记录，包括投注金额、盈亏情况等关键数据。

## 📊 HAR文件分析结果

### API详情
- **URL**: `https://m518.mos077.com/transform.php?ver=2025-06-03-fsError_97`
- **方法**: POST
- **Content-Type**: `application/x-www-form-urlencoded`

### 请求参数
```
p=get_history_data          # 操作类型
uid=1efuzffhgm36609797l6269395b1  # 用户ID
langx=zh-cn                 # 语言设置
gtype=ALL                   # 游戏类型（ALL/BK/FT等）
isAll=N                     # 是否获取全部数据
```

### 响应数据结构（XML格式）
```xml
<serverresponse>
    <total_gold>10,710</total_gold>          <!-- 总投注金额 -->
    <total_vgold>9,679</total_vgold>         <!-- 总有效投注 -->
    <total_winloss>1,358.80</total_winloss>  <!-- 总盈亏金额 -->
    <total_winloss_calss>word_lightgreen</total_winloss_calss>
    <history>
        <date>2025-06-04</date>              <!-- 日期 -->
        <date_name>6月4日 星期三</date_name>   <!-- 日期名称 -->
        <gold>240</gold>                     <!-- 投注金额 -->
        <vgold>206</vgold>                   <!-- 有效投注 -->
        <winloss>206.40</winloss>            <!-- 盈亏金额 -->
        <winloss_class>word_green</winloss_class>  <!-- 盈亏样式 -->
    </history>
    <!-- 更多历史记录... -->
</serverresponse>
```

## 🛠️ 新增功能

### 1. 皇冠系统API方法

#### `get_history_data(gtype="ALL", is_all="N")`
获取历史投注账单数据

**参数:**
- `gtype`: 游戏类型
  - `"ALL"` - 所有游戏
  - `"BK"` - 篮球
  - `"FT"` - 足球
- `is_all`: 是否获取全部数据
  - `"N"` - 获取摘要数据
  - `"Y"` - 获取详细数据

**返回值:**
```python
{
    'success': True,
    'total_gold': '10,710',      # 总投注金额
    'total_vgold': '9,679',      # 总有效投注
    'total_winloss': '1,358.80', # 总盈亏金额
    'history': [                 # 历史记录列表
        {
            'date': '2025-06-04',        # 日期
            'date_name': '6月4日 星期三',  # 日期名称
            'gold': '240',               # 投注金额
            'vgold': '206',              # 有效投注
            'winloss': '206.40',         # 盈亏金额
            'winloss_class': 'word_green' # 盈亏样式类
        }
    ],
    'raw_response': '...'        # 原始XML响应
}
```

#### `format_history_display(history_data)`
格式化历史账单数据用于显示

### 2. 主菜单集成

新增**菜单10: 皇冠历史账单查询**，包含以下子选项：

```
===== 皇冠历史账单查询 =====
1. 查询所有游戏历史账单
2. 查询篮球历史账单  
3. 查询足球历史账单
4. 查询详细历史账单
5. 导出历史账单数据
0. 返回主菜单
```

## 📋 使用方法

### 1. 基本查询

```python
# 通过主菜单
# 选择 10 -> 皇冠历史账单查询
# 选择 1 -> 查询所有游戏历史账单

# 或直接调用API
from platforms.hgbet_bk import HGBetBK

crown_system = HGBetBK()
# 登录后...

# 查询所有游戏历史账单
history_data = crown_system.get_history_data(gtype="ALL", is_all="N")

if history_data['success']:
    print(f"总投注金额: {history_data['total_gold']}")
    print(f"总盈亏金额: {history_data['total_winloss']}")
    
    for record in history_data['history']:
        print(f"{record['date']}: 投注{record['gold']}, 盈亏{record['winloss']}")
```

### 2. 按游戏类型查询

```python
# 查询篮球历史账单
basketball_data = crown_system.get_history_data(gtype="BK", is_all="N")

# 查询足球历史账单  
football_data = crown_system.get_history_data(gtype="FT", is_all="N")
```

### 3. 获取详细数据

```python
# 获取详细历史数据
detailed_data = crown_system.get_history_data(gtype="ALL", is_all="Y")
```

### 4. 格式化显示

```python
# 格式化显示历史账单
formatted_display = crown_system.format_history_display(history_data)
print(formatted_display)
```

输出示例：
```
============================================================
皇冠历史投注账单
============================================================
总投注金额: 10,710
总有效投注: 9,679
总盈亏金额: +1,358.80
------------------------------------------------------------
日期         投注金额     有效投注     盈亏金额    
------------------------------------------------------------
2025-06-04   240        206        +206.40     
2025-06-03   720        518        -201.60     
2025-06-02   800        786        +586.00     
============================================================
```

## 📈 统计功能

系统会自动计算并显示以下统计信息：

- **记录天数**: 总投注天数
- **盈利天数**: 盈利的天数
- **亏损天数**: 亏损的天数  
- **胜率**: 盈利天数占比

示例：
```
📊 统计信息:
记录天数: 15 天
盈利天数: 9 天
亏损天数: 6 天
胜率: 60.0%
```

## 💾 数据导出功能

### 1. JSON格式导出

```python
# 通过菜单: 10 -> 5 -> 导出历史账单数据
# 文件保存为: data/crown_history_YYYYMMDD_HHMMSS.json
```

### 2. CSV格式导出

```python
# 导出时选择同时生成CSV格式
# 文件保存为: data/crown_history_YYYYMMDD_HHMMSS.csv
```

CSV文件包含以下字段：
- 日期
- 日期名称
- 投注金额
- 有效投注
- 盈亏金额
- 盈亏状态（盈利/亏损/平局）

## 🔧 技术特性

### 1. 双重解析机制
- **主要方式**: XML解析（ET.fromstring）
- **备用方式**: 正则表达式解析
- **自动切换**: XML解析失败时自动使用正则表达式

### 2. XML内容清理
- 移除BOM标记
- 添加XML声明
- 替换HTML实体
- 移除HTML标签

### 3. 错误处理
- 网络请求异常处理
- XML解析异常处理
- 数据格式异常处理
- 用户友好的错误提示

### 4. 数据验证
- 登录状态检查
- 响应状态码验证
- 数据完整性验证

## 🎯 重要字段说明

根据用户要求，重点关注以下字段：

- **`gold`**: 投注金额 - 用户的投注总额
- **`winloss`**: 盈亏金额 - 用户的盈亏情况
  - 正数表示盈利（通常显示为绿色）
  - 负数表示亏损（通常显示为红色）

## 🚀 使用流程

1. **启动系统**: 运行主程序
2. **登录皇冠**: 确保皇冠系统已登录
3. **选择菜单**: 主菜单选择 10
4. **选择查询类型**: 根据需要选择查询选项
5. **查看结果**: 系统显示格式化的账单信息
6. **导出数据**: 可选择导出为JSON或CSV格式

## 💡 使用建议

1. **定期查询**: 建议定期查询历史账单，了解投注情况
2. **数据备份**: 重要数据建议导出备份
3. **统计分析**: 利用统计功能分析投注表现
4. **分类查询**: 根据游戏类型分别查询，便于分析

## 🎊 开发成果

✅ **成功实现的功能:**
1. 基于HAR抓包数据的API逆向工程
2. 完整的历史账单查询功能
3. 多种查询选项（游戏类型、详细程度）
4. 数据格式化显示
5. 统计信息计算
6. 数据导出功能（JSON/CSV）
7. 主菜单集成
8. 错误处理和异常恢复

**🎉 皇冠历史账单查询功能开发完成！用户现在可以通过菜单10查询和分析历史投注数据。**
