#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import time
import uuid
import logging
import os
import sys
import base64
import re
from datetime import datetime, timedelta
import pygame.mixer

# 智能导入：支持相对导入和绝对导入
try:
    from .smart_login_manager import SmartLoginManager
except ImportError:
    try:
        from smart_login_manager import SmartLoginManager
    except ImportError:
        # 如果无法导入智能登录管理器，定义一个简单的替代类
        class SmartLoginManager:
            def __init__(self, platform_name):
                self.platform_name = platform_name

            def is_session_valid(self):
                return False

            def can_attempt_login(self):
                return True, "可以尝试登录"

            def should_use_manual_login(self):
                return False, "可以尝试自动登录"

            def record_login_attempt(self, success, error_code=None, message=""):
                pass

            def record_successful_login(self, cookies, tokens=None):
                pass

            def get_session_data(self):
                return {}

            def get_login_statistics(self):
                return {"total_attempts": 0, "success_rate": 0}

            def get_recommended_action(self):
                return "使用传统登录"

            def clear_session(self):
                pass

# 设置日志配置
logger = logging.getLogger("PinBet")
logger.setLevel(logging.INFO)

# 创建日志过滤器，只允许特定的日志消息通过
class LoginBalanceFilter(logging.Filter):
    def filter(self, record):
        # 只允许登录成功和余额获取成功的日志消息通过
        return "登录成功" in record.getMessage() or "余额获取成功" in record.getMessage()

# 控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
# 应用过滤器到控制台处理器
console_handler.addFilter(LoginBalanceFilter())
console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(console_formatter)

# 文件处理器 - 记录所有日志，按日期分文件
from datetime import datetime
import os

# 创建logs目录
log_dir = 'logs'
os.makedirs(log_dir, exist_ok=True)

# 生成当前日期的日志文件名
today_str = datetime.now().strftime("%Y-%m-%d")
log_file = os.path.join(log_dir, f'pinbet_{today_str}.log')

file_handler = logging.FileHandler(log_file, encoding='utf-8')
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(file_formatter)

# 添加处理器到日志记录器
logger.addHandler(console_handler)
logger.addHandler(file_handler)

class PinBetAPI:
    """平博API接口类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://www.pin135.com"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Content-Type": "application/json",
            "Origin": "https://www.pin135.com",
            "Referer": "https://www.pin135.com/m/zh-cn/asian/"
        }
        self.auth_tokens = {}
        self.cookies = {}
        self.user_info = None
        self.balance = None
        self.is_logged_in = False

        # 初始化智能登录管理器
        self.login_manager = SmartLoginManager("pinbet")

        # 验证码相关
        self.captcha_token = None
        self.captcha_image_data = None

        # 会话保持相关
        self.last_activity_time = 0
        self.session_refresh_interval = 300  # 5分钟刷新一次会话
        self.auto_refresh_enabled = True
        self.session_keep_alive_enabled = True  # 启用会话保活
        self.max_session_idle_time = 240  # 4分钟无活动后主动刷新
    
    def login(self, username, password):
        """智能登录平博账号"""
        # 保存登录凭据用于重新登录
        self._last_username = username
        self._last_password = password

        # 1. 检查现有会话是否有效
        if self.login_manager.is_session_valid():
            logger.info("检测到现有会话，验证有效性")
            session_data = self.login_manager.get_session_data()

            # 恢复会话状态
            if session_data.get("cookies"):
                self.cookies = session_data["cookies"]
                # 恢复session cookies
                for name, value in self.cookies.items():
                    self.session.cookies.set(name, value)

            if session_data.get("tokens"):
                self.auth_tokens = session_data["tokens"]
                # 恢复headers
                for key, value in self.auth_tokens.items():
                    if key.startswith("X-"):
                        self.headers[key.lower().replace("_", "-")] = value

            self.is_logged_in = True

            # 验证会话是否真正有效（通过获取余额）
            balance_success, balance_data = self.get_balance()
            if balance_success:
                logger.info("现有会话验证成功")
                return True, "使用现有会话登录成功"
            else:
                logger.warning("现有会话验证失败，清除会话并重新登录")
                self.clear_login_session()
                # 继续执行下面的登录流程

        # 2. 检查是否可以尝试登录
        can_login, reason = self.login_manager.can_attempt_login()
        if not can_login:
            logger.warning(f"无法尝试登录: {reason}")
            return False, f"登录受限: {reason}"

        # 3. 检查是否建议手动登录（仅在严重情况下阻止登录）
        should_manual, manual_reason = self.login_manager.should_use_manual_login()
        if should_manual:
            logger.warning(f"建议手动登录: {manual_reason}")
            # 不再阻止登录，只是记录警告
            # 让验证码登录流程来处理这种情况

        # 4. 执行自动登录
        url = f"{self.base_url}/member-service/v2/authenticate"
        params = {
            "locale": "zh_CN",
            "_": str(int(time.time() * 1000)),
            "withCredentials": "true"
        }

        # 修改Content-Type为application/x-www-form-urlencoded
        headers = self.headers.copy()
        headers["Content-Type"] = "application/x-www-form-urlencoded"

        # 使用form-urlencoded格式
        login_data = {
            "captcha": "",
            "captchaToken": "",
            "loginId": username,
            "password": password
        }

        try:
            # 保存登录凭据以便自动重新登录
            self._last_username = username
            self._last_password = password

            # 记录登录尝试
            logger.info(f"尝试自动登录平博账号: {username}")
            logger.debug(f"登录请求URL: {url}")
            logger.debug(f"登录请求参数: {params}")
            logger.debug(f"登录请求数据: {login_data}")
            logger.debug(f"登录请求headers: {headers}")

            response = self.session.post(
                url,
                params=params,
                data=login_data,  # 使用data参数而不是json参数
                headers=headers
            )

            logger.debug(f"登录响应状态码: {response.status_code}")
            logger.debug(f"登录响应headers: {dict(response.headers)}")
            logger.debug(f"登录响应cookies: {list(response.cookies.keys()) if response.cookies else 'None'}")

            if response.status_code == 200:
                try:
                    resp_data = response.json()
                    logger.debug(f"登录响应数据: {resp_data}")

                    # 如果响应是整数0，表示登录成功
                    if resp_data == 0:
                        logger.info(f"登录成功: {username}")
                        self.is_logged_in = True
                        # 保存cookies
                        self.cookies = self.session.cookies.get_dict()

                        # 检查是否获取到有效的cookies
                        if not self.cookies:
                            logger.warning("登录成功但未获取到cookies")
                        else:
                            logger.debug(f"获取到cookies: {list(self.cookies.keys())}")

                        # 验证余额以确认登录真正成功
                        balance_success, balance_data = self.get_balance()
                        if balance_success and isinstance(balance_data, dict):
                            balance_amount = balance_data.get('betCredit', 0)
                            if balance_amount == 0:
                                logger.error("登录后余额为0，登录实际失败")
                                self.is_logged_in = False
                                self.clear_login_session()
                                return False, "登录失败：余额为0"

                        # 记录成功登录
                        self.login_manager.record_login_attempt(True, None, "登录成功")
                        self.login_manager.record_successful_login(self.cookies, self.auth_tokens)

                        # 更新活动时间
                        self.last_activity_time = time.time()

                        return True, "登录成功"

                    # 如果响应包含tokens，也表示登录成功
                    if isinstance(resp_data, dict) and "tokens" in resp_data:
                        logger.info(f"登录成功(带tokens): {username}")
                        self.auth_tokens = resp_data["tokens"]

                        # 更新headers中的认证信息
                        if "X-Browser-Session-Id" in self.auth_tokens:
                            self.headers["x-browser-session-id"] = self.auth_tokens["X-Browser-Session-Id"]
                        if "X-Custid" in self.auth_tokens:
                            self.headers["x-custid"] = self.auth_tokens["X-Custid"]
                        if "X-U" in self.auth_tokens:
                            self.headers["x-u"] = self.auth_tokens["X-U"]
                        if "X-SLID" in self.auth_tokens:
                            self.headers["x-slid"] = self.auth_tokens["X-SLID"]
                        if "X-Lcu" in self.auth_tokens:
                            self.headers["x-lcu"] = self.auth_tokens["X-Lcu"]

                        # 保存cookies
                        self.cookies = self.session.cookies.get_dict()
                        self.is_logged_in = True

                        # 验证余额以确认登录真正成功
                        balance_success, balance_data = self.get_balance()
                        if balance_success and isinstance(balance_data, dict):
                            balance_amount = balance_data.get('betCredit', 0)
                            if balance_amount == 0:
                                logger.error("登录后余额为0，登录实际失败")
                                self.is_logged_in = False
                                self.clear_login_session()
                                return False, "登录失败：余额为0"

                        # 记录成功登录
                        self.login_manager.record_login_attempt(True, None, "登录成功(带tokens)")
                        self.login_manager.record_successful_login(self.cookies, self.auth_tokens)

                        return True, "登录成功"

                    # 其他情况都视为登录失败，解析错误代码
                    error_code = resp_data
                    error_messages = {
                        1: "用户名不存在",
                        2: "密码错误",
                        3: "账户被锁定",
                        4: "账户被禁用",
                        5: "验证码错误",
                        16: "需要验证码登录",  # 修正：错误代码16确实是需要验证码
                        18: "登录频率过高，请稍后再试"
                    }

                    error_msg = error_messages.get(error_code, f"登录失败: 未知错误代码 {error_code}")
                    logger.error(f"登录失败: 错误代码={error_code}, 错误信息={error_msg}")
                    self.is_logged_in = False

                    # 记录失败登录
                    self.login_manager.record_login_attempt(False, error_code, error_msg)

                    # 如果是需要验证码登录，自动处理验证码流程
                    if error_code == 16:
                        logger.info("检测到需要验证码登录，启动验证码流程")
                        return self.handle_captcha_login(username, password)

                    return False, error_msg
                except Exception as e:
                    logger.error(f"登录失败: {str(e)}")
                    # 记录异常登录
                    self.login_manager.record_login_attempt(False, None, f"解析响应异常: {str(e)}")
                    return False, f"登录失败: {str(e)}"
            else:
                # 简化错误日志
                logger.error(f"登录失败: HTTP状态码 {response.status_code}")
                # 记录HTTP错误
                self.login_manager.record_login_attempt(False, response.status_code, f"HTTP状态码 {response.status_code}")
                return False, f"登录失败: HTTP状态码 {response.status_code}"

        except Exception as e:
            logger.error(f"登录失败: {str(e)}")
            # 记录网络异常
            self.login_manager.record_login_attempt(False, None, f"网络异常: {str(e)}")
            return False, f"登录失败: {str(e)}"

    # 已移除 get_login_status() 方法 - 断层方法，从未被调用

    def clear_login_session(self):
        """清除登录会话（用于重置验证码状态）"""
        logger.info("清除平博登录会话")
        self.cookies = {}
        self.auth_tokens = {}
        self.session.cookies.clear()
        self.is_logged_in = False

        # 重置headers到初始状态
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Content-Type": "application/json",
            "Origin": "https://www.pin135.com",
            "Referer": "https://www.pin135.com/m/zh-cn/asian/"
        }

        # 清除登录管理器的会话
        if hasattr(self, 'login_manager'):
            self.login_manager.clear_session()

        logger.info("登录会话已清除")

    def refresh_session(self):
        """刷新会话以保持活跃状态"""
        if not self.is_logged_in:
            return False, "未登录状态，无需刷新会话"

        try:
            # 通过获取余额来刷新会话
            success, result = self.get_balance()
            if success:
                self.last_activity_time = time.time()
                logger.debug("会话刷新成功")
                return True, "会话刷新成功"
            else:
                # 如果余额获取失败，可能是会话已失效
                if "403" in str(result) or "权限" in str(result) or "登录" in str(result):
                    logger.warning("会话已失效，需要重新登录")
                    self.is_logged_in = False
                    return False, "会话已失效，需要重新登录"
                else:
                    logger.warning(f"会话刷新失败: {result}")
                    return False, f"会话刷新失败: {result}"
        except Exception as e:
            logger.error(f"会话刷新异常: {e}")
            return False, f"会话刷新异常: {e}"

    def auto_retry_with_relogin(self, operation_func, *args, max_retries=2, **kwargs):
        """自动重试机制，支持会话失效时重新登录"""
        for retry in range(max_retries):
            try:
                # 执行操作
                success, result = operation_func(*args, **kwargs)

                if success:
                    return True, result

                # 检查是否是会话失效错误
                if ("403" in str(result) or "权限" in str(result) or
                    "登录" in str(result) or "会话" in str(result)):

                    if retry < max_retries - 1:  # 不是最后一次重试
                        logger.warning(f"检测到会话失效，尝试重新登录 (重试 {retry + 1}/{max_retries})")

                        # 清除会话并重新登录
                        self.clear_login_session()
                        if hasattr(self, '_last_username') and hasattr(self, '_last_password'):
                            login_success, login_message = self.login(self._last_username, self._last_password)
                            if login_success:
                                logger.info("重新登录成功，继续重试操作")
                                time.sleep(1)  # 等待1秒后重试
                                continue
                            else:
                                logger.error(f"重新登录失败: {login_message}")
                                return False, f"会话失效且重新登录失败: {login_message}"
                        else:
                            logger.error("会话失效但缺少登录凭据")
                            return False, "会话失效且缺少登录凭据"
                    else:
                        return False, f"操作失败，已重试{max_retries}次: {result}"
                else:
                    # 非会话失效错误，直接返回
                    return False, result

            except Exception as e:
                if retry < max_retries - 1:
                    logger.warning(f"操作异常，正在重试 ({retry + 1}/{max_retries}): {e}")
                    time.sleep(2)
                    continue
                else:
                    return False, f"操作异常: {e}"

        return False, "操作失败，已重试所有次数"

    def check_and_refresh_session(self):
        """检查并在需要时刷新会话"""
        if not self.auto_refresh_enabled or not self.is_logged_in:
            return True, "无需刷新会话"

        current_time = time.time()
        idle_time = current_time - self.last_activity_time

        # 如果启用会话保活且空闲时间超过阈值，主动刷新
        if self.session_keep_alive_enabled and idle_time > self.max_session_idle_time:
            logger.info(f"会话空闲{idle_time:.0f}秒，执行主动刷新以保持活跃")
            success, message = self.refresh_session()
            if success:
                return True, "会话已主动刷新"
            else:
                logger.warning(f"主动刷新失败: {message}")
                return False, f"主动刷新失败: {message}"

        # 如果空闲时间超过刷新间隔，强制刷新
        if idle_time > self.session_refresh_interval:
            logger.warning(f"会话空闲{idle_time:.0f}秒，执行强制刷新")
            return self.refresh_session()

        return True, "会话仍然活跃"

    def ensure_logged_in(self):
        """确保已登录，如果未登录或会话过期则自动重新登录"""
        if not self.is_logged_in:
            if hasattr(self, '_last_username') and hasattr(self, '_last_password'):
                logger.info("检测到未登录状态，尝试自动登录")
                success, message = self.login(self._last_username, self._last_password)
                return success, message
            else:
                return False, "未登录且缺少登录凭据"

        # 检查会话是否仍然有效（通过尝试获取余额）
        try:
            url = f"{self.base_url}/member-service/v2/account-balance"
            params = {
                "locale": "zh_CN",
                "_": str(int(time.time() * 1000)),
                "withCredentials": "true"
            }

            response = self.session.post(url, params=params, headers=self.headers, timeout=10)

            if response.status_code == 403:
                # 会话过期
                logger.warning("检测到会话过期，尝试重新登录")
                self.clear_login_session()

                if hasattr(self, '_last_username') and hasattr(self, '_last_password'):
                    success, message = self.login(self._last_username, self._last_password)
                    return success, message
                else:
                    return False, "会话过期且缺少登录凭据"
            elif response.status_code == 200:
                return True, "会话有效"
            else:
                logger.warning(f"会话状态检查返回状态码: {response.status_code}")
                return True, "会话状态未知，假设有效"

        except Exception as e:
            logger.warning(f"检查会话状态时出错: {e}")
            return True, "无法检查会话状态，假设有效"

    def get_captcha(self):
        """获取验证码图片和token"""
        url = f"{self.base_url}/captcha-mgmt/captcha/get"
        timestamp = str(int(time.time() * 1000))
        params = {
            "category": "login",
            timestamp: "",
            "locale": "zh_CN",
            "_": timestamp,
            "withCredentials": "true"
        }

        try:
            response = self.session.get(url, params=params, headers=self.headers)

            if response.status_code == 200:
                html_content = response.text
                logger.debug(f"验证码响应内容: {html_content[:500]}...")  # 调试日志

                # 更宽松的正则表达式匹配
                img_patterns = [
                    r'src="data:image/jpg;base64,([^"]*)"',
                    r'src="data:image/jpeg;base64,([^"]*)"',
                    r'src="data:image/png;base64,([^"]*)"',
                    r'data:image/[^;]+;base64,([^"]*)'
                ]

                token_patterns = [
                    r'name="captchaToken"[^>]*value="([^"]*)"',
                    r'value="([^"]*)"[^>]*name="captchaToken"',
                    r'captchaToken[^>]*value="([^"]*)"',
                    r'PINBET88:CAPTCHA:login:([^"]*)'
                ]

                base64_image = None
                captcha_token = None

                # 尝试多种模式匹配图片
                for pattern in img_patterns:
                    img_match = re.search(pattern, html_content)
                    if img_match:
                        base64_image = img_match.group(1)
                        logger.debug(f"使用模式匹配到图片: {pattern}")
                        break

                # 尝试多种模式匹配token
                for pattern in token_patterns:
                    token_match = re.search(pattern, html_content)
                    if token_match:
                        captcha_token = token_match.group(1)
                        # 如果匹配到的是UUID，需要构造完整的token
                        if not captcha_token.startswith("PINBET88:CAPTCHA:login:"):
                            captcha_token = f"PINBET88:CAPTCHA:login:{captcha_token}"
                        logger.debug(f"使用模式匹配到token: {pattern}")
                        break

                if base64_image and captcha_token:
                    # 保存验证码信息
                    self.captcha_token = captcha_token
                    self.captcha_image_data = base64_image

                    logger.info(f"验证码获取成功，token: {captcha_token[:50]}...")
                    return True, base64_image, captcha_token
                else:
                    logger.error(f"无法从响应中提取验证码信息")
                    logger.error(f"图片匹配结果: {base64_image is not None}")
                    logger.error(f"Token匹配结果: {captcha_token is not None}")
                    logger.error(f"响应内容: {html_content}")
                    return False, None, None
            else:
                logger.error(f"获取验证码失败: HTTP状态码 {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return False, None, None

        except Exception as e:
            logger.error(f"获取验证码异常: {str(e)}")
            return False, None, None

    def display_captcha(self, base64_image):
        """显示验证码图片"""
        try:
            # 解码base64图片
            image_data = base64.b64decode(base64_image)

            # 保存为临时文件
            temp_file = "temp_captcha.jpg"
            with open(temp_file, "wb") as f:
                f.write(image_data)

            # 尝试用系统默认程序打开图片
            import subprocess
            import platform

            try:
                if platform.system() == "Windows":
                    os.startfile(temp_file)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", temp_file])
                else:  # Linux
                    subprocess.run(["xdg-open", temp_file])

                logger.info("验证码图片已显示")
                print(f"✅ 验证码图片已保存并打开: {temp_file}")
                return True

            except Exception as e:
                logger.warning(f"自动打开图片失败: {e}")
                print(f"⚠️  验证码已保存为 {temp_file}，请手动查看")
                return True

        except Exception as e:
            logger.error(f"显示验证码失败: {str(e)}")
            return False

    def login_with_captcha(self, username, password, captcha_code):
        """使用验证码登录"""
        if not self.captcha_token:
            logger.error("没有有效的验证码token")
            return False, "没有有效的验证码token"

        url = f"{self.base_url}/member-service/v2/authenticate"
        params = {
            "locale": "zh_CN",
            "_": str(int(time.time() * 1000)),
            "withCredentials": "true"
        }

        # 修改Content-Type为application/x-www-form-urlencoded
        headers = self.headers.copy()
        headers["Content-Type"] = "application/x-www-form-urlencoded"

        # 使用form-urlencoded格式，包含验证码
        login_data = {
            "captcha": captcha_code,
            "captchaToken": self.captcha_token,
            "loginId": username,
            "password": password
        }

        try:
            logger.info(f"尝试验证码登录平博账号: {username}")
            response = self.session.post(
                url,
                params=params,
                data=login_data,
                headers=headers
            )

            if response.status_code == 200:
                try:
                    resp_data = response.json()
                    logger.debug(f"验证码登录响应数据: {resp_data}")

                    # 如果响应是整数0，表示登录成功
                    if resp_data == 0:
                        logger.info(f"验证码登录成功: {username}")
                        self.is_logged_in = True
                        # 保存cookies
                        self.cookies = self.session.cookies.get_dict()

                        # 保存登录凭据
                        self._last_username = username
                        self._last_password = password

                        # 验证余额以确认登录真正成功
                        balance_success, balance_data = self.get_balance()
                        if balance_success and isinstance(balance_data, dict):
                            balance_amount = balance_data.get('betCredit', 0)
                            if balance_amount == 0:
                                logger.error("验证码登录后余额为0，登录实际失败")
                                self.is_logged_in = False
                                self.clear_login_session()
                                return False, "验证码登录失败：余额为0"

                        # 记录成功登录
                        self.login_manager.record_login_attempt(True, None, "验证码登录成功")
                        self.login_manager.record_successful_login(self.cookies, self.auth_tokens)

                        # 清除验证码信息
                        self.captcha_token = None
                        self.captcha_image_data = None

                        return True, "验证码登录成功"

                    # 如果响应包含tokens，也表示登录成功
                    if isinstance(resp_data, dict) and "tokens" in resp_data:
                        logger.info(f"验证码登录成功(带tokens): {username}")
                        self.auth_tokens = resp_data["tokens"]

                        # 更新headers中的认证信息
                        if "X-Browser-Session-Id" in self.auth_tokens:
                            self.headers["x-browser-session-id"] = self.auth_tokens["X-Browser-Session-Id"]
                        if "X-Custid" in self.auth_tokens:
                            self.headers["x-custid"] = self.auth_tokens["X-Custid"]
                        if "X-U" in self.auth_tokens:
                            self.headers["x-u"] = self.auth_tokens["X-U"]
                        if "X-SLID" in self.auth_tokens:
                            self.headers["x-slid"] = self.auth_tokens["X-SLID"]
                        if "X-Lcu" in self.auth_tokens:
                            self.headers["x-lcu"] = self.auth_tokens["X-Lcu"]

                        # 保存cookies
                        self.cookies = self.session.cookies.get_dict()
                        self.is_logged_in = True

                        # 保存登录凭据
                        self._last_username = username
                        self._last_password = password

                        # 验证余额以确认登录真正成功
                        balance_success, balance_data = self.get_balance()
                        if balance_success and isinstance(balance_data, dict):
                            balance_amount = balance_data.get('betCredit', 0)
                            if balance_amount == 0:
                                logger.error("验证码登录后余额为0，登录实际失败")
                                self.is_logged_in = False
                                self.clear_login_session()
                                return False, "验证码登录失败：余额为0"

                        # 记录成功登录
                        self.login_manager.record_login_attempt(True, None, "验证码登录成功(带tokens)")
                        self.login_manager.record_successful_login(self.cookies, self.auth_tokens)

                        # 清除验证码信息
                        self.captcha_token = None
                        self.captcha_image_data = None

                        return True, "验证码登录成功"

                    # 其他情况都视为登录失败
                    error_code = resp_data
                    error_messages = {
                        1: "用户名不存在",
                        2: "密码错误",
                        3: "账户被锁定",
                        4: "账户被禁用",
                        5: "验证码错误",
                        16: "需要验证码登录",
                        18: "登录频率过高，请稍后再试"
                    }

                    error_msg = error_messages.get(error_code, f"登录失败: 未知错误代码 {error_code}")
                    logger.error(f"验证码登录失败: 错误代码={error_code}, 错误信息={error_msg}")
                    self.is_logged_in = False

                    # 记录失败登录
                    self.login_manager.record_login_attempt(False, error_code, error_msg)

                    # 如果是验证码错误，清除验证码信息以便重新获取
                    if error_code == 5:
                        self.captcha_token = None
                        self.captcha_image_data = None

                    return False, error_msg

                except Exception as e:
                    logger.error(f"验证码登录失败: {str(e)}")
                    self.login_manager.record_login_attempt(False, None, f"解析响应异常: {str(e)}")
                    return False, f"验证码登录失败: {str(e)}"
            else:
                logger.error(f"验证码登录失败: HTTP状态码 {response.status_code}")
                self.login_manager.record_login_attempt(False, response.status_code, f"HTTP状态码 {response.status_code}")
                return False, f"验证码登录失败: HTTP状态码 {response.status_code}"

        except Exception as e:
            logger.error(f"验证码登录失败: {str(e)}")
            self.login_manager.record_login_attempt(False, None, f"网络异常: {str(e)}")
            return False, f"验证码登录失败: {str(e)}"

    def handle_captcha_login(self, username, password):
        """处理验证码登录流程"""
        try:
            # 1. 获取验证码
            print("\n🔐 需要验证码登录，正在获取验证码...")
            success, base64_image, captcha_token = self.get_captcha()

            if not success:
                return False, "获取验证码失败"

            # 2. 显示验证码
            print("📷 正在显示验证码图片...")
            self.display_captcha(base64_image)

            # 3. 提示用户输入验证码
            print("\n" + "="*50)
            print("🔍 请查看弹出的验证码图片")
            print("📝 请在下方输入验证码（4位数字）")
            print("="*50)

            # 获取用户输入
            captcha_code = input("请输入验证码: ").strip()

            if not captcha_code:
                return False, "验证码不能为空"

            # 4. 使用验证码登录
            print(f"🔑 正在使用验证码登录...")
            success, message = self.login_with_captcha(username, password, captcha_code)

            if success:
                print("✅ 验证码登录成功！")
                # 清理临时文件
                try:
                    if os.path.exists("temp_captcha.jpg"):
                        os.remove("temp_captcha.jpg")
                except:
                    pass
                return True, message
            else:
                print(f"❌ 验证码登录失败: {message}")

                # 如果是验证码错误，可以重试
                if "验证码错误" in message:
                    retry = input("验证码错误，是否重新获取验证码？(y/n): ").strip().lower()
                    if retry == 'y':
                        return self.handle_captcha_login(username, password)

                return False, message

        except KeyboardInterrupt:
            print("\n❌ 用户取消验证码登录")
            return False, "用户取消验证码登录"
        except Exception as e:
            logger.error(f"验证码登录流程异常: {str(e)}")
            return False, f"验证码登录流程异常: {str(e)}"

    def get_balance(self):
        """获取账户余额，支持自动重新登录"""
        url = f"{self.base_url}/member-service/v2/account-balance"
        params = {
            "locale": "zh_CN",
            "_": str(int(time.time() * 1000)),
            "withCredentials": "true"
        }

        try:
            # 添加调试信息
            logger.debug(f"获取余额请求URL: {url}")
            logger.debug(f"请求参数: {params}")
            logger.debug(f"请求headers: {self.headers}")
            logger.debug(f"当前cookies: {list(self.session.cookies.keys()) if self.session.cookies else 'None'}")

            response = self.session.post(url, params=params, headers=self.headers)

            logger.debug(f"余额响应状态码: {response.status_code}")
            logger.debug(f"余额响应headers: {dict(response.headers)}")

            if response.status_code == 200:
                resp_data = response.json()
                self.balance = resp_data
                logger.debug(f"余额响应数据: {resp_data}")

                # 检查响应中的success字段
                if resp_data.get('success') == False:
                    logger.warning("余额API返回success=False，会话可能已失效")
                    self.clear_login_session()
                    return False, "会话已失效，需要重新登录"

                # 简化日志，只记录余额获取成功和金额
                balance_amount = resp_data.get('betCredit', 0)
                logger.info(f"余额获取成功: {balance_amount}")

                # 检查余额是否为0
                if balance_amount == 0:
                    logger.warning("余额为0，可能表示登录失败")
                    return False, "余额为0，登录可能失败"

                return True, resp_data
            elif response.status_code == 403:
                # 会话过期，不自动重新登录
                logger.warning("会话过期(403)，需要重新登录")
                logger.debug(f"403响应内容: {response.text}")
                self.clear_login_session()
                return False, "会话过期，需要重新登录"
            else:
                logger.error(f"获取余额失败: HTTP状态码 {response.status_code}")
                logger.debug(f"错误响应内容: {response.text}")
                return False, f"获取余额失败: HTTP状态码 {response.status_code}"

        except Exception as e:
            logger.error(f"获取余额失败: {str(e)}")
            return False, f"获取余额失败: {str(e)}"


    
    def get_today_matches(self):
        """获取今日篮球比赛"""
        url = f"{self.base_url}/sports-service/sv/compact/events"
        params = {
            "btg": "1",
            "c": "",
            "cl": "100",
            "d": "",
            "ec": "",
            "ev": "",
            "g": "QQ%3D%3D",
            "hle": "true",
            "ic": "false",
            "ice": "false",
            "inl": "false",
            "l": "100",
            "lang": "",
            "lg": "",
            "lv": "",
            "me": "0",
            "mk": "1",  # 今日比赛
            "more": "false",
            "o": "1",
            "ot": "2",  # 今日比赛
            "pa": "0",
            "pimo": "0%2C1%2C2",
            "pn": "-1",
            "pv": "1",
            "sp": "4",  # 篮球
            "tm": "0",
            "v": "0",
            "locale": "zh_CN",
            "_": str(int(time.time() * 1000)),
            "withCredentials": "true"
        }
        
        try:
            logger.info("获取今日篮球比赛数据")
            response = self.session.get(url, params=params, headers=self.headers)
            
            if response.status_code == 200:
                resp_data = response.json()
                logger.info("今日篮球比赛数据获取成功")
                return True, resp_data
            else:
                logger.error(f"获取今日篮球比赛失败: HTTP状态码 {response.status_code}")
                return False, f"获取今日篮球比赛失败: HTTP状态码 {response.status_code}"
                
        except Exception as e:
            logger.error(f"获取今日篮球比赛异常: {str(e)}")
            return False, f"获取今日篮球比赛异常: {str(e)}"
    
    def get_early_matches(self):
        """获取早场篮球比赛"""
        url = f"{self.base_url}/sports-service/sv/compact/events"
        params = {
            "btg": "1",
            "c": "",
            "cl": "100",
            "d": "",
            "ec": "",
            "ev": "",
            "g": "QQ%3D%3D",
            "hle": "true",
            "ic": "false",
            "ice": "false",
            "inl": "false",
            "l": "100",
            "lang": "",
            "lg": "",
            "lv": "",
            "me": "0",
            "mk": "0",
            "more": "false",
            "o": "0",
            "ot": "1",  # 早场比赛
            "pa": "0",
            "pimo": "0%2C1%2C2",
            "pn": "-1",
            "pv": "1",
            "sp": "4",  # 篮球
            "tm": "0",
            "v": "0",
            "locale": "zh_CN",
            "_": str(int(time.time() * 1000)),
            "withCredentials": "true"
        }
        
        try:
            logger.info("获取早场篮球比赛数据")
            response = self.session.get(url, params=params, headers=self.headers)
            
            if response.status_code == 200:
                resp_data = response.json()
                logger.info("早场篮球比赛数据获取成功")
                return True, resp_data
            else:
                logger.error(f"获取早场篮球比赛失败: HTTP状态码 {response.status_code}")
                return False, f"获取早场篮球比赛失败: HTTP状态码 {response.status_code}"
                
        except Exception as e:
            logger.error(f"获取早场篮球比赛异常: {str(e)}")
            return False, f"获取早场篮球比赛异常: {str(e)}"
    
    def get_bet_form(self, event_id, line_id, period_num, bet_type, team_side, handicap):
        """获取投注表单"""
        # 检查并刷新会话
        self.check_and_refresh_session()

        url = f"{self.base_url}/member-service/v2/all-odds-selections"
        params = {
            "locale": "zh_CN",
            "_": str(int(time.time() * 1000)),
            "withCredentials": "true"
        }
        
        # 根据API文档:
        # oddsId: eventId | periodNum | betType | team_side_code | additional_value | handicap
        # selectionId: lineId | eventId | periodNum | betType | team_side_code | additional_value | handicap | selection_direction

        # 将team_side转换为数字代码
        team_side_code = 0
        selection_direction = 0

        # 根据API字段说明设置正确的代码
        if bet_type == 1:  # 独赢盘
            team_side_code = 0 if team_side == 'home' else 1
            selection_direction = team_side_code
        elif bet_type == 2:  # 让分盘
            # 让分盘的team_side_code: 0=主队, 1=客队
            team_side_code = 0 if team_side == 'home' else 1
            # 让分盘的selection_direction: 0=主队, 1=客队
            selection_direction = team_side_code
            logger.info(f"让分盘设置 - team_side: {team_side}, team_side_code: {team_side_code}, selection_direction: {selection_direction}")
        elif bet_type == 3:  # 大小球盘
            # 大小球盘的team_side_code: 3=大球, 4=小球
            team_side_code = 3 if team_side == 'over' else 4
            # 大小球盘的selection_direction: 0=大球, 1=小球
            selection_direction = 0 if team_side == 'over' else 1
            logger.info(f"大小球盘设置 - team_side: {team_side}, team_side_code: {team_side_code}, selection_direction: {selection_direction}")

        # 附加值，根据API说明通常为1表示附加盘口，0为主盘口
        additional_value = 1
        
        # 处理不同比赛阶段的period_num
        # 根据API要求设置正确的period_num值
        # 全场使用0，半场使用1，其他保持原值
        original_period_num = period_num

        # 根据文档说明：periodNum：1为上半场，0为全场
        # 但是从_execute_bet传入的period_num需要转换：
        # - 如果是全场投注，传入的period_num=1，需要转换为0
        # - 如果是半场投注，传入的period_num=1，需要保持为1
        # 这里需要根据调用上下文来判断

        # 暂时保持原值，让调用方明确传递正确的值
        api_period_num = period_num

        logger.info(f"处理period_num: 原始值={original_period_num}, API使用值={api_period_num}")

        # 处理盘口值格式：正值不带+号，负值保留-号，整数不带小数点
        try:
            handicap_value = float(handicap)
            # 如果是整数，使用整数格式；否则保留小数
            if handicap_value == int(handicap_value):
                formatted_handicap = str(int(handicap_value))
            else:
                formatted_handicap = str(handicap_value)
        except (ValueError, TypeError):
            # 如果无法转换为数值，直接使用字符串，但移除+号
            formatted_handicap = str(handicap).replace('+', '')

        # 构建oddsId
        odds_id = f"{event_id}|{api_period_num}|{bet_type}|{team_side_code}|{additional_value}|{formatted_handicap}"

        # 根据API文档，正确的selectionId格式是:
        # lineId | eventId | periodNum | betType | team_side_code | additional_value | handicap | selection_direction
        selection_id = f"{line_id}|{event_id}|{api_period_num}|{bet_type}|{team_side_code}|{additional_value}|{formatted_handicap}|{selection_direction}"
        
        # 记录构建的ID信息
        logger.info(f"构建投注ID: odds_id={odds_id}, selection_id={selection_id}")
        
        data = {
            "oddsSelections": [
                {
                    "oddsFormat": 1,  # 欧洲盘
                    "oddsId": odds_id,
                    "oddsSelectionsType": "NORMAL",
                    "selectionId": selection_id
                }
            ]
        }
        
        # 记录完整的请求数据
        logger.debug(f"投注表单请求URL: {url}")
        logger.info(f"投注表单请求参数: {params}")
        logger.info(f"投注表单请求数据: {json.dumps(data, ensure_ascii=False)}")
        
        try:
            response = self.session.post(
                url, 
                params=params, 
                json=data, 
                headers=self.headers
            )
            
            # 记录响应信息
            logger.debug(f"投注表单响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                resp_data = response.json()
                # 记录响应数据
                logger.debug(f"投注表单响应数据: {json.dumps(resp_data, ensure_ascii=False)}")
                
                # 处理响应数据，确保返回格式正确
                if isinstance(resp_data, list) and len(resp_data) > 0:
                    # 检查返回的数据是否包含必要的投注信息
                    for bet_option in resp_data:
                        if isinstance(bet_option, dict):
                            # 确保包含必要的字段
                            if "selectionId" in bet_option and "oddsId" in bet_option and "odds" in bet_option:
                                logger.info(f"投注表单获取成功: {event_id}, 赔率: {bet_option.get('odds')}")
                                # 记录API返回的实际ID，用于调试
                                logger.info(f"API返回的selectionId: {bet_option.get('selectionId')}")
                                logger.info(f"API返回的oddsId: {bet_option.get('oddsId')}")
                                return True, resp_data
                    
                    # 如果没有找到有效的投注选项，但响应是列表，则尝试构建投注选项
                    if len(resp_data) > 0:
                        # 尝试从第一个元素提取信息
                        first_item = resp_data[0]
                        # 使用响应中的selectionId和oddsId，如果存在的话
                        constructed_option = {
                            "selectionId": first_item.get("selectionId", selection_id),
                            "oddsId": first_item.get("oddsId", odds_id),
                            "odds": first_item.get("odds") if isinstance(first_item, dict) and "odds" in first_item else 2.0
                        }
                        logger.info(f"从响应构建投注选项: {json.dumps(constructed_option, ensure_ascii=False)}")
                        return True, [constructed_option]
                
                # 如果响应不是列表或无法提取有效的投注选项，则尝试构建默认选项
                logger.warning(f"无法从响应中提取投注选项，构建默认选项")
                default_option = {
                    "selectionId": selection_id,
                    "oddsId": odds_id,
                    "odds": 2.0  # 默认赔率
                }
                return True, [default_option]
            elif response.status_code == 403:
                # 403错误表示会话失效，尝试重新登录
                error_msg = f"获取投注表单失败: HTTP状态码 {response.status_code}"
                try:
                    error_data = response.json()
                    error_msg += f", 错误信息: {json.dumps(error_data, ensure_ascii=False)}"
                except:
                    try:
                        error_msg += f", 响应内容: {response.text[:200]}"
                    except:
                        pass

                logger.warning(f"检测到会话失效(403)，尝试重新登录")
                self.clear_login_session()

                # 尝试重新登录
                if hasattr(self, '_last_username') and hasattr(self, '_last_password'):
                    logger.info("尝试重新登录以恢复会话")
                    success, message = self.login(self._last_username, self._last_password)
                    if success:
                        logger.info("重新登录成功，请重试投注操作")
                        return False, "会话已失效，已重新登录，请重试投注操作"
                    else:
                        logger.error(f"重新登录失败: {message}")
                        return False, f"会话失效且重新登录失败: {message}"
                else:
                    logger.error("会话失效但缺少登录凭据")
                    return False, "会话失效且缺少登录凭据，请手动重新登录"
            else:
                # 尝试获取更详细的错误信息
                error_msg = f"获取投注表单失败: HTTP状态码 {response.status_code}"
                try:
                    error_data = response.json()
                    error_msg += f", 错误信息: {json.dumps(error_data, ensure_ascii=False)}"
                except:
                    try:
                        error_msg += f", 响应内容: {response.text[:200]}"
                    except:
                        pass
                logger.error(error_msg)
                return False, error_msg
                
        except Exception as e:
            logger.error(f"获取投注表单异常: {str(e)}", exc_info=True)
            return False, f"获取投注表单异常: {str(e)}"
    
    def place_bet_with_retry(self, bet_info, stake):
        """下注投注（带自动重试）"""
        return self.auto_retry_with_relogin(self._place_bet_internal, bet_info, stake)

    def _place_bet_internal(self, bet_info, stake):
        """内部投注方法"""
        return self.place_bet(bet_info, stake)

    def place_bet(self, bet_info, stake):
        """下注投注"""
        # 检查并刷新会话
        self.check_and_refresh_session()

        url = f"{self.base_url}/bet-placement/buyV2"
        request_id = str(uuid.uuid4())
        params = {
            "uniqueRequestId": request_id,
            "locale": "zh_CN",
            "_": str(int(time.time() * 1000)),
            "withCredentials": "true"
        }
        
        # 从bet_info中提取投注信息
        # 确保使用API返回的原始ID
        if isinstance(bet_info, list) and len(bet_info) > 0:
            # 如果bet_info是列表，取第一个元素
            bet_option = bet_info[0]
            selection_id = bet_option.get("selectionId")
            odds_id = bet_option.get("oddsId")
            odds = bet_option.get("odds")
            logger.info(f"从列表中提取投注信息: selection_id={selection_id}, odds_id={odds_id}, odds={odds}, stake={stake}")
        else:
            # 如果bet_info是字典，直接使用
            selection_id = bet_info.get("selectionId")
            odds_id = bet_info.get("oddsId")
            odds = bet_info.get("odds")
            logger.info(f"从字典中提取投注信息: selection_id={selection_id}, odds_id={odds_id}, odds={odds}, stake={stake}")
        
        # 验证必要的投注信息是否存在
        if not selection_id or not odds_id or not odds:
            # 尝试从响应中提取更多信息
            logger.warning("投注信息不完整，尝试从响应中提取更多信息")
            if isinstance(bet_info, list) and len(bet_info) > 0:
                first_item = bet_info[0]
                # 检查是否有其他字段可以使用
                event_id = first_item.get("eventId") if isinstance(first_item, dict) else None
                line_id = first_item.get("lineId") if isinstance(first_item, dict) else None
                
                # 如果bet_info中包含bet_type和team_side信息，尝试重新构建ID
                bet_type = first_item.get("betType") if isinstance(first_item, dict) else None
                team_side = first_item.get("team_side") if isinstance(first_item, dict) else None
                handicap = first_item.get("handicap") if isinstance(first_item, dict) else None
                period_num = first_item.get("periodNum", 0) if isinstance(first_item, dict) else 0
                
                if event_id and line_id and bet_type is not None and handicap is not None:
                    # 设置team_side_code和selection_direction
                    team_side_code = 0
                    selection_direction = 0
                    
                    if bet_type == 1:  # 独赢盘
                        team_side_code = 0 if team_side == 'home' else 1
                        selection_direction = team_side_code
                    elif bet_type == 2:  # 让球盘
                        team_side_code = 0 if team_side == 'home' else 1
                        selection_direction = team_side_code
                        logger.info(f"让球盘重构ID - team_side: {team_side}, team_side_code: {team_side_code}, selection_direction: {selection_direction}")
                    elif bet_type == 3:  # 大小球盘
                        # 大小球盘的team_side_code: 3=大球, 4=小球
                        team_side_code = 3 if team_side == 'over' else 4
                        # 大小球盘的selection_direction: 0=大球, 1=小球
                        selection_direction = 0 if team_side == 'over' else 1
                        logger.info(f"大小球盘重构ID - team_side: {team_side}, team_side_code: {team_side_code}, selection_direction: {selection_direction}")
                    
                    # 附加值，通常为1表示附加盘口
                    additional_value = 1
                    
                    # 构建ID
                    odds_id = f"{event_id}|{period_num}|{bet_type}|{team_side_code}|{additional_value}|{handicap}"
                    selection_id = f"{line_id}|{event_id}|{period_num}|{bet_type}|{team_side_code}|{additional_value}|{handicap}|{selection_direction}"
                    
                    # 使用默认赔率
                    odds = first_item.get("odds", 2.0) if isinstance(first_item, dict) else 2.0
                    
                    logger.info(f"重新构建投注信息: selection_id={selection_id}, odds_id={odds_id}, odds={odds}")
                else:
                    logger.warning(f"缺少重构ID所需的关键信息: event_id={event_id}, line_id={line_id}, bet_type={bet_type}, handicap={handicap}")
            
            # 如果仍然缺少必要信息，则返回错误
            if not selection_id or not odds_id or not odds:
                error_msg = f"投注信息不完整: selection_id={selection_id}, odds_id={odds_id}, odds={odds}"
                logger.error(error_msg)
                return False, error_msg
        
        # 创建投注请求
        unique_bet_id = str(uuid.uuid4())
        data = {
            "acceptBetterOdds": False,
            "oddsFormat": 1,
            "selections": [
                {
                    "odds": odds,
                    "oddsId": odds_id,
                    "selectionId": selection_id,
                    "stake": stake,
                    "uniqueRequestId": unique_bet_id,
                    "wagerType": "NORMAL",
                    "winRiskStake": "RISK",  # 使用RISK而不是BASE，与成功请求保持一致
                    "betLocationTracking": {
                        "view": "NEW_ASIAN_VIEW",  # 更新为与成功请求一致的值
                        "navigation": "SPORTS",
                        "device": "DESKTOP",  # 更改为DESKTOP
                        "displayMode": "LIGHT",
                        "mainPages": "SPORT",
                        "marketTab": "EARLY",
                        "market": "MATCHES",
                        "oddsContainerTitle": "TODAY",  # 更新为与成功请求一致的值
                        "oddsContainerCategory": "MAIN",
                        "reuseSelection": False,
                        "language": "zh_CN",
                        "marketType": "ALL",  # 更新为与成功请求一致的值
                        "eventSorting": "TIME",
                        "pageType": "DOUBLE",
                        "defaultPage": "TODAY",
                        "timeZone": "Asia/Shanghai",
                        "isLiveStreamPlaying": None
                    }
                }
            ]
        }
        
        # 记录完整的请求数据
        logger.debug(f"投注请求URL: {url}")
        logger.debug(f"投注请求参数: {params}")
        logger.debug(f"投注请求数据: {json.dumps(data, ensure_ascii=False)}")
        
        try:
            response = self.session.post(
                url, 
                params=params, 
                json=data, 
                headers=self.headers
            )
            
            # 记录响应信息
            logger.debug(f"投注响应状态码: {response.status_code}")
            logger.debug(f"投注响应头: {response.headers}")
            
            if response.status_code == 200:
                resp_data = response.json()
                logger.info(f"投注响应数据: {json.dumps(resp_data, ensure_ascii=False)}")
                
                # 检查投注是否被接受
                if resp_data.get("response") and len(resp_data["response"]) > 0:
                    bet_response = resp_data["response"][0]
                    if bet_response.get("status") == "ACCEPTED":
                        wager_id = bet_response.get("wagerId")
                        bet_id = bet_response.get("betId")
                        logger.info(f"投注成功: 注单ID {wager_id}, 投注ID {bet_id}")
                        return True, resp_data
                    else:
                        error_code = bet_response.get("errorCode")
                        error_msg = f"投注被拒绝: 状态={bet_response.get('status')}, 错误代码={error_code}"
                        logger.error(error_msg)
                        return False, error_msg
                else:
                    logger.error(f"投注响应格式不正确: {resp_data}")
                    return False, resp_data
            elif response.status_code == 403:
                # 403错误表示会话失效，尝试重新登录
                error_msg = f"投注请求失败: HTTP状态码 {response.status_code}"
                try:
                    error_data = response.json()
                    error_msg += f", 错误信息: {json.dumps(error_data, ensure_ascii=False)}"
                except:
                    try:
                        error_msg += f", 响应内容: {response.text[:200]}"
                    except:
                        pass

                logger.warning(f"检测到投注会话失效(403)，尝试重新登录")
                self.clear_login_session()

                # 尝试重新登录
                if hasattr(self, '_last_username') and hasattr(self, '_last_password'):
                    logger.info("尝试重新登录以恢复投注会话")
                    success, message = self.login(self._last_username, self._last_password)
                    if success:
                        logger.info("重新登录成功，请重试投注操作")
                        return False, "投注会话已失效，已重新登录，请重试投注操作"
                    else:
                        logger.error(f"重新登录失败: {message}")
                        return False, f"投注会话失效且重新登录失败: {message}"
                else:
                    logger.error("投注会话失效但缺少登录凭据")
                    return False, "投注会话失效且缺少登录凭据，请手动重新登录"
            else:
                # 尝试获取更详细的错误信息
                error_msg = f"投注请求失败: HTTP状态码 {response.status_code}"
                try:
                    error_data = response.json()
                    error_msg += f", 错误信息: {json.dumps(error_data, ensure_ascii=False)}"
                except:
                    try:
                        error_msg += f", 响应内容: {response.text[:200]}"
                    except:
                        pass
                logger.error(error_msg)
                return False, error_msg
                
        except Exception as e:
            logger.error(f"投注异常: {str(e)}", exc_info=True)
            return False, f"投注异常: {str(e)}"
    
    def parse_matches(self, matches_data):
        """解析比赛数据"""
        result = []
        
        try:
            # 检查数据结构
            if not matches_data:
                return []
                
            # 检查是否是单场比赛的更多盘口数据
            if 'e' in matches_data and isinstance(matches_data['e'], list) and len(matches_data['e']) > 0:
                logger.info("检测到单场比赛的更多盘口数据格式")
                event_data = matches_data['e'][0]
                
                # 检查数据格式是否正确
                if len(event_data) >= 3 and isinstance(event_data[2], list) and len(event_data[2]) >= 1:
                    match_data = event_data[2][0]
                    
                    # 提取基本信息
                    event_id = match_data[0] if len(match_data) > 0 else None
                    home_team = match_data[1] if len(match_data) > 1 else "未知主队"
                    away_team = match_data[2] if len(match_data) > 2 else "未知客队"
                    event_time = match_data[4] if len(match_data) > 4 else 0
                    
                    match_info = {
                        'event_id': event_id,
                        'home_team': home_team,
                        'away_team': away_team,
                        'event_time': event_time,
                        'league_id': 0,
                        'league_name': "未知联赛",
                        'markets': {}
                    }
                    
                    # 解析盘口数据
                    if len(match_data) > 8 and isinstance(match_data[8], dict):
                        markets = match_data[8]
                        
                        # 全场盘口
                        if '0' in markets and len(markets['0']) > 0:
                            market_data = markets['0']
                            
                            # 让分盘 - 存储为数组
                            handicap_markets = []
                            if len(market_data) > 0 and isinstance(market_data[0], list):
                                for handicap_market in market_data[0]:
                                    if isinstance(handicap_market, list) and len(handicap_market) > 5:
                                        handicap_markets.append(handicap_market)
                                        logger.debug(f"解析到让球盘口: {handicap_market}")
                            
                            if handicap_markets:
                                match_info['markets']['handicap'] = handicap_markets
                                logger.info(f"解析到 {len(handicap_markets)} 个让球盘口")
                            
                            # 大小盘 - 存储为数组
                            totals_markets = []
                            if len(market_data) > 1 and isinstance(market_data[1], list):
                                for total_market in market_data[1]:
                                    if isinstance(total_market, list) and len(total_market) > 4:
                                        totals_markets.append(total_market)
                                        logger.debug(f"解析到大小球盘口: {total_market}")
                            
                            if totals_markets:
                                match_info['markets']['totals'] = totals_markets
                                logger.info(f"解析到 {len(totals_markets)} 个大小球盘口")
                    
                    result.append(match_info)
                    return result
            
            # 常规比赛数据格式处理
            if 'n' not in matches_data and 'l' not in matches_data:
                logger.warning("数据结构不包含 'n' 或 'l' 字段")
                return []
            
            # 解析数据
            for sport_type in ['l', 'n']:  # l为滚球, n为早盘/今日
                if sport_type not in matches_data or not matches_data[sport_type]:
                    continue
                
                sports = matches_data[sport_type]
                
                # 遍历体育类型
                for sport in sports:
                    if len(sport) < 3 or sport[0] != 4:  # 4代表篮球
                        continue
                    
                    # 遍历联赛
                    for league in sport[2]:
                        league_id = league[0]
                        league_name = league[1]
                        
                        # 遍历比赛
                        for match in league[2]:
                            match_info = {
                                'event_id': match[0],
                                'home_team': match[1],
                                'away_team': match[2],
                                'event_time': match[4],
                                'league_id': league_id,
                                'league_name': league_name,
                                'markets': {}
                            }
                            
                            # 解析盘口数据
                            if len(match) > 8 and isinstance(match[8], dict):
                                markets = match[8]
                                
                                # 全场盘口
                                if '0' in markets and len(markets['0']) > 0:
                                    market_data = markets['0']
                                    
                                    # 让分盘 - 存储为数组
                                    handicap_markets = []
                                    if len(market_data) > 0 and isinstance(market_data[0], list):
                                        for handicap_market in market_data[0]:
                                            if isinstance(handicap_market, list) and len(handicap_market) > 5:
                                                handicap_markets.append(handicap_market)
                                    
                                    if handicap_markets:
                                        match_info['markets']['handicap'] = handicap_markets
                                    
                                    # 大小盘 - 存储为数组
                                    totals_markets = []
                                    if len(market_data) > 1 and isinstance(market_data[1], list):
                                        for total_market in market_data[1]:
                                            if isinstance(total_market, list) and len(total_market) > 4:
                                                totals_markets.append(total_market)
                                    
                                    if totals_markets:
                                        match_info['markets']['totals'] = totals_markets
                                
                                # 上半场盘口
                                if '1' in markets and len(markets['1']) > 0:
                                    market_data = markets['1']
                                    
                                    # 让分盘 - 存储为数组
                                    first_half_handicap_markets = []
                                    if len(market_data) > 0 and isinstance(market_data[0], list):
                                        for handicap_market in market_data[0]:
                                            if isinstance(handicap_market, list) and len(handicap_market) > 5:
                                                first_half_handicap_markets.append(handicap_market)
                                    
                                    if first_half_handicap_markets:
                                        match_info['markets']['first_half_handicap'] = first_half_handicap_markets
                                    
                                    # 大小盘 - 存储为数组
                                    first_half_totals_markets = []
                                    if len(market_data) > 1 and isinstance(market_data[1], list):
                                        for total_market in market_data[1]:
                                            if isinstance(total_market, list) and len(total_market) > 4:
                                                first_half_totals_markets.append(total_market)
                                    
                                    if first_half_totals_markets:
                                        match_info['markets']['first_half_totals'] = first_half_totals_markets
                            
                            result.append(match_info)
                            
                            # 打印日志以便调试
                            if len(result) <= 3:  # 只打印前3个比赛的市场信息
                                logger.debug(f"比赛: {match_info['home_team']} vs {match_info['away_team']}")
                                for market_type, markets_list in match_info['markets'].items():
                                    if isinstance(markets_list, list):
                                        logger.debug(f"  {market_type} 盘口数量: {len(markets_list)}")
                                        for i, market in enumerate(markets_list[:3]):  # 只打印前3个盘口
                                            logger.debug(f"    盘口{i+1}: {market}")
        
        except Exception as e:
            logger.error(f"解析比赛数据异常: {str(e)}", exc_info=True)
        
        return result
    
    def get_pending_wagers_with_retry(self):
        """查询未结算注单（带自动重试）"""
        return self.auto_retry_with_relogin(self._get_pending_wagers_internal)

    def _get_pending_wagers_internal(self):
        """内部查询方法"""
        return self.get_pending_wagers()

    def get_pending_wagers(self):
        """查询未结算注单"""
        # 检查并刷新会话
        self.check_and_refresh_session()

        # 不检查登录状态，直接尝试调用API，让API响应来判断是否需要登录
        logger.info("开始查询未结算注单（跳过登录状态检查）")
            
        logger.info("开始查询未结算注单")
        print("\n正在查询未结算注单...")
        
        # 检查session和headers
        logger.debug(f"Session cookies: {self.session.cookies.get_dict()}")
        logger.debug(f"Headers: {self.headers}")
        
        # 使用正确的API端点和参数
        url = f"{self.base_url}/member-service/v2/unified-account/wager-filter-all"
        params = {
            "betTypes": "ALL",
            "byDateType": "ACCEPTED_DATE",
            "fromDate": (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d %H:%M:%S"),
            "toDate": (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S"),
            "pageNumber": "1",
            "pageSize": "20",
            "pendingFilterDateType": "ALL",
            "pendingStatus": "ALL",
            "product": "ALL",
            "sortByType": "ACCEPTED_DATE",
            "locale": "zh_CN",
            "_": str(int(time.time() * 1000)),
            "withCredentials": "true"
        }
        
        # 请求参数
        data = {"SB": None, "BB": None}
        
        try:
            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求参数: {params}")
            logger.debug(f"请求数据: {data}")
            
            response = self.session.post(
                url, 
                params=params,
                json=data,
                headers=self.headers
            )
            
            logger.debug(f"API响应状态码: {response.status_code}")
            
            # 记录响应内容以便调试
            response_text = response.text
            logger.debug(f"API响应内容前100个字符: {response_text[:100] if response_text else '空响应'}")
            
            if response.status_code == 200:
                if not response_text:
                    logger.error("API返回空响应")
                    return False, "API返回空响应"

                try:
                    wagers_data = response.json()
                    logger.info(f"成功获取未结算注单数据")

                    # 处理和过滤投注记录，添加状态索引18和投注确认OPEN字段
                    filtered_wagers = self._filter_pending_wagers(wagers_data)

                    return True, filtered_wagers
                except json.JSONDecodeError as je:
                    logger.error(f"JSON解析错误: {str(je)}", exc_info=True)
                    return False, f"JSON解析错误: {str(je)}"
            elif response.status_code == 403:
                logger.warning("获取未结算注单失败: 权限不足，检测到会话失效")

                # 尝试解析错误响应
                error_msg = "会话已过期"
                try:
                    error_data = response.json()
                    if error_data.get("error") == 403:
                        error_msg = "会话已过期，需要重新登录"
                except:
                    pass

                # 清除会话并尝试重新登录
                self.clear_login_session()

                if hasattr(self, '_last_username') and hasattr(self, '_last_password'):
                    logger.info("尝试重新登录以恢复查询会话")
                    success, message = self.login(self._last_username, self._last_password)
                    if success:
                        logger.info("重新登录成功，请重试查询操作")
                        return False, "查询会话已失效，已重新登录，请重试查询操作"
                    else:
                        logger.error(f"重新登录失败: {message}")
                        return False, f"查询会话失效且重新登录失败: {message}"
                else:
                    logger.error("查询会话失效但缺少登录凭据")
                    return False, "查询会话失效且缺少登录凭据，请手动重新登录"
            elif response.status_code == 401:
                logger.error("获取未结算注单失败: 未授权，需要重新登录")
                return False, "未授权，请重新登录"
            else:
                logger.error(f"获取未结算注单失败: HTTP状态码 {response.status_code}")
                return False, f"HTTP错误: {response.status_code}, 响应: {response_text[:200]}"
        except Exception as e:
            logger.error(f"获取未结算注单异常: {str(e)}", exc_info=True)
            return False, f"异常: {str(e)}"

    def _filter_pending_wagers(self, wagers_data):
        """
        过滤和处理未结算注单数据

        参数:
            wagers_data: API返回的原始注单数据

        返回:
            处理后的注单数据
        """
        try:
            if not isinstance(wagers_data, dict):
                logger.warning(f"注单数据格式错误: {type(wagers_data)}")
                return wagers_data

            # 获取page信息
            page_info = wagers_data.get('page', {})
            records = page_info.get('records', [])

            if not records:
                logger.info("没有未结算注单记录")
                return wagers_data

            # 显示所有注单（移除OPEN状态过滤）
            filtered_records = []
            for record in records:
                try:
                    # 检查记录格式
                    if not isinstance(record, list) or len(record) < 19:
                        logger.debug(f"跳过格式不正确的记录: {record}")
                        continue

                    # 保留所有格式正确的注单记录
                    filtered_records.append(record)

                    # 记录状态信息用于调试
                    status = record[18] if len(record) > 18 else "unknown"
                    logger.debug(f"保留注单记录: {record[0] if record else 'unknown'}, 状态: {status}")

                except Exception as e:
                    logger.warning(f"处理单条记录时出错: {e}")
                    continue

            # 更新处理后的记录
            page_info['records'] = filtered_records
            page_info['totalRecords'] = len(filtered_records)
            wagers_data['page'] = page_info

            logger.info(f"处理完成: 原始记录 {len(records)} 条，处理后 {len(filtered_records)} 条记录")

            return wagers_data

        except Exception as e:
            logger.error(f"过滤未结算注单数据时出错: {e}")
            return wagers_data

    def get_match_more_markets(self, event_id):
        """获取单场比赛的更多盘口数据"""
        url = f"{self.base_url}/sports-service/sv/am/events"
        params = {
            "_g": "0",
            "btg": "1",
            "cl": "1",
            "g": "QQ==",
            "me": str(event_id),
            "mk": "3",
            "more": "true",  # 获取更多盘口
            "ot": "1",
            "pa": "0",
            "pimo": "0,1,2",
            "v": "0",
            "wm": "ld",
            "locale": "zh_CN",
            "_": str(int(time.time() * 1000)),
            "withCredentials": "true"
        }
        
        try:
            logger.info(f"获取比赛 {event_id} 的更多盘口数据")
            response = self.session.get(url, params=params, headers=self.headers)
            
            if response.status_code == 200:
                resp_data = response.json()
                logger.info(f"比赛 {event_id} 的更多盘口数据获取成功")
                return True, resp_data
            else:
                logger.error(f"获取比赛 {event_id} 的更多盘口数据失败: HTTP状态码 {response.status_code}")
                return False, f"获取更多盘口数据失败: HTTP状态码 {response.status_code}"
                
        except Exception as e:
            logger.error(f"获取比赛 {event_id} 的更多盘口数据异常: {str(e)}")
            return False, f"获取更多盘口数据异常: {str(e)}"

class PinBetSystem:
    """平博投注系统"""
    
    def __init__(self):
        self.api = PinBetAPI()
        self.is_logged_in = False
        self.today_matches = []
        self.early_matches = []
        self.arbitrage_opportunities = []
        self.bet_record_file = os.path.join("data", f"pinbet_records_{datetime.now().strftime('%Y-%m-%d')}.json")
        # 确保data目录存在
        os.makedirs(os.path.dirname(self.bet_record_file), exist_ok=True)
    
    def login(self, username=None, password=None):
        """登录系统"""
        if not username or not password:
            username = input("请输入用户名: ")
            password = input("请输入密码: ")
        
        success, result = self.api.login(username, password)
        if success:
            self.is_logged_in = True

            # 获取账户余额并验证登录状态
            balance_success, balance_data = self.api.get_balance()
            if balance_success and isinstance(balance_data, dict):
                balance_amount = balance_data.get('betCredit', 0)
                currency = balance_data.get('currency', 'CNY')
                print(f"账户余额: {balance_amount} {currency}")
                if balance_amount == 0:
                    print("错误: 账户余额为0，表示登录失败或会话无效")
                    print("尝试清除会话并重新登录...")
                    # 余额为0表示登录失败，清除会话并重新登录
                    self.api.clear_login_session()

                    # 强制重新登录
                    success, result = self.api.login(username, password)
                    if success:
                        # 再次验证余额
                        balance_success, balance_data = self.api.get_balance()
                        if balance_success and isinstance(balance_data, dict):
                            balance_amount = balance_data.get('betCredit', 0)
                            if balance_amount > 0:
                                print(f"重新登录成功，账户余额: {balance_amount} {currency}")
                                self.is_logged_in = True
                            else:
                                print("重新登录后余额仍为0，登录失败")
                                self.is_logged_in = False
                        else:
                            print("重新登录后无法获取余额")
                            self.is_logged_in = False
                    else:
                        print(f"重新登录失败: {result}")
                        self.is_logged_in = False
                    return False
                else:
                    # 只有余额大于0时才认为登录成功
                    self.is_logged_in = True
                    return True
            else:
                print("错误: 无法获取账户余额，登录失败")
                self.is_logged_in = False
                return False
        else:
            print(f"登录失败: {result}")

        return success
    
    def get_account_balance(self):
        """获取账户余额"""
        if not self.is_logged_in:
            print("请先登录系统")
            return False
        
        success, balance_data = self.api.get_balance()
        if success and isinstance(balance_data, dict):
            print(f"\n===== 账户余额 =====")
            print(f"余额: {balance_data.get('betCredit', 0)} {balance_data.get('currency', 'CNY')}")
            return True
        else:
            print("获取账户余额失败")
            return False
    
    def get_today_matches(self):
        """获取今日篮球比赛"""
        if not self.is_logged_in:
            print("请先登录系统")
            return False
        
        print("\n正在获取今日篮球比赛...")
        success, matches_data = self.api.get_today_matches()
        
        if success:
            self.today_matches = self.api.parse_matches(matches_data)
            self.display_matches(self.today_matches)
            return True
        else:
            print("获取今日篮球比赛失败")
            return False
    
    def get_early_matches(self):
        """获取早场篮球比赛"""
        if not self.is_logged_in:
            print("请先登录系统")
            return False
        
        print("\n正在获取早场篮球比赛...")
        success, matches_data = self.api.get_early_matches()
        
        if success:
            self.early_matches = self.api.parse_matches(matches_data)
            self.display_matches(self.early_matches)
            return True
        else:
            print("获取早场篮球比赛失败")
            return False
    
    def display_matches(self, matches):
        """显示比赛列表"""
        if not matches:
            print("没有找到比赛数据")
            return
        
        print(f"\n===== 找到 {len(matches)} 场比赛 =====")
        print(f"{'序号':<5}{'联赛':<20}{'主队':<30}{'客队':<30}{'时间':<20}")
        print("-" * 100)
        
        for idx, match in enumerate(matches):
            event_time = datetime.fromtimestamp(match['event_time']/1000).strftime('%Y-%m-%d %H:%M')
            print(f"{idx+1:<5}{match['league_name'][:18]:<20}{match['home_team'][:28]:<30}{match['away_team'][:28]:<30}{event_time:<20}")
    
    def place_bet(self):
        """投注下单"""
        if not self.is_logged_in:
            print("请先登录系统")
            return False
        
        # 确保有比赛数据
        if not self.today_matches and not self.early_matches:
            print("请先获取比赛数据")
            return False
        
        matches = self.today_matches if self.today_matches else self.early_matches
        self.display_matches(matches)
        
        try:
            match_idx = int(input("\n请选择要投注的比赛序号: ")) - 1
            if match_idx < 0 or match_idx >= len(matches):
                print("无效的比赛序号")
                return False
                
            match = matches[match_idx]
            
            print("\n请选择投注盘口类型:")
            print("1. 全场让分盘")
            print("2. 全场大小盘")
            print("3. 上半场让分盘")
            print("0. 取消")
            
            market_choice = int(input("\n请选择: "))
            if market_choice == 0:
                return False
                
            if market_choice == 1 and 'handicap' in match['markets']:
                handicap_markets = match['markets']['handicap']
                
                # 显示所有可用的让球盘口
                print(f"\n全场让分盘:")
                for idx, market in enumerate(handicap_markets):
                    if len(market) > 4:
                        handicap = market[2]
                        home_odds = market[3]
                        away_odds = market[4]
                        # 显示更清楚的盘口含义：主队让分，客队受让
                        print(f"{idx+1}. 主队 -{handicap} (赔率: {home_odds}) / 客队 +{handicap} (赔率: {away_odds})")
                
                # 让用户选择具体的盘口
                market_idx = int(input("\n请选择盘口序号: ")) - 1
                if market_idx < 0 or market_idx >= len(handicap_markets):
                    print("无效的盘口序号")
                    return False
                
                selected_market = handicap_markets[market_idx]
                handicap = selected_market[2]
                home_odds = selected_market[3]
                away_odds = selected_market[4]
                line_id = selected_market[7] if len(selected_market) > 7 else None
                
                print(f"\n您选择了让分盘: {handicap}")
                print(f"主队 {match['home_team']}: {home_odds}")
                print(f"客队 {match['away_team']}: {away_odds}")
                
                side_choice = int(input("请选择投注方向 (1:主队, 2:客队): "))
                if side_choice not in [1, 2]:
                    print("无效的投注方向")
                    return False
                    
                team_side = 'home' if side_choice == 1 else 'away'
                
                stake = float(input("请输入投注金额: "))
                if stake <= 0:
                    print("无效的投注金额")
                    return False
                
                print(f"\n确认投注信息:")
                print(f"比赛: {match['home_team']} vs {match['away_team']}")
                print(f"盘口: 让分盘 {handicap}")
                print(f"投注: {'主队' if side_choice == 1 else '客队'}")
                print(f"赔率: {home_odds if side_choice == 1 else away_odds}")
                print(f"金额: {stake}")
                
                confirm = input("\n确认投注? (y/n): ")
                if confirm.lower() != 'y':
                    print("已取消投注")
                    return False
                
                # 获取投注表单
                success, form_data = self.api.get_bet_form(
                    match['event_id'],
                    line_id,
                    0,  # period_num
                    2,  # bet_type=2 为让分盘（根据正确格式修正）
                    team_side,
                    handicap
                )
                
                if not success:
                    logger.error(f"获取投注表单失败: {form_data}")
                    print(f"获取投注表单失败: {form_data}")
                    return False
                
                # 记录投注表单数据
                logger.debug(f"投注表单数据: {json.dumps(form_data, ensure_ascii=False)}")
                
                # 检查投注表单数据格式
                if not form_data or not isinstance(form_data, list) or len(form_data) == 0:
                    logger.error(f"投注表单数据格式错误: {form_data}")
                    print("投注表单数据格式错误，无法执行投注")
                    return False
                
                # 提取第一个投注选项
                bet_option = form_data[0]
                if not isinstance(bet_option, dict):
                    logger.error(f"投注选项数据格式错误: {bet_option}")
                    print("投注选项数据格式错误，无法执行投注")
                    return False
                
                # 执行投注
                success, result = self.api.place_bet(bet_option, stake)
                
                if success:
                    # 注意：开赛时间现在由前端显示时实时获取，不在投注时保存
                    print("投注成功!")
                    return True
                else:
                    print(f"投注失败: {result}")
                    return False
                
            elif market_choice == 2 and 'totals' in match['markets']:
                totals_markets = match['markets']['totals']
                
                # 显示所有可用的大小球盘口
                print(f"\n全场大小盘:")
                for idx, market in enumerate(totals_markets):
                    if len(market) > 3:
                        total = market[2]
                        over_odds = market[3]
                        under_odds = market[4]
                        print(f"{idx+1}. {total} - 大球: {over_odds}, 小球: {under_odds}")
                
                # 让用户选择具体的盘口
                market_idx = int(input("\n请选择盘口序号: ")) - 1
                if market_idx < 0 or market_idx >= len(totals_markets):
                    print("无效的盘口序号")
                    return False
                
                selected_market = totals_markets[market_idx]
                total = selected_market[2]
                over_odds = selected_market[3]
                under_odds = selected_market[4]
                line_id = selected_market[7] if len(selected_market) > 7 else None
                
                print(f"\n您选择了大小盘: {total}")
                print(f"大球: {over_odds}")
                print(f"小球: {under_odds}")
                
                side_choice = int(input("请选择投注方向 (1:大球, 2:小球): "))
                if side_choice not in [1, 2]:
                    print("无效的投注方向")
                    return False
                    
                team_side = 'over' if side_choice == 1 else 'under'
                
                stake = float(input("请输入投注金额: "))
                if stake <= 0:
                    print("无效的投注金额")
                    return False
                
                print(f"\n确认投注信息:")
                print(f"比赛: {match['home_team']} vs {match['away_team']}")
                print(f"盘口: 大小盘 {total}")
                print(f"投注: {'大球' if side_choice == 1 else '小球'}")
                print(f"赔率: {over_odds if side_choice == 1 else under_odds}")
                print(f"金额: {stake}")
                
                confirm = input("\n确认投注? (y/n): ")
                if confirm.lower() != 'y':
                    print("已取消投注")
                    return False
                
                # 获取投注表单
                success, form_data = self.api.get_bet_form(
                    match['event_id'],
                    line_id,
                    0,  # period_num
                    3,  # bet_type=3 为大小盘
                    team_side,
                    total
                )
                
                if not success:
                    logger.error(f"获取投注表单失败: {form_data}")
                    print(f"获取投注表单失败: {form_data}")
                    return False
                
                # 记录投注表单数据
                logger.debug(f"投注表单数据: {json.dumps(form_data, ensure_ascii=False)}")
                
                # 检查投注表单数据格式
                if not form_data or not isinstance(form_data, list) or len(form_data) == 0:
                    logger.error(f"投注表单数据格式错误: {form_data}")
                    print("投注表单数据格式错误，无法执行投注")
                    return False
                
                # 提取第一个投注选项
                bet_option = form_data[0]
                if not isinstance(bet_option, dict):
                    logger.error(f"投注选项数据格式错误: {bet_option}")
                    print("投注选项数据格式错误，无法执行投注")
                    return False
                
                # 执行投注
                success, result = self.api.place_bet(bet_option, stake)
                
                if success:
                    print("投注成功!")
                    return True
                else:
                    print(f"投注失败: {result}")
                    return False
                
            elif market_choice == 3 and 'first_half_handicap' in match['markets']:
                first_half_handicap_markets = match['markets']['first_half_handicap']
                
                # 显示所有可用的上半场让球盘口
                print(f"\n上半场让分盘:")
                for idx, market in enumerate(first_half_handicap_markets):
                    if len(market) > 4:
                        handicap = market[2]
                        home_odds = market[3]
                        away_odds = market[4]
                        # 显示更清楚的盘口含义：主队让分，客队受让
                        print(f"{idx+1}. 主队 -{handicap} (赔率: {home_odds}) / 客队 +{handicap} (赔率: {away_odds})")
                
                # 让用户选择具体的盘口
                market_idx = int(input("\n请选择盘口序号: ")) - 1
                if market_idx < 0 or market_idx >= len(first_half_handicap_markets):
                    print("无效的盘口序号")
                    return False
                
                selected_market = first_half_handicap_markets[market_idx]
                handicap = selected_market[2]
                home_odds = selected_market[3]
                away_odds = selected_market[4]
                line_id = selected_market[7] if len(selected_market) > 7 else None
                
                print(f"\n您选择了上半场让分盘: {handicap}")
                print(f"主队 {match['home_team']}: {home_odds}")
                print(f"客队 {match['away_team']}: {away_odds}")
                
                side_choice = int(input("请选择投注方向 (1:主队, 2:客队): "))
                if side_choice not in [1, 2]:
                    print("无效的投注方向")
                    return False
                    
                team_side = 'home' if side_choice == 1 else 'away'
                
                stake = float(input("请输入投注金额: "))
                if stake <= 0:
                    print("无效的投注金额")
                    return False
                
                print(f"\n确认投注信息:")
                print(f"比赛: {match['home_team']} vs {match['away_team']}")
                print(f"盘口: 上半场让分盘 {handicap}")
                print(f"投注: {'主队' if side_choice == 1 else '客队'}")
                print(f"赔率: {home_odds if side_choice == 1 else away_odds}")
                print(f"金额: {stake}")
                
                confirm = input("\n确认投注? (y/n): ")
                if confirm.lower() != 'y':
                    print("已取消投注")
                    return False
                
                # 获取投注表单
                success, form_data = self.api.get_bet_form(
                    match['event_id'],
                    line_id,
                    1,  # period_num=1 为上半场
                    2,  # bet_type=2 为让分盘（根据正确格式修正）
                    team_side,
                    handicap
                )
                
                if not success:
                    logger.error(f"获取投注表单失败: {form_data}")
                    print(f"获取投注表单失败: {form_data}")
                    return False
                
                # 记录投注表单数据
                logger.debug(f"投注表单数据: {json.dumps(form_data, ensure_ascii=False)}")
                
                # 检查投注表单数据格式
                if not form_data or not isinstance(form_data, list) or len(form_data) == 0:
                    logger.error(f"投注表单数据格式错误: {form_data}")
                    print("投注表单数据格式错误，无法执行投注")
                    return False
                
                # 提取第一个投注选项
                bet_option = form_data[0]
                if not isinstance(bet_option, dict):
                    logger.error(f"投注选项数据格式错误: {bet_option}")
                    print("投注选项数据格式错误，无法执行投注")
                    return False
                
                # 执行投注
                success, result = self.api.place_bet(bet_option, stake)
                
                if success:
                    print("投注成功!")
                    return True
                else:
                    print(f"投注失败: {result}")
                    return False
            else:
                print("\n所选盘口不可用")
                return False
            
        except Exception as e:
            print(f"投注过程中出现错误: {str(e)}")
            return False
    
    def process_arbitrage(self):
        """处理套利数据并自动投注"""
        if not self.is_logged_in:
            print("请先登录系统")
            return False
        
        # 实现套利逻辑
        # 这里需要集成皇冠系统数据，进行套利计算
        print("\n===== 套利计算 =====")
        print("功能开发中...")
        print("需要整合皇冠数据进行套利计算")
        
        # 模拟一些套利机会
        self.arbitrage_opportunities = [
            {
                "match": "塔拉纳基山 vs 南国鲨鱼",
                "market": "让分盘",
                "pin_odds": 1.869,
                "hg_odds": 2.05,
                "arbitrage_profit": "3.2%"
            },
            {
                "match": "霍巴特电光 vs 朗瑟士敦龙卷风",
                "market": "大小盘",
                "pin_odds": 1.840,
                "hg_odds": 2.10,
                "arbitrage_profit": "4.1%"
            }
        ]
        
        # 显示套利机会
        if self.arbitrage_opportunities:
            print("\n找到以下套利机会:")
            print(f"{'序号':<5}{'比赛':<40}{'盘口':<10}{'平博赔率':<10}{'皇冠赔率':<10}{'理论收益':<10}")
            print("-" * 90)
            
            for idx, opp in enumerate(self.arbitrage_opportunities):
                print(f"{idx+1:<5}{opp['match'][:38]:<40}{opp['market']:<10}{opp['pin_odds']:<10}{opp['hg_odds']:<10}{opp['arbitrage_profit']:<10}")
            
            try:
                choice = int(input("\n请选择要执行的套利机会 (0:取消): "))
                if choice == 0:
                    return False
                
                if choice < 1 or choice > len(self.arbitrage_opportunities):
                    print("无效选择")
                    return False
                
                print(f"\n您选择了: {self.arbitrage_opportunities[choice-1]['match']}")
                print("套利投注流程需要进一步开发...")
                return True
                
            except Exception as e:
                print(f"处理套利数据时出错: {str(e)}")
                return False
        else:
            print("\n未找到套利机会")
            return False

    def show_menu(self):
        """显示菜单并处理用户选择"""
        while True:
            print("\n===== 平博篮球投注系统 =====")
            print("1. 获取今日篮球比赛")
            print("2. 查询账户余额")
            print("3. 获取早场篮球比赛")
            print("4. 手动投注")
            print("5. 处理套利数据并投注")
            print("6. 查询未结算注单")
            print("0. 退出")
            
            try:
                choice = input("\n请输入您的选择: ")
                
                if choice == "1":
                    self.get_today_matches()
                elif choice == "2":
                    self.get_account_balance()
                elif choice == "3":
                    self.get_early_matches()
                elif choice == "4":
                    self.place_bet()
                elif choice == "5":
                    self.process_bet_data()
                elif choice == "6":
                    self.get_pending_wagers()
                elif choice == "0":
                    print("退出系统")
                    break
                else:
                    print("无效选择，请重新输入")
                    
            except Exception as e:
                print(f"操作出错: {str(e)}")

    def fetch_latest_arbs(self):
        """
        获取最新的套利数据
        
        返回:
            list: 套利数据列表，如果获取失败则返回空列表
        """
        try:
            from betburger_fetcher import fetch_and_save_data, process_betburger_data
            
            # 获取最新数据
            success, file_path = fetch_and_save_data()
            
            if not success:
                # 不打印日志，静默返回空列表
                return []
            
            # 加载JSON文件
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            except Exception as e:
                logger.error(f"读取套利数据文件出错: {e}")
                return []
            
            # 检查数据格式并处理
            if isinstance(data, list):
                # 数组格式: [{"bets": [...多个投注选项...], ...}, ...]
                arb_items = []
                
                for item in data:
                    if "bets" in item:
                        bets = item.get("bets", [])
                        if bets:
                            # 只提取平博的投注选项
                            for bet in bets:
                                bookmaker = bet.get("bookmaker", "")
                                if "平博" in bookmaker:
                                    arb_items.append(bet)
                
                # 只有当找到套利数据时才记录日志
                if arb_items:
                    logger.info(f"检测到数组格式的套利数据，共 {len(data)} 条，提取到 {len(arb_items)} 个平博投注选项")
                return arb_items
                
            elif isinstance(data, dict) and "bets" in data:
                # 字典格式: {"bets": [...多个投注选项...]}
                bets = data.get("bets", [])
                if not bets:
                    return []
                    
                # 处理套利数据
                matches = process_betburger_data(bets)
                
                if not matches:
                    return []
                
                # 提取所有平博投注选项
                arb_items = []
                for _, bet_options in matches.items():
                    for bet_option in bet_options:
                        bookmaker = bet_option.get("bookmaker", "")
                        if "平博" in bookmaker:
                            arb_items.append(bet_option)
                
                # 只有当找到套利数据时才记录日志
                if arb_items:
                    logger.info(f"成功加载最新套利数据，共 {len(bets)} 条，提取到 {len(arb_items)} 个平博投注选项")
                return arb_items
            else:
                logger.error(f"套利数据格式错误")
                return []
                
        except Exception as e:
            logger.error(f"获取最新套利数据时出错: {e}", exc_info=True)
            return []
    


    def _play_notification_sound(self):
        """播放提示音（使用 pygame）"""
        try:
            from betburger_fetcher import play_notification_sound
            play_notification_sound()
        except Exception as e:
            logger.error(f"播放提示音失败: {e}")

            # 如果导入失败，尝试直接播放
            try:
                sound_file = "alert.mp3"
                if os.path.exists(sound_file):
                    pygame.mixer.init()  # 初始化音频模块
                    pygame.mixer.music.load(sound_file)
                    pygame.mixer.music.play()
                    while pygame.mixer.music.get_busy():  # 等待播放完成
                        time.sleep(0.1)
            except Exception as e2:
                logger.error(f"直接播放提示音也失败: {e2}")

    def process_bet_data(self):
        """处理投注数据并执行自动投注"""
        if not self.is_logged_in:
            print("请先登录系统")
            return False
        
        print("\n===== 处理投注数据 =====")
        
        try:
            # 获取最新套利数据
            arb_data = self.fetch_latest_arbs()
            if not arb_data:
                print("未获取到套利数据，请稍后再试")
                return False
                
            print(f"获取到 {len(arb_data)} 条套利数据，开始分析...")
            
            # 处理每一条套利数据
            processed_count = 0
            valid_bets = []
            
            for item in arb_data:
                try:
                    # 解析投注信息
                    bet_info = self._parse_bet_info_from_data(item)
                    if not bet_info:
                        continue
                        
                    # 检查是否满足投注条件
                    if not self._should_bet_on_option(bet_info):
                        continue
                        
                    valid_bets.append(bet_info)
                    processed_count += 1
                    
                except Exception as e:
                    logger.error(f"处理套利项时出错: {str(e)}")
                    continue
            
            # 显示有效投注选项
            if valid_bets:
                print(f"\n找到 {len(valid_bets)} 个有效投注选项:")
                print(f"{'序号':<5}{'比赛':<40}{'投注类型':<10}{'投注选项':<10}{'盘口值':<10}{'赔率':<10}{'阶段':<10}")
                print("-" * 105)
                
                for idx, bet in enumerate(valid_bets):
                    home_team = bet.get('home_team', '未知')
                    away_team = bet.get('away_team', '未知')
                    match_name = f"{home_team} vs {away_team}"
                    bet_type = bet.get('bet_type', '未知')
                    bet_team = bet.get('bet_team', '未知')
                    handicap = bet.get('handicap_value', '0')
                    odds = bet.get('odds', '0')
                    period = bet.get('period', '全场')
                    
                    print(f"{idx+1:<5}{match_name[:38]:<40}{bet_type:<10}{bet_team[:8]:<10}{handicap:<10}{odds:<10}{period:<10}")
                
                # 询问是否执行投注
                while True:
                    try:
                        choice = input("\n请选择要投注的选项 (输入序号，0表示取消): ")
                        if choice == '0':
                            print("已取消投注")
                            return False
                            
                        choice_idx = int(choice) - 1
                        if choice_idx < 0 or choice_idx >= len(valid_bets):
                            print("无效选择，请重新输入")
                            continue
                            
                        selected_bet = valid_bets[choice_idx]
                        break
                    except ValueError:
                        print("请输入有效的数字")
                
                # 询问投注金额
                while True:
                    try:
                        stake_input = input(f"请输入投注金额 (默认8): ")
                        if not stake_input:
                            stake = 8.0
                            break
                            
                        stake = float(stake_input)
                        if stake <= 0:
                            print("投注金额必须大于0")
                            continue
                        break
                    except ValueError:
                        print("请输入有效的数字")
                
                # 显示投注确认信息
                period = selected_bet.get('period', '全场')
                print(f"\n准备投注: {selected_bet.get('home_team')} vs {selected_bet.get('away_team')}")
                print(f"投注类型: {selected_bet.get('bet_type')}, 选项: {selected_bet.get('bet_team')}")
                print(f"盘口值: {selected_bet.get('handicap_value')}, 赔率: {selected_bet.get('odds')}")
                print(f"比赛阶段: {period}")
                print(f"投注金额: {stake}")
                
                # 确认投注
                while True:
                    confirm = input("\n确认投注? (y/n): ")
                    if confirm.lower() in ['y', 'yes', '是', '确认']:
                        break
                    elif confirm.lower() in ['n', 'no', '否', '取消']:
                        print("已取消投注")
                        return False
                    else:
                        print("请输入 y 确认或 n 取消")
                
                # 实际执行投注
                print("\n正在执行投注...")
                success = self._execute_bet(selected_bet, stake)
                
                if success:
                    self._play_notification_sound()  # 播放提示音
                    
                    # 检查是否需要刷新账户余额
                    print("\n正在刷新账户余额...")
                    self.get_account_balance()
                    
                    return True
                else:
                    print("\n投注失败，请检查日志获取详细信息")
                    return False
                    
            else:
                print("\n未找到符合条件的投注选项")
                return False
                
        except Exception as e:
            print(f"处理投注数据时出错: {str(e)}")
            logger.error(f"处理投注数据时出错: {str(e)}", exc_info=True)
            return False

    def _execute_bet(self, bet_info, stake):
        """执行实际投注操作"""
        try:
            logger.info(f"开始执行投注: {bet_info.get('home_team')} vs {bet_info.get('away_team')}, {bet_info.get('bet_type')}={bet_info.get('bet_team')}")
            
            # 1. 在今日比赛中查找匹配的比赛
            if not self.today_matches:
                logger.info("获取今日比赛数据")
                success, matches_data = self.api.get_today_matches()
                if success:
                    self.today_matches = self.api.parse_matches(matches_data)
                else:
                    logger.error("获取今日比赛失败")
                    return False
            
            # 2. 在早场比赛中查找匹配的比赛
            if not self.early_matches:
                logger.info("获取早场比赛数据")
                success, matches_data = self.api.get_early_matches()
                if success:
                    self.early_matches = self.api.parse_matches(matches_data)
                else:
                    logger.error("获取早场比赛失败")
            
            # 3. 合并比赛列表
            all_matches = self.today_matches + self.early_matches
            if not all_matches:
                logger.error("没有找到任何比赛数据")
                return False
            
            # 4. 查找匹配的比赛
            target_home = bet_info.get('home_team')
            target_away = bet_info.get('away_team')
            target_bet_type = bet_info.get('bet_type')
            target_handicap = bet_info.get('handicap_value')
            target_team = bet_info.get('bet_team')
            
            # 标准化目标盘口值
            is_total_bet = (target_bet_type == '大小球')
            target_handicap = self._normalize_handicap(target_handicap, is_total=is_total_bet)
            
            logger.info(f"查找匹配比赛: {target_home} vs {target_away}, {target_bet_type}={target_team}, 盘口={target_handicap}")
            
            matched_match = None
            best_match_score = 0

            for match in all_matches:
                home_team = match.get('home_team', '')
                away_team = match.get('away_team', '')
                actual_league = match.get('league_name', '')

                # 使用更宽松的匹配逻辑，包含联赛信息以区分男女比赛
                target_league = bet_info.get("league", "")  # 从投注信息中获取目标联赛
                match_score = self._calculate_team_match_score(
                    target_home, target_away, home_team, away_team,
                    target_league=target_league, actual_league=actual_league
                )

                # 如果匹配分数足够高，选择这个比赛
                if match_score > best_match_score and match_score >= 0.3:  # 降低阈值到0.3
                    best_match_score = match_score
                    matched_match = match
                    logger.info(f"找到匹配比赛: {home_team} vs {away_team} (联赛: {actual_league}) (匹配分数: {match_score:.2f})")

            # 如果没有找到足够好的匹配，尝试更宽松的匹配
            if not matched_match:
                logger.info("尝试更宽松的比赛匹配...")
                for match in all_matches:
                    home_team = match.get('home_team', '')
                    away_team = match.get('away_team', '')
                    actual_league = match.get('league_name', '')

                    # 检查是否有任何关键词匹配，包含联赛信息以区分男女比赛
                    target_league = bet_info.get("league", "")  # 从投注信息中获取目标联赛
                    if self._loose_team_match(target_home, target_away, home_team, away_team,
                                            target_league=target_league, actual_league=actual_league):
                        matched_match = match
                        logger.info(f"宽松匹配找到比赛: {home_team} vs {away_team} (联赛: {actual_league})")
                        break
            
            if not matched_match:
                logger.error(f"未找到匹配的比赛: {target_home} vs {target_away}")
                return False
            
            # 5. 确定投注市场和参数
            event_id = matched_match.get('event_id')
            period_num = 0  # 默认全场
            
            # 根据bet_info中的period确定period_num
            # 注意：API文档说明 periodNum：1为上半场，0为全场
            period = bet_info.get('period', '全场')
            if period == '上半场':
                period_num = 1  # 半场使用1
            elif period == '下半场':
                period_num = 2
            elif period == '第一节':
                period_num = 5
            elif period == '第二节':
                period_num = 6
            elif period == '第三节':
                period_num = 7
            elif period == '第四节':
                period_num = 8
            else:
                period_num = 0  # 全场使用0
            
            # 确定投注类型和盘口
            if target_bet_type == '让球':
                bet_type = 2  # 让球盘（修正：应该是2，不是1）
                team_side = 'home' if target_team == target_home else 'away'
                handicap = target_handicap
            elif target_bet_type == '大小球':
                bet_type = 3  # 大小球盘
                team_side = 'over' if target_team == '大' else 'under'
                handicap = target_handicap
            elif target_bet_type == '独赢':
                bet_type = 1  # 独赢盘（修正：应该是1，不是0）
                team_side = 'home' if target_team == target_home else 'away'
                handicap = '0'
            else:
                logger.error(f"不支持的投注类型: {target_bet_type}")
                return False
            
            # 查找匹配的盘口
            markets = matched_match.get('markets', {})
            line_id = None
            
            # 设置盘口容忍度，允许在一定范围内的盘口被接受
            handicap_tolerance = 1.0  # 默认容忍度为1.0
            
            # 尝试获取更多盘口数据
            more_markets_tried = False
            
            def process_markets(markets_dict):
                nonlocal line_id, handicap
                
                if bet_type == 2:  # 让球盘（修正：bet_type从1改为2）
                    if 'handicap' not in markets_dict:
                        logger.error("比赛没有让球盘口")
                        return False
                        
                    handicap_markets = markets_dict['handicap']
                    
                    # 记录所有可用的让球盘口
                    available_handicaps = []
                    for market in handicap_markets:
                        if len(market) > 2:
                            # 标准化盘口值（让球盘口）
                            normalized_value = self._normalize_handicap(market[2], is_total=False)
                            original_value = market[2]

                            # 提取赔率信息
                            home_odds = market[3] if len(market) > 3 else 0.0
                            away_odds = market[4] if len(market) > 4 else 0.0
                            odds = home_odds if team_side == 'home' else away_odds

                            # 尝试从多个位置获取line_id
                            line_id = None
                            if len(market) > 7 and market[7] is not None:
                                line_id = market[7]
                            elif len(market) > 5 and market[5] is not None:
                                line_id = market[5]
                            elif len(market) > 6 and market[6] is not None:
                                line_id = market[6]

                            # 记录市场数据结构以便调试
                            logger.debug(f"让球盘口数据: {market}, 提取的line_id: {line_id}")

                            available_handicaps.append({
                                'value': normalized_value,
                                'original_value': original_value,
                                'line_id': line_id,
                                'odds': odds,
                                'home_odds': home_odds,
                                'away_odds': away_odds
                            })
                    
                    logger.info(f"可用的让球盘口: {[h['value'] for h in available_handicaps]}")
                    logger.debug(f"原始盘口值: {[h['original_value'] for h in available_handicaps]}")
                    
                    # 标准化目标盘口值
                    target_handicap = self._normalize_handicap(handicap)
                    logger.info(f"标准化后的目标盘口值: {target_handicap}")
                    
                    # 尝试精确匹配
                    exact_matches = []
                    for market_info in available_handicaps:
                        if market_info['value'] == target_handicap:
                            exact_matches.append(market_info)
                    
                    if exact_matches:
                        # 如果有多个精确匹配，选择赔率最接近的一个
                        if len(exact_matches) > 1:
                            target_odds = bet_info.get('odds', 0)
                            closest_match = min(exact_matches, key=lambda x: abs(float(x['odds']) - float(target_odds)) if target_odds and x['odds'] else float('inf'))
                            line_id = closest_match['line_id']
                            handicap = closest_match['original_value']
                            logger.info(f"找到多个精确匹配的让球盘口，选择赔率最接近的: {closest_match['value']}, 赔率: {closest_match['odds']}, line_id={line_id}")
                        else:
                            line_id = exact_matches[0]['line_id']
                            handicap = exact_matches[0]['original_value']
                            logger.info(f"找到精确匹配的让球盘口: {exact_matches[0]['value']}, line_id={line_id}")
                        return True
                    
                    # 如果没有精确匹配，尝试数值匹配（保留符号，确保盘口方向正确）
                    if not line_id:
                        try:
                            # 解析目标盘口值，保留符号
                            target_value = float(target_handicap.replace('+', ''))
                            closest_market = None
                            min_diff = float('inf')

                            for market_info in available_handicaps:
                                try:
                                    # 解析市场盘口值，保留符号
                                    market_value = float(market_info['value'].replace('+', ''))

                                    # 只有符号相同的盘口才能匹配
                                    if (target_value >= 0 and market_value >= 0) or (target_value < 0 and market_value < 0):
                                        diff = abs(market_value - target_value)

                                        # 更新最接近的盘口
                                        if diff < min_diff:
                                            min_diff = diff
                                            closest_market = market_info
                                except ValueError:
                                    continue

                            # 如果找到了最接近的盘口，并且在容忍范围内
                            if closest_market and min_diff <= handicap_tolerance:
                                line_id = closest_market['line_id']
                                handicap = closest_market['original_value']  # 使用原始盘口值，因为API可能需要原始格式
                                logger.info(f"找到最接近的让球盘口: {closest_market['value']} (差值: {min_diff}), line_id={line_id}")
                                return True
                            else:
                                logger.warning(f"未找到符号匹配的让球盘口: 目标={target_handicap}, 可用盘口符号不匹配")
                        except ValueError:
                            logger.error(f"无法将盘口值转换为数值: {handicap}")
                    
                    # 如果仍然没有找到匹配的盘口，提供手动选择选项
                    if not line_id and available_handicaps:
                        print(f"\n未找到匹配的让球盘口: {handicap}")
                        print("可用的让球盘口:")
                        for idx, market_info in enumerate(available_handicaps):
                            home_odds = market_info['home_odds']
                            away_odds = market_info['away_odds']
                            handicap_value = market_info['value']
                            # 显示更清楚的盘口含义
                            print(f"{idx+1}. 主队 -{handicap_value} (赔率: {home_odds}) / 客队 +{handicap_value} (赔率: {away_odds})")
                        
                        try:
                            choice = input("\n请选择盘口 (输入序号，0表示取消): ")
                            if choice == '0':
                                print("已取消投注")
                                return False
                                
                            choice_idx = int(choice) - 1
                            if choice_idx >= 0 and choice_idx < len(available_handicaps):
                                selected_market = available_handicaps[choice_idx]
                                line_id = selected_market['line_id']
                                handicap = selected_market['original_value']  # 使用原始盘口值
                                logger.info(f"手动选择让球盘口: {selected_market['value']}, line_id={line_id}")
                                return True
                            else:
                                print("无效选择")
                                return False
                        except ValueError:
                            print("无效输入")
                            return False
                    
                elif bet_type == 3:  # 大小球盘
                    if 'totals' not in markets_dict:
                        logger.error("比赛没有大小球盘口")
                        return False
                        
                    totals_markets = markets_dict['totals']
                    
                    # 记录所有可用的大小球盘口
                    available_totals = []
                    for market in totals_markets:
                        if len(market) > 2:
                            # 大小球盘口格式可能是["162.5",162.5,"2.150","1.588",48821445799,1,50.00,1]
                            # 或者是其他格式，需要适应不同的格式
                            
                            # 尝试从不同位置提取盘口值
                            total_value = None
                            if isinstance(market[0], str) and market[0].replace('.', '', 1).isdigit():
                                # 如果第一个元素是字符串且可以转换为数字
                                total_value = market[0]
                            elif isinstance(market[1], (int, float)) or (isinstance(market[1], str) and market[1].replace('.', '', 1).isdigit()):
                                # 如果第二个元素是数字或可以转换为数字
                                total_value = market[1]
                            elif isinstance(market[2], str) and market[2].replace('.', '', 1).isdigit():
                                # 如果第三个元素是字符串且可以转换为数字
                                total_value = market[2]
                            else:
                                # 如果都不是，使用第一个元素
                                total_value = market[0]
                            
                            # 尝试从不同位置提取赔率
                            over_odds = None
                            under_odds = None
                            line_id = None
                            
                            # 尝试提取大球赔率
                            if len(market) > 2 and (isinstance(market[2], (int, float)) or (isinstance(market[2], str) and market[2].replace('.', '', 1).isdigit())):
                                over_odds = market[2]
                            elif len(market) > 3 and (isinstance(market[3], (int, float)) or (isinstance(market[3], str) and market[3].replace('.', '', 1).isdigit())):
                                over_odds = market[3]
                            
                            # 尝试提取小球赔率
                            if len(market) > 3 and (isinstance(market[3], (int, float)) or (isinstance(market[3], str) and market[3].replace('.', '', 1).isdigit())):
                                under_odds = market[3]
                            elif len(market) > 4 and (isinstance(market[4], (int, float)) or (isinstance(market[4], str) and market[4].replace('.', '', 1).isdigit())):
                                under_odds = market[4]
                            
                            # 尝试提取line_id
                            if len(market) > 4 and isinstance(market[4], (int, float, str)):
                                line_id = market[4]
                            elif len(market) > 7 and isinstance(market[7], (int, float, str)):
                                line_id = market[7]
                            
                            # 记录原始盘口值和标准化盘口值（大小球盘口）
                            original_value = total_value
                            normalized_value = self._normalize_handicap(total_value, is_total=True)
                            
                            # 根据team_side选择赔率
                            odds = over_odds if team_side == 'over' else under_odds
                            
                            # 添加到可用盘口列表
                            available_totals.append({
                                'value': normalized_value,
                                'original_value': original_value,
                                'line_id': line_id,
                                'odds': odds,
                                'over_odds': over_odds,
                                'under_odds': under_odds
                            })
                            
                            # 记录提取的盘口信息
                            logger.debug(f"提取大小球盘口: 值={normalized_value}, 大球赔率={over_odds}, 小球赔率={under_odds}, line_id={line_id}")
                    
                    logger.info(f"可用的大小球盘口: {[t['value'] for t in available_totals]}")
                    logger.debug(f"原始盘口值: {[t['original_value'] for t in available_totals]}")
                    
                    # 标准化目标盘口值（大小球盘口）
                    target_handicap = self._normalize_handicap(handicap, is_total=True)
                    logger.info(f"标准化后的目标盘口值: {target_handicap}")
                    
                    # 尝试精确匹配
                    exact_matches = []
                    for market_info in available_totals:
                        if market_info['value'] == target_handicap:
                            exact_matches.append(market_info)
                    
                    if exact_matches:
                        # 如果有多个精确匹配，选择赔率最接近的一个
                        if len(exact_matches) > 1:
                            target_odds = bet_info.get('odds', 0)
                            closest_match = min(exact_matches, key=lambda x: abs(float(x['odds']) - float(target_odds)) if target_odds and x['odds'] else float('inf'))
                            line_id = closest_match['line_id']
                            handicap = closest_match['original_value']
                            logger.info(f"找到多个精确匹配的大小球盘口，选择赔率最接近的: {closest_match['value']}, 赔率: {closest_match['odds']}, line_id={line_id}")
                        else:
                            line_id = exact_matches[0]['line_id']
                            handicap = exact_matches[0]['original_value']
                            logger.info(f"找到精确匹配的大小球盘口: {exact_matches[0]['value']}, line_id={line_id}")
                        return True
                    
                    # 如果没有精确匹配，尝试数值匹配（忽略符号）
                    if not line_id:
                        try:
                            # 移除符号，只比较数值
                            target_value = float(target_handicap.replace('+', '').replace('-', ''))
                            closest_market = None
                            min_diff = float('inf')
                            
                            for market_info in available_totals:
                                try:
                                    market_value = float(market_info['value'].replace('+', '').replace('-', ''))
                                    diff = abs(market_value - target_value)
                                    
                                    # 更新最接近的盘口
                                    if diff < min_diff:
                                        min_diff = diff
                                        closest_market = market_info
                                except ValueError:
                                    continue
                            
                            # 如果找到了最接近的盘口，并且在容忍范围内
                            if closest_market and min_diff <= handicap_tolerance:
                                line_id = closest_market['line_id']
                                handicap = closest_market['original_value']  # 使用原始盘口值，因为API可能需要原始格式
                                logger.info(f"找到最接近的大小球盘口: {closest_market['value']} (差值: {min_diff}), line_id={line_id}")
                                return True
                        except ValueError:
                            logger.error(f"无法将盘口值转换为数值: {handicap}")
                    
                    # 如果仍然没有找到匹配的盘口，提供手动选择选项
                    if not line_id and available_totals:
                        print(f"\n未找到匹配的大小球盘口: {handicap}")
                        print("可用的大小球盘口:")
                        for idx, market_info in enumerate(available_totals):
                            team_odds = market_info['over_odds'] if team_side == 'over' else market_info['under_odds']
                            print(f"{idx+1}. 盘口值: {market_info['value']}, 赔率: {team_odds}")
                        
                        try:
                            choice = input("\n请选择盘口 (输入序号，0表示取消): ")
                            if choice == '0':
                                print("已取消投注")
                                return False
                                
                            choice_idx = int(choice) - 1
                            if choice_idx >= 0 and choice_idx < len(available_totals):
                                selected_market = available_totals[choice_idx]
                                line_id = selected_market['line_id']
                                handicap = selected_market['original_value']  # 使用原始盘口值
                                logger.info(f"手动选择大小球盘口: {selected_market['value']}, line_id={line_id}")
                                return True
                            else:
                                print("无效选择")
                                return False
                        except ValueError:
                            print("无效输入")
                            return False
                    
                return line_id is not None
            
            # 首先尝试使用现有市场数据
            if not process_markets(markets) and not more_markets_tried and bet_type != 1:
                # 如果没有找到匹配的盘口，尝试获取更多盘口数据
                logger.info(f"未找到匹配的盘口，尝试获取更多盘口数据: {event_id}")
                more_markets_tried = True
                success, more_markets_data = self.api.get_match_more_markets(event_id)
                
                if success and more_markets_data:
                    logger.info(f"成功获取更多盘口数据，开始解析: {event_id}")
                    # 解析更多盘口数据
                    more_matches = self.api.parse_matches(more_markets_data)
                    if more_matches:
                        logger.info(f"解析到 {len(more_matches)} 场比赛的更多盘口数据")
                        for match in more_matches:
                            if match.get('event_id') == event_id:
                                logger.info(f"成功找到匹配比赛的更多盘口数据: {event_id}")
                                # 打印市场信息
                                markets_info = match.get('markets', {})
                                
                                # 记录找到的盘口数量
                                handicap_count = len(markets_info.get('handicap', []))
                                totals_count = len(markets_info.get('totals', []))
                                logger.info(f"更多盘口数据中的让球盘口数量: {handicap_count}, 大小球盘口数量: {totals_count}")
                                
                                # 打印前几个盘口值
                                if 'handicap' in markets_info and handicap_count > 0:
                                    logger.info("让球盘口值示例:")
                                    for i, market in enumerate(markets_info['handicap'][:5]):
                                        if len(market) > 2:
                                            logger.info(f"  盘口 {i+1}: 值={market[2]}, 主队赔率={market[3]}, 客队赔率={market[4]}")
                                
                                if 'totals' in markets_info and totals_count > 0:
                                    logger.info("大小球盘口值示例:")
                                    for i, market in enumerate(markets_info['totals'][:5]):
                                        if len(market) > 2:
                                            logger.info(f"  盘口 {i+1}: 值={market[2]}, 大球赔率={market[3]}, 小球赔率={market[4]}")
                                
                                # 使用更多盘口数据重新尝试匹配
                                if process_markets(markets_info):
                                    logger.info("使用更多盘口数据成功匹配到盘口")
                                    return True
                                else:
                                    logger.warning("使用更多盘口数据仍然无法匹配到盘口")
                                break
                        else:
                            logger.warning(f"在更多盘口数据中未找到匹配的比赛: {event_id}")
                    else:
                        logger.warning("解析更多盘口数据失败，未找到任何比赛")
                else:
                    error_msg = more_markets_data if not success else '数据为空'
                    logger.warning(f"获取更多盘口数据失败: {error_msg}")
            
            if not line_id and bet_type != 1:
                logger.error(f"未找到匹配的盘口: {target_bet_type}={target_handicap}, 目标赔率: {bet_info.get('odds')}")
                print(f"\n未找到匹配的盘口: {target_bet_type}={target_handicap}")
                print("请尝试手动投注或选择其他比赛")
                return False
            
            # 6. 获取投注表单
            logger.info(f"获取投注表单: event_id={event_id}, line_id={line_id}, period_num={period_num}, bet_type={bet_type}, team_side={team_side}, handicap={handicap}")
            
            # 添加重试逻辑
            max_retries = 3
            retry_count = 0
            bet_form = None
            
            while retry_count < max_retries:
                success, result = self.api.get_bet_form(event_id, line_id, period_num, bet_type, team_side, handicap)
                if success:
                    bet_form = result
                    break
                else:
                    retry_count += 1
                    logger.warning(f"获取投注表单失败，正在重试 ({retry_count}/{max_retries}): {result}")
                    time.sleep(1)  # 等待1秒后重试
            
            if not bet_form:
                logger.error(f"获取投注表单失败，已重试 {max_retries} 次")
                print("获取投注表单失败，请稍后再试")
                return False
            
            # 确保bet_form是一个列表，并且包含至少一个元素
            if not isinstance(bet_form, list) or len(bet_form) == 0:
                logger.error(f"投注表单格式不正确: {bet_form}")
                print("投注表单格式不正确，请稍后再试")
                return False
            
            # 验证第一个投注选项是否包含必要的字段
            first_option = bet_form[0]
            if not isinstance(first_option, dict) or not all(key in first_option for key in ["selectionId", "oddsId", "odds"]):
                logger.error(f"投注选项缺少必要字段: {first_option}")
                print("投注选项缺少必要字段，请稍后再试")
                return False
            
            # 记录要使用的投注选项
            logger.info(f"将使用投注选项: selectionId={first_option.get('selectionId')}, oddsId={first_option.get('oddsId')}, odds={first_option.get('odds')}")
            
            # 7. 执行投注
            logger.info(f"执行投注: 金额={stake}")
            
            # 添加重试逻辑
            retry_count = 0
            bet_result = None
            
            while retry_count < max_retries:
                # 直接传递整个bet_form给place_bet方法，让它提取第一个投注选项
                success, result = self.api.place_bet(bet_form, stake)
                if success:
                    bet_result = result
                    break
                else:
                    error_message = str(result)
                    # 检查是否是服务器错误
                    if "500" in error_message or "服务器" in error_message:
                        retry_count += 1
                        logger.warning(f"投注失败，服务器错误，正在重试 ({retry_count}/{max_retries}): {error_message}")
                        time.sleep(1)  # 等待1秒后重试
                    else:
                        # 检查是否是连接错误
                        if "ConnectionError" in error_message or "RemoteDisconnected" in error_message or "Connection aborted" in error_message:
                            retry_count += 1
                            logger.warning(f"投注失败，连接错误，正在重试 ({retry_count}/{max_retries}): {error_message}")
                            # 连接错误时等待更长时间
                            time.sleep(3)
                            # 尝试重新登录
                            if hasattr(self.api, 'check_login_status'):
                                self.api.check_login_status()
                        # 如果不是服务器错误，检查是否是其他已知错误
                        elif "余额不足" in error_message or "insufficient" in error_message.lower():
                            logger.error(f"投注失败: 账户余额不足")
                            print(f"投注失败: 账户余额不足，请充值后再试")
                            return False
                        elif "盘口已关闭" in error_message or "market closed" in error_message.lower():
                            logger.error(f"投注失败: 盘口已关闭")
                            print(f"投注失败: 盘口已关闭，请选择其他盘口")
                            return False
                        elif "赔率变动" in error_message or "odds changed" in error_message.lower():
                            logger.error(f"投注失败: 赔率已变动")
                            print(f"投注失败: 赔率已变动，请刷新后重试")
                            return False
                        else:
                            # 其他未知错误
                            logger.error(f"投注失败: {error_message}")
                            print(f"投注失败: {error_message}")
                            return False
            
            if not bet_result:
                logger.error(f"投注失败，已重试 {max_retries} 次")
                print("投注失败，服务器可能繁忙，请稍后再试")
                return False
            
            logger.info(f"投注成功: {bet_result}")
            print("\n投注成功！")
            
            # 8. 保存投注记录
            bet_record = {
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "home_team": target_home,
                "away_team": target_away,
                "bet_type": target_bet_type,
                "bet_team": target_team,
                "handicap": target_handicap,
                "odds": bet_info.get('odds'),
                "stake": stake,
                "result": "pending"
            }
            
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.bet_record_file), exist_ok=True)
            
            # 读取现有记录
            existing_records = []
            if os.path.exists(self.bet_record_file):
                try:
                    with open(self.bet_record_file, 'r', encoding='utf-8') as f:
                        existing_records = json.load(f)
                except Exception as e:
                    logger.error(f"读取投注记录文件出错: {e}")
            
            # 添加新记录
            existing_records.append(bet_record)
            
            # 保存记录
            try:
                with open(self.bet_record_file, 'w', encoding='utf-8') as f:
                    json.dump(existing_records, f, ensure_ascii=False, indent=2)
                logger.info(f"投注记录已保存到: {self.bet_record_file}")
            except Exception as e:
                logger.error(f"保存投注记录失败: {e}")
            
            # 验证投注是否成功
            print("正在验证投注结果...")
            time.sleep(2)  # 等待2秒，确保投注已处理
            success, wagers_data = self.api.get_pending_wagers()
            
            if success:
                # 检查是否有未结算注单
                page_info = wagers_data.get('page', {})
                records = page_info.get('records', [])
                
                if records:
                    print("投注已确认，未结算注单已更新")
                else:
                    print("警告: 投注已接受，但未找到相应的未结算注单，请检查账户状态")
            else:
                print("投注已接受，但无法验证未结算注单状态")
            
            return True
            
        except Exception as e:
            logger.error(f"执行投注时出错: {str(e)}", exc_info=True)
            print(f"投注过程中出错: {str(e)}")
            return False

    def _parse_bet_info_from_data(self, bet_data):
        """从套利数据中解析投注信息"""
        try:
            # 获取比赛信息
            match = bet_data.get("match", "")
            teams = match.split(" - ")
            if len(teams) != 2:
                logger.warning(f"无法解析比赛队伍: {match}")
                return None

            home_team, away_team = teams
            market_and_bet_type = bet_data.get("market_and_bet_type")
            market_param = bet_data.get("market_param", "0")
            koef = bet_data.get("koef", "0")
            period_id = bet_data.get("period_id", 0)
            description = bet_data.get("description", "")
            league = bet_data.get("league", "")  # 提取联赛信息
            
            # 记录原始数据以便调试
            logger.debug(f"解析投注数据: match={match}, market_and_bet_type={market_and_bet_type}, market_param={market_param}, koef={koef}")
            
            # 尝试转换赔率为浮点数
            try:
                odds = float(koef)
            except:
                odds = 0.0
                
            # 根据盘口类型确定投注类型
            bet_type = "未知"
            bet_team = None
            handicap_value = None
            
            # 处理投注类型和盘口
            if market_and_bet_type == 17:  # 主队让球
                bet_type = "让球"
                bet_team = home_team
                handicap_value = self._normalize_handicap(market_param, is_total=False)
            elif market_and_bet_type == 18:  # 客队让球
                bet_type = "让球"
                bet_team = away_team
                handicap_value = self._normalize_handicap(market_param, is_total=False)
            elif market_and_bet_type == 19:  # 大球
                bet_type = "大小球"
                bet_team = "大"
                handicap_value = self._normalize_handicap(market_param, is_total=True)
            elif market_and_bet_type == 20:  # 小球
                bet_type = "大小球"
                bet_team = "小"
                handicap_value = self._normalize_handicap(market_param, is_total=True)
            elif market_and_bet_type in [1, 2]:  # 独赢盘口: 1=主胜, 2=客胜
                bet_type = "独赢"
                bet_team = home_team if market_and_bet_type == 1 else away_team
                handicap_value = "0"
            elif isinstance(description, str) and description:
                # 从描述中提取信息
                if "让球" in description or "+" in market_param or "-" in market_param:
                    bet_type = "让球"
                    if home_team in description:
                        bet_team = home_team
                    elif away_team in description:
                        bet_team = away_team
                    else:
                        bet_team = home_team  # 默认为主队
                    handicap_value = self._normalize_handicap(market_param, is_total=False)
                elif "大" in description:
                    bet_type = "大小球"
                    bet_team = "大"
                    handicap_value = self._normalize_handicap(market_param, is_total=True)
                elif "小" in description:
                    bet_type = "大小球"
                    bet_team = "小"
                    handicap_value = self._normalize_handicap(market_param, is_total=True)
            else:
                logger.warning(f"不支持的投注类型: {market_and_bet_type}，描述: {description}")
                return None
                
            # 确定比赛阶段
            bet_period = "全场"
            if period_id == 3:
                bet_period = "全场"
            elif period_id == 5:
                bet_period = "第一节"
            elif period_id == 6:
                bet_period = "第二节"
            elif period_id == 7:
                bet_period = "第三节"
            elif period_id == 8:
                bet_period = "第四节"
            elif period_id == 10:
                bet_period = "上半场"
            elif period_id == 13:
                bet_period = "下半场"
            
            # 构建结果对象
            result = {
                "home_team": home_team,
                "away_team": away_team,
                "bet_type": bet_type,
                "bet_team": bet_team,
                "handicap_value": handicap_value,
                "odds": odds,
                "period": bet_period,
                "league": league,  # 包含联赛信息
                "original_data": bet_data
            }
            
            logger.info(f"解析结果: {result['home_team']} vs {result['away_team']}, {result['bet_type']}={result['bet_team']}, 盘口={result['handicap_value']}, 赔率={result['odds']}")
            return result
        except Exception as e:
            logger.error(f"解析投注信息时出错: {str(e)}")
            return None
            
    def _calculate_team_match_score(self, target_home, target_away, home_team, away_team, target_league=None, actual_league=None):
        """
        计算队名匹配分数，增加联赛匹配逻辑以区分男女比赛

        参数:
            target_home: 目标主队名
            target_away: 目标客队名
            home_team: 实际主队名
            away_team: 实际客队名
            target_league: 目标联赛名称（可选）
            actual_league: 实际联赛名称（可选）

        返回:
            匹配分数 (0-1)
        """
        try:
            # 标准化队名（去除空格，转小写）
            target_home_norm = target_home.lower().strip()
            target_away_norm = target_away.lower().strip()
            home_team_norm = home_team.lower().strip()
            away_team_norm = away_team.lower().strip()

            # 计算主队匹配分数
            home_score = 0
            if target_home_norm == home_team_norm:
                home_score = 1.0
            elif target_home_norm in home_team_norm or home_team_norm in target_home_norm:
                home_score = 0.8
            elif self._contains_key_words(target_home_norm, home_team_norm):
                home_score = 0.6

            # 计算客队匹配分数
            away_score = 0
            if target_away_norm == away_team_norm:
                away_score = 1.0
            elif target_away_norm in away_team_norm or away_team_norm in target_away_norm:
                away_score = 0.8
            elif self._contains_key_words(target_away_norm, away_team_norm):
                away_score = 0.6

            # 基础队名匹配分数
            team_score = (home_score + away_score) / 2

            # 如果提供了联赛信息，进行联赛匹配检查
            league_penalty = 0
            if target_league and actual_league:
                target_gender = self._detect_gender_from_league(target_league)
                actual_gender = self._detect_gender_from_league(actual_league)

                # 如果性别不匹配，大幅降低匹配分数
                if target_gender != actual_gender and target_gender != 'unknown' and actual_gender != 'unknown':
                    league_penalty = 0.7  # 性别不匹配时降低70%的分数
                    logger.debug(f"性别不匹配: 目标联赛='{target_league}'({target_gender}) vs 实际联赛='{actual_league}'({actual_gender})")

            # 应用联赛惩罚
            final_score = team_score * (1 - league_penalty)

            logger.debug(f"队名匹配分数: {target_home} vs {home_team} = {home_score:.2f}, {target_away} vs {away_team} = {away_score:.2f}, 队名分数: {team_score:.2f}, 联赛惩罚: {league_penalty:.2f}, 最终分数: {final_score:.2f}")

            return final_score

        except Exception as e:
            logger.error(f"计算队名匹配分数时出错: {e}")
            return 0

    def _detect_gender_from_league(self, league_name):
        """
        从联赛名称检测比赛性别

        参数:
            league_name: 联赛名称

        返回:
            'male': 男子比赛
            'female': 女子比赛
            'unknown': 无法确定
        """
        try:
            if not league_name:
                return 'unknown'

            league_lower = league_name.lower().strip()

            # 女子比赛关键词
            female_keywords = [
                '女子', '女篮', '女', 'women', 'woman', 'female', 'ladies', 'lady',
                'wnba', 'wbb', 'wcba', 'w-', '-w', ' w ', 'girls', 'girl'
            ]

            # 男子比赛关键词（通常不明确标注，但有些会有）
            male_keywords = [
                '男子', '男篮', '男', 'men', 'male', 'boys', 'boy',
                'nba', 'mbb', 'cba', 'm-', '-m', ' m '
            ]

            # 检查女子关键词
            for keyword in female_keywords:
                if keyword in league_lower:
                    return 'female'

            # 检查男子关键词
            for keyword in male_keywords:
                if keyword in league_lower:
                    return 'male'

            # 如果没有明确的性别标识，默认认为是男子比赛
            # 因为大多数联赛不标注性别时通常是男子比赛
            return 'male'

        except Exception as e:
            logger.error(f"检测联赛性别时出错: {e}")
            return 'unknown'

    def _contains_key_words(self, name1, name2):
        """检查两个队名是否包含相同的关键词"""
        try:
            # 分割队名为单词
            words1 = set(name1.split())
            words2 = set(name2.split())

            # 检查是否有共同单词
            common_words = words1.intersection(words2)

            # 如果有共同单词且单词长度大于2，认为匹配
            return len(common_words) > 0 and any(len(word) > 2 for word in common_words)

        except Exception as e:
            logger.error(f"检查关键词时出错: {e}")
            return False

    def _loose_team_match(self, target_home, target_away, home_team, away_team, target_league=None, actual_league=None):
        """
        宽松的队名匹配，用于最后的尝试，增加联赛匹配以区分男女比赛

        参数:
            target_home: 目标主队名
            target_away: 目标客队名
            home_team: 实际主队名
            away_team: 实际客队名
            target_league: 目标联赛名称（可选）
            actual_league: 实际联赛名称（可选）

        返回:
            是否匹配
        """
        try:
            # 标准化队名
            target_home_norm = target_home.lower().strip()
            target_away_norm = target_away.lower().strip()
            home_team_norm = home_team.lower().strip()
            away_team_norm = away_team.lower().strip()

            # 检查是否有任何部分匹配
            home_match = (target_home_norm in home_team_norm or
                         home_team_norm in target_home_norm or
                         self._contains_key_words(target_home_norm, home_team_norm))

            away_match = (target_away_norm in away_team_norm or
                         away_team_norm in target_away_norm or
                         self._contains_key_words(target_away_norm, away_team_norm))

            # 基础队名匹配
            team_match = home_match or away_match

            # 如果队名匹配，但提供了联赛信息，需要检查性别是否匹配
            if team_match and target_league and actual_league:
                target_gender = self._detect_gender_from_league(target_league)
                actual_gender = self._detect_gender_from_league(actual_league)

                # 如果性别明确不匹配，则不认为是匹配的比赛
                if target_gender != actual_gender and target_gender != 'unknown' and actual_gender != 'unknown':
                    logger.debug(f"宽松匹配中性别不匹配: 目标联赛='{target_league}'({target_gender}) vs 实际联赛='{actual_league}'({actual_gender})")
                    return False

            return team_match

        except Exception as e:
            logger.error(f"宽松队名匹配时出错: {e}")
            return False

    def _normalize_handicap(self, handicap_value, is_total=False):
        """
        标准化盘口值

        参数:
            handicap_value: 盘口值
            is_total: 是否为大小球盘口
        """
        try:
            # 处理None或空值
            if handicap_value is None:
                return "0"

            # 确保是字符串类型
            handicap_str = str(handicap_value).strip()

            # 处理空字符串
            if not handicap_str:
                return "0"

            # 移除引号（如果有）
            handicap_str = handicap_str.replace('"', '').replace("'", "")

            # 尝试转换为浮点数
            value = float(handicap_str.replace('+', ''))

            # 大小球盘口不需要+/-号，只返回数值
            if is_total:
                return str(value)

            # 让球盘口：负值保留-号，正值不带+号，整数不带小数点
            if value < 0:
                return str(value)
            elif value > 0:
                # 修复：正值不带+号
                return str(value)
            else:
                return "0"
        except Exception as e:
            logger.warning(f"标准化盘口值失败: {handicap_value}, 错误: {str(e)}")
            # 如果转换失败，尝试返回字符串形式
            if handicap_value is not None:
                return str(handicap_value)
            return "0"  # 默认返回0

    def _should_bet_on_option(self, bet_info):
        """判断是否应该对这个选项进行投注"""
        try:
            # 检查基本条件
            if not bet_info.get("home_team") or not bet_info.get("away_team"):
                return False

            if not bet_info.get("bet_type") or not bet_info.get("bet_team"):
                return False

            # 从配置文件读取赔率范围
            try:
                from utils.utils import load_config
                config = load_config()
                auto_bet_config = config.get("auto_bet", {})
                min_odds = auto_bet_config.get("min_odds", 1.3)
                max_odds = auto_bet_config.get("max_odds", 5.0)
            except Exception:
                # 如果读取配置失败，使用默认值
                min_odds = 1.3
                max_odds = 5.0

            # 检查赔率是否在配置的范围内
            odds = bet_info.get("odds", 0)
            if odds < min_odds or odds > max_odds:
                logger.debug(f"赔率 {odds} 不在允许范围 [{min_odds}, {max_odds}] 内，跳过")
                return False

            # 这里可以添加更多的投注条件检查
            # 例如：检查黑名单队伍、检查特定联赛等

            return True
        except Exception as e:
            logger.error(f"检查投注条件时出错: {str(e)}")
            return False

    def get_pending_wagers(self):
        """查询未结算注单"""
        if not self.is_logged_in:
            print("请先登录系统")
            return False
            
        print("\n正在查询未结算注单...")
        success, wagers_data = self.api.get_pending_wagers()
        
        if not success:
            print(f"获取未结算注单失败: {wagers_data}")
            return False
            
        if not isinstance(wagers_data, dict):
            print(f"获取未结算注单数据格式错误")
            return False
            
        # 解析数据
        try:
            page_info = wagers_data.get('page', {})
            records = page_info.get('records', [])
            total_records = page_info.get('totalRecords', 0)
            
            if total_records == 0 or not records:
                print("没有未结算注单")
                return True
                
            print(f"\n===== 找到 {total_records} 个未结算注单 =====")
            print(f"{'序号':<5}{'比赛':<40}{'投注信息':<20}{'投注金额':<10}{'可能赢取':<10}{'赔率':<8}{'状态':<8}{'投注时间':<20}")
            print("-" * 123)
            
            for idx, record in enumerate(records):
                # 解析记录数据
                try:
                    if not record or len(record) < 15:  # 确保至少有基本字段
                        logger.warning(f"跳过无效记录: {record}")
                        continue
                    
                    # 提取基本字段，并为每个字段设置默认值
                    wager_id = str(record[7]) if len(record) > 7 and record[7] is not None else "未知"
                    match_name = str(record[9]) if len(record) > 9 and record[9] is not None else "未知比赛"
                    bet_time = str(record[14]) if len(record) > 14 and record[14] is not None else "未知时间"

                    # 综合投注状态：优先使用索引18，如果没有则查找OPEN字段
                    bet_status = "未知"

                    # 首先检查索引18的投注状态
                    if len(record) > 18 and record[18] is not None:
                        status_18 = str(record[18])
                        if status_18 in ["OPEN", "PENDING", "CLOSED"]:
                            bet_status = status_18

                    # 如果索引18没有有效状态，则查找OPEN字段
                    if bet_status == "未知":
                        for i in range(15, min(len(record), 25)):
                            if record[i] in ["OPEN", "CLOSED", "PENDING"]:
                                bet_status = str(record[i])
                                break

                    # 只显示OPEN状态的投注（未结算）
                    if bet_status != "OPEN":
                        logger.debug(f"跳过非OPEN状态投注: ID={wager_id}, 状态={bet_status}")
                        continue
                    
                    # 提取投注类型，将索引22和24的内容组合
                    bet_type = ""
                    if len(record) > 22 and record[22] is not None:
                        bet_type = str(record[22])
                    
                    # 如果有索引24的内容，添加到投注类型中
                    if len(record) > 24 and record[24] is not None and record[24] != record[22]:
                        additional_info = str(record[24])
                        if bet_type:
                            bet_type = f"{bet_type} {additional_info}"
                        else:
                            bet_type = additional_info
                    
                    # 提取投注金额和可能赢取金额
                    stake = 0.0
                    to_win = 0.0
                    
                    # 查找RISK或WIN字段的位置
                    risk_type_index = -1
                    for i, value in enumerate(record):
                        if value == "RISK" or value == "WIN":
                            risk_type_index = i
                            break
                    
                    # 如果找到了RISK字段，获取其后的投注金额和潜在赢取金额
                    if risk_type_index > 0 and risk_type_index + 2 < len(record):
                        # 潜在赢取金额通常在RISK后的第一个位置
                        if record[risk_type_index + 1] is not None:
                            try:
                                to_win = float(record[risk_type_index + 1])
                            except (ValueError, TypeError):
                                to_win = 0.0
                        
                        # 投注金额通常在RISK后的第二个位置
                        if record[risk_type_index + 2] is not None:
                            try:
                                stake = float(record[risk_type_index + 2])
                            except (ValueError, TypeError):
                                stake = 0.0
                    
                    # 如果上面的方法没找到，尝试直接使用索引24（如果存在）
                    if stake == 0.0 and len(record) > 24 and record[24] is not None:
                        try:
                            stake = float(record[24])
                        except (ValueError, TypeError):
                            stake = 0.0

                    # 提取赔率 - 根据抓包发现，赔率字段在索引25
                    odds = 0.0
                    if len(record) > 25 and record[25] is not None:
                        try:
                            odds = float(record[25])
                        except (ValueError, TypeError):
                            # 如果索引25解析失败，尝试通过投注金额和可赢金额计算
                            if stake > 0 and to_win > 0:
                                try:
                                    odds = (to_win + stake) / stake
                                except ZeroDivisionError:
                                    odds = 0.0
                            else:
                                odds = 0.0
                    
                    # 根据索引42的值确定比赛阶段
                    # 索引42 = 0 表示全场，索引42 = 1 表示半场
                    period = "全场"
                    if len(record) > 42 and record[42] is not None:
                        try:
                            event_phase = int(record[42])
                            if event_phase == 1:
                                period = "上半场"  # 半场
                            elif event_phase == 0:
                                period = "全场"    # 全场
                            # 记录索引42的值以便调试
                            logger.debug(f"注单ID: {wager_id}, 索引42值: {event_phase}, 解析为: {period}")
                        except (ValueError, TypeError):
                            # 如果无法转换为整数，回退到基于投注类型文本的判断
                            logger.warning(f"无法将索引42的值 '{record[42]}' 转换为整数，回退到文本判断")
                            if bet_type:
                                if "上半" in bet_type or "上半场" in bet_type or "HOU" in bet_type or "HR" in bet_type:
                                    period = "上半场"
                                elif "下半" in bet_type or "下半场" in bet_type or "DOU" in bet_type or "DR" in bet_type:
                                    period = "下半场"
                                elif "第一节" in bet_type or "第1节" in bet_type or "_1Q" in bet_type:
                                    period = "第一节"
                                elif "第二节" in bet_type or "第2节" in bet_type or "_2Q" in bet_type:
                                    period = "第二节"
                                elif "第三节" in bet_type or "第3节" in bet_type or "_3Q" in bet_type:
                                    period = "第三节"
                                elif "第四节" in bet_type or "第4节" in bet_type or "_4Q" in bet_type:
                                    period = "第四节"
                    else:
                        # 如果索引42不存在，回退到基于投注类型文本的判断
                        if bet_type:
                            if "上半" in bet_type or "上半场" in bet_type or "HOU" in bet_type or "HR" in bet_type:
                                period = "上半场"
                            elif "下半" in bet_type or "下半场" in bet_type or "DOU" in bet_type or "DR" in bet_type:
                                period = "下半场"
                            elif "第一节" in bet_type or "第1节" in bet_type or "_1Q" in bet_type:
                                period = "第一节"
                            elif "第二节" in bet_type or "第2节" in bet_type or "_2Q" in bet_type:
                                period = "第二节"
                            elif "第三节" in bet_type or "第3节" in bet_type or "_3Q" in bet_type:
                                period = "第三节"
                            elif "第四节" in bet_type or "第4节" in bet_type or "_4Q" in bet_type:
                                period = "第四节"
                    
                    # 将比赛阶段信息添加到投注类型中显示
                    if period != "全场":
                        bet_type = f"[{period}] {bet_type}"
                    
                    # 格式化输出，确保所有值都是字符串
                    stake_str = f"{stake:.2f}" if stake != 0.0 else "未知"
                    to_win_str = f"{to_win:.2f}" if to_win != 0.0 else "未知"
                    odds_str = f"{odds:.2f}" if odds != 0.0 else "未知"
                    status_str = bet_status[:7] if bet_status != "未知" else "未知"

                    # 打印记录，添加赔率字段
                    print(f"{idx+1:<5}{match_name[:38]:<40}{bet_type[:18]:<20}{stake_str:<10}{to_win_str:<10}{odds_str:<8}{status_str:<8}{bet_time:<20}")

                    # 记录详细信息到日志
                    logger.debug(f"注单ID: {wager_id}, 比赛: {match_name}, 投注信息: {bet_type}, 金额: {stake_str}, 可赢: {to_win_str}, 赔率: {odds_str}, 状态: {bet_status}")
                    
                except Exception as e:
                    logger.error(f"解析注单记录出错: {str(e)}", exc_info=True)
                    print(f"解析注单记录出错: {str(e)}")
                    continue
            
            # 显示总计信息
            total_stake = wagers_data.get('totalToRisk', 0)
            total_to_win = wagers_data.get('totalToWin', 0)
            if total_stake and total_to_win:
                print("-" * 115)
                print(f"总计: 投注金额 {total_stake:.2f}, 可能赢取 {total_to_win:.2f}")

            # 显示过滤统计信息
            open_count = sum(1 for record in records if self._is_open_status_record(record))
            print(f"过滤统计: 总记录 {len(records)}, OPEN状态 {open_count}")
            
            return True
            
        except Exception as e:
            logger.error(f"处理未结算注单数据出错: {str(e)}", exc_info=True)
            print(f"处理未结算注单数据出错: {str(e)}")
            return False

    def _is_open_status_record(self, record):
        """
        检查记录是否为OPEN状态

        参数:
            record: 投注记录

        返回:
            bool: 是否为OPEN状态
        """
        try:
            if not isinstance(record, list) or len(record) < 20:
                return False

            # 查找OPEN字段
            for i in range(15, min(len(record), 25)):
                if record[i] == "OPEN":
                    return True

            return False
        except Exception:
            return False

def main():
    """主函数"""
    system = PinBetSystem()

    # 登录循环，直到成功登录或用户选择退出
    while True:
        try:
            # 添加项目根目录到Python路径
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)
            if project_root not in sys.path:
                sys.path.append(project_root)

            from utils.utils import load_config
            config = load_config()

            # 获取平博账号信息
            username = config.get("pinbo_api", {}).get("account", {}).get("username")
            password = config.get("pinbo_api", {}).get("account", {}).get("password")

            login_success = False

            # 验证配置
            if not username or not password:
                print("配置文件中未找到平博账号信息，将使用手动输入")
                login_success = system.login()
            else:
                # 确保用户名和密码是字符串类型
                if not isinstance(username, str) or not isinstance(password, str):
                    print(f"配置文件中的账号信息格式不正确，username类型: {type(username).__name__}, password类型: {type(password).__name__}")
                    print("将使用手动输入")
                    login_success = system.login()
                else:
                    print(f"使用配置文件中的账号 {username} 登录")
                    login_success = system.login(username, password)

            # 如果登录成功，跳出循环
            if login_success:
                break

            # 登录失败，询问用户是否重试
            print("\n" + "="*50)
            print("登录失败！可能的原因：")
            print("1. 账号密码错误")
            print("2. 账户余额为0（表示登录实际失败）")
            print("3. 网络连接问题")
            print("4. 需要验证码登录")
            print("="*50)

            while True:
                choice = input("\n请选择操作：\n1. 重新登录\n2. 手动输入账号密码\n3. 退出系统\n请输入选择 (1/2/3): ").strip()

                if choice == "1":
                    print("\n正在重新尝试登录...")
                    break  # 跳出内层循环，继续外层登录循环
                elif choice == "2":
                    print("\n请手动输入账号密码：")
                    if system.login():  # 手动登录
                        login_success = True
                        break
                    else:
                        print("手动登录也失败了")
                        break
                elif choice == "3":
                    print("退出系统")
                    return
                else:
                    print("无效选择，请输入 1、2 或 3")

            # 如果手动登录成功，跳出外层循环
            if login_success:
                break

        except Exception as e:
            print(f"读取配置文件失败: {e}")
            print("将使用手动输入登录")
            if system.login():
                break
            else:
                retry = input("登录失败，是否重试？(y/n): ").strip().lower()
                if retry != 'y':
                    print("退出系统")
                    return

    # 显示菜单
    system.show_menu()

if __name__ == "__main__":
    main()