# 重复投注检测功能说明

## 概述

重复投注检测模块用于防止系统对同一场比赛的同类型盘口进行多次投注，避免不必要的风险。本文档介绍了系统的重复投注检测机制及其配置方法。

## 检测原理

系统通过以下方式判断一个投注是否为重复投注：

1. **基本判断逻辑**：
   - 比赛匹配：通过比较比赛ID或主客队名称
   - 盘口类型匹配：检查是否为相同类型的盘口（如大小球、让球等）

2. **可选的严格匹配**：
   - 投注方向匹配：检查投注方向是否相同（例如"大"vs"小"，主队vs客队）
   - 盘口值匹配：检查盘口值是否在容差范围内

3. **跨日期检测**：
   - 凌晨时段（0-4点）自动检查前一天的记录
   - 定期更新文件路径，确保使用当前日期的记录文件
   - 防止午夜前后的重复投注

## 配置选项

在`config/config.json`中添加以下配置来控制重复投注检测的行为：

```json
"duplicate_check": {
  "strict_matching": false,
  "ou_handicap_tolerance": 5,
  "enable_time_window": false,
  "time_window_minutes": 60
}
```

### 配置项说明

- **strict_matching**：是否启用严格匹配模式
  - `false`：只要比赛ID和盘口类型匹配，即认为是重复（默认）
  - `true`：还需要检查投注方向和盘口值是否匹配

- **ou_handicap_tolerance**：大小球盘口值的容差范围
  - 单位：盘口点数
  - 默认值：5
  - 例如：容差为5时，盘口130.5和133.5会被视为同一盘口

- **enable_time_window**：是否启用时间窗口限制
  - `false`：不限制时间窗口（默认）
  - `true`：只检查指定时间窗口内的投注记录

- **time_window_minutes**：时间窗口大小（分钟）
  - 默认值：60分钟
  - 只在`enable_time_window`为`true`时生效

## 日志说明

系统会在以下情况记录重复投注相关的日志：

1. **检测到重复投注时**：
   ```
   重复投注: [主队] vs [客队], [盘口类型]盘口
   已有记录: [时间], [时段]/[投注方向]/[盘口值], 当前尝试: [时段]/[投注方向]/[盘口值]
   ```

2. **详细的匹配信息**（每60秒最多记录一次）：
   ```
   重复投注检测: 同一场比赛的[盘口类型]盘口已有投注记录，不允许重复投注
   比赛ID匹配: [true/false], 队伍匹配: [true/false], 盘口类型匹配: [true/false]
   投注方向匹配: [true/false], 盘口值匹配: [true/false]
   ```

3. **文件路径更新**：
   ```
   更新投注记录文件路径: [旧路径] -> [新路径]
   ```

## 跨日期处理

系统特别处理了跨日期的情况，以防止午夜前后的重复投注：

1. **凌晨检测**：在凌晨0-4点时，系统会自动检查前一天的投注记录
2. **定期更新**：系统每5分钟更新一次文件路径，确保使用正确的日期
3. **关键操作前更新**：在检查重复和保存记录前，系统会更新文件路径

## 故障排除

如果您发现系统仍然出现重复投注问题，请检查：

1. **日志文件**：查看是否有重复投注的警告日志
2. **投注记录文件**：检查`data/bet_records_[日期].json`文件中的记录
3. **配置设置**：确认`duplicate_check`配置是否正确
4. **跨日期问题**：检查凌晨时段的日志，确认是否正确处理了跨日期情况

如果设置`strict_matching`为`true`仍不能解决问题，可以：

1. 减小`ou_handicap_tolerance`的值
2. 启用`enable_time_window`并设置合适的时间窗口
3. 在系统非高峰期运行，减少并发投注请求 