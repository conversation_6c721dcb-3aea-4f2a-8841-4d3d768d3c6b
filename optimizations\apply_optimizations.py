#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用优化脚本 - 将平博会话优化和皇冠匹配优化集成到现有系统

使用方法:
1. 运行此脚本自动应用优化
2. 重启系统以使用优化功能
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

logger = logging.getLogger(__name__)


def apply_pinbet_optimization():
    """应用平博会话优化"""
    try:
        from optimizations.pinbet_session_optimizer import apply_pinbet_session_optimization
        
        # 查找平博实例
        try:
            from platforms.pinbet_bk import PinBetAPI
            
            # 这里需要根据实际的平博实例获取方式来调整
            # 通常在hedge_main.py或其他地方会有全局实例
            
            print("✅ 平博会话优化准备就绪")
            print("📝 需要在系统初始化时调用 apply_pinbet_session_optimization(pinbet_instance)")
            
            return True
            
        except ImportError as e:
            print(f"❌ 无法导入平博API: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 应用平博优化失败: {e}")
        return False


def apply_crown_optimization():
    """应用皇冠匹配优化"""
    try:
        from optimizations.crown_match_optimizer import apply_crown_match_optimization
        
        # 查找皇冠实例
        try:
            from platforms.hgbet_bk import HGBetAPI
            
            print("✅ 皇冠匹配优化准备就绪")
            print("📝 需要在系统初始化时调用 apply_crown_match_optimization(crown_instance)")
            
            return True
            
        except ImportError as e:
            print(f"❌ 无法导入皇冠API: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 应用皇冠优化失败: {e}")
        return False


def create_integration_patch():
    """创建集成补丁文件"""
    patch_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统优化集成补丁 - 自动应用平博和皇冠优化

在hedge_main.py中的HedgeBettingSystem.__init__方法中添加以下代码：
"""

def apply_system_optimizations(self):
    """应用系统优化"""
    try:
        # 应用平博会话优化
        if hasattr(self, 'pinbet_system') and self.pinbet_system:
            from optimizations.pinbet_session_optimizer import apply_pinbet_session_optimization
            self.pinbet_optimizer = apply_pinbet_session_optimization(self.pinbet_system)
            print("✅ 平博会话优化已应用")
        
        # 应用皇冠匹配优化
        if hasattr(self, 'crown_system') and self.crown_system:
            from optimizations.crown_match_optimizer import apply_crown_match_optimization
            self.crown_optimizer = apply_crown_match_optimization(self.crown_system)
            print("✅ 皇冠匹配优化已应用")
            
    except Exception as e:
        print(f"⚠️ 应用系统优化时出现异常: {e}")

# 在HedgeBettingSystem.__init__方法的最后添加：
# self.apply_system_optimizations()
'''
    
    with open('optimizations/integration_patch.py', 'w', encoding='utf-8') as f:
        f.write(patch_content)
    
    print("📝 已创建集成补丁文件: optimizations/integration_patch.py")


def test_optimizations():
    """测试优化功能"""
    print("\n🧪 测试优化功能...")
    
    # 测试皇冠匹配优化
    try:
        from optimizations.crown_match_optimizer import test_crown_match_optimization
        print("🔍 测试皇冠匹配优化:")
        test_crown_match_optimization()
        print("✅ 皇冠匹配优化测试通过")
    except Exception as e:
        print(f"❌ 皇冠匹配优化测试失败: {e}")
    
    # 测试平博会话优化
    try:
        from optimizations.pinbet_session_optimizer import PinbetSessionOptimizer
        
        # 创建模拟实例进行测试
        class MockPinbet:
            def __init__(self):
                self.session = None
                self.is_logged_in = True
                self.username = "test"
                self.password = "test"
                self.base_url = "https://test.com"
        
        mock_pinbet = MockPinbet()
        optimizer = PinbetSessionOptimizer(mock_pinbet)
        
        # 测试时间戳计算
        idle_time = optimizer.get_session_idle_time()
        assert idle_time >= 0 and idle_time < 60, f"异常的空闲时间: {idle_time}"
        
        print("✅ 平博会话优化测试通过")
        
    except Exception as e:
        print(f"❌ 平博会话优化测试失败: {e}")


def main():
    """主函数"""
    print("🚀 系统优化应用程序")
    print("=" * 50)
    
    print("\n📋 优化内容:")
    print("1. 平博会话激活延迟优化")
    print("   - 修复时间戳计算错误")
    print("   - 优化会话保活策略")
    print("   - 实现快速重连机制")
    
    print("\n2. 皇冠比赛匹配优化")
    print("   - 智能队名标准化")
    print("   - 模糊匹配算法优化")
    print("   - 多级匹配策略")
    
    # 检查优化文件是否存在
    required_files = [
        'optimizations/pinbet_session_optimizer.py',
        'optimizations/crown_match_optimizer.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ 缺少必要文件: {missing_files}")
        return
    
    # 应用优化
    print("\n🔧 应用优化...")
    
    pinbet_success = apply_pinbet_optimization()
    crown_success = apply_crown_optimization()
    
    if pinbet_success and crown_success:
        print("\n✅ 所有优化准备完成!")
        
        # 创建集成补丁
        create_integration_patch()
        
        # 测试优化功能
        test_optimizations()
        
        print("\n📋 下一步操作:")
        print("1. 在 hedge_main.py 的 HedgeBettingSystem.__init__ 方法最后添加:")
        print("   self.apply_system_optimizations()")
        print("2. 将 optimizations/integration_patch.py 中的代码集成到系统中")
        print("3. 重启系统以使用优化功能")
        
    else:
        print("\n⚠️ 部分优化应用失败，请检查错误信息")


if __name__ == "__main__":
    main()