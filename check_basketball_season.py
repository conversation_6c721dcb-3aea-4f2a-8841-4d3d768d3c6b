#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查当前篮球赛季情况
"""

from datetime import datetime
import pytz

def check_basketball_season():
    """检查当前篮球赛季情况"""
    
    # 获取当前时间
    now = datetime.now()
    utc_now = datetime.now(pytz.UTC)
    
    print("=== 当前时间和篮球赛季分析 ===")
    print(f"当前本地时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"当前UTC时间: {utc_now.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 分析当前月份对应的篮球赛季
    month = now.month
    
    print(f"\n=== 篮球赛季分析 (当前月份: {month}) ===")
    
    # NBA赛季分析
    if month in [10, 11, 12, 1, 2, 3, 4]:
        nba_status = "常规赛季"
    elif month in [5, 6]:
        nba_status = "季后赛"
    else:  # 7, 8, 9
        nba_status = "休赛期"
    
    print(f"NBA: {nba_status}")
    
    # WNBA赛季分析
    if month in [5, 6, 7, 8, 9]:
        wnba_status = "常规赛季"
    elif month in [10]:
        wnba_status = "季后赛"
    else:
        wnba_status = "休赛期"
    
    print(f"WNBA: {wnba_status}")
    
    # 欧洲篮球联赛分析
    if month in [10, 11, 12, 1, 2, 3, 4, 5]:
        euroleague_status = "常规赛季/季后赛"
    else:
        euroleague_status = "休赛期"
    
    print(f"欧洲篮球联赛: {euroleague_status}")
    
    # CBA分析
    if month in [10, 11, 12, 1, 2, 3, 4]:
        cba_status = "常规赛季/季后赛"
    else:
        cba_status = "休赛期"
    
    print(f"CBA (中国篮球协会): {cba_status}")
    
    # 总结
    print(f"\n=== 总结 ===")
    if month in [7, 8, 9]:
        print("⚠️  当前是篮球休赛期，大部分主流联赛都在休息")
        print("   这可能解释了为什么平博只有2场比赛")
        print("   通常只有一些国际友谊赛或夏季联赛")
    else:
        print("✅ 当前应该是篮球赛季，应该有更多比赛")
        print("   平博只有2场比赛可能是其他原因造成的")
    
    # 建议
    print(f"\n=== 建议 ===")
    print("1. 检查皇冠平台的篮球比赛数量作为对比")
    print("2. 尝试在不同时间段重新获取数据")
    print("3. 考虑地区限制或账户权限问题")
    print("4. 检查是否有其他API端点可以获取更多比赛")
    
    return {
        "month": month,
        "nba_status": nba_status,
        "wnba_status": wnba_status,
        "euroleague_status": euroleague_status,
        "cba_status": cba_status,
        "is_off_season": month in [7, 8, 9]
    }

if __name__ == "__main__":
    result = check_basketball_season()
    
    # 额外的时间检查
    print(f"\n=== 额外信息 ===")
    now = datetime.now()
    
    # 检查是否是周末
    weekday = now.weekday()  # 0=Monday, 6=Sunday
    if weekday in [5, 6]:  # Saturday, Sunday
        print("📅 今天是周末，可能比赛较少")
    else:
        print("📅 今天是工作日")
    
    # 检查时间
    hour = now.hour
    if 0 <= hour < 6:
        print("🌙 当前是深夜/凌晨时间，可能比赛较少")
    elif 6 <= hour < 12:
        print("🌅 当前是上午时间")
    elif 12 <= hour < 18:
        print("☀️ 当前是下午时间")
    else:
        print("🌆 当前是晚上时间，可能有更多比赛")
