#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
内存缓存管理器
提供高效的内存缓存功能，支持过期时间和LRU淘汰策略
"""

import time
import threading
import logging
from typing import Any, Optional, Dict, Tuple
from collections import OrderedDict

logger = logging.getLogger(__name__)

class MemoryCache:
    """内存缓存管理器"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        """
        初始化缓存管理器
        
        Args:
            max_size: 最大缓存条目数
            default_ttl: 默认过期时间（秒）
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        
        # 使用 OrderedDict 实现 LRU
        self._cache: OrderedDict[str, Tuple[Any, float]] = OrderedDict()
        self._lock = threading.RLock()
        
        # 统计信息
        self._hits = 0
        self._misses = 0
        self._evictions = 0
        
        logger.info(f"初始化内存缓存，最大大小: {max_size}, 默认TTL: {default_ttl}秒")
    
    def _is_expired(self, expire_time: float) -> bool:
        """检查是否过期"""
        return time.time() > expire_time
    
    def _evict_expired(self):
        """清理过期条目"""
        current_time = time.time()
        expired_keys = []
        
        for key, (value, expire_time) in self._cache.items():
            if current_time > expire_time:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._cache[key]
            self._evictions += 1
            logger.debug(f"清理过期缓存: {key}")
    
    def _evict_lru(self):
        """LRU淘汰策略"""
        while len(self._cache) >= self.max_size:
            key, _ = self._cache.popitem(last=False)  # 移除最旧的条目
            self._evictions += 1
            logger.debug(f"LRU淘汰缓存: {key}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            default: 默认值
            
        Returns:
            缓存值或默认值
        """
        with self._lock:
            if key in self._cache:
                value, expire_time = self._cache[key]
                
                if self._is_expired(expire_time):
                    # 过期，删除并返回默认值
                    del self._cache[key]
                    self._misses += 1
                    self._evictions += 1
                    logger.debug(f"缓存过期: {key}")
                    return default
                else:
                    # 命中，移动到末尾（最近使用）
                    self._cache.move_to_end(key)
                    self._hits += 1
                    logger.debug(f"缓存命中: {key}")
                    return value
            else:
                self._misses += 1
                logger.debug(f"缓存未命中: {key}")
                return default
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None):
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒），None使用默认值
        """
        if ttl is None:
            ttl = self.default_ttl
        
        expire_time = time.time() + ttl
        
        with self._lock:
            # 清理过期条目
            self._evict_expired()
            
            # LRU淘汰
            if key not in self._cache:
                self._evict_lru()
            
            # 设置缓存
            self._cache[key] = (value, expire_time)
            self._cache.move_to_end(key)  # 移动到末尾
            
            logger.debug(f"设置缓存: {key}, TTL: {ttl}秒")
    
    def delete(self, key: str) -> bool:
        """
        删除缓存条目
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                logger.debug(f"删除缓存: {key}")
                return True
            return False
    
    def clear(self):
        """清空所有缓存"""
        with self._lock:
            count = len(self._cache)
            self._cache.clear()
            logger.info(f"清空缓存，删除了 {count} 个条目")
    
    def cleanup(self):
        """清理过期条目"""
        with self._lock:
            old_size = len(self._cache)
            self._evict_expired()
            new_size = len(self._cache)
            cleaned = old_size - new_size
            
            if cleaned > 0:
                logger.info(f"清理了 {cleaned} 个过期缓存条目")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self._hits + self._misses
            hit_rate = (self._hits / total_requests * 100) if total_requests > 0 else 0
            
            return {
                "size": len(self._cache),
                "max_size": self.max_size,
                "hits": self._hits,
                "misses": self._misses,
                "evictions": self._evictions,
                "hit_rate": round(hit_rate, 2),
                "total_requests": total_requests
            }
    
    def get_keys(self) -> list:
        """获取所有缓存键"""
        with self._lock:
            return list(self._cache.keys())
    
    def has_key(self, key: str) -> bool:
        """检查键是否存在且未过期"""
        with self._lock:
            if key in self._cache:
                _, expire_time = self._cache[key]
                return not self._is_expired(expire_time)
            return False


class CacheManager:
    """缓存管理器，管理多个命名缓存"""
    
    def __init__(self):
        self._caches: Dict[str, MemoryCache] = {}
        self._lock = threading.Lock()
    
    def get_cache(self, name: str, max_size: int = 1000, default_ttl: int = 300) -> MemoryCache:
        """
        获取或创建命名缓存
        
        Args:
            name: 缓存名称
            max_size: 最大缓存条目数
            default_ttl: 默认过期时间（秒）
            
        Returns:
            缓存实例
        """
        with self._lock:
            if name not in self._caches:
                self._caches[name] = MemoryCache(max_size, default_ttl)
                logger.info(f"创建新缓存: {name}")
            return self._caches[name]
    
    def remove_cache(self, name: str) -> bool:
        """
        移除命名缓存
        
        Args:
            name: 缓存名称
            
        Returns:
            是否移除成功
        """
        with self._lock:
            if name in self._caches:
                del self._caches[name]
                logger.info(f"移除缓存: {name}")
                return True
            return False
    
    def cleanup_all(self):
        """清理所有缓存的过期条目"""
        with self._lock:
            for name, cache in self._caches.items():
                cache.cleanup()
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有缓存的统计信息"""
        with self._lock:
            stats = {}
            for name, cache in self._caches.items():
                stats[name] = cache.get_stats()
            return stats
    
    def clear_all(self):
        """清空所有缓存"""
        with self._lock:
            for name, cache in self._caches.items():
                cache.clear()
            logger.info("清空所有缓存")


# 全局缓存管理器实例
_cache_manager = CacheManager()

def get_cache(name: str = "default", max_size: int = 1000, default_ttl: int = 300) -> MemoryCache:
    """获取缓存实例"""
    return _cache_manager.get_cache(name, max_size, default_ttl)

def get_cache_manager() -> CacheManager:
    """获取缓存管理器实例"""
    return _cache_manager
